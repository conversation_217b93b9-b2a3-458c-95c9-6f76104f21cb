package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.system.impl.ReleaseBizServiceImpl
import com.alibabacloud.devops.appstack.libs.model.`do`.release.AppRelease
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.SearchAppReleaseRequest
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR>
 * @create 2023/7/19 4:04 PM
 **/
class ReleaseBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var appReleaseBizServiceImpl: ReleaseBizServiceImpl

    @Test
    fun searchPaginatedUnderAppTest() {
        val request = SearchAppReleaseRequest(keyword = "", states = listOf(AppRelease.State.RELEASED))
        val records = appReleaseBizServiceImpl.searchPaginatedUnderApp("allEndList", 1, 50, request)
        Assertions.assertEquals("instSn", records.records.get(0).baseAppReleaseInstSn)
    }

}