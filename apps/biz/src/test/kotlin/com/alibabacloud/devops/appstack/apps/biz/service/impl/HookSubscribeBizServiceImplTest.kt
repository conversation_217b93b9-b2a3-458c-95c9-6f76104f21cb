package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.HookSubscribeBizService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.model.UserInfo
import com.alibabacloud.devops.appstack.libs.integration.spring.boot.starter.config.IntegrationProperties
import com.alibabacloud.devops.appstack.libs.integration.spring.boot.starter.service.IntegrationFacades
import com.alibabacloud.devops.appstack.libs.integration.spring.boot.starter.service.facade.HookChannelSubscribeFacade
import com.alibabacloud.devops.appstack.libs.model.api.integration.HookChannelSubscribeApi
import com.alibabacloud.devops.appstack.libs.model.api.integration.mock.HookChannelSubscribeApiMock
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.*
import com.alibabacloud.devops.appstack.libs.model.event.EventResult
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.PaginationQuery
import com.alibabacloud.devops.iam.constant.ProtocolType
import feign.Feign
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.kotlin.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean
import java.util.*

@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class HookSubscribeBizServiceImplTest: BizServerApplicationTests() {
    @Autowired
    lateinit var hookSubscribeBizService: HookSubscribeBizServiceImpl

    @MockBean
    lateinit var iamService: IamService

    private fun getTestEvent(): HookEvent {
        return HookEvent(
            headers = mutableMapOf(
                "X-AppStack-Event" to "Env",
                "X-AppStack-Action" to "Create",
                "X-AppStack-App" to "test app"
            ),
            payload = HookEventPayload(
                id = "test id",
                user = UserInfo(
                    id = "test user id",
                    name = "test user name",
                    email = "test user email"
                ),
                orgId = "test org id",
                time = Date(),
                objectKind = "test object kind",
                objectAttributes = mapOf(
                    "name" to "test object attributes name"
                )
            )
        )
    }

    @Test
    fun create_shouldReturnCreatedHookChannelSubscribe() {
        val hookChannelSubscribe = HookChannelSubscribeApiMock.getMockHookChannelSubscribe()

        given(iamService.can(any(), any(), any())).willReturn(true)
        val result = hookSubscribeBizService.create(hookChannelSubscribe)

        assertEquals(result, hookChannelSubscribe)
    }

    @Test
    fun delete_shouldDeleteHookChannelSubscribe() {
        val sn = "test"

        given(iamService.can(any(), any(), any())).willReturn(true)
        val result = hookSubscribeBizService.delete(sn)

        assertTrue(result)
    }

    @Test
    fun update_shouldUpdateHookChannelSubscribe() {
        val sn = "test"
        val hookChannelSubscribe = HookChannelSubscribeApiMock.getMockHookChannelSubscribe()

        given(iamService.can(any(), any(), any())).willReturn(true)
        val result = hookSubscribeBizService.update(sn, hookChannelSubscribe)

        assertEquals(result, hookChannelSubscribe)
    }

    @Test
    fun get_shouldReturnHookChannelSubscribe() {
        val sn = "test"
        val hookChannelSubscribe = HookChannelSubscribeApiMock.getMockHookChannelSubscribe()

        val result = hookSubscribeBizService.get(sn)

        assertEquals(result, hookChannelSubscribe)
    }

    @Test
    fun findByScopeAndType_shouldReturnHookChannelSubscribeList() {
        val scopeType = "APP"
        val scopeTypeEnum = HookSubscribeScopeType.APP
        val scopeId = "test"
        val channelType = "WEBHOOK"
        val channelTypeEnum = HookChannelType.WEBHOOK
        val hookChannelSubscribe = HookChannelSubscribeApiMock.getMockHookChannelSubscribe()

        given(iamService.can(any(), any(), any())).willReturn(true)
        val result = hookSubscribeBizService.findByScopeAndType(scopeType, scopeId, channelType)

        assertEquals(result, listOf(hookChannelSubscribe))
    }

    @Test
    fun findByScopeAndType_should_throw_BizException_scope_type_unsupported() {
        val scopeType = "test"
        val scopeId = "test"
        val channelType = "WEBHOOK"
        val hookChannelSubscribe = HookChannelSubscribeApiMock.getMockHookChannelSubscribe()

        given(iamService.can(any(), any(), any())).willReturn(true)
        assertThrows<IllegalArgumentException> {
            hookSubscribeBizService.findByScopeAndType(scopeType, scopeId, channelType)
        }
    }

    @Test
    fun findByScopeAndType_should_throw_BizException_channel_type_unsupported() {
        val scopeType = "APP"
        val scopeId = "test"
        val channelType = "test"
        val hookChannelSubscribe = HookChannelSubscribeApiMock.getMockHookChannelSubscribe()

        given(iamService.can(any(), any(), any())).willReturn(true)
        assertThrows<IllegalArgumentException> {
            hookSubscribeBizService.findByScopeAndType(scopeType, scopeId, channelType)
        }
    }

    @Test
    fun invokeEvent_shouldInvokeEvent() {
        val sn = "test"
        val event = getTestEvent()
        val eventResult = EventResult(true, "test")

        given(iamService.can(any(), any(), any())).willReturn(true)
        val result = hookSubscribeBizService.invokeEvent(sn, event)

        assertEquals(result, eventResult)
    }

    @Test
    fun findInvokeLogsPaginated_shouldReturnHookSendLogList() {
        val sn = "test"
        val query = PaginationQuery()
        val event = "Env"
        val action = "Create"
        val hookSendLog = HookChannelSubscribeApiMock.getMockHookSendLog()

        given(iamService.can(any(), any(), any())).willReturn(true)
        val result = hookSubscribeBizService.findInvokeLogsPaginated(sn, query, event, action)

        assertEquals(result.data[0].sn, hookSendLog.sn)
    }

    @Test
    fun getSupportEventTypes_shouldReturnHookEventTypeVOList() {
        val channelType = "WEBHOOK"
        val scopeType = "APP"
        val hookEventTypeVO = HookChannelSubscribeApiMock.getMockHookEventTypeVO()

        val result = hookSubscribeBizService.getSupportEventTypes(channelType, scopeType)

        assertEquals(result, listOf(hookEventTypeVO))
    }
}