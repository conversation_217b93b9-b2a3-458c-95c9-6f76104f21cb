package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.OrgConfigItemBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.ChangeRequestMustBindWorkItemV1OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.OrgConfigItem
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.kotlin.given
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR> <EMAIL>
 * @version : ProjexChangeRequestBizServiceImplTest, v0.1
 * @date : 2024-06-17 16:59
 */
class ProjexChangeRequestBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var projexChangeRequestBizService: ProjexChangeRequestBizServiceImpl

    @Mock
    lateinit var orgConfigItemBizService: OrgConfigItemBizService

    @Test
    fun unbindTest() {
        given(orgConfigItemBizService.find(OrgConfigItem.CHANGE_REQUEST_MUST_BIND_WORKITEM)).willReturn(
            ChangeRequestMustBindWorkItemV1OrgConfigItem(enable = true)
        )
        projexChangeRequestBizService.orgConfigItemBizService = orgConfigItemBizService
        try {
            projexChangeRequestBizService.unbindCrToWorkItem("test", "test")
        } catch (e: BizException) {
            Assertions.assertEquals(ErrorCode.AS_CR_MUST_BIND_WORKITEM, e.errorEntry.code)
        }
    }
}