package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.model.OrgEventRequest
import com.alibabacloud.devops.appstack.apps.biz.model.OrganizationEventData
import com.alibabacloud.devops.appstack.apps.biz.model.OrganizationMemberCreateEventData
import com.alibabacloud.devops.appstack.apps.biz.model.OrganizationMemberRemoveEventData
import com.alibabacloud.devops.appstack.apps.biz.model.OrganizationMemberUpdateEventData
import com.alibabacloud.devops.appstack.libs.model.`do`.Label
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired


/**
 * <AUTHOR> <EMAIL>
 * @version : OrgEventServiceImplTest, v0.1
 * @date : 2021-11-30 10:20
 */
internal class OrgEventServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var jacksonObjectMapper: ObjectMapper

    @Test
    fun handleOrgEvent() {
        val body = """
            {
            	"event": "organization.create",
            	"data": {
            		"extra": {
            			"ip": "*************",
            			"category": "",
            			"trialType": "org"
            		},
            		"organization": {
            			"ip": "",
            			"_id": "61a4894c9cbfc1b4f0468411",
            			"name": "andyliu",
            			"desiredMemberCount": 10,
            			"contact": {
            				"name": "andyliulan刘",
            				"phone": ""
            			},
            			"creator": {
            				"_id": "61a4893ba96ac5d9b0e602c7",
            				"avatarUrl": "https://tcs-devops.aliyuncs.com/thumbnail/112c1b4d2526a19bf8788fcb9faa53358d35/w/100/h/100",
            				"title": "",
            				"email": "<EMAIL>",
            				"lang": "",
            				"name": "andyliulan刘",
            				"region": "us",
            				"isRobot": false,
            				"phone": "",
            				"phoneForLogin": "",
            				"countryCode": "",
            				"latestActived": "2021-11-29T08:03:08.205Z"
            			},
            			"createdAt": "2021-11-29T08:03:24.306Z"
            		},
            		"user": {
            			"_id": "61a4893ba96ac5d9b0e602c7",
            			"avatarUrl": "https://tcs-devops.aliyuncs.com/thumbnail/112c1b4d2526a19bf8788fcb9faa53358d35/w/100/h/100",
            			"title": "",
            			"email": "<EMAIL>",
            			"lang": "",
            			"name": "andyliulan刘",
            			"region": "us",
            			"isRobot": false,
            			"phone": "",
            			"phoneForLogin": "",
            			"countryCode": "",
            			"latestActived": "2021-11-29T08:03:08.205Z"
            		}
            	},
            	"appId": "5e58ddaef67d582abc7ad326",
            	"resourceId": "61a4894c9cbfc1b4f0468411",
            	"resourceType": "organization"
            }
        """.trimIndent()
        val orgEventRequest = jacksonObjectMapper.readValue(body, OrgEventRequest::class.java)
        val organizationEventData = jacksonObjectMapper.readValue<OrganizationEventData>(orgEventRequest.data)
        Assertions.assertEquals("61a4893ba96ac5d9b0e602c7", organizationEventData.user?.id)
        Assertions.assertEquals("61a4894c9cbfc1b4f0468411", organizationEventData.organization.id)
    }


    @Test
    fun testMemberCreateEvent() {
        val body = """
            {
            	"event": "organization.member.create",
            	"data": {
            		"member": [{
            			"_id": "61a4942fd6190210d8ec902f",
            			"name": "雷二朴",
            			"userName": "雷二朴",
            			"memberName": "雷二朴",
            			"avatarUrl": "https://tcs-devops.aliyuncs.com/thumbnail/112cd3851010fd6a0555dffb4076316a9067/w/100/h/100",
            			"_userId": "61a4942fd6190210d8ec902f",
            			"_memberId": "61a4981bdb20fb96553e68b7",
            			"_roleId": "61014d80a7058185874ee741"
            		}],
            		"organization": {
            			"_id": "61014d80f71d4ebf894574e7",
            			"name": "金数信息",
            			"url": "https://devops.aliyun.com/organization/61014d80f71d4ebf894574e7/projects"
            		},
            		"type": ""
            	},
            	"appId": "5e7370223bb69373660601a4",
            	"resourceId": "61014d80f71d4ebf894574e7",
            	"resourceType": "organization"
            }
        """.trimIndent()
        val orgEventRequest = jacksonObjectMapper.readValue(body, OrgEventRequest::class.java)
        val memberCreateEventData =
            jacksonObjectMapper.readValue<OrganizationMemberCreateEventData>(orgEventRequest.data)
        Assertions.assertEquals("61a4942fd6190210d8ec902f", memberCreateEventData.member[0].userId)
        Assertions.assertEquals("61014d80f71d4ebf894574e7", memberCreateEventData.organization.id)
    }

    @Test
    fun testMemberUpdateEvent() {
        val body = """
            {
            	"event": "organization.member.create",
            	"data": {
            		"member": [{
            			"_id": "61a4942fd6190210d8ec902f",
            			"name": "雷二朴",
            			"userName": "雷二朴",
            			"memberName": "雷二朴",
            			"avatarUrl": "https://tcs-devops.aliyuncs.com/thumbnail/112cd3851010fd6a0555dffb4076316a9067/w/100/h/100",
            			"_userId": "61a4942fd6190210d8ec902f",
            			"_memberId": "61a4981bdb20fb96553e68b7",
            			"_roleId": "61014d80a7058185874ee741"
            		}],
            		"organization": {
            			"_id": "61014d80f71d4ebf894574e7",
            			"name": "金数信息",
            			"url": "https://devops.aliyun.com/organization/61014d80f71d4ebf894574e7/projects"
            		},
            		"type": ""
            	},
            	"appId": "5e7370223bb69373660601a4",
            	"resourceId": "61014d80f71d4ebf894574e7",
            	"resourceType": "organization"
            }
        """.trimIndent()
        val orgEventRequest = jacksonObjectMapper.readValue(body, OrgEventRequest::class.java)
        val memberUpdateEventData =
            jacksonObjectMapper.readValue<OrganizationMemberUpdateEventData>(orgEventRequest.data)
        Assertions.assertEquals("61a4942fd6190210d8ec902f", memberUpdateEventData.member[0].userId)
        Assertions.assertEquals("61014d80f71d4ebf894574e7", memberUpdateEventData.organization.id)
    }

    fun testMemberRemoveEvent() {
        val body = """
            {
            	"event": "organization.member.remove",
            	"data": {
            		"member": {
            			"_id": "618b6177c596f7c215e6d2c0",
            			"name": "颜骏",
            			"userName": "颜骏",
            			"memberName": "颜骏",
            			"avatarUrl": "https://tcs-devops.aliyuncs.com/thumbnail/112b37d638e5e6054e8305a9620c39db2d3c/w/100/h/100",
            			"_userId": "618b6177c596f7c215e6d2c0",
            			"_memberId": "618b618dba5e0db70ee6ad10",
            			"_roleId": "5ea95177cd3711dd6400b8d4"
            		},
            		"organization": {
            			"_id": "5ea95177e17c0e0001fda5c8",
            			"name": "浙江远图互联科技股份有限公司",
            			"url": "https://devops.aliyun.com/organization/5ea95177e17c0e0001fda5c8/projects"
            		},
            		"type": "",
            		"user": {
            			"_id": "618b6177c596f7c215e6d2c0",
            			"avatarUrl": "https://tcs-devops.aliyuncs.com/thumbnail/112b37d638e5e6054e8305a9620c39db2d3c/w/100/h/100",
            			"title": "",
            			"email": "<EMAIL>",
            			"lang": "",
            			"name": "颜骏",
            			"region": "us",
            			"isRobot": false,
            			"phone": "",
            			"phoneForLogin": "",
            			"countryCode": "",
            			"latestActived": "2021-11-29T08:38:41.716Z"
            		}
            	},
            	"appId": "5e5e0d3f6d89573f56bb668f",
            	"resourceId": "5ea95177e17c0e0001fda5c8",
            	"resourceType": "organization"
            }
        """.trimIndent()
        val orgEventRequest = jacksonObjectMapper.readValue(body, OrgEventRequest::class.java)
        val eventData = jacksonObjectMapper.readValue<OrganizationMemberRemoveEventData>(orgEventRequest.data)
        Assertions.assertEquals("618b6177c596f7c215e6d2c0", eventData.member?.userId)
        Assertions.assertEquals("5ea95177e17c0e0001fda5c8", eventData.organization.id)
    }
}
