package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.SearchGlobalVarRequest
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.kotlin.given
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean

class GlobalVarBizServiceImplTest: BizServerApplicationTests() {

    @Autowired
    lateinit var globalVarBizService: GlobalVarBizServiceImpl

    @MockBean
    lateinit var iamService: IamService

    @Test
    fun test_listPaginatedCanUse_withManageAccess(){
        given(iamService.can(ProtocolType.AppStack, "any", Action.ORG_VAR_MANAGE)).willReturn(true)
        val result = globalVarBizService.listPaginatedCanUse(1, 10, SearchGlobalVarRequest())
        Assertions.assertTrue(result.records.isEmpty())
        Assertions.assertEquals(0, result.total)
    }

    @Test
    fun test_listPaginatedCanUse_withUserAccess(){
        given(iamService.can(ProtocolType.AppStack, "any", Action.ORG_VAR_MANAGE)).willReturn(false)
        given(iamService.countResourceWithPolicy(
            protocolType = ProtocolType.AppStackVar,
            subjectType = SubjectType.User,
            subjectId = AuthUtil.getUserId(),
            policyName = Action.VAR_USE.code,
            search = ""
        )).willReturn(0)
        given(iamService.searchResourceWithPolicy(
            protocolType = ProtocolType.AppStackVar,
            subjectType = SubjectType.User,
            subjectId = AuthUtil.getUserId(),
            policyName = Action.VAR_USE.code,
            search = "",
            page = 1,
            size = 10,
        )).willReturn(emptyList())
        val result = globalVarBizService.listPaginatedCanUse(1, 10, SearchGlobalVarRequest())
        Assertions.assertTrue(result.records.isEmpty())
        Assertions.assertEquals(0, result.total)
    }
}