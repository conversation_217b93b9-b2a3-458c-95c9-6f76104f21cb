package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.facade.v2.DomainTagFacade
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.service.AuthorizeService
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.app.DomainTag
import com.alibabacloud.devops.appstack.libs.model.request.TagBatchUnbindingRequest
import com.alibabacloud.devops.appstack.libs.model.request.TagSearchRequest
import com.alibabacloud.devops.appstack.libs.model.vo.DomainTagWithCreatorVO
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.given
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean
import javax.annotation.PostConstruct

class DomainTagBizServiceImplTest: BizServerApplicationTests() {
    @Autowired
    lateinit var tagBizService: DomainTagBizServiceImpl

    @Autowired
    lateinit var coreFacades: CoreFacades

    @MockBean
    lateinit var domainTagFacade: DomainTagFacade

    @MockBean
    lateinit var authorizeService: AuthorizeService

    @PostConstruct
    fun init() {
        coreFacades.domainTagFacade = domainTagFacade
    }

    @BeforeEach
    fun beforeEach() {
        given(authorizeService.can(any(), any(), any())).willReturn(true)
    }

    @Test
    fun create_shouldCreateTag() {
        val domainTag = DomainTag("name", "frontendStyle")
        given(domainTagFacade.create(domainTag)).willReturn(domainTag)
        val result = tagBizService.create(domainTag)
        assert(result == domainTag)
        verify(domainTagFacade, times(1)).create(any())
    }

    @Test
    fun update_shouldUpdateTag() {
        val oldName = "name"
        val newName = "newName"
        val domainTag = DomainTag(newName, "frontendStyle")
        given(domainTagFacade.update(oldName, domainTag)).willReturn(domainTag)
        val result = tagBizService.update(oldName, domainTag)
        assert(result == domainTag)
        verify(domainTagFacade, times(1)).update(any(), any())
    }

    @Test
    fun delete_shouldDeleteTag() {
        val name = "name"
        tagBizService.delete(name)
        verify(domainTagFacade, times(1)).delete(any())
    }

    @Test
    fun list_shouldListTag() {
        val req = TagSearchRequest()
        val current = 1L
        val pageSize = 10L
        given(domainTagFacade.list(req, current, pageSize)).willReturn(PageList(
            data = listOf(
                DomainTagWithCreatorVO("name1", "frontendStyle1"),
                DomainTagWithCreatorVO("name2", "frontendStyle2")
            ),
            total = 2,
            current = 1,
            perPage = 10,
            pages = 1
        ))
        val res = tagBizService.list(req, current, pageSize)
        assert(res.data.size == 2)
        assert(res.data[0].name == "name1")
        assert(res.data[1].name == "name2")
        verify(domainTagFacade, times(1)).list(any(), any(), any())
    }

    @Test
    fun listAppNamesByTag_shouldListAppNamesByTag() {
        val tagName = "name"
        given(domainTagFacade.listAppNamesByTag(tagName)).willReturn(listOf("appName1", "appName2"))
        val res = tagBizService.listAppNamesByTag(tagName)
        assert(res.size == 2)
        assert(res[0] == "appName1")
        assert(res[1] == "appName2")
        verify(domainTagFacade, times(1)).listAppNamesByTag(any())
    }

    @Test
    fun listTagsByAppName_shouldListTagsByAppName() {
        val appName = "appName"
        given(domainTagFacade.listTagsByAppName(appName)).willReturn(listOf(
            DomainTag("name1", "frontendStyle1"),
            DomainTag("name2", "frontendStyle2")
        ))
        val res = tagBizService.listTagsByAppName(appName)
        assert(res.size == 2)
        assert(res[0].name == "name1")
        assert(res[1].name == "name2")
        verify(domainTagFacade, times(1)).listTagsByAppName(any())
    }

    @Test
    fun listTagsByAppNames_shouldListTagsByAppNames() {
        val appNames = listOf("appName1", "appName2")
        given(domainTagFacade.listTagsByAppNames(appNames)).willReturn(listOf(
            DomainTag("name1", "frontendStyle1"),
            DomainTag("name2", "frontendStyle2")
        ))
        val res = tagBizService.listTagsByAppNames(appNames)
        assert(res.size == 2)
        assert(res[0].name == "name1")
        assert(res[1].name == "name2")
        verify(domainTagFacade, times(1)).listTagsByAppNames(any())
    }

    @Test
    fun updateTagBindings_shouldUpdateTagBindings() {
        val tagNames = listOf("name1", "name2")
        val appName = "appName"
        tagBizService.updateTagBindings(tagNames, appName)
        verify(domainTagFacade, times(1)).updateTagBindings(any(), any())
    }

    @Test
    fun bind_shouldBindTagsToApp() {
        val tagNames = listOf("name1", "name2")
        val appName = "appName"
        tagBizService.bind(tagNames, appName)
        verify(domainTagFacade, times(1)).bind(any(), any())
    }

    @Test
    fun batchBind_shouldBatchBindTagsToApps() {
        val tagNames = listOf("name1", "name2")
        val appNames = listOf("appName1", "appName2")
        val res = tagBizService.batchBind(tagNames, appNames)
        assert(res.successAppNames[0] == "appName1")
        assert(res.successAppNames[1] == "appName2")
    }

    @Test
    fun batchUnbind_shouldNotBindTagsWhenPermissionDenied() {
        val tagNames = listOf("name1", "name2")
        val appNames = listOf("appName1", "appName2")
        given(authorizeService.can(any(), any(), any())).willReturn(false)
        val res = tagBizService.batchBind(tagNames, appNames)
        assert(res.failAppInfo.size == 2)
    }

    @Test
    fun unbind_shouldUnbindTagsFromApp() {
        val tagNames = listOf("name1", "name2")
        val appName = "appName"
        tagBizService.unbind(tagNames, appName)
        verify(domainTagFacade, times(1)).unbind(any(), any())
    }

    @Test
    fun batchUnbind_shouldBatchUnbindTagsFromApps() {
        val tagNames = listOf("name1", "name2")
        val appNames = listOf("appName1", "appName2")
        val res = tagBizService.batchUnbind(tagNames, appNames)
        assert(res.successAppNames[0] == "appName1")
        assert(res.successAppNames[1] == "appName2")
    }

    @Test
    fun batchUnbind_shouldNotUnbindTagsWhenPermissionDenied() {
        val tagNames = listOf("name1", "name2")
        val appNames = listOf("appName1", "appName2")
        given(authorizeService.can(any(), any(), any())).willReturn(false)
        val res = tagBizService.batchUnbind(tagNames, appNames)
        assert(res.failAppInfo.size == 2)
    }
}