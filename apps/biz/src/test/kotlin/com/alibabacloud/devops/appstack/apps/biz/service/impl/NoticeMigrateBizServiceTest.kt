package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * @author: <EMAIL>
 * @date: 2023-04-17 16:45
 * @version: NoticeMigrateBizServiceTest, v0.1
 **/
class NoticeMigrateBizServiceTest : BizServerApplicationTests() {

    @Autowired
    lateinit var noticeMigrateBizService: NoticeMigrateBizService

    @Test
    fun fetchTest() {
        val notice = noticeMigrateBizService.fetch()
        assert(notice != null)
    }
}