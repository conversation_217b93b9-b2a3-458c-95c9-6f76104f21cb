package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.ResourceItemBizService
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * @author: <EMAIL>
 * @date: 2023-05-09 16:20
 * @version: ResourceItemBizServiceImplTest, v0.1
 **/
class ResourceItemBizServiceImplTest: BizServerApplicationTests() {

    @Autowired
    lateinit var resourceItemBizService: ResourceItemBizService

    @Test
    fun findPaginatedTest(){
        val page_1 = resourceItemBizService.findPaginated("default","mock-name",current = 1, pageSize = 10)
        assert(page_1.records.isEmpty())
        val page_2 = resourceItemBizService.findPaginated("default","mock-name","mock",1,  10)
        assert(page_2.records.isEmpty())
    }
}