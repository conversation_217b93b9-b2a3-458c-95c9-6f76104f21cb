package com.alibabacloud.devops.appstack.apps.biz.service.api

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.api.rest.web.forward.flow.FlowForwardApi
import com.alibabacloud.devops.appstack.apps.biz.service.FlowForwardBizService
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.given
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.mock.web.MockHttpServletRequest
import org.springframework.mock.web.MockHttpServletResponse

/**
 * <AUTHOR>
 * @create 2024/5/8 6:10 PM
 **/
class FlowForwardApiTest : BizServerApplicationTests() {

    @Autowired
    lateinit var flowForwardApi: FlowForwardApi

    @MockBean
    lateinit var flowForwardBizService: FlowForwardBizService

    @Test
    fun testQueryByRegion() {
        val request = MockHttpServletRequest()
        val response = MockHttpServletResponse()
        given(flowForwardBizService.handleCommon(any(), any())).willReturn(jacksonObjectMapper().createObjectNode())
        flowForwardApi.queryByRegion(request, response)
        verify(flowForwardBizService, times(1)).handleCommon(request, response)
    }
}