package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.iam.model.ResourcePlayer
import com.alibabacloud.devops.iam.model.Role
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.anyString
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.given
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean

/**
 * @author: <EMAIL>
 * @date: 2023-09-20 11:33
 * @version: PermissionChangedHandlerServiceImplTest, v0.1
 **/
@Slf4k
class PermissionChangedHandlerServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var permissionChangedHandlerService: PermissionChangedHandlerServiceImpl

    @MockBean
    lateinit var iamService: IamService

    companion object {
        val USER_ID = "600573ac4fb2132a19bc06e0"
        val reosurceFirst = "test444"
    }

    @Test
    fun appPublishStepHandlerTest() {
        given(
            iamService.searchResourceName(
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull(),
                anyOrNull()
            )
        ).willReturn(listOf(reosurceFirst))

        given(iamService.findRolePlayers(anyOrNull(), anyOrNull(), anyString())).willReturn(
            mapOf(
                reosurceFirst to listOf(USER_ID)
            )
        )

        val resourcePlayer1 = ResourcePlayer()
        resourcePlayer1.id = "60a62065395ee4198d5516a8"
        val role1 = Role()
        role1.name = "developer"

        val resourcePlayer2 = ResourcePlayer()
        resourcePlayer2.id = "600573ac4fb2132a19bc06e0"
        val role21 = Role()
        role21.name = "developer"
        val role22 = Role()
        role22.name = "admin"
        val role23 = Role()
        role23.name = "tester"
        val role24 = Role()
        role24.name = "operator"

        val resourcePlayer3 = ResourcePlayer()
        resourcePlayer3.id = "5e706d5503283833284f41c1"
        val role31 = Role()
        role31.name = "developer"
        val role32 = Role()
        role32.name = "tester"
        given(iamService.findPlayerRoles(anyOrNull(), anyOrNull())).willReturn(
            mapOf(
                resourcePlayer1 to mutableListOf(role1),
                resourcePlayer3 to mutableListOf(role31, role22, role32, role24),
                resourcePlayer3 to mutableListOf(role31, role24),
            )
        )

        val resourcePlayer4 = ResourcePlayer()
        resourcePlayer4.id = "600580ffa50912375337600a"
        val role4 = Role()
        role4.name = "developer"
        given(iamService.findPlayerRoles(anyOrNull(), anyOrNull())).willReturn(
            mapOf(
                resourcePlayer1 to mutableListOf(role1),
                resourcePlayer3 to mutableListOf(role21, role22, role32, role24),
                resourcePlayer3 to mutableListOf(role31, role32),
                resourcePlayer4 to mutableListOf(role4),
            )
        )

        permissionChangedHandlerService.removeResourceAuth(USER_ID)
        verify(iamService, times(4)).updatePlayer(anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull(), anyOrNull())
    }
}