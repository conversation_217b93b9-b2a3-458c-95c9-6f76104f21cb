package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.StageBaseService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.facade.VariableFacade
import com.alibabacloud.devops.appstack.libs.model.api.core.VariableApi
import com.alibabacloud.devops.appstack.libs.model.api.workflowcontroller.ReleaseStageApi
import com.alibabacloud.devops.appstack.libs.model.api.workflowcontroller.mock.ReleaseWorkflowApiMock
import com.alibabacloud.devops.appstack.libs.model.`do`.app.App
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.Profile
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Branch
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.BranchInfo
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.RepoMeta
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.*
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.CreateReleaseWorkflowBizRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.UpdateReleaseStagePipelineRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.ga.AppTemplateWorkflowRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.VariableStorageVO
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.facade.ReleaseStageFacade
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.Mock
import org.mockito.kotlin.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean
import javax.annotation.PostConstruct

/**
 * <AUTHOR>
 * @date 2023-07-11
 */
class WorkflowBaseServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var releaseWorkflowBizService: WorkflowBaseServiceImpl

    @MockBean
    lateinit var auditLogService: AuditLogService

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Mock
    lateinit var variableApi: VariableApi

    @Mock
    lateinit var releaseStageApi: ReleaseStageApi

    @MockBean
    lateinit var stageBaseService: StageBaseService

    companion object {
        const val NOT_PREMIUN_VERSION_ORG_ID = "5fd1e3841acb1ae7cc187cd5"
        const val RELEASE_WORKFLOW_SN = "test-workflow-with-stages"
        const val RELEASE_WORKFLOW_APP_NAME = "test-workflow-app-name"
        val APP_VARIABLE_STORAGE = VariableStorageVO(
            app = App(RELEASE_WORKFLOW_APP_NAME),
            profileMap = mapOf(
                "group-1" to Profile(
                    name = "group-1",
                    displayName = "",
                    vars = emptyList()
                )
            ),
            revision = Revision(RepoMeta("", ""), "", "", ""),
            branchInfo = BranchInfo(
                head = Revision(
                    repoMeta = RepoMeta("", ""),
                    sha = "sha",
                    message = "init",
                    author = "",
                ),
                repoMeta = RepoMeta("", ""),
                name = Branch.TRUNK
            ),
            refEnvMap = null
        )
        val MOCK_STAGE = ReleaseStage(name = "mock", sn = "mock-create")
        val APPTEMPLATE_REQUEST = AppTemplateWorkflowRequest(
            name = "test-worklfow",
            appTemplateName = "template-1",
            releaseStageTemplate = listOf(
                ReleaseStageTemplate(
                    sn = "sn-1",
                    name = "stage-1",
                    pipeline = UpdateReleaseStagePipelineRequest(
                        data = ReleaseWorkflowApiMock::class.java.getResource("/mock/pipeline/FlowV1Pipeline.json").readText(),
                        type = Pipeline.Type.FlowV1
                    ),
                    stageRuleConfig = WorkflowStageRuleConfigTemplate(),
                ),
                ReleaseStageTemplate(
                    sn = "sn-2",
                    name = "stage-2",
                    stageRuleConfig = WorkflowStageRuleConfigTemplate(),
                    variableGroups = mutableListOf(
                        VariableGroup(
                            name = "group-1",
                            type = VariableGroup.Type.TEMPLATE
                        ),
                        VariableGroup(
                            name = "group-2",
                            type = VariableGroup.Type.GLOBAL
                        )
                    )
                ),
                ReleaseStageTemplate(
                    sn = "sn-4",
                    name = "stage-4",
                    stageRuleConfig = WorkflowStageRuleConfigTemplate()
                )
            ),
            message = "同步"
        )
    }

    @PostConstruct
    fun postConstruct() {
        coreFacades.variableFacade = VariableFacade(variableApi)
        releaseWorkflowBizService.setCoreFacades(coreFacades)
        workflowControllerFacade.releaseStageFacade = ReleaseStageFacade(releaseStageApi)
        releaseWorkflowBizService.setWorkflowControllerFacade(workflowControllerFacade)
    }


    @Test
    fun create_success_test() {
        val appName = "appName"
        val request = CreateReleaseWorkflowBizRequest(
            name = "mock",
            templateSn = "1",
            type = ReleaseWorkflow.TypeEnum.CR,
            note = ""
        )
        doNothing().`when`(auditLogService).log(any(), any())
        given(releaseStageApi.create(any())).willReturn(Response.success(MOCK_STAGE))
        given(releaseStageApi.updatePipeline(any(), any())).willReturn(Response.success(MOCK_STAGE))
        given(stageBaseService.delete(any(), any(), any(), any())).willReturn(MOCK_STAGE)
        releaseWorkflowBizService.create(appName, request)
    }

    @Test
    fun create_premiun_check_test() {
        AuthThreadContext.setTenant(NOT_PREMIUN_VERSION_ORG_ID)
        val appName = "appName"
        val request = CreateReleaseWorkflowBizRequest(
            name = "mock",
            templateSn = "1",
            type = ReleaseWorkflow.TypeEnum.CR,
            note = ""
        )
        doNothing().`when`(auditLogService).log(any(), any())
        given(releaseStageApi.create(any())).willReturn(Response.success(MOCK_STAGE))
        given(releaseStageApi.updatePipeline(any(), any())).willReturn(Response.success(MOCK_STAGE))
        given(stageBaseService.delete(any(), any(), any(), any())).willReturn(MOCK_STAGE)
        try {
            releaseWorkflowBizService.create(appName, request)
        } catch (e: Exception) {
            Assertions.assertEquals(ErrorCode.AS_PREMIUM_VERSION_CHECK_FAILED, (e as BizException).errorEntry.code)
        }
        releaseWorkflowBizService.create(appName, request)
    }

    @Test
    fun update_with_template_success_test() {

        given(variableApi.find(RELEASE_WORKFLOW_APP_NAME)).willReturn(Response.success(APP_VARIABLE_STORAGE))
        given(releaseStageApi.update(any(), any())).willReturn(Response.success(MOCK_STAGE))
        given(releaseStageApi.create(any())).willReturn(Response.success(MOCK_STAGE))
        given(releaseStageApi.updatePipeline(any(), any())).willReturn(Response.success(MOCK_STAGE))
        given(stageBaseService.delete(RELEASE_WORKFLOW_APP_NAME, RELEASE_WORKFLOW_SN, "exist-sn-3", null)).willReturn(MOCK_STAGE)

        releaseWorkflowBizService.update(RELEASE_WORKFLOW_APP_NAME, RELEASE_WORKFLOW_SN, APPTEMPLATE_REQUEST)

        verify(variableApi, times(1)).find(RELEASE_WORKFLOW_APP_NAME)
        verify(releaseStageApi, times(1)).update(eq("exist-sn-1"), any())
        verify(releaseStageApi, times(1)).update(eq("exist-sn-2"), any())
        verify(releaseStageApi, times(1)).create(any())
        verify(releaseStageApi, times(1)).updatePipeline(eq("exist-sn-1"), any())
        verify(stageBaseService, times(1)).delete(RELEASE_WORKFLOW_APP_NAME, RELEASE_WORKFLOW_SN, "exist-sn-3", null)

    }

    @Test
    fun update_with_template_failed_test() {

        given(variableApi.find(RELEASE_WORKFLOW_APP_NAME)).willReturn(Response.success(null))
        given(releaseStageApi.update(any(), any())).willReturn(Response.success(MOCK_STAGE))
        given(releaseStageApi.create(any())).willReturn(Response.success(MOCK_STAGE))
        given(releaseStageApi.updatePipeline(any(), any())).willThrow(BizException(ErrorCode.AS_RELEASE_STAGE_PIPELINE_SAVE_TO_FLOW_ERROR))
        doThrow(BizException(ErrorCode.AS_RELEASE_STAGE_NOT_FOUND)).`when`(stageBaseService).delete(RELEASE_WORKFLOW_APP_NAME, RELEASE_WORKFLOW_SN, "exist-sn-3", null)

        val exception = assertThrows<BizException> {
            releaseWorkflowBizService.update(RELEASE_WORKFLOW_APP_NAME, RELEASE_WORKFLOW_SN, APPTEMPLATE_REQUEST)
        }

        assert(exception.errorEntry.code == ErrorCode.AS_RELEASE_WORKFLOW_UPSERT_WITH_TEMPLATE_FAIELD)
        assert(exception.errorEntry.message!!.contains("stage-1"))
        assert(exception.errorEntry.message!!.contains("stage-2"))
        assert(!exception.errorEntry.message!!.contains("mock-create"))
        assert(exception.errorEntry.message!!.contains("stage-3"))
        assert(exception.errorEntry.advice!!.contains("stage-1"))
        assert(exception.errorEntry.advice!!.contains("stage-2"))
        assert(!exception.errorEntry.advice!!.contains("mock-create"))
        assert(exception.errorEntry.advice!!.contains("stage-3"))

        verify(variableApi, times(1)).find(RELEASE_WORKFLOW_APP_NAME)
        verify(releaseStageApi, times(1)).update(eq("exist-sn-1"), any())
        verify(releaseStageApi, times(0)).update(eq("exist-sn-2"), any())
        verify(releaseStageApi, times(1)).create(any())
        verify(releaseStageApi, times(1)).updatePipeline(eq("exist-sn-1"), any())
        verify(stageBaseService, times(1)).delete(RELEASE_WORKFLOW_APP_NAME, RELEASE_WORKFLOW_SN, "exist-sn-3", null)

    }
}