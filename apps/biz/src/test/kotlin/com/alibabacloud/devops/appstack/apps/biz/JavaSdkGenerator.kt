package com.alibabacloud.devops.appstack.apps.biz

import org.springframework.boot.runApplication

fun main(args: Array<String>) {
    System.setProperty("spring.profiles.active", "test")
    val application = runApplication<BizServerApplication>(*args)
    //生成 biz-api java sdk
    val processor = ProcessBuilder("sh", "generate_java_sdk.sh")
        .redirectError(ProcessBuilder.Redirect.INHERIT)
        .redirectInput(ProcessBuilder.Redirect.INHERIT)
        .redirectOutput(ProcessBuilder.Redirect.INHERIT)
        .start()
    processor.waitFor()
    application.close()
}
