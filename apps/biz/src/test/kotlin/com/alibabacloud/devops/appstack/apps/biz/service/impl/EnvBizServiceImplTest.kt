package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.model.AgentStatus
import com.alibabacloud.devops.appstack.apps.biz.service.AgentService
import com.alibabacloud.devops.appstack.apps.biz.service.EnvBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.config.MessageConfig
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.impl.I18nMessageServiceImpl
import com.alibabacloud.devops.appstack.libs.model.`do`.Label
import com.alibabacloud.devops.appstack.libs.model.`do`.app.App
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeAction
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeableEnv
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.Job
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.env.RolloutStrategy
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.Orchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppBuiltInOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Component
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.request.CreateEnvRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateEnvRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.EnvQuery
import com.alibabacloud.devops.appstack.libs.model.vo.EnvAction
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.given
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.MessageSource
import java.util.*

/**
 * <AUTHOR>
 * @date 2023-05-21
 */
class EnvBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var envBizService: EnvBizService

    @MockBean
    lateinit var agentService: AgentService

    @Test
    fun test_findAllChangeableEnvs() {
        val changeableEnvRecordVOs = envBizService.findAllChangeableEnvs("appName")
        Assertions.assertTrue(changeableEnvRecordVOs.isNotEmpty())
        Assertions.assertNotNull(changeableEnvRecordVOs.first().latestChangeOrder)
        Assertions.assertNotNull(changeableEnvRecordVOs.first().name)
        Assertions.assertNotNull(changeableEnvRecordVOs.first().deployGroup)
    }

    @Test
    fun test_findEnvActions() {
        val envBizServiceImpl = EnvBizServiceImpl::class.java.newInstance()
        val findEnvActions = envBizServiceImpl::class.java.getDeclaredMethod(
            "findEnvActions",
            Env::class.java,
            ChangeOrder::class.java
        )
        val (env, orchestration, changeOrder) = mock_triple()
        findEnvActions.isAccessible = true
        envBizServiceImpl.i18nMessageService = mock_i18nMessage()
        val envActions = findEnvActions.invoke(envBizServiceImpl, env, changeOrder) as List<EnvAction>
        assert(!envActions.first { it.action == EnvAction.Action.Delete }.disabled)
    }

    @Test
    fun test_findChangeableEnv() {
        val changeableEnvVO = envBizService.findChangeableEnv("appName-mock", "envName-mock")
        assert(changeableEnvVO?.deployGroup?.claimList?.first()?.resourceInstance?.displayName == "mock-instance-display-name")
    }

    private fun mock_i18nMessage(): I18nMessageService {
        val messageConfig = MessageConfig::class.java.newInstance()
        val errorMessageResource = messageConfig::class.java.getDeclaredMethod("errorMessageResource")
        val errorAdviceResource = messageConfig::class.java.getDeclaredMethod("errorAdviceResource")
        val commonResource = messageConfig::class.java.getDeclaredMethod("commonMessageResource")
        errorMessageResource.isAccessible = true
        errorAdviceResource.isAccessible = true
        commonResource.isAccessible = true
        return I18nMessageServiceImpl(
            errorMessageResource = errorMessageResource.invoke(messageConfig) as MessageSource,
            errorAdviceResource = errorAdviceResource.invoke(messageConfig) as MessageSource,
            commonResource = commonResource.invoke(messageConfig) as MessageSource,
        )
    }

    private fun mock_triple(): Triple<Env, AppBuiltInOrchestration, ChangeOrder> {
        val env = Env()
        env.deployGroupName = "deployGroupName"
        env.spec = Env.Spec(migrateState = Env.Spec.MigrateState.WAITING)
//        val changeableEnv = ChangeableEnv(appName = "appName", envName = "envName")
            /*changeableEnv = ChangeableEnv(appName = "appName", envName = "envName"),
            latestChangeOrder = ChangeOrder(
                appName = "appName",
                name = "",
                version = "123",
                tag = "",
                type = ChangeOrder.Type.Deploy,
                jobs = emptyList(),
                state = ChangeOrder.State.SUCCESS
            )
        )*/
        val orchestration = AppBuiltInOrchestration(
            app = App(),
            name = "mock编排",
            creatorId = "st",
            modifierId = "st",
            gmtCreate = Date(),
            gmtModified = Date(),
            description = "desc",
            revision = Orchestration.OrchestrationRevision(
                "sha",
                "message",
                "sutian",
                Date()
            ),
            suitableResourceTypes = listOf(ResourceTypeEnum.KUBERNETES),
            format = Orchestration.FormatEnum.MANIFEST,
            sn = ""
        )
        orchestration.componentList = listOf(
            Component(
                "ConfigMap0914",
                "ConfigMap",
                "这是创建于0914的ConfigMap",
                "---\\napiVersion: v1\\nkind: ConfigMap\\nmetadata:\\n  name: env-config\\n  namespace: default\\ndata:\\n  envConfig.envName: envName\\n  envConfig.hsfVersion: charm",
                1,
                ResourceTypeEnum.KUBERNETES
            )
        )
        return Triple(env, orchestration, ChangeOrder(
            appName = "appName",
            name = "",
            version = "123",
            tag = "",
            type = ChangeOrder.Type.Deploy,
            jobs = emptyList(),
            state = ChangeOrder.State.SUCCESS
        ))
    }

    @Test
    fun testFetchChangeableEnvVO() {
        val changeableEnvVO = envBizService.findChangeableEnv(appName = "test", envName = "test")
        Assertions.assertEquals("sha", changeableEnvVO?.appOrchestration?.revision?.sha)
    }

    @Test
    fun testFetchChangeableEnvVO_2() {
        val changeableEnvVO = envBizService.findChangeableEnv(appName = "any", envName = "brand-new")
        Assertions.assertEquals(null, changeableEnvVO?.appOrchestration)
    }

    @Test
    fun findEnvStrategyFormTest() {
        val k8sForm = envBizService.findEnvStrategyForm("hpa-app-name", "k8s")
        println(jacksonObjectMapper().writeValueAsString(k8sForm))
        assert(k8sForm?.locator?.any { it.locator == "*" } == true)
        val hostForm = envBizService.findEnvStrategyForm("hpa-app-name", "host")
        println(jacksonObjectMapper().writeValueAsString(hostForm))
        assert(hostForm?.locator?.any { it.locator == "*" } == true)
    }

    @Test
    fun update_test() {
        val env = Env(
            name = "envName",
            displayName = "envName",
            appName = "appName",
            profiles = mutableListOf(VariableGroup(type = VariableGroup.Type.APP, name = "customProfile")),
        )
        val updateEnvRequest = UpdateEnvRequest(
            spec = Env.Spec(
                rolloutStrategy = listOf(
                    RolloutStrategy(
                        locator = "*",
                        batches = 2,
                        batchSteps = listOf(
                            RolloutStrategy.BatchStep(
                                type = RolloutStrategy.BatchStep.Type.REPLICAS,
                                value = "3"
                            ),
                            RolloutStrategy.BatchStep(
                                type = RolloutStrategy.BatchStep.Type.WEIGHT,
                                value = "50"
                            )
                        )
                    )
                )
            )
        )
        try {
            envBizService.update(env, updateEnvRequest)
            assert(false)
        } catch (e: Exception) {
            assert(true)
        }
    }

    @Test
    fun test_update_cannot_change_deploy_group() {
        val env = Env(
            name = "envName",
            displayName = "envName",
            appName = "test_update_cannot_change_deploy_group",
            resourcePoolName = "default",
            deployGroupName = "exist-a",
            labelList = mutableListOf(
                Label(
                    name = "envType",
                    value = "dev",
                    namespace = "default",
                    displayName = "环境级别",
                    displayValue = "开发环境"
                )
            )
        )
        val updateEnvRequest = UpdateEnvRequest(
            deployGroupName = "exist-b"
        )
        try {
            envBizService.update(env, updateEnvRequest)
            Assertions.fail<String>()
        } catch (e: Exception) {
            Assertions.assertEquals(ErrorCode.AS_ENV_DEPLOY_GROUP_CANNOT_CHANGE_DUE_TO_RUNNING_CHANGE_ORDER, (e as BizException).errorEntry.code)
        }
    }

    @Test
    fun test_update_can_change_deploy_group() {
        val env = Env(
            name = "brand-new",
            displayName = "envName",
            appName = "test_update_cannot_change_deploy_group",
            resourcePoolName = "default",
            deployGroupName = "exist-a",
            labelList = mutableListOf(
                Label(
                    name = "envType",
                    value = "dev",
                    namespace = "default",
                    displayName = "环境级别",
                    displayValue = "开发环境"
                )
            )
        )
        val updateEnvRequest = UpdateEnvRequest(
            deployGroupName = "exist-b"
        )
        given(agentService.findStatus(any(), any())).willReturn(
            AgentStatus(
                state = AgentStatus.State.SUCCESS
            )
        )
        envBizService.update(env, updateEnvRequest)
    }

    @Test
    fun test_create() {
        val appName = "myapp"
        val request = CreateEnvRequest(
            name = "dev",
            displayName = "dev",
            labelList = mutableListOf(
                Label(
                    name = "envType",
                    value = "dev",
                    namespace = "default",
                    displayName = "环境级别",
                    displayValue = "开发环境"
                )
            ),
            deployGroupName = null,
            description = null
        )
        val env = envBizService.create(appName, request)
        Assertions.assertNotNull(env)
    }

    @Test
    fun update_strategy_batch_empty_test() {
        val env = Env(
            name = "envName",
            displayName = "envName",
            appName = "appName",
            profiles = mutableListOf(VariableGroup(type = VariableGroup.Type.APP, name = "customProfile")),
            labelList = mutableListOf(
                Label(
                    displayName = "环境级别",
                    displayValue = "开发环境",
                    name = "envType",
                    namespace = "default",
                    value = "dev"
                )
            )
        )
        val updateEnvRequest = UpdateEnvRequest(
            spec = Env.Spec(
                rolloutStrategy = listOf(
                    RolloutStrategy(
                        locator = "*",
                        batches = null,
                        batchSteps = listOf(
                        )
                    )
                )
            )
        )
        try {
            envBizService.update(env, updateEnvRequest)
            assert(false)
        } catch (e: BizException) {
            assert(e.errorEntry.code == ErrorCode.AS_ENV_STRATEGY_BATCH_INVALID)
        } catch (e: Exception) {
            assert(false)
        }
    }



    @Test
    fun testFindEnvActionsWithEnvLocked() {
        val env = Env(name = "dev", displayName = "dev", appName = "app",lockBy = "user1")
        val findEnvAction = EnvBizServiceImpl::class.java.newInstance()::class.java.getDeclaredMethod("findEnvActions", Env::class.java, ChangeOrder::class.java)
        findEnvAction.isAccessible = true
        val result = findEnvAction.invoke(envBizService, env, null)
        Assertions.assertTrue((result as List<EnvAction>).all { it.disabled })
    }

    @Test
    fun testFindEnvActionsWithNoDeployGroup() {
        val env = Env(name = "dev", displayName = "dev", appName = "app")
        val findEnvAction = EnvBizServiceImpl::class.java.newInstance()::class.java.getDeclaredMethod("findEnvActions", Env::class.java, ChangeOrder::class.java)
        findEnvAction.isAccessible = true
        val result = findEnvAction.invoke(envBizService, env, null)
        Assertions.assertTrue((result as List<EnvAction>).filter { it.action != EnvAction.Action.Delete }.all { it.disabled })
        Assertions.assertFalse((result as List<EnvAction>).first { it.action == EnvAction.Action.Delete }.disabled)
    }

    @Test
    fun list_query_not_pass() {
        val query = EnvQuery(pagination = "xx", perPage = 1, orderBy = "xx", sort = "xx", nextToken = "xx")
        try {
            envBizService.list("appName", query)
        } catch (e: BizException) {
            Assertions.assertEquals(ErrorCode.AS_PAGINATION_PARAM_INVALID, e.errorEntry.code)
        }
    }


    @Test
    fun testFindEnvActionsWithReplicasManagedByUser() {
        val env = Env(name = "dev", displayName = "dev", appName = "app", deployGroupName = "dg", spec = Env.Spec(replicasManagement = Env.Spec.ReplicasManagement.USER))
        val latestChangeOrder = ChangeOrder(
            sn = "changeOrderSn",
            name = "2022第2个部署单",
            appName = "st-008",
            type = ChangeOrder.Type.Deploy,
            version = "20220117161434924",
            tag = "dfasfaseqe",
            state = ChangeOrder.State.SUCCESS,
            description = "desc",
            jobs = listOf(
                Job(
                    sn = "jobSn",
                    name = "jobName",
                    envName = "envName",
                    appName = "appName",
                    engineType = ChangeableEnv.EngineType.Ade1,
                    stages = emptyList(),
                    state = Job.State.SUSPENDED,
                    type = Job.Type.Deploy,
                    controlMode = ChangeAction.ControlMode.Auto,
                    profiles = emptyList(),
                    changeableEnvSn = "",
                )
            )
        )
        val findEnvAction = EnvBizServiceImpl::class.java.newInstance()::class.java.getDeclaredMethod("findEnvActions", Env::class.java, ChangeOrder::class.java)
        findEnvAction.isAccessible = true
        val result = findEnvAction.invoke(envBizService, env, latestChangeOrder)
        Assertions.assertTrue((result as List<EnvAction>).first { it.action == EnvAction.Action.Scale }.disabled)
        Assertions.assertFalse((result as List<EnvAction>).filter { it.action != EnvAction.Action.Scale }.all { it.disabled })
    }

}