package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibaba.aone.framework.tbs.sdk.auth.AuthContext
import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.config.IamContext
import com.alibabacloud.devops.iam.constant.ProtocolType
import feign.Logger
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.test.context.ActiveProfiles

/**
 * <AUTHOR> <EMAIL>
 * @version : IamServiceImplTest, v0.1
 * @date : 2021-11-29 19:58
 */
@Disabled
@ActiveProfiles("vpc")
internal class IamVpcServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var iamService: IamService

    companion object {
        var orgId = "6c7a511e-304b-49a9-969b-a36ba730f7d9"
        var userId = "77c5b5d1-9154-4d6a-a345-e6843ada4eb9"
    }

    @Value("\${devops.iam.apiHost}")
    lateinit var apiHost: String

    @Value("\${devops.iam.appId}")
    lateinit var appId: String

    @Value("\${devops.iam.appSecret}")
    lateinit var appSecret: String

    @BeforeEach
    fun before() {

        AuthThreadContext.setTenant(orgId)
        IamContext.setTenant(orgId)
        AuthThreadContext.setUserId(userId)
        IamContext.setOperator(userId)

        AuthContext.getContext().currentUserId = userId
        AuthContext.getContext().tenantId = orgId
        AuthContext.setApiHost(apiHost)
        AuthContext.setAppId(appId)
        AuthContext.setAppSecret(appSecret)
        AuthContext.setLogLevel(Logger.Level.FULL)
        AuthContext.setEnableLog(true)
    }

    @Test
    fun testFetchOrgMember() {
        val orgMembers = iamService.fetchOrgMembers()
        orgMembers.forEach {
            println(it)
        }
    }
}