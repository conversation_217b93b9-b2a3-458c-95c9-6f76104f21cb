package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.ServiceConnectionBizService
import com.alibabacloud.devops.appstack.apps.biz.service.StageBaseService
import com.alibabacloud.devops.appstack.apps.biz.service.WorkflowBaseService
import com.alibabacloud.devops.appstack.apps.biz.service.system.impl.ReleaseStageBizServiceImpl
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.facade.AppFacade
import com.alibabacloud.devops.appstack.libs.model.constant.WorkflowEnvVariable
import com.alibabacloud.devops.appstack.libs.model.`do`.app.*
import com.alibabacloud.devops.appstack.libs.model.`do`.release.AppRelease
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ConnectionType
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ServiceConnection
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.Pipeline
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStage
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.constant.FlowPermission
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageBriefVO
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowBriefVO
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.ExecuteReleaseStagePipelineRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.QueryReleaseStageCrMetadataRequest
import com.alibabacloud.devops.appstack.libs.model.vo.AppWithRelationVO
import com.alibabacloud.devops.appstack.libs.model.vo.ReleaseIntegratedMetadata
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.facade.AppReleaseFacade
import com.alibabacloud.devops.iam.exception.IamForbiddenException
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.given
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean
import java.lang.Exception
import java.util.Date
import javax.annotation.PostConstruct

/**
 * <AUTHOR>
 * @create 2024/5/21 6:21 PM
 **/
class ReleaseStageBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var releaseStageBizService: ReleaseStageBizServiceImpl

    @MockBean
    lateinit var serviceConnectionBizService: ServiceConnectionBizService

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Mock
    lateinit var appFacade: AppFacade

    @Mock
    lateinit var appReleaseFacade: AppReleaseFacade

    @MockBean
    lateinit var stageBaseService: StageBaseService

    @MockBean
    lateinit var releaseWorkflowBaseService: WorkflowBaseService

    @MockBean
    lateinit var flowPermissionHandlerChain: FlowPermissionHandlerChain


    companion object {
        const val SYSTEM_NAME = "sys"
        const val RELEASE_WORKFLOW_SN = "releaseWorkflowSn"
        const val RELEASE_STAGE_SN = "releaseStageSn"
        const val APP_RELEASE_SN = "appReleaseSn"
        const val APP_NAME = "app"
        const val CODE_IDENTIFIER = "code"
        const val ARTI_IDENTIFIER = "arti"
        const val REPO_URL = "repo_url"
        const val CONNECTION_ID = 1L
        const val CONNECTION_UUID = "uuid"
        const val GENERIC_NAME = "generic"
        val APP_CODE_REPO = AppRelease.AppReleaseItem.AppSpec.CodeRepo(
            spec = AppRelease.AppReleaseItem.AppCodeRepoSpec(
                appCodeRepoSn = "code-sn-1",
                identifier = CODE_IDENTIFIER,
                branchName = "master",
                repoContext = CodeupRepoContext(
                    repoUrl = REPO_URL,
                    defaultBranch = "master",
                    projectId = ""
                ),
                connectionConfig = FlowConnectionConfig(
                    connectionId = CONNECTION_ID.toString()
                )
            )
        )
        val APP_ARTIFACT_REPO = AppRelease.AppReleaseItem.AppSpec.AppArtifactRepoSpec(
            appArtifactRepoSn = "sn",
            versionType = AppRelease.AppReleaseItem.AppSpec.AppArtifactRepoSpec.Type.SPECIFIC,
            identifier = ARTI_IDENTIFIER,
            artifactContext = CustomDockerRegistryContext(
                repoUrl = ChangeRequestStageBizServiceImplTest.REPO_URL,
                defaultVersion = "v1"
            ),
            connectionConfig = FlowConnectionConfig(
                connectionId = ChangeRequestStageBizServiceImplTest.CONNECTION_ID.toString()
            )
        )
        val RELEASE_ITEM_APP = AppRelease.AppReleaseItem(
            type = AppRelease.AppReleaseItem.TypeEnum.APP,
            sn = "sn-1",
            appReleaseSn = APP_RELEASE_SN,
            spec = AppRelease.AppReleaseItem.AppSpec(
                appName = APP_NAME,
                codeRepos = listOf(APP_CODE_REPO),
                artifactRepos = listOf(APP_ARTIFACT_REPO)
            )
        )
        val RELEASE_ITEM_GENERIC = AppRelease.AppReleaseItem(
            type = AppRelease.AppReleaseItem.TypeEnum.GENERIC,
            sn = "sn-2",
            appReleaseSn = APP_RELEASE_SN,
            spec = AppRelease.AppReleaseItem.AppGenericSpec(
                name = GENERIC_NAME,
                value = ""
            )
        )
    }

    @PostConstruct
    fun init() {
        coreFacades.appFacade = appFacade
        releaseStageBizService.setCoreFacades(coreFacades)
        workflowControllerFacade.appReleaseFacade = appReleaseFacade
        releaseStageBizService.setWorkflowControllerFacade(workflowControllerFacade)
    }

    @Test
    fun testHandlePipelineEnvWithEmptyID() {
        val value = "{\"serviceConnectionID\":\"\"}"
        val result = releaseStageBizService.exposeHandlePipelineEnv("APP", value)
        assert(result == "{\"serviceConnectionID\":\"\"}")
    }

    @Test
    fun testHandlePipelineEnvWithID() {
        val value = "{\"serviceConnectionID\":\"123\"}"
        releaseStageBizService.serviceConnectionBizService = serviceConnectionBizService
        given(serviceConnectionBizService.findByIds(any())).willReturn(listOf(
            ServiceConnection(
            id = 123L,
            connectionName = "name",
            connectionType = ConnectionType.Codeup,
            uuid = "uuid"
        )
        ))
        val result = releaseStageBizService.exposeHandlePipelineEnv("APP", value)
        assert(result == "{\"serviceConnectionID\":\"uuid\"}")
    }

    @Test
    fun testHandlePipelineEnvWithoutAPPType() {
        val value = "{\"serviceConnectionID\":\"123\"}"
        releaseStageBizService.serviceConnectionBizService = serviceConnectionBizService
        given(serviceConnectionBizService.findByIds(any())).willReturn(listOf(
            ServiceConnection(
                id = 123L,
                connectionName = "name",
                connectionType = ConnectionType.Codeup,
                uuid = "uuid"
            )
        ))
        val result = releaseStageBizService.exposeHandlePipelineEnv("GENERIC", value)
        assert(result == "{\"serviceConnectionID\":\"123\"}")
    }

    @Test
    fun testGetPipelineParamsWithRelease() {
        given(appFacade.listAllRelatedApps(SYSTEM_NAME)).willReturn(listOf(
            AppWithRelationVO(APP_NAME)
        ))
        given(appReleaseFacade.find(SYSTEM_NAME, APP_RELEASE_SN)).willReturn(AppRelease(
            sn = "sn",
            name = "name",
            appName = APP_NAME,
            version = "",
            state = AppRelease.State.CLOSED,
            gmtCreate = Date(),
            gmtModified = Date(),
            creatorId = "",
            modifierId = "",
            releaseItems = listOf(RELEASE_ITEM_APP, RELEASE_ITEM_GENERIC),
            description = ""
        ))
        val result = releaseStageBizService.getPipelineParams(SYSTEM_NAME, RELEASE_WORKFLOW_SN, RELEASE_STAGE_SN, APP_RELEASE_SN)
        val expectResult = mapOf(
            WorkflowEnvVariable.APPSTACK_SYS_NAME to SYSTEM_NAME,
            WorkflowEnvVariable.APPSTACK_SYS_WORKFLOW_SN to RELEASE_WORKFLOW_SN,
            WorkflowEnvVariable.APPSTACK_SYS_WORKFLOW_STAGE_SN to RELEASE_STAGE_SN,
            WorkflowEnvVariable.APPSTACK_SYS_SOURCE_APP_NAMES to listOf(APP_NAME),
            WorkflowEnvVariable.APPSTACK_RELEASE_ID to null,
            WorkflowEnvVariable.APPSTACK_RELEASE_SN to null,
            WorkflowEnvVariable.APPSTACK_RELEASE_SCHEDULED_RELEASE_TIME to null,
            WorkflowEnvVariable.APPSTACK_RELEASE_VERSION to null,
            WorkflowEnvVariable.APPSTACK_RELEASE_NAME to null,
            WorkflowEnvVariable.APPSTACK_RELEASE_BASELINE_VERSION to null,
            WorkflowEnvVariable.APPSTACK_RELEASE_DESCRIPTION to null,
            WorkflowEnvVariable.APPSTACK_RELEASE_ITEM_APP_NAMES to listOf(APP_NAME),
            WorkflowEnvVariable.APPSTACK_RELEASE_ITEM_GENERIC_NAMES to listOf(GENERIC_NAME),
            WorkflowEnvVariable.APPSTACK_RELEASE_ITEM_PREFIX + APP_NAME to null,
            WorkflowEnvVariable.APPSTACK_RELEASE_ITEM_PREFIX + GENERIC_NAME to null,
            WorkflowEnvVariable.APPSTACK_RELEASE_ITEM_APPS to null
        )
        expectResult.forEach { k, v ->
            // 检查参数是否存在
            assert(result.contains(k))
            // 判断值是否满足期望
            if (v != null) {
                assert(result[k] == v)
            }
        }
    }

    @Test
    fun testFindReleaseStageCrMetadata(){
        val appName = "myapp"
        val workflowSn = "workflowSn"
        val stageSn = "stageSn"
        val number = 1L
        val releaseBranch = "release"
        given(stageBaseService.findReleaseStageCrMetadata(appName, workflowSn, stageSn, number))
            .willReturn(ReleaseIntegratedMetadata(
                releaseStageSn = stageSn,
                releaseBranch = releaseBranch,
                releaseRevision = "sha1",
                repoUrl = "url",
                repoType = "codeup",
                crDetailList = listOf(ReleaseIntegratedMetadata.CrDetail(
                    sn = "crSn",
                    name = "crName",
                    ownerId = "ownerId",
                    branchName = "feature",
                    commitId = "sha2"
                )),
                runNumber = 1L
            ))
        given(appFacade.find(appName)).willReturn(App(name = appName, type = AppType.SYSTEM))
        Assertions.assertNotNull(releaseStageBizService.findReleaseStageCrMetadata(appName, workflowSn, stageSn, QueryReleaseStageCrMetadataRequest(engineType = Pipeline.Type.FlowV1, buildNum = 1)))
    }

    @Test
    fun testExecutePipeline_withoutPermission() {
        val releaseWorkflowSn = "sn1"
        val releaseStageSn = "sn2"
        given(flowPermissionHandlerChain.handle(eq((FlowPermission.PERMISSION_PIPELINE_RUN)), any(), any(), any())).willReturn(false)
        given(releaseWorkflowBaseService.findBrief(eq(ChangeRequestStageBizServiceImplTest.APP_NAME), eq(releaseWorkflowSn))).willReturn(
            ReleaseWorkflowBriefVO()
        )
        given(stageBaseService.findBrief(eq(APP_NAME), eq(releaseWorkflowSn), eq(releaseStageSn))).willReturn(
            ReleaseStageBriefVO(name = "stageName", processEngineType = Pipeline.Type.FlowV1, processEngineSn = "processEngineSn")
        )
        given(appFacade.find(APP_NAME)).willReturn(App(name = APP_NAME, type = AppType.SYSTEM))
        try{
            releaseStageBizService.executePipeline(APP_NAME, releaseWorkflowSn, releaseStageSn, ExecuteReleaseStagePipelineRequest(params = mutableMapOf()))
            Assertions.fail<String>("Should fail")
        }catch (e: Exception){
            Assertions.assertTrue(e is IamForbiddenException)
        }
    }
}