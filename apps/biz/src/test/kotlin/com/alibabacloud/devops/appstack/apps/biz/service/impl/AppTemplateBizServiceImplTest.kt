package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibaba.aone.framework.tbs.app.sdk.model.User
import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.*
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.OnlineGuidanceService
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.request.AppRequest
import com.alibabacloud.devops.appstack.libs.model.request.AppTemplateQuery
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateRequest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.any
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.given
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean


/**
 * <AUTHOR>
 * @create 2023/10/16 7:46 PM
 **/
class AppTemplateBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var appTemplateBizService: AppTemplateBizService

    @Autowired
    lateinit var appBizService: AppBizService

    @MockBean
    lateinit var auditLogService: AuditLogService

    @MockBean
    lateinit var onlineGuidanceService: OnlineGuidanceService

    @MockBean
    lateinit var appCodeRepoBizService: AppCodeRepoBizService

    @MockBean
    lateinit var appArtifactRepoBizService: AppArtifactRepoBizService

    @MockBean
    lateinit var iamService: IamService

    @MockBean
    lateinit var appTemplateConfigBizService: AppTemplateConfigBizService

    companion object{
        val name = "app-template-name"
        val name2 = "app-template-name2"
        val nameCopy = "app-template-name_copy"
        val displayName = "app-template-display-name"
        val displayNameCopy = "app-template-display-name-copy"
        val displayNameSearchKey = "display"
        val description = "app-template-description"
        val cover = "app-template-cover"
        var request = UpsertAppTemplateRequest(
            name = name,
            displayName = displayName,
            description = description,
            cover = cover,
        )
        val appName = "app-name"
        val appName2 = "app-name-2"
        val user = User()
    }

    @Test
    fun createTest() {
        try {
            // 跳过审计
            doNothing().`when`(auditLogService).commonLog(any(), any(), any())
            // 测试应用模版名为空
            assertThrows<BizException> {
                appTemplateBizService.create(UpsertAppTemplateRequest(displayName = displayName))
            }
            // 测试显示名为空
            assertThrows<BizException> {
                appTemplateBizService.create(UpsertAppTemplateRequest(name = name))
            }
            val appTemplate = appTemplateBizService.create(request)
            assertEquals(appTemplate.name, name)

            // 测试应用模版已存在
            assertThrows<BizException> {
                appTemplateBizService.create(request)
            }
        } finally {
            // 清理数据
            // 避免is_deleted删除时间冲突
            Thread.sleep(1000)
            appTemplateBizService.delete(name)
        }
    }

    @Test
    fun deleteTest() {
        // 跳过审计
        doNothing().`when`(auditLogService).commonLog(any(), any(), any())
        doNothing().`when`(iamService).registerResource(any(), any(), any(), any())
        doNothing().`when`(iamService).unregisterResource(any(), any(), any(), any())
        doNothing().`when`(onlineGuidanceService).delete(any())
        AuthThreadContext.setUserId("")
        // 跳过代码源、制品源删除
        given(appCodeRepoBizService.findAll(any())).willReturn(emptyList())
        given(appArtifactRepoBizService.findAll(any())).willReturn(emptyList())
        // 测试应用模版不存在
        assertThrows<BizException> {
            appTemplateBizService.delete(name)
        }
        // 插入数据待删除
        appTemplateBizService.create(request)
        val appTemplate = appTemplateBizService.find(name)
        assertNotNull(appTemplate)
        // 测试关联应用后无法删除
        appBizService.create(AppRequest(name = appName, appTemplateName = name))
        assertThrows<BizException> {
            appTemplateBizService.delete(name)
        }
        // 测试删除关联应用后成功删除
        // 避免is_deleted删除时间冲突
        Thread.sleep(1000)
        appBizService.delete(appName)
        appTemplateBizService.delete(name)
        assertThrows<BizException> {
            appTemplateBizService.find(name)
        }
        // TODO: 测试关联资源删除情况
    }

    @Test
    fun copyTest() {
        try {
            // 跳过审计
            doNothing().`when`(auditLogService).commonLog(any(), any(), any())
            // 测试应用模版不存在
            assertThrows<BizException> {
                appTemplateBizService.copy(name, request)
            }
            // 插入数据待复制
            appTemplateBizService.create(request)
            val appTemplate = appTemplateBizService.copy(
                name,
                UpsertAppTemplateRequest(name = nameCopy, displayName = displayNameCopy)
            )
            assertNotNull(appTemplate)
            assertEquals(appTemplate.name, nameCopy)
            assertEquals(appTemplate.displayName, displayNameCopy)
            assertEquals(appTemplate.description, "")
            assertEquals(appTemplate.cover, "")
            // TODO: 测试配置复制
        } finally {
            // 清理数据
            // 避免is_deleted删除时间冲突
            Thread.sleep(1000)
            appTemplateBizService.delete(name)
            appTemplateBizService.delete(nameCopy)
        }
    }

    @Test
    fun listTest() {
        try {
            // 跳过审计
            doNothing().`when`(auditLogService).commonLog(any(), any(), any())
            // 插入数据
            appTemplateBizService.create(UpsertAppTemplateRequest(name = name, displayName = displayName))
            appTemplateBizService.create(UpsertAppTemplateRequest(name = name2, displayName = displayName))
            // 全量列表
            var query = AppTemplateQuery(
                pagination = "keyset",
                perPage = 20,
                orderBy = "id",
                sort = "asc",
                nextToken = null,
                search = null
            )
            var pageList = appTemplateBizService.list(query)
            assertEquals( 3,pageList.data.size)
            assertEquals(pageList.data[1].name, name)
            // 逆序测试
            query = AppTemplateQuery(
                pagination = "keyset",
                perPage = 20,
                orderBy = "id",
                sort = "desc",
                nextToken = null,
                search = null
            )
            pageList = appTemplateBizService.list(query)
            assertEquals( 3, pageList.data.size)
            assertEquals(pageList.data[0].name, name2)
            // 模糊查询列表
            query = AppTemplateQuery(
                pagination = "keyset",
                perPage = 20,
                orderBy = "id",
                sort = "asc",
                nextToken = null,
                search = displayNameSearchKey
            )
            pageList = appTemplateBizService.list(query)
            assertEquals(pageList.data.size, 2)
            assertEquals(pageList.data[0].name, name)
            // 分页列表
            query = AppTemplateQuery(
                pagination = "keyset",
                perPage = 1,
                orderBy = "id",
                sort = "asc",
                nextToken = pageList.nextToken,
                search = null
            )
            pageList = appTemplateBizService.list(query)
            assertEquals(pageList.data.size, 1)
        } finally {
            // 清理数据
            // 避免is_deleted删除时间冲突
            Thread.sleep(1000)
            appTemplateBizService.delete(name)
            appTemplateBizService.delete(name2)
        }
    }

    @Test
    fun findPaginatedTest() {
        try {
            // 跳过审计
            doNothing().`when`(auditLogService).commonLog(any(), any(), any())
            // 插入数据
            appTemplateBizService.create(UpsertAppTemplateRequest(name = name, displayName = displayName))
            appTemplateBizService.create(UpsertAppTemplateRequest(name = name2, displayName = displayName))
            // 全量列表
            var pageList = appTemplateBizService.findPaginated("", 1, 100)
            assertEquals(3, pageList.total)
            assertEquals(pageList.records.size, 3)
            assertEquals(pageList.records[1].name, name)
            // 带查询列表
            pageList = appTemplateBizService.findPaginated(name2, 1, 100)
            assertEquals(pageList.total, 1)
            assertEquals(pageList.records.size, 1)
            assertEquals(pageList.records[0].name, name2)
            // 模糊查询
            pageList = appTemplateBizService.findPaginated(displayNameSearchKey, 1, 100)
            assertEquals(pageList.total, 2)
            assertEquals(pageList.records.size, 2)
            assertEquals(pageList.records[0].name, name)
            // 分页列表
            pageList = appTemplateBizService.findPaginated("", 1, 1)
            assertEquals(3, pageList.total)
            assertEquals( 3, pageList.pages)
            assertEquals(pageList.records.size, 1)
            pageList = appTemplateBizService.findPaginated("", 2, 1)
            assertEquals(pageList.records.size, 1)
            assertEquals(pageList.records[0].name, name)
            pageList = appTemplateBizService.findPaginated("",1,1, AppTemplate.Type.PRESET)
            assertEquals(1, pageList.records.size)
        } finally {
            // 清理数据
            // 避免is_deleted删除时间冲突
            Thread.sleep(1000)
            appTemplateBizService.delete(name)
            appTemplateBizService.delete(name2)
        }
    }

    @Test
    fun findTest() {
        try {
            // 跳过审计
            doNothing().`when`(auditLogService).commonLog(any(), any(), any())
            // 测试应用模版不存在
            assertThrows<BizException> {
                appTemplateBizService.find(name)
            }
            // 插入数据
            appTemplateBizService.create(request)
            var appTemplate = appTemplateBizService.find(name)
            assertNotNull(appTemplate)
            assertEquals(appTemplate.name, name)

            appTemplate = appTemplateBizService.find(AppTemplateServiceImpl.PRESET_TEMPLATE_NAMES[0])
            assertNotNull(appTemplate)
        } finally {
            // 清理数据
            // 避免is_deleted删除时间冲突
            Thread.sleep(1000)
            appTemplateBizService.delete(name)
        }
    }

    @Test
    fun updateTest() {
        try {
            // 跳过审计
            doNothing().`when`(auditLogService).commonLog(any(), any(), any())
            // 测试应用模版找不到
            assertThrows<BizException> {
                appTemplateBizService.update(name, request)
            }
            // 插入数据
            appTemplateBizService.create(request)
            val appTemplate =
                appTemplateBizService.update(name, UpsertAppTemplateRequest(displayName = displayNameCopy))
            assertEquals(appTemplate.displayName, displayNameCopy)
        } finally {
            // 清理数据
            // 避免is_deleted删除时间冲突
            Thread.sleep(1000)
            appTemplateBizService.delete(name)
        }
    }

    @Test
    fun findPaginatedAppsTest() {
        try {
            // 跳过审计
            doNothing().`when`(auditLogService).commonLog(any(), any(), any())
            doNothing().`when`(iamService).registerResource(any(), any(), any(), any())
            AuthThreadContext.setUserId("")
            // 跳过代码源、制品源删除
            given(appCodeRepoBizService.findAll(any())).willReturn(emptyList())
            given(appArtifactRepoBizService.findAll(any())).willReturn(emptyList())
            // 测试应用模版找不到
            assertThrows<BizException> {
                appTemplateBizService.findPaginatedApps(name, 1, 10)
            }
            // 插入数据
            appTemplateBizService.create(request)
            appTemplateBizService.create(UpsertAppTemplateRequest(name = name2, displayName = displayName))
            var pageResult = appTemplateBizService.findPaginatedApps(name, 1, 10)
            assertEquals(pageResult.total, 0)

            appBizService.create(AppRequest(name = appName, appTemplateName = name))
            pageResult = appTemplateBizService.findPaginatedApps(name, 1, 10)
            assertEquals(pageResult.total, 1)
            assertEquals(pageResult.records.size, 1)
            assertEquals(pageResult.records[0].name, appName)
            appBizService.create(AppRequest(name = appName2, appTemplateName = name2))
            pageResult = appTemplateBizService.findPaginatedApps(name2, 1, 10)
            assertEquals(pageResult.total, 1)
            assertEquals(pageResult.records.size, 1)
            assertEquals(pageResult.records[0].name, appName2)
            appTemplateBizService.findPaginatedApps(AppTemplateServiceImpl.PRESET_TEMPLATE_NAMES[0], 1, 10)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            // 清理数据
            // 避免is_deleted删除时间冲突
            Thread.sleep(1000)
            appBizService.delete(appName)
            appBizService.delete(appName2)
            appTemplateBizService.delete(name)
            appTemplateBizService.delete(name2)
        }
    }

    @Test
    fun presetTest(){
        given(appTemplateConfigBizService.upsert(any(), any(), any())).willReturn(null)
        appTemplateBizService.preset()
    }

}