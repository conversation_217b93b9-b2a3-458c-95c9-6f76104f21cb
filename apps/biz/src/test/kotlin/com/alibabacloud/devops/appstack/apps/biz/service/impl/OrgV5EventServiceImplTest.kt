package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibaba.aone.framework.tbs.app.sdk.webhook.model.v1.RoleShipCreated
import com.alibaba.fastjson.JSON
import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.model.OrgV5EventRequest
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ActiveProfiles

/**
 * <AUTHOR> <EMAIL>
 * @version : OrgV5EventServiceImpl, v0.1
 * @date : 2022-09-25 17:06
 **/
@ActiveProfiles("vpc")
class OrgV5EventServiceImplTest : BizServerApplicationTests() {
    @Autowired
    lateinit var jacksonObjectMapper: ObjectMapper

    @Test
    fun testV5Event() {
        val body = """
            {
              "action": "created",
              "created_at": "2022-09-25T08:53:40.766492102Z",
              "data": {
                "org": {
                  "creator_id": "4298ade9-3cba-4c1c-a607-af35fac956f8",
                  "default_role": "org_member",
                  "description": "",
                  "id": "82147fde-cb22-449d-81c0-c34aa7318715",
                  "logo": "",
                  "name": "元琦的企业",
                  "source": "",
                  "type": ""
                },
                "role": {
                  "app_identity": "app_stack",
                  "creator_id": "82147fde-cb22-449d-81c0-c34aa7318715",
                  "id": "appstack_admin",
                  "is_default": false,
                  "labels": {
                    "zh_CN": "应用交付管理员"
                  },
                  "name": "appstack_admin",
                  "organization_id": "82147fde-cb22-449d-81c0-c34aa7318715",
                  "permissions": null,
                  "resource": "base/organization",
                  "resource_id": "82147fde-cb22-449d-81c0-c34aa7318715",
                  "role_type": "SYSTEM_ORGANIZATION_ROLE"
                },
                "user": {
                  "avatar": "/objects/ajax/default/avatars/5LqR5pWI",
                  "email": "",
                  "id": "aebb0f9a-f48e-401f-9026-d9c5d9d03f19",
                  "is_admin": true,
                  "name": "全伟",
                  "phone_number": "",
                  "staff_id": "",
                  "username": "quanwei"
                }
              },
              "id": "",
              "operator": {
                "ip": "***********",
                "is_admin": true,
                "member_id": "",
                "name": "元琦",
                "organization_id": "82147fde-cb22-449d-81c0-c34aa7318715",
                "user_id": "4298ade9-3cba-4c1c-a607-af35fac956f8",
                "user_name": "yuanqi"
              },
              "organization_id": "82147fde-cb22-449d-81c0-c34aa7318715",
              "resource": "roleship",
              "resource_id": "appstack_admin",
              "subject": "role/appstack_admin",
              "type": "yunxiao.base.roleship.v1.created",
              "version": "v1"
            }
        """.trimIndent()
        val orgV5EventRequest = jacksonObjectMapper.readValue<OrgV5EventRequest>(body)
        val roleShipCreated = JSON.parseObject(orgV5EventRequest.data, RoleShipCreated::class.java)
        Assertions.assertEquals("4298ade9-3cba-4c1c-a607-af35fac956f8", orgV5EventRequest.operator.userId)
        Assertions.assertEquals("82147fde-cb22-449d-81c0-c34aa7318715", roleShipCreated.org.id)
    }
}