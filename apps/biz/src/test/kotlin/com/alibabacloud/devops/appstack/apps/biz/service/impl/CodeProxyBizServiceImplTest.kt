package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.CodeProxyBizService
import com.alibabacloud.devops.appstack.libs.code.spring.boot.starter.service.CodeService
import com.alibabacloud.devops.appstack.libs.model.`do`.app.CodeupRepoContext
import com.alibabacloud.devops.appstack.libs.model.`do`.app.FlowConnectionConfig
import com.alibabacloud.devops.appstack.libs.model.response.cr.GitBranchInfo
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.given
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean

/**
 * <AUTHOR> <EMAIL>
 * @version : CodeProxyBizServiceImplTest, v0.1
 * @date : 2023-04-28 14:24
 */
class CodeProxyBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var codeProxyBizService: CodeProxyBizService

    @MockBean
    lateinit var codeService: CodeService

    @Test
    fun findBranch() {
        val branchName = "test"
        given(codeService.findBranch(any(), any(), any())).willReturn(
            GitBranchInfo(
                commit = null,
                name = branchName
            )
        )
        val findBranch = codeProxyBizService.findBranch(
            repoContext = CodeupRepoContext(projectId = ""),
            connectionConfig = FlowConnectionConfig(connectionId = ""),
            branchName = branchName,
        )
        Assertions.assertEquals(branchName, findBranch.name)
    }
}