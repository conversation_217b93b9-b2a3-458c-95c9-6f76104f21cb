package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.*
import com.alibabacloud.devops.appstack.libs.code.spring.boot.starter.service.CodeService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.model.`do`.OnlyViewAccessableAppV1OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.app.*
import com.alibabacloud.devops.appstack.libs.model.`do`.apptemplate.ArtifactRepoTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.apptemplate.CodeRepoTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.env.EnvTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.env.RolloutStrategy
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.iam.AppRole
import com.alibabacloud.devops.appstack.libs.model.request.AppRequest
import com.alibabacloud.devops.appstack.libs.model.request.SearchAppRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.EnvConfigurationUpsertRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.SourceConfigurationUpsertRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateConfigRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.AddMembersRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.ApplicationQuery
import com.alibabacloud.devops.appstack.libs.model.response.cr.GitProject
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.ArgumentMatchers.anyString
import org.mockito.kotlin.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean

/**
 * @author: <EMAIL>
 * @date: 2023-06-13 17:33
 * @version: AppBizServiceImplTest, v0.1
 **/
class AppBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var appBizServiceImpl: AppBizServiceImpl

    @Autowired
    lateinit var appTemplateBizService: AppTemplateBizService

    @Autowired
    lateinit var appTemplateConfigBizService: AppTemplateConfigBizService

    @MockBean
    lateinit var iamService: IamService

    @MockBean
    lateinit var auditLogService: AuditLogService

    @MockBean
    lateinit var permissionService: PermissionService

    @MockBean
    lateinit var codeService: CodeService

    @MockBean
    lateinit var appCodeRepoBizService: AppCodeRepoBizService

    @MockBean
    lateinit var appArtifactRepoBizService: AppArtifactRepoBizService

    @MockBean
    lateinit var envBizService: EnvBizService

    @Autowired
    lateinit var i18nMessageService: I18nMessageService

    @Autowired
    lateinit var domainTagBizService: DomainTagBizService

    @MockBean
    lateinit var orgConfigItemBizService: OrgConfigItemBizService

    companion object {
        val appName = "app-name"
        val appName2 = "app-name-2"
        val appTemplateName = "app-template-name"
        val CODE_REPO_TEMPLATES = listOf(
            CodeRepoTemplate(
                name = "\${APPSTACK_APP_NAME}",
                repoUrl = "http://\${APPSTACK_APP_NAME}.git",
                identifier = "\${APPSTACK_APP_NAME}",
                repoContext = CodeupRepoContext(
                    repoUrl = "http://\${APPSTACK_APP_NAME}.git",
                    projectId = "",
                    defaultBranch = "master"
                ),
                connectionConfig = FlowConnectionConfig("")
            )
        )
        val ARTIFACT_REPO_TEMPLATES = listOf(
            ArtifactRepoTemplate(
                identifier = "\${APPSTACK_APP_NAME}_artifact",
                repoContext = CustomDockerRegistryContext(
                    repoUrl = "\${APPSTACK_APP_NAME}_image",
                    defaultVersion = "\${APPSTACK_APP_NAME}_\${DATETIME}_\${TIMESTAMP}"
                ),
                connectionConfig = FlowConnectionConfig("")
            )
        )
        val appCodeRepo = AppCodeRepo(
            sn = "",
            name = "app-name",
            appName = appName,
            repoUrl = "http://app-name.git",
            identifier = "app-name",
            repoContext = CodeupRepoContext(
                repoUrl = "http://app-name.git",
                projectId = "",
                defaultBranch = "master"
            ),
            connectionConfig = FlowConnectionConfig("")
        )
        val appArtifactRepo = AppArtifactRepo(
            sn = "",
            appName = appName,
            identifier = "app-name_artifact",
            repoContext = CustomDockerRegistryContext(
                repoUrl = "app-name_image",
                defaultVersion = "app-name"
            ),
            connectionConfig = FlowConnectionConfig("")
        )
        val envTemplateName = "env-template-name"
        val envTemplateDisplayName = "env-template-display-name"
    }

    @Test
    fun ownerTransferTest() {
        given(iamService.findRolePlayers(ProtocolType.AppStackApp, listOf("test"), AppRole.owner.name)).willReturn(
            mapOf("test" to listOf("userId_A"))
        )
        appBizServiceImpl.transferOwner("test", ResourcePlayerRequest("userId", SubjectType.User))
    }

    @Test
    fun addRoleMemberTest() {
        val role = Role()
        role.name = AppRole.owner.name
        given(permissionService.findAppRole(role.name)).willReturn(role)
        try {
            appBizServiceImpl.addRoleMember(
                "test",
                role.name,
                listOf(ResourcePlayerRequest("userId", SubjectType.User))
            )
        } catch (e: BizException) {
            assertEquals(ErrorCode.AS_ROLE_OWNER_ONLY_CAN_BE_MODIFIED_BY_TRANSFER, e.errorEntry.code)
        }
    }

    @Test
    fun updateRoleMemberTest() {
        val role = Role()
        role.name = AppRole.owner.name
        given(permissionService.findAppRole(role.name)).willReturn(role)
        try {
            appBizServiceImpl.updateRoleMember(
                "test",
                role.name,
                listOf(ResourcePlayerRequest("userId", SubjectType.User))
            )
        } catch (e: BizException) {
            assertEquals(ErrorCode.AS_ROLE_OWNER_ONLY_CAN_BE_MODIFIED_BY_TRANSFER, e.errorEntry.code)
        }
    }

    @Test
    fun createWithoutAppTemplateTest() {
        AuthThreadContext.setUserId("")
        try {
            // 跳过审计
            doNothing().`when`(auditLogService).commonLog(any(), any(), any())
            doNothing().`when`(iamService).registerResource(any(), any(), any(), any())
            // 跳过代码源、制品源删除
            given(appCodeRepoBizService.findAll(any())).willReturn(emptyList())
            given(appArtifactRepoBizService.findAll(any())).willReturn(emptyList())
            val app = appBizServiceImpl.create(AppRequest(name = appName))
            assertEquals(app.name, appName)
            // TODO: 无模版创建，相关配置为空
        } finally {
            // 清理数据
            // 避免is_deleted删除时间冲突
            Thread.sleep(1000)
            appBizServiceImpl.delete(appName)
        }
    }

    @Test
    fun createWithoutAppTemplateWithTagsTest() {
        AuthThreadContext.setUserId("")

        // 跳过审计
        doNothing().`when`(auditLogService).commonLog(any(), any(), any())
        doNothing().`when`(iamService).registerResource(any(), any(), any(), any())
        // 跳过代码源、制品源删除
        given(appCodeRepoBizService.findAll(any())).willReturn(emptyList())
        given(appArtifactRepoBizService.findAll(any())).willReturn(emptyList())
        val tag1 = DomainTag("tag1", "style")
        val tag2 = DomainTag("tag2", "style")
        domainTagBizService.create(tag1)
        domainTagBizService.create(tag2)
        val app = appBizServiceImpl.create(AppRequest(name = appName, domainTags = listOf(tag1.name, tag2.name)))
        assertEquals(app.name, appName)
        val res = domainTagBizService.listTagsByAppName(appName)
        assertEquals(res.size, 2)
        // 清理数据
        // 避免is_deleted删除时间冲突
        Thread.sleep(1000)
        domainTagBizService.delete(tag1.name)
        domainTagBizService.delete(tag2.name)
        appBizServiceImpl.delete(appName)
    }

    @Test
    fun createWithAppTemplateTest() {
        AuthThreadContext.setUserId("")
        try {
            // 跳过审计
            doNothing().`when`(auditLogService).commonLog(any(), any(), any())
            doNothing().`when`(iamService).registerResource(any(), any(), any(), any())
            // 跳过代码源、制品源删除
            given(appCodeRepoBizService.findAll(any())).willReturn(emptyList())
            given(appArtifactRepoBizService.findAll(any())).willReturn(emptyList())
            // 应用模版不存在
            assertThrows<BizException> {
                appBizServiceImpl.create(AppRequest(name = appName, appTemplateName = appTemplateName))
            }
            // TODO: 初始化应用模版
            appTemplateBizService.create(
                UpsertAppTemplateRequest(
                    name = appTemplateName,
                    displayName = appTemplateName
                )
            )
            // 模板带配置
            appTemplateConfigBizService.upsert(
                appTemplateName, AppTemplateConfig.TypeEnum.Source, UpsertAppTemplateConfigRequest(
                    modeSetting = SourceConfigModeSetting(),
                    configuration = SourceConfigurationUpsertRequest(
                        codeRepos = CODE_REPO_TEMPLATES,
                        artifactRepos = ARTIFACT_REPO_TEMPLATES
                    )
                )
            )
            // Mock 代码源配置创建相关
            given(codeService.searchProjects(any(), any(), any())).willReturn(
                listOf(
                    GitProject(
                        name = appName,
                        id = 1L,
                        webUrl = "http://${appName}.git"
                    )
                )
            )
            given(appCodeRepoBizService.create(any(), any())).willReturn(appCodeRepo)
            given(appArtifactRepoBizService.create(any(), any())).willReturn(appArtifactRepo)
            val app = appBizServiceImpl.create(AppRequest(name = appName, appTemplateName = appTemplateName))
            assertEquals(app.name, appName)
            // TODO: 带模版创建，验证相关配置
            verify(appCodeRepoBizService, times(1)).create(any(), any())
            verify(appArtifactRepoBizService, times(1)).create(any(), any())
        } finally {
            // 清理数据
            // 避免is_deleted删除时间冲突
            Thread.sleep(1000)
            appBizServiceImpl.delete(appName)
            appTemplateBizService.delete(appTemplateName)
        }
    }

    @Test
    fun createWithAppTemplateTest2() {
        AuthThreadContext.setUserId("")
        try {
            // 跳过审计
            doNothing().`when`(auditLogService).commonLog(any(), any(), any())
            doNothing().`when`(iamService).registerResource(any(), any(), any(), any())
            // 跳过代码源、制品源删除
            given(appCodeRepoBizService.findAll(any())).willReturn(emptyList())
            given(appArtifactRepoBizService.findAll(any())).willReturn(emptyList())
            // 应用模版不存在
            assertThrows<BizException> {
                appBizServiceImpl.create(AppRequest(name = appName, appTemplateName = appTemplateName))
            }
            // TODO: 初始化应用模版
            appTemplateBizService.create(
                UpsertAppTemplateRequest(
                    name = appTemplateName,
                    displayName = appTemplateName
                )
            )
            // 验证模板配置：环境带策略
            appTemplateConfigBizService.upsert(
                appTemplateName, AppTemplateConfig.TypeEnum.Env, UpsertAppTemplateConfigRequest(
                    modeSetting = EnvConfigModeSetting(),
                    configuration = EnvConfigurationUpsertRequest(
                        envs = listOf(
                            EnvTemplate(
                                name = envTemplateName,
                                displayName = envTemplateDisplayName,
                                labels = emptyList(),
                                spec = Env.Spec(
                                    rolloutStrategy = listOf(
                                        RolloutStrategy(
                                            locator = "*",
                                            batches = 2,
                                            batchSteps = listOf(
                                                RolloutStrategy.BatchStep(
                                                    type = RolloutStrategy.BatchStep.Type.REPLICAS,
                                                    value = "3"
                                                ),
                                                RolloutStrategy.BatchStep(
                                                    type = RolloutStrategy.BatchStep.Type.WEIGHT,
                                                    value = "50"
                                                )
                                            )
                                        )
                                    )
                                ),
                            )
                        )
                    )
                )
            )
            val app1 = appBizServiceImpl.create(AppRequest(name = appName, appTemplateName = appTemplateName))
            assertEquals(app1.name, appName)
            // TODO: 带模版创建，验证相关配置
            verify(envBizService, times(1)).create(any(), any())
        } finally {
            // 清理数据
            // 避免is_deleted删除时间冲突
            Thread.sleep(1000)
            appBizServiceImpl.delete(appName)
            appTemplateBizService.delete(appTemplateName)
        }
    }

    @Test
    fun findTemplateTest() {
        AuthThreadContext.setUserId("")
        try {
            // 跳过审计
            doNothing().`when`(auditLogService).commonLog(any(), any(), any())
            doNothing().`when`(iamService).registerResource(any(), any(), any(), any())
            // 跳过代码源、制品源删除
            given(appCodeRepoBizService.findAll(any())).willReturn(emptyList())
            given(appArtifactRepoBizService.findAll(any())).willReturn(emptyList())
            // 应用不存在
            assertThrows<BizException> {
                appBizServiceImpl.findTemplate("none")
            }
            // 无模版创建
            appBizServiceImpl.create(AppRequest(name = appName))
            var appTemplate = appBizServiceImpl.findTemplate(appName)
            assertNull(appTemplate)
            // 带模版创建
            appTemplateBizService.create(
                UpsertAppTemplateRequest(
                    name = appTemplateName,
                    displayName = appTemplateName
                )
            )
            appBizServiceImpl.create(AppRequest(name = appName2, appTemplateName = appTemplateName))
            appTemplate = appBizServiceImpl.findTemplate(appName2)
            assertNotNull(appTemplate)
            assertEquals(appTemplate!!.name, appTemplateName)
        } finally {
            // 清理数据
            // 避免is_deleted删除时间冲突
            Thread.sleep(1000)
            appBizServiceImpl.delete(appName)
            appBizServiceImpl.delete(appName2)
            appTemplateBizService.delete(appTemplateName)
        }
    }

    @Test
    fun listAdminedAppsPaginatedTest() {
        AuthThreadContext.setUserId("user")
        given(iamService.countResource(ProtocolType.AppStackApp, "user", SubjectType.User, "")).willReturn(2)
        given(iamService.searchResourceName(ProtocolType.AppStackApp, "user", SubjectType.User, "", 1, 2)).willReturn(
            listOf("myapp", "myapp2")
        )
        // 设置应用owner
        given(
            iamService.findRolePlayers(
                ProtocolType.AppStackApp,
                listOf("myapp", "myapp2"),
                AppRole.owner.name
            )
        ).willReturn(
            mapOf("myapp" to listOf("user"), "myapp2" to listOf("user2"))
        )
        // 具有应用管理权限
        given(iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_MANAGE)).willReturn(true)
        var apps = appBizServiceImpl.listAdminedAppsPaginated("", 1, 10)
        assertEquals(apps.total, 2)
        assertEquals(apps.records.size, 2)
        assertEquals(apps.records[0].name, "myapp")
        assertEquals(apps.records[1].name, "myapp2")
        // 不具有应用管理权限
        given(iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_MANAGE)).willReturn(false)
        apps = appBizServiceImpl.listAdminedAppsPaginated("", 1, 10)
        // 由于过滤，目前的 total 可能不准
        assertEquals(apps.total, 2)
        assertEquals(apps.records.size, 1)
        assertEquals(apps.records[0].name, "myapp")
    }


    @Test
    fun deleteTest() {
        // 跳过代码源、制品源删除
        given(appCodeRepoBizService.findAll(any())).willReturn(emptyList())
        given(appArtifactRepoBizService.findAll(any())).willReturn(emptyList())
        appBizServiceImpl.delete("test")
    }

    @Test
    fun test() {
        val a = "{\"/ec/ajax/pipelines/queryByRegion\": \n" +
            "\"/inner/api/pop/v2/pipelines/queryByRegion\",\n" +
            " \"/ec/ajax/pipeline/(?<segment>[^/]*)/getPipelineBaseInfo\":\n" +
            " \"/inner/api/pipelines/baseInfo?pipelineId=\$\\\\{segment}\",\n" +
            " \"/ec/ajax/pipelines/(?<segment>.*)\":\n" +
            " \"/inner/api/pipelines/\$\\\\{segment}\"\n" +
            " }"
        val mp = mutableMapOf<String, String>()
        try {
            val map = jacksonObjectMapper().readValue(a, MutableMap::class.java)
            map.forEach { (k, v) ->
                val key = k as String
                val value = v as String
                mp[key] = value
            }
            logger.info("load flowForwardMap init: $map")
        } catch (e: Throwable) {
            logger.error("load flowForwardMap error: str: $a", e)
        }
    }


    @Test
    fun createWithPresetAppTemolateTest() {
        doNothing().`when`(auditLogService).commonLog(any(), any(), any())
        doNothing().`when`(iamService).registerResource(any(), any(), any(), any())
        given(envBizService.create(any(), any())).willReturn(
            Env(
                name = "envName",
                displayName = "测试环境",
                appName = "appName",
                status = Env.Status.NEW,
                description = "description",
                creatorId = "creator",
                gmtCreate = null,
                lockBy = null,
                resourcePoolName = "resourcePool-name",
                deployGroupName = "deployGroup-name",
                orchestrationName = "orchestration-name",
                profiles = mutableListOf(
                    VariableGroup(
                        name = "dev",
                        displayName = null,
                        type = VariableGroup.Type.APP
                    )
                ),
            )
        )
        val app = appBizServiceImpl.create(
            AppRequest(
                name = "my-app3",
                appTemplateName = AppTemplateServiceImpl.PRESET_TEMPLATE_NAMES.first()
            )
        )
        assertNotNull(app)
        verify(envBizService, times(4)).create(any(), any())
    }

    @Test
    fun shouldHave5xxErrorDirectlyThrown() {
        // given
        val rawError = BizException(ErrorCode.AS_UNKNOWN)

        // when
        var error: Throwable? = null
        try {
            appBizServiceImpl.normalizeError(rawError, "tmpl", "app")
        } catch (e: Throwable) {
            error = e
        }

        // then
        assert(error != null)
        assert(error is BizException)
        assert((error as BizException).errorEntry.code == ErrorCode.AS_UNKNOWN)
    }

    @Test
    fun shouldHaveNonBizExceptionConvertedToBizException() {
        // given
        val rawError = RuntimeException("mock error")

        // when
        var error: Throwable? = null
        try {
            appBizServiceImpl.normalizeError(rawError, "tmpl", "app")
        } catch (e: Throwable) {
            error = e
        }

        // then
        assert(error != null)
        assert(error is BizException)
        assert((error as BizException).errorEntry.code == ErrorCode.AS_APP_CREATE_WITH_TEMPLATE_FAILED)
    }

    @Test
    fun `batch add members`() {
        val role = Role()
        role.name = AppRole.developer.name
        given(permissionService.findAppRole(role.name)).willReturn(role)
        given(iamService.checkPlayersExist(any())).willReturn(true)
        try {
            appBizServiceImpl.batchAddMembers(
                listOf("test"),
                AddMembersRequest(
                    listOf("test"),
                    listOf(role.name),
                    listOf(ResourcePlayerRequest("userId", SubjectType.User))
                )
            )
        } catch (e: BizException) {
            assertEquals(ErrorCode.AS_ROLE_OWNER_ONLY_CAN_BE_MODIFIED_BY_TRANSFER, e.errorEntry.code)
        }
    }

    @Test
    fun `batch add members - user not exist`() {
        val role = Role()
        role.name = AppRole.developer.name
        given(permissionService.findAppRole(role.name)).willReturn(role)
        given(iamService.checkPlayersExist(any())).willReturn(false)
        val batchAddMembers = appBizServiceImpl.batchAddMembers(
            listOf("test"),
            AddMembersRequest(
                listOf("test"),
                listOf(role.name),
                listOf(ResourcePlayerRequest("userId", SubjectType.User))
            )
        )
        assert(!batchAddMembers.first().result)
        assert(batchAddMembers.first().errorMsg == i18nMessageService.getErrorMessage(BizException(errorCode = ErrorCode.AS_BASE_USER_NOT_FOUND)))
    }

    @Test
    fun `batch delete members - without owner id`() {
        val role = Role()
        role.name = AppRole.developer.name
        given(permissionService.findAppRole(role.name)).willReturn(role)
        given(iamService.checkPlayersExist(any())).willReturn(true)
        try {
            appBizServiceImpl.batchDeleteMembers(
                listOf("test"),
                listOf(
                    ResourcePlayerRequest("userId", SubjectType.User),
                    ResourcePlayerRequest("userId2", SubjectType.User)
                )
            )
        } catch (e: BizException) {
            assertEquals(ErrorCode.AS_ROLE_OWNER_ONLY_CAN_BE_MODIFIED_BY_TRANSFER, e.errorEntry.code)
        }
        verify(iamService, times(2)).updatePlayer(any(), any(), any(), any(), any())
    }

    @Test
    fun `batch delete members - with owner id`() {
        val role = Role()
        role.name = AppRole.developer.name
        given(permissionService.findAppRole(role.name)).willReturn(role)
        given(iamService.checkPlayersExist(any())).willReturn(true)
        given(
            iamService.findRolePlayers(
                ProtocolType.AppStackApp,
                resourceNameList = listOf("test"),
                roleName = AppRole.owner.name
            )
        ).willReturn(mapOf("test" to listOf("userId2")))
        val batchDeleteMembers = appBizServiceImpl.batchDeleteMembers(
            listOf("test"),
            listOf(
                ResourcePlayerRequest("userId", SubjectType.User),
                ResourcePlayerRequest("userId2", SubjectType.User)
            )
        )
        assert(!batchDeleteMembers.first().result)
        assert(batchDeleteMembers.first().errorMsg == i18nMessageService.getErrorMessage(BizException(errorCode = ErrorCode.AS_ROLE_OWNER_ONLY_CAN_BE_MODIFIED_BY_TRANSFER)))
    }

    @Test
    fun groupCount_shouldReturnCorrectCountWhenSelectTags() {
        val tags = listOf("tag1", "tag2")
        val req = SearchAppRequest(
            search = "",
            isFavoured = false,
            isMine = false,
            domainTags = tags
        )

        given(orgConfigItemBizService.find(OrgConfigItem.ONLY_VIEW_ACCESSABLE_APP)).willReturn(
            OnlyViewAccessableAppV1OrgConfigItem(enable = false)
        )
        given(iamService.countResource(any(), any(), any(), any())).willReturn(1)
        given(iamService.searchResourceName(any(), any(), any(), any(), anyInt(), anyInt())).willReturn(listOf("myapp"))
        val appCountVO = appBizServiceImpl.groupCount(req)
        assert(appCountVO.all == 2L)
        assert(appCountVO.mine == 1L)
        assert(appCountVO.favorite == 2L)
    }

    @Test
    fun list_query_unpassed(){
        val query = ApplicationQuery(pagination = "xxx", orderBy = "dd")
        try{
            appBizServiceImpl.list(query)
            Assertions.fail("should fail")
        }catch (e: BizException){
            assertEquals(ErrorCode.AS_PAGINATION_PARAM_INVALID, e.errorEntry.code)
        }
    }

    @Test
    fun list_only_view_accessable_app_without_permission(){
        val query = ApplicationQuery(pagination = "keyset", orderBy = "id", sort = "desc")
        given(orgConfigItemBizService.find(OrgConfigItem.ONLY_VIEW_ACCESSABLE_APP)).willReturn(
            OnlyViewAccessableAppV1OrgConfigItem(enable = true)
        )
        given(iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_VIEW)).willReturn(false)
        given(iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_MANAGE)).willReturn(false)
        try{
            appBizServiceImpl.list(query)
            Assertions.fail("should fail")
        }catch (e: BizException){
            assertEquals(ErrorCode.AS_PERMISSION_DENIED, e.errorEntry.code)
        }
    }

    @Test
    fun list_isMine_empty(){
        val query = ApplicationQuery(pagination = "keyset",  orderBy = "id", sort = "desc", isMine = true)
        given(orgConfigItemBizService.find(OrgConfigItem.ONLY_VIEW_ACCESSABLE_APP)).willReturn(
            OnlyViewAccessableAppV1OrgConfigItem(enable = false)
        )
        given(iamService.searchResourceName(eq(ProtocolType.AppStackApp), eq(AuthUtil.getUserId()), eq(SubjectType.User),eq(""), any(), any())).willReturn(emptyList())
        val pageList = appBizServiceImpl.list(query)
        Assertions.assertEquals(0 , pageList.total)
        Assertions.assertEquals(0 , pageList.data.size)
    }

    @Test
    fun list_isMine_not_empty(){
        val query = ApplicationQuery(pagination = "keyset",  orderBy = "id", sort = "desc", isMine = true)
        given(orgConfigItemBizService.find(OrgConfigItem.ONLY_VIEW_ACCESSABLE_APP)).willReturn(
            OnlyViewAccessableAppV1OrgConfigItem(enable = false)
        )
        given(iamService.searchResourceName(eq(ProtocolType.AppStackApp), eq(AuthUtil.getUserId()), eq(SubjectType.User),eq(""), any(), any())).willReturn(
            listOf("myapp1"))
        val pageList = appBizServiceImpl.list(query)
        Assertions.assertTrue(pageList.total!! >0)
        Assertions.assertTrue(pageList.data.isNotEmpty())
    }

    @Test
    fun list_notMine(){
        val query = ApplicationQuery(pagination = "keyset",  orderBy = "id", sort = "desc", isMine = false)
        given(orgConfigItemBizService.find(OrgConfigItem.ONLY_VIEW_ACCESSABLE_APP)).willReturn(
            OnlyViewAccessableAppV1OrgConfigItem(enable = false)
        )
        val pageList = appBizServiceImpl.list(query)
        Assertions.assertTrue(pageList.total!! >0)
        Assertions.assertTrue(pageList.data.isNotEmpty())
    }
}