package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.model.DeployFlow
import com.alibabacloud.devops.appstack.apps.biz.service.DeployFlowService
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.MethodOrderer
import org.junit.jupiter.api.Order
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestMethodOrder
import org.springframework.beans.factory.annotation.Autowired

/**
 * @author: <EMAIL>
 * @date: 2022-03-07 19:51
 * @version: DeployFlowServiceImplTest, v0.1
 **/
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class DeployFlowServiceImplTest : BizServerApplicationTests()  {

    @Autowired
    lateinit var deployFlowService: DeployFlowService

    companion object{
        val objectId = "12384+1"
        var deployFlow = DeployFlow(
            appName = "app-name",
            envName = "env-name",
            objectType = DeployFlow.ObjectType.FLOW_APP_STACK,
            objectId = objectId,
            changeOrderSn = "abc",
        )
    }

    @Order(10)
    @Test
    fun createTest(){
        val deployFlow = deployFlowService.create(deployFlow)
        assertEquals(deployFlow.objectId,objectId)
    }
    @Order(20)
    @Test
    fun findTest(){
        val deployFlow = deployFlowService.find(deployFlow.objectType, deployFlow.objectId, "app-name", "env-name")
        assertNotNull(deployFlow)
    }
}