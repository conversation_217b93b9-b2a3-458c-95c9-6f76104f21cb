package com.alibabacloud.devops.appstack.apps.biz.api

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderWebSocketService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestWebSocketService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.Header
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.socket.TextMessage
import org.springframework.web.socket.WebSocketHttpHeaders
import org.springframework.web.socket.WebSocketSession
import org.springframework.web.socket.client.standard.StandardWebSocketClient
import org.springframework.web.socket.handler.TextWebSocketHandler
import org.springframework.web.socket.sockjs.client.SockJsClient
import org.springframework.web.socket.sockjs.client.Transport
import org.springframework.web.socket.sockjs.client.WebSocketTransport
import java.net.URI
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger


/**
 * <AUTHOR>
 * @date 2022-01-29
 */
class ChangeOrderWebSocketTest : BizServerApplicationTests() {

    companion object {
        val changeOrderSn1 = "123sss"
        val changeOrderSn2 = "00998"
        val pipelineId = "213445"
        val content = "Socket-Content-for Test"

        val countMap = ConcurrentHashMap<String, AtomicInteger>()
    }

    @Autowired
    lateinit var changeOrderWebSocketService: ChangeOrderWebSocketService

    @Autowired
    lateinit var changeRequestWebSocketService: ChangeRequestWebSocketService

    @Test
    fun testWebSocket() {
        // 1 client test
        logger.info("---------------Test Case 1----------------------")
        val client = SockJsClient(createTransportClient())
        val webSocketHttpHeaders = WebSocketHttpHeaders()
        webSocketHttpHeaders.add(Header.OPERATOR_ID_HEADER, "5fd1e3841acb1ae7cc187cd4")
        webSocketHttpHeaders.add(Header.TENANT_ID_HEADER, "609cf9669db2b2a61be81b82")
        val uri = URI("ws://localhost:$port/ws/changeOrders?changeOrderSn=$changeOrderSn1")
        val session1 = client.doHandshake(TestWebSocketHandler(), webSocketHttpHeaders, uri).get()
        countMap.put(session1.id, AtomicInteger(0))
        changeOrderWebSocketService.notifyStateChanged(changeOrderSn1, content)
        Thread.sleep(1000)
        Assertions.assertEquals(1, countMap.get(session1.id)!!.get())


        // 2 client test
        logger.info("---------------Test Case 2----------------------")
        val uri2 = URI("ws://localhost:$port/ws/changeOrders?changeOrderSn=$changeOrderSn2")
        val session2 = client.doHandshake(TestWebSocketHandler(), webSocketHttpHeaders, uri2).get()
        countMap.put(session2.id, AtomicInteger(0))
        changeOrderWebSocketService.notifyStateChanged(changeOrderSn2, content)
        Thread.sleep(1000)
        Assertions.assertEquals(1, countMap.get(session1.id)!!.get())
        Assertions.assertEquals(1, countMap.get(session2.id)!!.get())

        // 3 client test
        logger.info("---------------Test Case 3----------------------")
        val session3 = client.doHandshake(TestWebSocketHandler(), webSocketHttpHeaders, uri).get()
        countMap.put(session3.id, AtomicInteger(0))
        changeOrderWebSocketService.notifyStateChanged(changeOrderSn1, content)
        Thread.sleep(1000)
        Assertions.assertEquals(2, countMap.get(session1.id)!!.get())
        Assertions.assertEquals(1, countMap.get(session2.id)!!.get())
        Assertions.assertEquals(1, countMap.get(session3.id)!!.get())


        // remove 1 client
        logger.info("---------------Test Case 4----------------------")
        session1.close()
        changeOrderWebSocketService.notifyStateChanged(changeOrderSn1, content)
        Thread.sleep(1000)
        Assertions.assertEquals(2, countMap.get(session1.id)!!.get())
        Assertions.assertEquals(1, countMap.get(session2.id)!!.get())
        Assertions.assertEquals(2, countMap.get(session3.id)!!.get())

    }

    private fun createTransportClient(): List<Transport> {
        val transports: MutableList<Transport> = ArrayList<Transport>(1)
        transports.add(WebSocketTransport(StandardWebSocketClient()))
        return transports
    }

    private class TestWebSocketHandler : TextWebSocketHandler() {

        override fun handleTextMessage(session: WebSocketSession, message: TextMessage) {
            if (message.payload.equals(content)) {
                countMap.get(session.id)!!.incrementAndGet()
            }
        }
    }
}


