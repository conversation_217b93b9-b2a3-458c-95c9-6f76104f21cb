package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.model.Resource
import com.fasterxml.jackson.module.kotlin.readValue
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.kotlin.any
import org.mockito.kotlin.given
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR> <EMAIL>
 * @version : FlowPipelineBizServiceImplTest, v0.1
 * @date : 2024-06-17 16:23
 */
class FlowPipelineBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var flowPipelineBizServiceImpl: FlowPipelineBizServiceImpl

    @Mock
    lateinit var iamService: IamService

    @Test
    fun testAppMember() {

        val json = """
            [
              {
                "description": null,
                "protocol": "appstack-app",
                "tsCreate": "2024-06-14T09:19:12.216+00:00",
                "tsModified": "2024-06-14T09:19:12.216+00:00",
                "name": "test",
                "displayName": "test",
                "attributes": {
                  
                },
                "rolePlayerList": [
                  {
                    "description": "iam client init data",
                    "protocol": "appstack-app",
                    "tsCreate": "2022-01-11T06:11:58.309+00:00",
                    "tsModified": "2022-01-11T06:11:58.309+00:00",
                    "name": "owner",
                    "displayName": "拥有者",
                    "playerList": [
                      {
                        "id": "609cf9669db2b2a61be81b82",
                        "type": "User",
                        "displayName": "元琦",
                        "description": null,
                        "avatar": "https://tcs-devops.aliyuncs.com/thumbnail/112d3f98ecc48247594255bbbd4aa12178a4/w/100/h/100"
                      },
                      {
                        "id": "group_id",
                        "type": "Group",
                        "displayName": "group",
                        "description": null,
                        "avatar": ""
                      }
                    ]
                  },
                  {
                    "description": "iam client init data",
                    "protocol": "appstack-app",
                    "tsCreate": "2022-01-11T06:11:58.982+00:00",
                    "tsModified": "2022-01-11T06:11:58.982+00:00",
                    "name": "admin",
                    "displayName": "负责人",
                    "playerList": [
                      {
                        "id": "5e7839e1032838332856935f",
                        "type": "User",
                        "displayName": "沈全伟",
                        "description": null,
                        "avatar": "https://tcs-devops.aliyun.com/thumbnail/111r2963097cda83dd6a3cc3be82f69b37ed/w/200/h/200"
                      }
                    ]
                  }
                ]
              }
            ]
        """.trimIndent()
        val roleNameList = listOf("owner")
        val resources = JacksonUtil.jacksonObjectMapper().readValue<List<Resource>>(json)
        val map = resources.associate { resource ->
            resource.name to resource.rolePlayerList.filter {
                roleNameList.contains(it.name)
            }
        }
        given(iamService.findRolePlayers(ProtocolType.AppStackApp, listOf("test"), roleNameList)).willReturn(map)
        given(iamService.listUserIdsByGroup(any(), any())).willReturn(listOf("group_user_id"))
        flowPipelineBizServiceImpl.iamService = iamService

        val appMembers = flowPipelineBizServiceImpl.getAppMembers(roleNameList, "123456")
        Assertions.assertEquals(listOf("609cf9669db2b2a61be81b82","group_user_id"), appMembers)
    }
}