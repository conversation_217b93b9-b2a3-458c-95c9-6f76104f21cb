package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.iam.constant.ProtocolType
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR> <EMAIL>
 * @version : IamServiceImplTest, v0.1
 * @date : 2021-11-29 19:58
 */
internal class IamServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var iamService: IamService

    companion object {
        val orgId = "5fd1e3841acb1ae7cc187cd4"
        val userId = "609cf9669db2b2a61be81b82"
    }

    /**
     * 本地手动运行，依赖外部服务
     */
    @Test
    @Disabled
    fun registerApp() {
        val appName = "test-app"
        iamService.registerResource(ProtocolType.AppStackApp, appName, orgId, userId)
    }

    @Test
    @Disabled
    fun registerCr() {
        iamService.registerResource(ProtocolType.AppstackCr, "cr-sn", orgId, userId);
    }

    @Test
    @Disabled
    fun registerCrSet() {
        iamService.registerResource(ProtocolType.AppstackCrSet, "crset-sn", orgId, userId);
    }
}