package com.alibabacloud.devops.appstack.apps.biz.service.api

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderBizService
import com.alibabacloud.devops.appstack.libs.common.constant.Header
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.kotlin.eq
import org.mockito.kotlin.given
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.boot.test.web.client.TestRestTemplate
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import java.net.URI

/**
 * <AUTHOR>
 * @date 2023-04-25
 */
class ChangeOrderBizApiTest : BizServerApplicationTests() {

    @MockBean
    lateinit var changeOrderBizService: ChangeOrderBizService

    @Autowired
    lateinit var restTemplate: TestRestTemplate

    @Test
    fun test_findPaginatedVersion(){
        val appName = "app"
        val url = "http://localhost:$port/api/v1/changeOrders/versions?appName=$appName"
        val headers = HttpHeaders()
        headers.add(Header.OPERATOR_ID_HEADER, "5fd1e3841acb1ae7cc187cd4")
        headers.add(Header.TENANT_ID_HEADER, "609cf9669db2b2a61be81b82")
        val response = restTemplate.exchange(URI(url), HttpMethod.GET, HttpEntity<String>(headers), Pagination::class.java)
        Assertions.assertNotNull(response.body)
    }
}