package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.model.AppTemplateConfigReference
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR>
 * @date 2023-12-20
 */
class AppTemplateConfigReferenceServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var appTemplateConfigReferenceServiceImpl: AppTemplateConfigReferenceServiceImpl

    @Test
    fun rebindTest(){
        val appTemplateName = "templateName"
        val appTemplateConfigSn = "configSn"
        val references = listOf(
            AppTemplateConfigReference(
                type = AppTemplateConfigReference.TypeEnum.RESOURCE_INSTANCE,
                id = "default::test"
            ),
            AppTemplateConfigReference(
                type = AppTemplateConfigReference.TypeEnum.RESOURCE_INSTANCE,
                id = "default::test"
            ),
        )
        appTemplateConfigReferenceServiceImpl.reBind(appTemplateName, appTemplateConfigSn, references)

        val count = appTemplateConfigReferenceServiceImpl.deleteAll(appTemplateName, appTemplateConfigSn)
        Assertions.assertEquals(1, count)
    }

}