package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.EnvLogBizService
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.model.`do`.Label
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeAction
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeableEnv
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.Job
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.Stage
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.TaskContext
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeDeploymentStrategy
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeTask
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeTaskContext
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.constant.OpLogConstants
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeDeploymentStrategyV1
import com.alibabacloud.devops.appstack.libs.model.event.OpLog
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderContext
import com.alibabacloud.devops.appstack.libs.model.vo.JobContext
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.util.*

/**
 * @author: <EMAIL>
 * @date: 2022-03-04 17:29
 * @version: EnvOpLogServiceTest, v0.1
 **/

class EnvLogBizServiceTest : BizServerApplicationTests() {
    @Autowired
    lateinit var envLogBizService: EnvLogBizService

    companion object {
        val appName = "sutian008"
        val envName = "env-name-v2"
        val changeOrderName = "一次部署"
        val changeOrderSn = "changeOrderSn"
        val jobName = "jobName"
        val jobSn = "jobSn"
        val beforeEnv = Env(
            name = "env-name-v1",
            displayName = "env-displayName-v1",
            appName = appName,
            profiles = mutableListOf(
                VariableGroup(
                    name = "env-profile-name-v1",
                    displayName = null,
                    type = VariableGroup.Type.APP
                )
            ),
            labelList = mutableListOf(
                Label(
                    name = "before-label-name-v1",
                    value = "before-label-value-v1",
                    displayName = "before-label-displayName-v1"
                )
            ),
            description = "这是描述123",
        )

        val env = Env(
            name = envName,
            displayName = "env-displayName-v2",
            appName = appName,
            profiles = mutableListOf(
                VariableGroup(
                    name = "profileName",
                    displayName = "开发环境变量组",
                    type = VariableGroup.Type.APP
                )
            ),
            labelList = mutableListOf(
                Label(
                    name = "label-name-v2",
                    value = "label-value-v2",
                    displayName = "显示标签名",
                    displayValue = "显示标签值"
                )
            ),
            description = "这是描述",
            resourcePoolName = "resource-pool-name",
            deployGroupName = "deployGroup"
        )

        val env2 = Env(
            name = "env-name-v1",
            displayName = "env-displayName-v1",
            appName = appName,
            labelList = mutableListOf(
                Label(
                    name = "before-label-name-v1",
                    value = "before-label-value-v1",
                    displayName = "before-label-displayName-v1"
                )
            )
        )
    }

    @Test
    fun createTest() {
        val envLogVO = envLogBizService.recordCreateEnvAction(env)
        Assertions.assertEquals(OpLog.Action.ENV_CREATE, envLogVO.action)
        Assertions.assertEquals(
            "环境名：env-name-v2，环境显示名：env-displayName-v2，资源：deployGroup-displayName[deployGroup]，变量组：开发环境变量组，标签：显示标签名/显示标签值，描述：这是描述",
            envLogVO.message
        )
    }

    @Test
    fun updateTest() {
        val envLogVO = envLogBizService.recordUpdateEnvAction(env, beforeEnv)
        Assertions.assertEquals(OpLog.Action.ENV_UPDATE, envLogVO?.action)
        Assertions.assertEquals(
            "修改环境显示名为：env-displayName-v2；修改环境关联资源为：deployGroup-displayName[deployGroup]；修改环境标签为：显示标签名/显示标签值；修改环境描述为：这是描述；修改环境关联变量组为：开发环境变量组；",
            envLogVO?.message
        )

        val envLogVO2 = envLogBizService.recordUpdateEnvAction(env2, beforeEnv)
        Assertions.assertEquals(
            "删除环境描述；删除环境关联变量组；",
            envLogVO2?.message
        )
    }

    @Test
    fun lockTest() {
        val envLogVO = envLogBizService.recordLockEnvAction(env, true)
        Assertions.assertEquals(OpLog.Action.ENV_LOCK, envLogVO.action)
        Assertions.assertEquals(
            "锁定环境",
            envLogVO.message
        )
    }

    @Test
    fun deleteTest() {
        val envLogVO =
            envLogBizService.recordDeleteEnvAction(
                env,
                ChangeOrderContext(
                    name = changeOrderName,
                    sn = changeOrderSn,
                    job = JobContext(name = jobName, sn = jobSn, type = Job.Type.Deploy),
                    type = ChangeOrder.Type.Deploy
                )
            )
        Assertions.assertEquals(OpLog.Action.ENV_DELETE, envLogVO.action)
        Assertions.assertEquals(
            "删除环境",
            envLogVO.message
        )
    }

    @Test
    fun recordCreateChangeOrderActionTest() {
        val deployOrder = ChangeOrder(
            sn = "changeOrderSn",
            name = "2022第2个部署单",
            appName = "st-008",
            type = ChangeOrder.Type.Deploy,
            version = "20220117161434924",
            tag = "dfasfaseqe",
            state = ChangeOrder.State.RUNNING,
            description = "desc",
            jobs = listOf(
                Job(
                    sn = "jobSn",
                    name = "jobName",
                    envName = "envName",
                    appName = "appName",
                    engineType = ChangeableEnv.EngineType.Ade1,
                    stages = emptyList(),
                    state = Job.State.SUSPENDED,
                    type = Job.Type.Deploy,
                    controlMode = ChangeAction.ControlMode.Auto,
                    profiles = emptyList(),
                    changeableEnvSn = "",
                )
            ),
            gmtCreate = Date(),
            creator = AuthUtil.getUserId(),
        )
        val envLog1 = envLogBizService.recordCreateChangeOrderAction(deployOrder)
        Assertions.assertEquals(1, envLog1.size)
        Assertions.assertEquals(OpLog.Action.ENV_DEPLOY, envLog1[0].action)
        Assertions.assertEquals("部署到 20220117161434924 版本", envLog1[0].message)
        Assertions.assertEquals(AuthUtil.getUserId(), envLog1[0].operator)
        val changeOrderContext = envLog1[0].context[OpLogConstants.CONTEXT_CHANGE_ORDER] as ChangeOrderContext
        Assertions.assertEquals("changeOrderSn", changeOrderContext.sn)
        Assertions.assertEquals("2022第2个部署单", changeOrderContext.name)
        Assertions.assertEquals(ChangeOrder.State.RUNNING, changeOrderContext.state)
        Assertions.assertEquals("jobSn", changeOrderContext.job.sn)
        Assertions.assertEquals(Job.State.SUSPENDED, changeOrderContext.job.state)
        Assertions.assertEquals("jobName", changeOrderContext.job.name)


        val rollbackOrder = ChangeOrder(
            sn = "changeOrderSn",
            name = "2022第2个部署单",
            appName = "st-008",
            type = ChangeOrder.Type.Rollback,
            version = "20220117161434924",
            tag = "dfasfaseqe",
            state = ChangeOrder.State.RUNNING,
            description = "desc",
            jobs = listOf(
                Job(
                    sn = "jobSn",
                    name = "jobName",
                    envName = "envName",
                    appName = "appName",
                    engineType = ChangeableEnv.EngineType.Ade1,
                    stages = emptyList(),
                    state = Job.State.SUSPENDED,
                    type = Job.Type.Rollback,
                    controlMode = ChangeAction.ControlMode.Auto,
                    profiles = emptyList(),
                    changeableEnvSn = "",
                )
            ),
            gmtCreate = Date(),
            creator = AuthUtil.getUserId(),
        )
        val envLog2 = envLogBizService.recordCreateChangeOrderAction(rollbackOrder)
        Assertions.assertEquals(1, envLog2.size)
        Assertions.assertEquals(OpLog.Action.ENV_ROLLBACK, envLog2[0].action)
        Assertions.assertEquals("回滚到 20220117161434924 版本", envLog2[0].message)
        Assertions.assertEquals(AuthUtil.getUserId(), envLog2[0].operator)
        val changeOrderContext2 = envLog2[0].context[OpLogConstants.CONTEXT_CHANGE_ORDER] as ChangeOrderContext
        Assertions.assertEquals("changeOrderSn", changeOrderContext2.sn)
        Assertions.assertEquals("2022第2个部署单", changeOrderContext2.name)
        Assertions.assertEquals(ChangeOrder.State.RUNNING, changeOrderContext2.state)
        Assertions.assertEquals("jobSn", changeOrderContext2.job.sn)
        Assertions.assertEquals(Job.State.SUSPENDED, changeOrderContext2.job.state)
        Assertions.assertEquals("jobName", changeOrderContext2.job.name)


        val scaleOrder = ChangeOrder(
            sn = "changeOrderSn",
            name = "2022第2个部署单",
            appName = "st-008",
            type = ChangeOrder.Type.Scale,
            version = "20220117161434924",
            tag = "dfasfaseqe",
            state = ChangeOrder.State.RUNNING,
            description = "desc",
            jobs = listOf(
                Job(
                    sn = "jobSn",
                    name = "jobName",
                    envName = "envName",
                    appName = "appName",
                    engineType = ChangeableEnv.EngineType.Ade1,
                    stages = listOf(
                        Stage(
                            name = "部署",
                            sn = "stageSn",
                            state = Stage.State.PREPARING,
                            jobSn = jobSn,
                            tasks = listOf(
                                AdeTask(
                                    engineType = ChangeableEnv.EngineType.Ade1,
                                    resourceType = ResourceTypeEnum.KUBERNETES,
                                    name = "t1",
                                    sn = "123",
                                    // todo jiangqi
//                                    strategy = AdeDeploymentStrategy(
                                    strategy = AdeDeploymentStrategyV1(
                                        targetReplicas = 2,
                                        fromReplicas = 1
                                    ),
                                    context = AdeTaskContext(
                                        appName = "appName",
                                        envName = "envName",
                                        name = "t1",
                                        version = "v1",
                                        jobType = Job.Type.Scale,
                                        kind = TaskContext.Kind.Deployment.name,
                                        group = "api",
                                        deployGroupName = ""
                                    ),
                                ).toTask()
                            )
                        ),
                    ),
                    state = Job.State.SUSPENDED,
                    type = Job.Type.Scale,
                    controlMode = ChangeAction.ControlMode.Auto,
                    profiles = emptyList(),
                    changeableEnvSn = ""
                )
            ),
            gmtCreate = Date(),
            creator = AuthUtil.getUserId(),
        )
        val envLog3 = envLogBizService.recordCreateChangeOrderAction(scaleOrder)
        Assertions.assertEquals(1, envLog3.size)
        Assertions.assertEquals(OpLog.Action.ENV_SCALE, envLog3[0].action)
        Assertions.assertEquals("t1组件由1扩容至2；", envLog3[0].message)
        Assertions.assertEquals(AuthUtil.getUserId(), envLog3[0].operator)
        val changeOrderContext3 = envLog3[0].context[OpLogConstants.CONTEXT_CHANGE_ORDER] as ChangeOrderContext
        Assertions.assertEquals("changeOrderSn", changeOrderContext3.sn)
        Assertions.assertEquals("2022第2个部署单", changeOrderContext3.name)
        Assertions.assertEquals(ChangeOrder.State.RUNNING, changeOrderContext3.state)
        Assertions.assertEquals("jobSn", changeOrderContext3.job.sn)
        Assertions.assertEquals(Job.State.SUSPENDED, changeOrderContext3.job.state)
        Assertions.assertEquals("jobName", changeOrderContext3.job.name)
    }

}

