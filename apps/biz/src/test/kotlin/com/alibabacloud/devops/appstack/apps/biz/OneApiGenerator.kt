package com.alibabacloud.devops.appstack.apps.biz

import com.alibabacloud.devops.appstack.libs.common.util.OneApiUtil
import org.springframework.boot.runApplication

fun main(args: Array<String>) {
    System.setProperty("spring.profiles.active", "test")
    val application = runApplication<BizServerApplication>(*args)
    OneApiUtil.fetchAndUpload(application, "/appstack/gateway/biz", "./", "/forward/")
    application.close()
}
