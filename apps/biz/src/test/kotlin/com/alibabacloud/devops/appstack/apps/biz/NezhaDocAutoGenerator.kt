package com.alibabacloud.devops.appstack.apps.biz

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.apache.http.client.methods.HttpGet
import org.apache.http.impl.client.HttpClientBuilder
import org.apache.http.util.EntityUtils
import org.junit.jupiter.api.Test
import java.io.File
import java.net.URI

/**
 * <AUTHOR>
 * @date 2024-05-21
 */
class NezhaDocAutoGenerator: BizServerApplicationForNezhaDocAutoGenerator() {

    companion object {
        val nezhaGatewayOrgParameter = mapOf(
            "name" to "organizationId",
            "in" to "path",
            "description" to "企业ID",
            "required" to true,
            "style" to "simple",
            "schema" to mapOf(
                "type" to "string"
            )
        )
    }

    @Test
    fun generateTest(){
        val client = HttpClientBuilder.create().build()
        val httpGet = HttpGet(URI("http://localhost:$port/v3/api-docs"))
        val response = client.execute(httpGet)
        val body = EntityUtils.toString(response.entity)
        val openapi = jacksonObjectMapper().readValue(body, OpenApiDocs::class.java)
        val reformDocs = reformDoc(openapi)
        val objectMapper = jacksonObjectMapper()
        // 有序输出，方便提交到nezha库时查看diff
        objectMapper.configure(SerializationFeature.ORDER_MAP_ENTRIES_BY_KEYS, true)
        objectMapper.configure(SerializationFeature.INDENT_OUTPUT, true)
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL)
        val content = objectMapper.writeValueAsString(reformDocs)
        println(content)
        writeFile2Resources(content)
    }

    fun writeFile2Resources(content: String){
        val file = File("src/test/resources/nezha-docs/nezha-output-api-docs.json")
        file.writeText(content)
    }

    fun reformDoc(sourceDoc: OpenApiDocs): OpenApiDocs {
        val reformedPaths = sourceDoc.paths.map { pathDef ->
            val url = pathDef.key
            val methodDefs = pathDef.value as Map<*, *>

            // 重写 URL
            val reformedUrl = when (url) {
                // 特例，app-env-profile 这一接口上有 url rewrite
                "/api/v2/apps/{appName}/envs/{envName}/profile" ->
                    "/appstack/organizations/{organizationId}/apps/{appName}/envs/{envName}/variableGroup"
                else -> url.replace("/api/v2", "/appstack/organizations/{organizationId}")
            }

            // 重组方法定义，增加 org 参数
            val reformedMethodDefs = methodDefs.map { methodDef ->
                val method = methodDef.key as String
                val def = (methodDef.value as Map<*, *>).toMutableMap()
                // 添加 org 参数
                if (!def.containsKey("parameters")) {
                    def["parameters"] = mutableListOf<Any>()
                }
                (def["parameters"]as MutableList<Any>).add(nezhaGatewayOrgParameter)
                Pair(method, def)
            }.toMap()

            Pair(reformedUrl, reformedMethodDefs)
        }.toMap()

        return OpenApiDocs(
            openapi = sourceDoc.openapi,
            info = sourceDoc.info,
            tags = sourceDoc.tags,
            paths = reformedPaths,
            components = sourceDoc.components
        )
    }
}


@JsonIgnoreProperties(ignoreUnknown = true)
data class OpenApiDocs(
    val openapi: String,
    val info: OpenApiInfoDefinition,
    val tags: List<OpenApiTagDefinition>,
    var paths: Map<String, Any>,
    val components: Map<String, Any>
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class OpenApiTagDefinition(
    val name: String,
    val description: String
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class OpenApiInfoDefinition(
    val title: String,
    val description: String? = null,
    val version: String
)