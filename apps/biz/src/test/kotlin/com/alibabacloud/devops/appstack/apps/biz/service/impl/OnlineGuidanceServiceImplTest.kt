package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.model.State
import com.alibabacloud.devops.appstack.apps.biz.model.Step
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.OnlineGuidanceMapper
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.find
import com.alibabacloud.devops.appstack.apps.biz.service.EnvBizService
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.OnlineGuidanceService
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.OnlineGuideStepFactory
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.impl.AppPublishStepHandlerServiceImpl
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.impl.CodeRepoConfigStepHandlerServiceImpl
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.impl.EnvConfigStepHandlerServiceImpl
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.impl.WorkflowConfigStepHandlerServiceImpl
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import org.junit.jupiter.api.*
import org.mockito.ArgumentMatchers.anyString
import org.mockito.BDDMockito.given
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean

/**
 * @author: <EMAIL>
 * @date: 2022-08-25 22:49
 * @version: OnlineGuidanceServiceImplTest, v0.1
 **/
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class OnlineGuidanceServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var onlineGuidanceService: OnlineGuidanceService

    @MockBean
    lateinit var onlineGuideStepFactory: OnlineGuideStepFactory

    @MockBean
    lateinit var envBizService: EnvBizService

    @MockBean
    lateinit var onlineGuidanceMapper: OnlineGuidanceMapper

    @Autowired
    lateinit var appPublishStepHandlerServiceImpl: AppPublishStepHandlerServiceImpl

    @Autowired
    lateinit var envConfigStepHandlerServiceImpl: EnvConfigStepHandlerServiceImpl

    @Autowired
    lateinit var workflowConfigStepHandlerServiceImpl: WorkflowConfigStepHandlerServiceImpl

    @Autowired
    lateinit var codeRepoConfigStepHandlerServiceImpl: CodeRepoConfigStepHandlerServiceImpl

    companion object {
        val appName = "appName"
        val step = Step(
            "ENV_CONFIG",
            state = State.INIT
        )
        val env = Env(
            name = "dev",
            displayName = "开发环境",
            appName = "appName",
            deployGroupName = "ppp",
            resourcePoolName = "kkk",
        )
    }

    @Order(1)
    @Test
    fun codeRepoConfigStepHandlerTest() {
        var newStep = codeRepoConfigStepHandlerServiceImpl.handle("myapp", step)
        Assertions.assertEquals(State.FINISH, newStep.step.state)

        newStep = codeRepoConfigStepHandlerServiceImpl.handle("myappMock", step)
        Assertions.assertEquals(State.INIT, newStep.step.state)
    }

    @Order(1)
    @Test
    fun workflowConfigStepHandlerTest() {
        val newStep = workflowConfigStepHandlerServiceImpl.handle("myapp", step)
        Assertions.assertEquals(State.FINISH, newStep.step.state)
    }



    @Order(1)
    @Test
    fun appPublishStepHandlerTest() {
        given(envBizService.findAll(appName)).willReturn(listOf(env))
        val newStep = appPublishStepHandlerServiceImpl.handle(appName, step)
        Assertions.assertNotEquals(newStep.step.state, step.state)
    }

    @Order(1)
    @Test
    fun envConfigStepHandlerTest() {
        given(envBizService.findAll(appName)).willReturn(listOf(env))
        val newStep = envConfigStepHandlerServiceImpl.handle(appName, step)
        Assertions.assertEquals(true, newStep.step.state != step.state)
    }

    @Order(1)
    @Test
    fun generateTest() {
        given(onlineGuidanceMapper.find(appName)).willReturn(null)
        given(onlineGuideStepFactory.build(anyString())).willReturn(envConfigStepHandlerServiceImpl)
        given(envBizService.findAll(appName)).willReturn(listOf(env))
        val newStep = envConfigStepHandlerServiceImpl.handle(appName, step)
        val onlineGuidance = onlineGuidanceService.find(appName)
        assert(onlineGuidance.stepList.first { it.name == newStep.step.name }.state == newStep.step.state)
    }

    @Order(2)
    @Test
    fun deleteTest() {
        onlineGuidanceService.delete(appName)
        try {
            onlineGuidanceService.find(appName)
            Assertions.fail<String>()
        } catch (_: Exception) {

        }
    }

}