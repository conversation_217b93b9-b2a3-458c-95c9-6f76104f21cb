package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderBizService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderInputBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.request.FlowCreateChangeOrderRequest
import com.alibabacloud.devops.appstack.libs.model.request.ImageMapItem
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateChangeOrderRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.SearchChangeOrderRequest
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.BDDMockito
import org.mockito.BDDMockito.given
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean

/**
 * @author: <EMAIL>
 * @date: 2023-04-24 17:56
 * @version: ChangeOrderBizServiceTest, v0.1
 **/
class ChangeOrderBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var changeOrderBizService: ChangeOrderBizService

    @MockBean
    lateinit var changeOrderInputBizService: ChangeOrderInputBizService

    @Test
    fun searchPaginatedTest() {
        val page = changeOrderBizService.searchPaginated(SearchChangeOrderRequest(), 1,20)
        assert(page.records.isEmpty())
    }

    @Test
    fun test_not_null() {
        val result = changeOrderBizService.findPaginatedVersion("appName", emptyList(), emptyList(), 1, 10)
        Assertions.assertNotNull(result)
    }


    @Test
    fun shouldHaveUnknownErrorWhenCreatingChangeOrderAndInputNotFound() {
        // given
        val request = CreateChangeOrderRequest(changeOrderInputSn = "mockSn", taskStrategies = listOf())

        var error: Throwable? = null
        given(changeOrderInputBizService.find(any())).willReturn(null)
        try {
            changeOrderBizService.create(request)
        } catch (e: Throwable) {
            error = e
        }
        assert(error != null)
        assert(error is BizException)
        assert((error as BizException).errorEntry.code == ErrorCode.AS_CHANGE_ORDER_INPUT_NOT_EXISTED)
    }

}