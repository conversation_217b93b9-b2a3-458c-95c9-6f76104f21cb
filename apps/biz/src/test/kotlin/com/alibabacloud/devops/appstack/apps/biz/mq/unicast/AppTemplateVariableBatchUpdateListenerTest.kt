package com.alibabacloud.devops.appstack.apps.biz.mq.unicast

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.AppVariableGroupsBizService
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil.jacksonObjectMapper
import com.alibabacloud.devops.appstack.libs.model.`do`.app.App
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.Profile
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.Variable
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Branch
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.BranchInfo
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.RepoMeta
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.request.UpdateVariableGroupDryRunRequest
import com.alibabacloud.devops.appstack.libs.model.vo.VariableStorageVO
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.BroadcastSender
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.AppTemplateVariableGroupBatchUpdateBody
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.BroadcastTagEnum
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean

class AppTemplateVariableBatchUpdateListenerTest : BizServerApplicationTests()  {

    @Autowired
    lateinit var appTemplateVariableBatchUpdateListener: AppTemplateVariableBatchUpdateListener

    @MockBean
    lateinit var appVariableGroupsBizService: AppVariableGroupsBizService

    @MockBean
    lateinit var broadcastSender: BroadcastSender



    @Test
    fun handleTest(){
        val body = AppTemplateVariableGroupBatchUpdateBody(
            appTemplateName = "test",
            mode = UpdateVariableGroupDryRunRequest.Mode.OVERRIDE_KEY_VALUE,
            profile = Profile(name = "dev", displayName = "开发环境", vars = listOf(Variable(key = "k1", value = "k2", description = "desc"))),
            appNames = listOf("app1", "app2", "app3", "app4"),
            commitMsg = "test",
            transactionId = "123"
        )
        given(appVariableGroupsBizService.findAllProfiles(eq("app1"))).willReturn(null)
        given(appVariableGroupsBizService.findAllProfiles(eq("app4"))).willThrow(RuntimeException())
        given(appVariableGroupsBizService.findAllProfiles(eq("app2"))).willReturn(
            VariableStorageVO(
                app = App("app1"),
                profileMap = mapOf(
                    "dev" to Profile(
                        name = "group-1",
                        displayName = "",
                        vars = emptyList()
                    )
                ),
                revision = Revision(RepoMeta("", ""), "abc", "", ""),
                branchInfo = BranchInfo(
                    head = Revision(
                        repoMeta = RepoMeta("", ""),
                        sha = "sha",
                        message = "init",
                        author = "",
                    ),
                    repoMeta = RepoMeta("", ""),
                    name = Branch.TRUNK
                ),
                refEnvMap = null
            )
        )
        given(appVariableGroupsBizService.findAllProfiles(eq("app3"))).willReturn(VariableStorageVO(
            app = App("app1"),
            profileMap = emptyMap(),
            revision = Revision(RepoMeta("", ""), "abc", "", ""),
            branchInfo = BranchInfo(
                head = Revision(
                    repoMeta = RepoMeta("", ""),
                    sha = "sha",
                    message = "init",
                    author = "",
                ),
                repoMeta = RepoMeta("", ""),
                name = Branch.TRUNK
            ),
            refEnvMap = null
        ))
        given(appVariableGroupsBizService.updateDryrun(any(), any(), any())).willReturn(listOf(Variable(key = "k1", value = "k2", description = "desc")))
        given(appVariableGroupsBizService.createProfile(any(), any())).willReturn(VariableStorageVO(
            app = App("app1"),
            profileMap = mapOf(
                "dev" to Profile(
                    name = "group-1",
                    displayName = "",
                    vars = emptyList()
                )
            ),
            revision = Revision(RepoMeta("", ""), "abc", "", ""),
            branchInfo = BranchInfo(
                head = Revision(
                    repoMeta = RepoMeta("", ""),
                    sha = "sha",
                    message = "init",
                    author = "",
                ),
                repoMeta = RepoMeta("", ""),
                name = Branch.TRUNK
            ),
            refEnvMap = null
        ))
        given(appVariableGroupsBizService.updateProfile(any(), any())).willReturn(VariableStorageVO(
            app = App("app1"),
            profileMap = mapOf(
                "dev" to Profile(
                    name = "group-1",
                    displayName = "",
                    vars = emptyList()
                )
            ),
            revision = Revision(RepoMeta("", ""), "abc", "", ""),
            branchInfo = BranchInfo(
                head = Revision(
                    repoMeta = RepoMeta("", ""),
                    sha = "sha",
                    message = "init",
                    author = "",
                ),
                repoMeta = RepoMeta("", ""),
                name = Branch.TRUNK
            ),
            refEnvMap = null
        ))
        doNothing().`when`(broadcastSender).send(any(), any())
        appTemplateVariableBatchUpdateListener.handle(jacksonObjectMapper().writeValueAsBytes(body))
        verify(appVariableGroupsBizService, times(1)).createProfile(eq("app1"), any())
        verify(appVariableGroupsBizService, times(1)).createProfile(eq("app3"), any())
        verify(appVariableGroupsBizService, times(1)).updateProfile(eq("app2"), any())
        verify(broadcastSender, times(4)).send(eq(BroadcastTagEnum.APP_TEMPLATE_SYNC_PROGRESS), any())
    }

}