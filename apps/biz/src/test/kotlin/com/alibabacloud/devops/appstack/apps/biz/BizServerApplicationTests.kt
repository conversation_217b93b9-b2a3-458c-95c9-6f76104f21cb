package com.alibabacloud.devops.appstack.apps.biz

import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.common.util.DdlUtil
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.config.IamContext
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.readValue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.web.server.LocalServerPort

import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.io.File
import java.nio.file.Paths
import java.util.*

/**
 * @author: <EMAIL>
 * @date: 2021-10-20 20:34
 * @version: BizServerApplicationTests, v0.1
 **/
@ExtendWith(SpringExtension::class)
@SpringBootTest(classes = [BizServerApplication::class], webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
open class BizServerApplicationTests {

    @LocalServerPort
    var port = 0

    @Value("\${devops.iam.environment}")
    var environment: String = "aliyun"

    companion object {
        init {
            val os = System.getProperty("os.name")
            val isWindows = os.lowercase().startsWith("win")

            val dbType = "h2"
            val dbName = "appstack"
            val packageName = "com.alibabacloud.devops.appstack.apps.biz.model.po"
            val lockPackageName = "com.alibabacloud.devops.appstack.libs.lock.spring.boot.starter.dbv2.model"
            val schema = DdlUtil.generate(dbType, dbName, listOf(packageName, lockPackageName))

            val url = BizServerApplicationTests::class.java.getResource("/application-test.yml")
            val node: JsonNode = JacksonUtil.yamlObjectMapper().readValue(url)
            val schemaLocation = node.get("spring").get("sql").get("init").get("schema-locations").textValue()
            val classPath = Paths.get(BizServerApplicationTests::class.java.getResource("/").toURI()).toString()
            val schemaFilePath = if (!isWindows)
                schemaLocation.replace("^classpath:".toRegex(), "${classPath}/")
            else
                "$classPath\\${schemaLocation.replace("^classpath:".toRegex(), "").replace('/', '\\')}"
            val schemaFile = File(schemaFilePath)
            if (!schemaFile.exists()) {
                schemaFile.createNewFile()
            }
            File(schemaFilePath).writeText(schema)
            println(schemaFilePath)
            AuthThreadContext.setLocale("zh_CN")
        }
    }

    @BeforeEach
    fun contextLoad() {
        if (environment == "aliyun") {
            AuthThreadContext.setTenant("5fd1e3841acb1ae7cc187cd4")
            IamContext.setTenant("5fd1e3841acb1ae7cc187cd4")
            AuthThreadContext.setUserId("609cf9669db2b2a61be81b82")
            IamContext.setOperator("609cf9669db2b2a61be81b82")
        }
    }
}