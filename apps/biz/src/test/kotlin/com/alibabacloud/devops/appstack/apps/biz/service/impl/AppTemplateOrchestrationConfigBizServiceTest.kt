package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateOrchestrationConfigBizService
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * @author: <EMAIL>
 * @date: 2024-05-30 12:20
 * @version: AppTemplateOrchestrationConfigBizServiceTest, v0.1
 **/
class AppTemplateOrchestrationConfigBizServiceTest : BizServerApplicationTests() {

    @Autowired
    lateinit var appTemplateOrchestrationConfigBizService: AppTemplateOrchestrationConfigBizService

    @Test
    fun findLocatorList() {
        val locatorList =
            appTemplateOrchestrationConfigBizService.getLocatorList("app", "sn", "locator-all", emptyList())
        assert(locatorList.size == 1)
        assert(locatorList.get(0).locator == "*")
    }
}