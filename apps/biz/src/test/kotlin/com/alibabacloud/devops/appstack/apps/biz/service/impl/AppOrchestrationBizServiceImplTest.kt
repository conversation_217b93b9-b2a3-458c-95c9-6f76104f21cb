package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.Orchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppBuiltInOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.Profile
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.Variable
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * @author: <EMAIL>
 * @date: 2022-03-08 10:22
 * @version: OrchestrationBizServiceImplTest, v0.1
 **/
class AppOrchestrationBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var orchestrationBizServiceImpl: AppOrchestrationBizServiceImpl

    companion object {
        fun getAppOrchestration(): AppBuiltInOrchestration = jacksonObjectMapper().readValue(
            javaClass.getResource("/orchestration/app-orchestration.json").readText()
        )
    }

    @Test
    fun fileNameAndContentMap() {
        val appOrchestration = getAppOrchestration()
        val profile = Profile("test", "test", listOf(Variable("key", "val", "desc")))
        val fileNameAndContentMap = orchestrationBizServiceImpl.fileNameAndContentMap(
            "jiangqitest202203071052",
            appOrchestration.componentList,
            appOrchestration.placeholderList,
            profile,
            Orchestration.RenderTypeEnum.CHARTS,
            "sha",
            "username1"
        )
        assert(fileNameAndContentMap.keys.size == 4)
        assert(fileNameAndContentMap.keys.contains("Chart.yaml"))
        println(fileNameAndContentMap["Chart.yaml"])
        assert(fileNameAndContentMap.keys.contains("values.yaml"))
        println(fileNameAndContentMap["values.yaml"])
        assert(fileNameAndContentMap.keys.contains("templates/demo-deployment.yaml"))
        println(fileNameAndContentMap["templates/deployment.yaml"])
        assert(fileNameAndContentMap.keys.contains("templates/demo-service.yaml"))
        println(fileNameAndContentMap["templates/service.yaml"])
    }

    @Test
    fun deleteFailedTest() {
        try {
            orchestrationBizServiceImpl.delete("test-delete-app-orc-appName", "appRepoSn_notEndList")
            assert(false)
        } catch (e: BizException) {
            assert(e.errorEntry.code == ErrorCode.AS_APP_RELEASE_NOT_ALL_END)
        } catch (e: Exception) {
            assert(false)
        }
    }

    @Test
    fun deleteSuccessTest() {
        try {
            orchestrationBizServiceImpl.delete("test-app", "sn")
            assert(true)
        } catch (e: Exception) {
            assert(false)
        }
    }

    @Test
    fun getLocatorListTest() {
        val locatorList = orchestrationBizServiceImpl.getLocatorList("hpa-app-name", "k8s")
        assert(locatorList.size == 1)
        assert(locatorList.get(0).locator == "*")
        val locatorList1 = orchestrationBizServiceImpl.getLocatorList("hpa-app-name", "null")
        assert(locatorList1.isEmpty())
    }
}