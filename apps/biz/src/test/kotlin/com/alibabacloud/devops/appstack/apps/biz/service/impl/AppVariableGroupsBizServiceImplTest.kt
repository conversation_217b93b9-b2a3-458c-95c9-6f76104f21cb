package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.Variable
import com.alibabacloud.devops.appstack.libs.model.request.UpdateVariableGroupDryRunRequest
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class AppVariableGroupsBizServiceImplTest: BizServerApplicationTests() {

    @Autowired
    lateinit var appVariableGroupsBizService: AppVariableGroupsBizServiceImpl


    @Test
    fun test_updateDryrun_appendKey(){
        val appName = "appName"
        val profileName = "profileName"
        val request = UpdateVariableGroupDryRunRequest(
            mode = UpdateVariableGroupDryRunRequest.Mode.APPEND_KEY,
            variables = listOf(
                Variable(
                    key = "key",
                    value = "v1",
                    description = "desc"
                ),
                Variable(
                    key = "k2",
                    value = "v2",
                    description = "d2"
                )
            )
        )
        val result = appVariableGroupsBizService.updateDryrun(appName, profileName, request)
        Assertions.assertEquals(2, result.size)
        Assertions.assertTrue(result.contains(Variable(
            key = "key",
            value = "value",
            description = "desc"
        )))
        Assertions.assertTrue(result.contains(Variable(
            key = "k2",
            value = "v2",
            description = "d2"
        )))
    }

    @Test
    fun test_updateDryrun_overrideKeyValue(){
        val appName = "appName"
        val profileName = "profileName"
        val request = UpdateVariableGroupDryRunRequest(
            mode = UpdateVariableGroupDryRunRequest.Mode.OVERRIDE_KEY_VALUE,
            variables = listOf(
                Variable(
                    key = "key",
                    value = "v1",
                    description = "desc"
                ),
                Variable(
                    key = "k2",
                    value = "v2",
                    description = "d2"
                )
            )
        )
        val result = appVariableGroupsBizService.updateDryrun(appName, profileName, request)
        Assertions.assertEquals(2, result.size)
        Assertions.assertTrue(result.contains(Variable(
            key = "key",
            value = "v1",
            description = "desc"
        )))
        Assertions.assertTrue(result.contains(Variable(
            key = "k2",
            value = "v2",
            description = "d2"
        )))
    }

}