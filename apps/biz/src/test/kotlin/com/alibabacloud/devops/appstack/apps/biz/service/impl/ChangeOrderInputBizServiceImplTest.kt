package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateDeployInputRequest
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR>
 * @date 2023-04-22
 */
class ChangeOrderInputBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var changeOrderInputBizService: ChangeOrderInputBizServiceImpl

    @Test
    fun test_findAllForms() {
        val appName = "myapp"
        val envName = "myenv"
        val forms = changeOrderInputBizService.findAllForms(appName, listOf(envName), ChangeOrder.Type.Deploy)
        Assertions.assertEquals(1, forms.size)
        Assertions.assertNotNull(forms.first().appOrchestration)
    }

    @Test
    fun test_findAllForms_withoutKind() {
        val appName = "myapp-withoutkind"
        val envName = "myenv-withoutkind"
        try {
            changeOrderInputBizService.findAllForms(appName, listOf(envName), ChangeOrder.Type.Deploy)
        } catch (e: BizException) {
            Assertions.assertEquals(ErrorCode.AS_ORC_COMP_KIND_NOT_FOUND, e.errorEntry.code)
            println(e.message)
        } catch (e: Exception) {
            assert(false)
        }
    }

    @Test
    fun test_create(){
        val envName = "dev"
        val request = CreateDeployInputRequest(
            name = "a",
            appName = "myapp",
            version = "ver",
            orchestrationSha = "tag",
            envValues = mapOf(envName to mutableMapOf())
        )
        val forms = changeOrderInputBizService.create(request)
        Assertions.assertTrue(forms.appOrchestrations.containsKey(envName))
        Assertions.assertTrue(forms.profiles.containsKey(envName))
        Assertions.assertTrue(forms.deployGroups.containsKey(envName))
        Assertions.assertTrue(forms.taskForms.containsKey(envName))
        Assertions.assertTrue(forms.envs.containsKey(envName))

    }
}