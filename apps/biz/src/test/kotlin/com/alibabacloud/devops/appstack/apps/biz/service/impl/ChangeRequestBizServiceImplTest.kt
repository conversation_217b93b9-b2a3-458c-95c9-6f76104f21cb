package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestBizService
import com.alibabacloud.devops.appstack.apps.biz.service.OrgConfigItemBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.ChangeRequestMustBindWorkItemV1OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.request.cr.CreateChangeContext
import com.alibabacloud.devops.appstack.libs.model.request.cr.CreateChangeRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.MetadataSearch
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.kotlin.given
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR>
 * @date 2023-09-19
 */
class ChangeRequestBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var changeRequestBizService: ChangeRequestBizService

    @InjectMocks
    lateinit var service: ChangeRequestBizServiceImpl

    @Mock
    lateinit var orgConfigItemBizService: OrgConfigItemBizService

    @Test
    fun findNotFoundTest() {
        try {
            changeRequestBizService.find("notFoundApp", "sn")
            Assertions.fail<String>()
        } catch (e: BizException) {
            Assertions.assertEquals(ErrorCode.AS_CR_NOT_EXIST, e.errorEntry.code)
        }
    }

    @Test
    fun closeTest() {
        changeRequestBizService.close("appName", "sn")
    }

    @Test
    fun findBySnSTest() {
        val list = changeRequestBizService.findBySns("appName", "sn,")
        Assertions.assertEquals(1, list.size)
    }

    @Test
    fun createTest() {
        given(orgConfigItemBizService.find(OrgConfigItem.CHANGE_REQUEST_MUST_BIND_WORKITEM)).willReturn(
            ChangeRequestMustBindWorkItemV1OrgConfigItem(enable = true)
        )
        try {
            service.create(
                "test",
                CreateChangeRequest(
                    appName = "test",
                    title = "test",
                    owner = "abc",
                    createChangeContext = CreateChangeContext(
                        codeRepoSn = "sn",
                        branchName = "master",
                        createBranch = false,
                    )
                )
            )
        } catch (e: BizException) {
            Assertions.assertEquals(ErrorCode.AS_CR_MUST_BIND_WORKITEM, e.errorEntry.code)
        }
    }

    @Test
    fun listChangeRequestMetadataTest() {
        val listChangeRequestMetadata = changeRequestBizService.listChangeRequestMetadata(
            "appName", "4d7cd001bf974ca7958069ffe85880e0", MetadataSearch(
                perPage = 10,
                page = 1,
                orderBy = "id",
                sort = "DESC",
                releaseWorkflowSn = "ce000aeedcb14d339c5ecac19c7d27a1",
                releaseStageSn = "55ef3aedc8cd49d29389d30e907658ff"
            )
        )
        println(jacksonObjectMapper().writeValueAsString(listChangeRequestMetadata))
        assert(10 == listChangeRequestMetadata.records.size)
    }
}