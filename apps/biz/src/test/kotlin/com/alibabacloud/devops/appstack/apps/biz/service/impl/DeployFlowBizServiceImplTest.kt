package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.model.DeployFlow
import com.alibabacloud.devops.appstack.apps.biz.model.ext.ChangeOrderInputPatchValueInfoDTO
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderBizService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderInputBizService
import com.alibabacloud.devops.appstack.apps.biz.service.DeployFlowService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.facade.AppOrchestrationFacade
import com.alibabacloud.devops.appstack.libs.model.`do`.app.App
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.AppOrchestrationSource
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeAction
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderForm
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeableEnv
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.Job
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.TaskForm
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.TaskFormBehavior
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeDeploymentStrategy
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeTaskForm
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.Orchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppBuiltInOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Component
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Placeholder
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.toOrchestrationRevision
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.RepoMeta
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.request.FlowCreateChangeOrderRequest
import com.alibabacloud.devops.appstack.libs.model.request.FlowCreateCleanOrderRequest
import com.alibabacloud.devops.appstack.libs.model.request.ImageMapItem
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderVO
import com.alibabacloud.devops.appstack.libs.model.vo.JobVO
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.BDDMockito.anyString
import org.mockito.BDDMockito.given
import org.mockito.Mock
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.willReturn
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean
import java.util.*
import javax.annotation.PostConstruct

/**
 * <AUTHOR>
 * @date 2023-04-21
 */
class DeployFlowBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var deployFlowBizService: DeployFlowBizServiceImpl

    @MockBean
    lateinit var changeOrderBizService: ChangeOrderBizService

    @MockBean
    lateinit var changeOrderInputBizService: ChangeOrderInputBizService

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Mock
    lateinit var appOrchestrationFacade: AppOrchestrationFacade

    @MockBean
    lateinit var deployFlowService: DeployFlowService

    @PostConstruct
    fun init() {
        coreFacades.appOrchestrationFacade = appOrchestrationFacade
        deployFlowBizService.setCoreFacades(coreFacades)
    }

    @Test
    fun test_createChangeOrder_newOrder_singleImage() {
        val mockAppName = "mockapp"
        val mockEnvName = "mockenv"
        val imageValue = "myImageValue"

        val orchestration = AppBuiltInOrchestration(
            app = App(),
            name = "mock编排",
            creatorId = "st",
            modifierId = "st",
            gmtCreate = Date(),
            gmtModified = Date(),
            description = "desc",
            revision = Orchestration.OrchestrationRevision(
                "sha",
                "message",
                "sutian",
                Date()
            ),
            suitableResourceTypes = listOf(ResourceTypeEnum.KUBERNETES),
            format = Orchestration.FormatEnum.MANIFEST,
            sn = ""
        )

        mock_AppOrchestrationFacade_findAll()

        val request = FlowCreateChangeOrderRequest(
            appName = mockAppName,
            envName = mockEnvName,
            pipelineId = "123",
            imageUrl = null,
            imageList = listOf(ImageMapItem(name = "image", value = imageValue)),
            customParamList = null,
            sourceType = null,
            sourceSn = null
        )

        given(changeOrderInputBizService.create(any())).willReturn(
            ChangeOrderForm(
                changeOrderInputSn = "sn",
                version = "version",
                taskForms = mapOf(
                    mockAppName to listOf(
                        AdeTaskForm(
                            name = "name",
                            appName = mockAppName,
                            envName = mockEnvName,
                            engineType = ChangeableEnv.EngineType.Ade2,
                            items = listOf(
                                TaskForm.Item(
                                    name = "targetReplicas",
                                    type = TaskForm.Item.Type.Number,
                                    defaultValue = "4",
                                ),
                                TaskForm.Item(
                                    name = "deployType",
                                    type = TaskForm.Item.Type.Radio,
                                    defaultValue = "Batch",
                                ),
                                TaskForm.Item(
                                    name = "batches",
                                    type = TaskForm.Item.Type.Number,
                                    defaultValue = "4"
                                ),
                                TaskForm.Item(
                                    name = "batchSteps",
                                    type = TaskForm.Item.Type.ArrayNumber,
                                    defaultValue = "[1,1,1,1]"
                                ),
                                TaskForm.Item(
                                    name = "batchMode",
                                    type = TaskForm.Item.Type.Radio,
                                    defaultValue = AdeDeploymentStrategy.BatchMode.ConfirmFirstBatch,
                                ),
                                TaskForm.Item(
                                    name = "timeOutMS",
                                    type = TaskForm.Item.Type.Number,
                                    defaultValue = "1000000",
                                )
                            ),
                            artifacts = emptyMap(),
                            namespace = "namespace",
                            group = "first",
                            version = "second",
                            kind = "third",
                            behavior = TaskFormBehavior.Delete,
                        )
                    )
                ),
                appOrchestrations = emptyMap(),
                profiles = emptyMap(),
                deployGroups = emptyMap(),
                envs = emptyMap()
            )
        )
        given(changeOrderBizService.create(any())).willReturn(
            ChangeOrderVO(
                sn = "sn",
                name = "2022第2个部署单",
                appName = "st-008",
                type = ChangeOrder.Type.Deploy,
                version = "20220117161434924",
                tag = "dfasfaseqe",
                state = ChangeOrder.State.SUCCESS,
                description = "desc",
                jobs = listOf(
                    JobVO(
                        sn = "jobSn",
                        name = "jobName",
                        envName = "envName",
                        engineType = ChangeableEnv.EngineType.Ade1,
                        stages = emptyList(),
                        state = Job.State.SUCCESS,
                        type = Job.Type.Deploy,
                        priority = 1L,
                        controlMode = ChangeAction.ControlMode.Auto,
                        actions = listOf(),
                        changeOrderSn = "changeOrderSn"
                    )
                ),
                gmtCreate = Date(),
                creator = AuthUtil.getUserId(),
                actions = listOf()
            )
        )
        given(
            changeOrderInputBizService.getPatchValues(any(), any(), any(), eq(null))
        ).willReturn(
            ChangeOrderInputPatchValueInfoDTO(
                orchestrationSn = "",
                orchestrationSource = AppOrchestrationSource.SPECIFIC_SHA,
                orchestration = orchestration,
                profiles = null,
                deployGroup = null,
                patchValues = emptyMap()

            )
        )

        given(appOrchestrationFacade.findLatest(anyString(), anyString(), any())).willReturn(orchestration)

        deployFlowBizService.createChangeOrder(request)
        verify(changeOrderInputBizService, times(1)).create(any())
        verify(changeOrderBizService, times(1)).create(any())
        verify(changeOrderInputBizService, times(1)).getPatchValues(any(), any(), any(), eq(null))
    }

    @Test
    fun test_createChangeOrder_newOrder_singleImage_empty_form() {
        val mockAppName = "mockapp"
        val mockEnvName = "mockenv"
        val imageValue = "myImageValue"

        val orchestration = AppBuiltInOrchestration(
            app = App(),
            name = "mock编排",
            creatorId = "st",
            modifierId = "st",
            gmtCreate = Date(),
            gmtModified = Date(),
            description = "desc",
            revision = Orchestration.OrchestrationRevision(
                "sha",
                "message",
                "sutian",
                Date()
            ),
            suitableResourceTypes = listOf(ResourceTypeEnum.KUBERNETES),
            format = Orchestration.FormatEnum.MANIFEST,
            sn = ""
        )

        mock_AppOrchestrationFacade_findAll()

        val request = FlowCreateChangeOrderRequest(
            appName = mockAppName,
            envName = mockEnvName,
            pipelineId = "123",
            imageUrl = null,
            imageList = listOf(ImageMapItem(name = "image", value = imageValue)),
            customParamList = null,
            sourceType = null,
            sourceSn = null
        )

        given(changeOrderInputBizService.create(any())).willReturn(
            ChangeOrderForm(
                changeOrderInputSn = "sn",
                version = "version",
                taskForms = mapOf(
                    mockAppName to listOf(
                        AdeTaskForm(
                            name = "name",
                            appName = mockAppName,
                            envName = mockEnvName,
                            engineType = ChangeableEnv.EngineType.Ade2,
                            items = listOf(
                            ),
                            artifacts = emptyMap(),
                            namespace = "namespace",
                            group = "first",
                            version = "second",
                            kind = "third",
                            behavior = TaskFormBehavior.Delete,
                        )
                    )
                ),
                appOrchestrations = emptyMap(),
                profiles = emptyMap(),
                deployGroups = emptyMap(),
                envs = emptyMap()
            )
        )
        given(changeOrderBizService.create(any())).willReturn(
            ChangeOrderVO(
                sn = "sn",
                name = "2022第2个部署单",
                appName = "st-008",
                type = ChangeOrder.Type.Deploy,
                version = "20220117161434924",
                tag = "dfasfaseqe",
                state = ChangeOrder.State.SUCCESS,
                description = "desc",
                jobs = listOf(
                    JobVO(
                        sn = "jobSn",
                        name = "jobName",
                        envName = "envName",
                        engineType = ChangeableEnv.EngineType.Ade1,
                        stages = emptyList(),
                        state = Job.State.SUCCESS,
                        type = Job.Type.Deploy,
                        priority = 1L,
                        controlMode = ChangeAction.ControlMode.Auto,
                        actions = listOf(),
                        changeOrderSn = "changeOrderSn"
                    )
                ),
                gmtCreate = Date(),
                creator = AuthUtil.getUserId(),
                actions = listOf()
            )
        )
        given(
            changeOrderInputBizService.getPatchValues(any(), any(), any(), eq(null))
        ).willReturn(
            ChangeOrderInputPatchValueInfoDTO(
                orchestrationSn = "",
                orchestrationSource = AppOrchestrationSource.SPECIFIC_SHA,
                orchestration = orchestration,
                profiles = null,
                deployGroup = null,
                patchValues = emptyMap()

            )
        )

        given(appOrchestrationFacade.findLatest(anyString(), anyString(), any())).willReturn(orchestration)

        deployFlowBizService.createChangeOrder(request)
        verify(changeOrderInputBizService, times(1)).create(any())
        verify(changeOrderBizService, times(1)).create(any())
        verify(changeOrderInputBizService, times(1)).getPatchValues(any(), any(), any(), eq(null))
    }

    @Test
    fun test_createChangeOrder_newOrder_singleImage_empty_form_old_image() {
        val mockAppName = "mockapp"
        val mockEnvName = "mockenv"
        val imageValue = "myImageValue"

        val orchestration = AppBuiltInOrchestration(
            app = App(),
            name = "mock编排",
            creatorId = "st",
            modifierId = "st",
            gmtCreate = Date(),
            gmtModified = Date(),
            description = "desc",
            revision = Orchestration.OrchestrationRevision(
                "sha",
                "message",
                "sutian",
                Date()
            ),
            suitableResourceTypes = listOf(ResourceTypeEnum.KUBERNETES),
            format = Orchestration.FormatEnum.MANIFEST,
            sn = ""
        )

        mock_AppOrchestrationFacade_findAll()

        val request = FlowCreateChangeOrderRequest(
            appName = mockAppName,
            envName = mockEnvName,
            pipelineId = "123",
            imageUrl = imageValue,
            imageList = listOf(/*ImageMapItem(name = "image", value = imageValue)*/),
            customParamList = null,
            sourceType = null,
            sourceSn = null
        )

        given(changeOrderInputBizService.create(any())).willReturn(
            ChangeOrderForm(
                changeOrderInputSn = "sn",
                version = "version",
                taskForms = mapOf(
                    mockAppName to listOf(
                        AdeTaskForm(
                            name = "name",
                            appName = mockAppName,
                            envName = mockEnvName,
                            engineType = ChangeableEnv.EngineType.Ade2,
                            items = listOf(
                            ),
                            artifacts = emptyMap(),
                            namespace = "namespace",
                            group = "first",
                            version = "second",
                            kind = "third",
                            behavior = TaskFormBehavior.Delete,
                        )
                    )
                ),
                appOrchestrations = emptyMap(),
                profiles = emptyMap(),
                deployGroups = emptyMap(),
                envs = emptyMap()
            )
        )
        given(changeOrderBizService.create(any())).willReturn(
            ChangeOrderVO(
                sn = "sn",
                name = "2022第2个部署单",
                appName = "st-008",
                type = ChangeOrder.Type.Deploy,
                version = "20220117161434924",
                tag = "dfasfaseqe",
                state = ChangeOrder.State.SUCCESS,
                description = "desc",
                jobs = listOf(
                    JobVO(
                        sn = "jobSn",
                        name = "jobName",
                        envName = "envName",
                        engineType = ChangeableEnv.EngineType.Ade1,
                        stages = emptyList(),
                        state = Job.State.SUCCESS,
                        type = Job.Type.Deploy,
                        priority = 1L,
                        controlMode = ChangeAction.ControlMode.Auto,
                        actions = listOf(),
                        changeOrderSn = "changeOrderSn"
                    )
                ),
                gmtCreate = Date(),
                creator = AuthUtil.getUserId(),
                actions = listOf()
            )
        )
        given(
            changeOrderInputBizService.getPatchValues(any(), any(), any(), eq(null))
        ).willReturn(
            ChangeOrderInputPatchValueInfoDTO(
                orchestrationSn = "",
                orchestrationSource = AppOrchestrationSource.SPECIFIC_SHA,
                orchestration = orchestration,
                profiles = null,
                deployGroup = null,
                patchValues = emptyMap()

            )
        )

        given(appOrchestrationFacade.findLatest(anyString(), anyString(), any())).willReturn(orchestration)

        deployFlowBizService.createChangeOrder(request)
        verify(changeOrderInputBizService, times(1)).create(any())
        verify(changeOrderBizService, times(1)).create(any())
    }

    @Test
    fun test_createChangeOrder_newOrder_singleImage_empty_form_old_error() {
        val mockAppName = "mockapp"
        val mockEnvName = "mockenv-jobtype-scale"
        val imageValue = "myImageValue"


        val request = FlowCreateChangeOrderRequest(
            appName = mockAppName,
            envName = mockEnvName,
            pipelineId = "123",
            imageUrl = imageValue,
            imageList = listOf(/*ImageMapItem(name = "image", value = imageValue)*/),
            customParamList = null,
            sourceType = null,
            sourceSn = null
        )
        try {
            deployFlowBizService.createChangeOrder(request)
            assert(false)
        } catch (e: Exception) {
            assert(true)
        }
        val mockAppName1 = "mockapp"
        val mockEnvName1 = "mockenv-status-running"


        val request1 = FlowCreateChangeOrderRequest(
            appName = mockAppName1,
            envName = mockEnvName1,
            pipelineId = "123",
            imageUrl = imageValue,
            imageList = listOf(/*ImageMapItem(name = "image", value = imageValue)*/),
            customParamList = null,
            sourceType = null,
            sourceSn = null
        )
        try {
            deployFlowBizService.createChangeOrder(request1)
            assert(false)
        } catch (e: Exception) {
            assert(true)
        }
    }


    @Test
    fun test_createChangeOrder_newOrder_addedDeployment() {
        val mockAppName = "mockapp"
        val mockEnvName = "mockenv"
        val imageValue = "myImageValue"

        val orchestration = AppBuiltInOrchestration(
            app = App(),
            name = "mock编排",
            creatorId = "st",
            modifierId = "st",
            gmtCreate = Date(),
            gmtModified = Date(),
            description = "desc",
            revision = Orchestration.OrchestrationRevision(
                "sha",
                "message",
                "sutian",
                Date()
            ),
            suitableResourceTypes = listOf(ResourceTypeEnum.KUBERNETES),
            format = Orchestration.FormatEnum.MANIFEST,
            sn = ""
        )

        mock_AppOrchestrationFacade_findAll()

        val request = FlowCreateChangeOrderRequest(
            appName = mockAppName,
            envName = mockEnvName,
            pipelineId = "123",
            imageUrl = null,
            imageList = listOf(ImageMapItem(name = "image", value = imageValue)),
            customParamList = null,
            sourceType = null,
            sourceSn = null
        )

        given(changeOrderInputBizService.create(any())).willReturn(
            ChangeOrderForm(
                changeOrderInputSn = "sn",
                version = "version",
                taskForms = mapOf(
                    "dev" to listOf(
                        AdeTaskForm(
                            name = "name",
                            appName = "appName",
                            envName = "dev",
                            engineType = ChangeableEnv.EngineType.Ade2,
                            items = emptyList(),
                            behavior = TaskFormBehavior.Add,
                            artifacts = emptyMap(),
                            namespace = "default",
                            group = "apps",
                            version = "v1",
                            kind = "Deployment"
                        )
                    )
                ),
                appOrchestrations = emptyMap(),
                profiles = emptyMap(),
                deployGroups = emptyMap(),
                envs = emptyMap()
            )
        )
        given(changeOrderBizService.create(any())).willReturn(
            ChangeOrderVO(
                sn = "sn",
                name = "2022第2个部署单",
                appName = "st-008",
                type = ChangeOrder.Type.Deploy,
                version = "20220117161434924",
                tag = "dfasfaseqe",
                state = ChangeOrder.State.SUCCESS,
                description = "desc",
                jobs = listOf(
                    JobVO(
                        sn = "jobSn",
                        name = "jobName",
                        envName = "envName",
                        engineType = ChangeableEnv.EngineType.Ade1,
                        stages = emptyList(),
                        state = Job.State.SUCCESS,
                        type = Job.Type.Deploy,
                        priority = 1L,
                        controlMode = ChangeAction.ControlMode.Auto,
                        actions = listOf(),
                        changeOrderSn = "changeOrderSn"
                    )
                ),
                gmtCreate = Date(),
                creator = AuthUtil.getUserId(),
                actions = listOf()
            )
        )
        given(
            changeOrderInputBizService.getPatchValues(any(), any(), any(), eq(null))
        ).willReturn(
            ChangeOrderInputPatchValueInfoDTO(
                orchestrationSn = "",
                orchestrationSource = AppOrchestrationSource.SPECIFIC_SHA,
                orchestration = orchestration,
                profiles = null,
                deployGroup = null,
                patchValues = emptyMap()

            )
        )
        given(appOrchestrationFacade.findLatest(anyString(), anyString(), any())).willReturn(orchestration)
        try {
            deployFlowBizService.createChangeOrder(request)
            Assertions.fail()
        } catch (e: BizException) {
            Assertions.assertEquals(ErrorCode.AS_ORC_COMP_NEW_DEPLOYMENT, e.errorEntry.code)
        }
        verify(changeOrderInputBizService, times(1)).create(any())
        verify(changeOrderBizService, times(0)).create(any())
        verify(changeOrderInputBizService, times(1)).getPatchValues(any(), any(), any(), eq(null))
    }

    @Test
    fun test_createCleanOrder() {
        val mockAppName = "mockapp"
        val mockEnvName = "mockenv"

        val request = FlowCreateCleanOrderRequest(
            appName = mockAppName,
            envName = mockEnvName,
            pipelineId = "123",
            sourceType = null,
            sourceSn = null
        )

        given(changeOrderInputBizService.create(any())).willReturn(
            ChangeOrderForm(
                changeOrderInputSn = "sn",
                version = "version",
                taskForms = emptyMap(),
                appOrchestrations = emptyMap(),
                profiles = emptyMap(),
                deployGroups = emptyMap(),
                envs = emptyMap()
            )
        )
        given(changeOrderBizService.create(any())).willReturn(
            ChangeOrderVO(
                sn = "sn",
                name = "2022第2个部署单",
                appName = "st-008",
                type = ChangeOrder.Type.Destroy,
                version = "20220117161434924",
                tag = "dfasfaseqe",
                state = ChangeOrder.State.SUCCESS,
                description = "desc",
                jobs = listOf(
                    JobVO(
                        sn = "jobSn",
                        name = "jobName",
                        envName = "envName",
                        engineType = ChangeableEnv.EngineType.Ade1,
                        stages = emptyList(),
                        state = Job.State.SUCCESS,
                        type = Job.Type.Deploy,
                        priority = 1L,
                        controlMode = ChangeAction.ControlMode.Auto,
                        actions = listOf(),
                        changeOrderSn = "changeOrderSn"
                    )
                ),
                gmtCreate = Date(),
                creator = AuthUtil.getUserId(),
                actions = listOf()
            )
        )

        deployFlowBizService.createCleanOrder(request)
        verify(changeOrderInputBizService, times(1)).create(any())
        verify(changeOrderBizService, times(1)).create(any())
    }

    @Test
    fun test_fixSingleImage() {
        val imageValue = "myImageValue"
        val env = Env(
            name = "envName",
            displayName = "测试环境",
            appName = "appName",
            status = Env.Status.NEW,
            description = "description",
            creatorId = "creator",
            gmtCreate = null,
            lockBy = null,
            resourcePoolName = "resourcePool-name",
            deployGroupName = "deployGroup-name",
            orchestrationName = "orchestration-name",
            profiles = mutableListOf(
                VariableGroup(
                    name = "dev",
                    displayName = null,
                    type = VariableGroup.Type.APP
                )
            ),
        )
        val imageList = listOf(ImageMapItem("image", imageValue))

        mock_AppOrchestrationFacade_findAll()

        deployFlowBizService.fixSingleImage(env, imageList)
        Assertions.assertEquals(1, imageList.size)
        Assertions.assertEquals("backend", imageList.first().name)
        Assertions.assertEquals(imageValue, imageList.first().value)
    }

    private fun mock_AppOrchestrationFacade_findAll() {
        given(appOrchestrationFacade.findAll(anyString())).willReturn {
            val app = App().apply {
                name = "sutian008"
                description = "sutian008"
            }
            val placeholderList = listOf(
                Placeholder(
                    "namespace",
                    "Namespace",
                    Placeholder.Type.STRING,
                    "default",
                    false,
                    "{\\\"minimum\\\":10,\\\"maximum\\\":20}",
                    Placeholder.ValueSource.CONSTANT,
                    true
                ),
                Placeholder(
                    "image.backend",
                    "镜像占位符",
                    Placeholder.Type.STRING,
                    "NULL",
                    true,
                    null,
                    Placeholder.ValueSource.CONSTANT,
                    true
                )
            )
            val componentList = listOf(
                Component(
                    "ConfigMap0914",
                    "ConfigMap",
                    "这是创建于0914的ConfigMap",
                    "---\\napiVersion: v1\\nkind: ConfigMap\\nmetadata:\\n  name: env-config\\n  namespace: default\\ndata:\\n  envConfig.envName: envName\\n  envConfig.hsfVersion: charm",
                    1,
                    ResourceTypeEnum.KUBERNETES
                )
            )
            val groupNameMap = mapOf(1L to "顺序1")
            val repoMeta = RepoMeta("name", "type")
            val revision = Revision(
                repoMeta,
                "sha",
                "message",
                "sutian",
                emptyList(),
                123455777
            )

            listOf(
                AppBuiltInOrchestration(
                    format = Orchestration.FormatEnum.MANIFEST,
                    app = app,
                    sn = app.name,
                    name = "mock编排",
                    creatorId = "st",
                    modifierId = "st",
                    gmtCreate = Date(),
                    gmtModified = Date(),
                    description = "desc",
                    revision = revision.toOrchestrationRevision(),
                    suitableResourceTypes = listOf(ResourceTypeEnum.KUBERNETES)
                ).apply {
                    this.placeholderList = placeholderList
                    this.componentList = componentList
                    this.groupNameMap = groupNameMap
                }
            )
        }
    }

    @Test
    fun checkKindFailedError() {
        val mockAppName = "mockapp"
        val mockEnvName = "mockenv"
        val imageValue = "myImageValue"

        val orchestration = AppBuiltInOrchestration(
            app = App(),
            name = "mock编排",
            creatorId = "st",
            modifierId = "st",
            gmtCreate = Date(),
            gmtModified = Date(),
            description = "desc",
            revision = Orchestration.OrchestrationRevision(
                "sha",
                "message",
                "sutian",
                Date()
            ),
            suitableResourceTypes = listOf(ResourceTypeEnum.KUBERNETES),
            format = Orchestration.FormatEnum.MANIFEST,
            sn = ""
        )

        mock_AppOrchestrationFacade_findAll()

        val request = FlowCreateChangeOrderRequest(
            appName = mockAppName,
            envName = mockEnvName,
            pipelineId = "123",
            imageUrl = null,
            imageList = listOf(ImageMapItem(name = "image", value = imageValue)),
            customParamList = null,
            sourceType = null,
            sourceSn = null
        )

        given(changeOrderInputBizService.create(any())).willReturn(
            ChangeOrderForm(
                changeOrderInputSn = "sn",
                version = "version",
                taskForms = mapOf(
                    "aaa" to listOf(
                        AdeTaskForm(
                            "taskName",
                            "taskDesc",
                            "taskType",
                            ChangeableEnv.EngineType.Ade2,
                            emptyList(),
                            TaskFormBehavior.Update,
                            emptyMap(),
                            "ns",
                            "group",
                            "version",
                            ""
                        )
                    )
                ),
                appOrchestrations = emptyMap(),
                profiles = emptyMap(),
                deployGroups = emptyMap(),
                envs = emptyMap()
            )
        )
        given(changeOrderBizService.create(any())).willReturn(
            ChangeOrderVO(
                sn = "sn",
                name = "2022第2个部署单",
                appName = "st-008",
                type = ChangeOrder.Type.Deploy,
                version = "20220117161434924",
                tag = "dfasfaseqe",
                state = ChangeOrder.State.SUCCESS,
                description = "desc",
                jobs = listOf(
                    JobVO(
                        sn = "jobSn",
                        name = "jobName",
                        envName = "envName",
                        engineType = ChangeableEnv.EngineType.Ade1,
                        stages = emptyList(),
                        state = Job.State.SUCCESS,
                        type = Job.Type.Deploy,
                        priority = 1L,
                        controlMode = ChangeAction.ControlMode.Auto,
                        actions = listOf(),
                        changeOrderSn = "changeOrderSn"
                    )
                ),
                gmtCreate = Date(),
                creator = AuthUtil.getUserId(),
                actions = listOf()
            )
        )
        given(
            changeOrderInputBizService.getPatchValues(any(), any(), any(), eq(null))
        ).willReturn(
            ChangeOrderInputPatchValueInfoDTO(
                orchestrationSn = "",
                orchestrationSource = AppOrchestrationSource.SPECIFIC_SHA,
                orchestration = orchestration,
                profiles = null,
                deployGroup = null,
                patchValues = emptyMap()

            )
        )

        given(appOrchestrationFacade.findLatest(anyString(), anyString(), any())).willReturn(orchestration)

        try {
            deployFlowBizService.createChangeOrder(request)
        } catch (e: BizException) {
            Assertions.assertEquals(ErrorCode.AS_ORC_COMP_KIND_NOT_FOUND, e.errorEntry.code)
            println(e.message)
        } catch (e: Exception) {
            assert(false)
        }
    }

    @Test
    fun test_createChangeOrder_newOrder_last_deploy_not_success_error() {
        val mockAppName = "mockapp"
        val mockEnvName = "status-failed"
        val imageValue = "myImageValue"


        val request = FlowCreateChangeOrderRequest(
            appName = mockAppName,
            envName = mockEnvName,
            pipelineId = "123",
            imageUrl = null,
            imageList = listOf(ImageMapItem(name = "image", value = imageValue)),
            customParamList = null,
            sourceType = null,
            sourceSn = null,
            ignoreLatestFailed = false
        )
        try {
            deployFlowBizService.createChangeOrder(request)
            assert(false)
        } catch (e: BizException) {
            println(e)
            assert(e.errorEntry.code == ErrorCode.AS_CHANGE_ORDER_JOB_NOT_SUCCESS)
        } catch (e: Exception) {
            assert(false)
        }
    }

    @Test
    fun `find change order after create by flow`() {
        given(deployFlowService.find(DeployFlow.ObjectType.FLOW_APP_STACK, "123", "appName", "envName")).willReturn(
            DeployFlow(
                appName = "appName",
                envName = "envName",
                objectType = DeployFlow.ObjectType.FLOW_APP_STACK,
                objectId = "objectId",
                changeOrderSn = "changeOrderSn",
            )
        )
        val findChangeOrder = deployFlowBizService.findChangeOrder("123", false, "appName", "envName")
        assert(findChangeOrder.changeOrder!!.id == "changeOrderSn")
    }
}