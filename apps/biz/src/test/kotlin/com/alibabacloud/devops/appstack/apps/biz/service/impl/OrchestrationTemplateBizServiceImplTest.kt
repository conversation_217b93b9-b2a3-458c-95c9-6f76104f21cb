package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.facade.OrchestrationTemplateFacade
import com.alibabacloud.devops.appstack.libs.model.api.core.OrchestrationTemplateApi
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateCheckRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.fasterxml.jackson.module.kotlin.readValue
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import javax.annotation.PostConstruct

/**
 * <AUTHOR> <EMAIL>
 * @version : OrchestrationTemplateBizServiceImplTest, v0.1
 * @date : 2023-07-26 14:26
 */
class OrchestrationTemplateBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var orchestrationTemplateBizService: OrchestrationTemplateBizServiceImpl

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Mock
    lateinit var orchestrationTemplateApi: OrchestrationTemplateApi

    @PostConstruct
    fun postConstruct() {
        coreFacades.orchestrationTemplateFacade = OrchestrationTemplateFacade(orchestrationTemplateApi)
        orchestrationTemplateBizService.setCoreFacades(coreFacades)
    }


    @Test
    fun checkTest() {
        val objectMapper = JacksonUtil.jacksonObjectMapper()
        val request: OrchestrationTemplateCheckRequest = objectMapper.readValue(
            javaClass.getResource("/orchestration/orchestration-template-check.json")!!.readText()
        )

        whenever(
            orchestrationTemplateApi.check(any())
        ).thenReturn(
            Response.success(
                objectMapper.readValue(
                    javaClass.getResource("/orchestration/orchestration-template-check-output.json")!!.readText()
                )
            )
        )

        val invalid = orchestrationTemplateBizService.check(request)
        Assertions.assertTrue(invalid.isEmpty())
    }

    @Test
    fun checkEmptyTest() {
        val request = OrchestrationTemplateCheckRequest()
        val invalid = orchestrationTemplateBizService.check(request)
        Assertions.assertTrue(invalid.isEmpty())
    }
}