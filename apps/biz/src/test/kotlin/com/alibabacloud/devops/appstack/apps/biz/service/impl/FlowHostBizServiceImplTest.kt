package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.FlowHostBizService
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.FlowPurpose
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.FlowScopeType
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.HostGroupExperienceDTO
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.OsType
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.springframework.beans.factory.annotation.Autowired

/**
 * @author: <EMAIL>
 * @date: 2022-07-08 16:01
 * @version: FlowHostGroupServiceImplTest, v0.1
 **/
class FlowHostBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var flowHostBizService: FlowHostBizService


    @Disabled
    @Test
    fun findEcsTest() {
        val list =
            flowHostBizService.findEcs(310790L, "cn-hangzhou", FlowScopeType.appStack, false, null)
        assert(list.isNotEmpty())
    }

    @Disabled
    @Test
    fun findSelfHostTest() {
        val list = flowHostBizService.findSelf(FlowPurpose.DEPLOY_GROUP, FlowScopeType.appStack, null)
        assert(list.isNotEmpty())
    }

    @Disabled
    @Test
    fun fetchInstallCommandTests() {
        val installCommandUrl =
            flowHostBizService.fetchInstallCommand(FlowPurpose.DEPLOY_GROUP, FlowScopeType.appStack, OsType.linux)
        assert(installCommandUrl.isNotEmpty())
    }

    @Disabled
    @Test
    fun findAllByGroupIdTest() {
        AuthThreadContext.setTenant("620321f073741507cb7d0cfd")
        AuthThreadContext.setUserId("60d9be9e87be58ac63f244e0")
        val list = flowHostBizService.findAllByGroupId(25997L)
        //assert(list.isNotEmpty())
    }

    @Disabled
    @Test
    fun fetchMachineDeployLogTest() {
        AuthThreadContext.setTenant("62a827f46ac5cefd8f328616")
        AuthThreadContext.setUserId("609cbeb48e4139b4d7f5522e")
        assertDoesNotThrow { flowHostBizService.fetchMachineDeployLog(8977257L, "i-bp13jupqc1lx3sacspkk") }
    }


    @Disabled
    @Test
    fun createExperienceTest() {
        val hostGroupExperienceDTO = HostGroupExperienceDTO(
            aliyunRegion = "cn-shanghai",
            machineGroupName = "dadas",
            accessKeyId = "LTAI5t7tzWpytRi6R841Rf52",
            accessSecret = "******************************",
            instanceId = "i-uf6fy0y9i7j0p02x5ocx",
            scope = FlowScopeType.appStack
        )

        assertDoesNotThrow{flowHostBizService.createExperience(hostGroupExperienceDTO)}
        assertDoesNotThrow {flowHostBizService.fetchCreateResult("i-uf6fy0y9i7j0p02x5ocx", FlowScopeType.appStack)}
    }

}