package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.ReleaseWorkflowTemplateBizService
import com.alibabacloud.devops.appstack.libs.common.util.UuidUtils
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.Pipeline
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.toInstance
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR>
 * @date 2023-11-21
 */
class ReleaseAppTemplateWorkflowBizServiceTest : BizServerApplicationTests() {

    @Autowired
    lateinit var releaseWorkflowTemplateBizService: ReleaseWorkflowTemplateBizService

    @Test
    fun findTest() {
        val appName = "myapp1"
        val workflowName = "workflowName2"
        val template = releaseWorkflowTemplateBizService.find("sn")
        val releaseWorkflow = ReleaseWorkflow(
            appName = appName,
            name = workflowName,
            sn = UuidUtils.getUuid()
        )
        val instance = template.toInstance(releaseWorkflow)
        instance.releaseStageTemplateInstances.forEach {
            val stageName = it.releaseStage.name
            it.updateReleaseStagePipelineRequest?.let {
                if (it.type == Pipeline.Type.FlowV1 || it.type == Pipeline.Type.FlowV2 || it.type == Pipeline.Type.FlowAny) {
                    val dataJsonNode = jacksonObjectMapper().readTree(it.data) as JsonNode
                    Assertions.assertEquals(
                        "$appName-$workflowName-$stageName",
                        dataJsonNode.get("pipeline").get("name").asText()
                    )

                    val yaml = dataJsonNode.get("pipeline").get("pipelineConfigVo").get("flow").asText()
                    if(yaml.contains("appstack-flow-deploy")){
                        Assertions.assertFalse(yaml.contains("\${APPSTACK_APP_NAME}"))
                        Assertions.assertTrue(yaml.contains(appName))
                    }
                }
            }
        }
    }
}