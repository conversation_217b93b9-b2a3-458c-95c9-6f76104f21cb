package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.EnvLogChangeOrderAdapterFactory
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeableEnv
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * @author: <EMAIL>
 * @date: 2024-05-30 15:05
 * @version: EnvLogChangeOrderAdapterFactoryTest, v0.1
 **/
class EnvLogChangeOrderAdapterFactoryTest : BizServerApplicationTests() {

    @Autowired
    lateinit var envLogChangeOrderAdapterFactory: EnvLogChangeOrderAdapterFactory

    @Test
    fun shouldHaveUnknownErrorWhenEngineTypeMismatched() {
        // when
        var error: Throwable? = null
        try {
            envLogChangeOrderAdapterFactory.build(engineType = ChangeableEnv.EngineType.Ade1, resourceType = ResourceTypeEnum.HOST)
        } catch (e: Throwable) {
            error = e
        }

        // then
        assert(error != null)
        assert(error is BizException)
        assert((error as BizException).errorEntry.code == ErrorCode.AS_UNKNOWN)
    }

}