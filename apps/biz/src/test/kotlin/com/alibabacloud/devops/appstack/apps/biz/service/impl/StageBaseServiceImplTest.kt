package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.Pipeline
import com.alibabacloud.devops.appstack.libs.model.request.PaginationQuery
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.UpdateReleaseStagePipelineRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.UpdateReleaseStageRequest
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class StageBaseServiceImplTest: BizServerApplicationTests() {

    @Autowired
    lateinit var stageBaseService: StageBaseServiceImpl

    @Test
    fun testFindReleaseStageCrMetadata(){
        val result = stageBaseService.findReleaseStageCrMetadata("myapp", "sn", "sn", 1L)
        Assertions.assertNotNull(result)
    }

    @Test
    fun testListReleaseStageInstance(){
        val result = stageBaseService.listReleaseStageInstance("myapp", "sn", "sn", PaginationQuery(sort = "desc"))
        Assertions.assertNotNull(result)
    }

    @Test
    fun shouldMuteStageUpdate() {
        // just mute
        stageBaseService.update("appName", "sn", "sn", UpdateReleaseStageRequest("new-name"))
    }

    @Test
    fun shouldMuteStagePipelineUpdate() {
        // just mute
        stageBaseService.updatePipeline(
            "appName",
            "sn",
            "sn",
            UpdateReleaseStagePipelineRequest(
                "",
                Pipeline.Type.FlowV2,
                ""
            )
        )
    }

    @Test
    fun findBrief() {
        val findBrief = stageBaseService.findBrief("appName", "releaseWorkflowSn", "55ef3aedc8cd49d29389d30e907658ff")
        assert(findBrief.processEngineType == Pipeline.Type.FlowAny)
    }
}