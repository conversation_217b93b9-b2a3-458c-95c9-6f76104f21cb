package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibaba.aone.framework.tbs.app.sdk.group.GroupService
import com.alibaba.aone.framework.tbs.app.sdk.model.GroupMember
import com.alibaba.aone.framework.tbs.app.sdk.model.Member
import com.alibaba.aone.framework.tbs.app.sdk.team.TeamService
import com.alibaba.aone.framework.tbs.sdk.model.ApiResult
import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers
import org.mockito.Mockito
import org.mockito.Mockito.`when`
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ActiveProfiles


/**
 * <AUTHOR> <EMAIL>
 * @version : IamServiceImplTest, v0.1
 * @date : 2021-11-29 19:58
 */
@ActiveProfiles("vpc")
internal class IamVpcServiceTest : BizServerApplicationTests() {

    @Autowired
    lateinit var iamService: IamService

    @Test
    fun listUserIdsByTeam() {
        val ret1 = ApiResult<List<Member>>()
        val member1 = Member()
        member1.userId = "id1"
        ret1.result = listOf(member1)
        ret1.nextPageToken = "nextPageToken1"

        val ret2 = ApiResult<List<Member>>()
        ret2.result = listOf()
        ret2.nextPageToken = ""
        Mockito.mockStatic(TeamService::class.java).use {
            `when`(
                TeamService.getTeamMembers(
                    ArgumentMatchers.anyString(),
                    ArgumentMatchers.anyString(),
                    ArgumentMatchers.anyString(),
                    ArgumentMatchers.anyInt(),
                    ArgumentMatchers.anyBoolean()
                )
            ).thenReturn(ret1).thenReturn(ret2).thenThrow(RuntimeException())
            val listUserIdsByTeam = iamService.listUserIdsByTeam("orgId", "teamId")
            // println(jacksonObjectMapper().writeValueAsString(listUserIdsByTeam))
            assert(listUserIdsByTeam.size == 1)
        }
    }

    @Test
    fun listUserIdsByGroup() {
        val ret1 = ApiResult<List<GroupMember>>()
        val member1 = GroupMember()
        member1.userId = "id1"
        ret1.result = listOf(member1)
        ret1.nextPageToken = "nextPageToken1"

        val ret2 = ApiResult<List<GroupMember>>()
        ret2.result = listOf()
        ret2.nextPageToken = ""
        Mockito.mockStatic(GroupService::class.java).use {
            `when`(
                GroupService.listGroupMembers(
                    "orgId", "groupId",
                    null,
                    "",
                    100,
                )
            ).thenReturn(ret1)
            `when`(
                GroupService.listGroupMembers(
                    "orgId", "groupId",
                    null,
                    "nextPageToken1",
                    100,
                )
            ).thenReturn(ret2)
            val listUserIdsByGroup = iamService.listUserIdsByGroup("orgId", "groupId")
            // println(jacksonObjectMapper().writeValueAsString(listUserIdsByTeam))
            assert(listUserIdsByGroup.size == 1)
        }
    }
}