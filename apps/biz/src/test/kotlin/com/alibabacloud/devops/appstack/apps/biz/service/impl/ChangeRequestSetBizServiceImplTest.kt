package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestSetBizService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.model.iam.CrSetRole
import com.alibabacloud.devops.appstack.libs.model.request.cr.TransferChangeSetOwnerRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.UpdateChangeSetParticipatorsRequest
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.model.Resource
import org.junit.jupiter.api.Test
import org.mockito.kotlin.given
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean

/**
 * @author: <EMAIL>
 * @date: 2024-07-01 19:18
 * @version: ChangeRequestSetBizServiceImplTest, v0.1
 **/
class ChangeRequestSetBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var changeRequestSetBizService: ChangeRequestSetBizService

    @MockBean
    lateinit var iamService: IamService

    @Test
    fun `transfer owner`() {
        val crSetSn = "change-request-set-sn-1"
        val newOwner = "new-owner"
        val resource = Resource()
        resource.rolePlayerList = emptyList()

        val adminMap: Map<String, List<String>> = mapOf(crSetSn to listOf("old-admin-1"))
        given(iamService.findRolePlayers(ProtocolType.AppstackCrSet, listOf(crSetSn), CrSetRole.admin.name)).willReturn(
            adminMap
        )

        val userMap: Map<String, List<String>> = mapOf(crSetSn to listOf(newOwner, "old-user-2"))
        given(iamService.findRolePlayers(ProtocolType.AppstackCrSet, listOf(crSetSn), CrSetRole.user.name)).willReturn(
            userMap
        )

        changeRequestSetBizService.transferOwner(crSetSn, TransferChangeSetOwnerRequest(owner = newOwner))
    }

    @Test
    fun `update participators`() {
        val crSetSn = "change-request-set-sn-2"

        val oldUser1 = "old-user-1"
        val oldUser2 = "old-user-2"
        val newUser = "new-user"

        val resource = Resource()
        resource.rolePlayerList = emptyList()

        val adminMap: Map<String, List<String>> = mapOf(crSetSn to listOf("old-admin-1"))
        given(iamService.findRolePlayers(ProtocolType.AppstackCrSet, listOf(crSetSn), CrSetRole.admin.name)).willReturn(
            adminMap
        )

        val userMap: Map<String, List<String>> = mapOf(crSetSn to listOf(oldUser1, oldUser2))
        given(iamService.findRolePlayers(ProtocolType.AppstackCrSet, listOf(crSetSn), CrSetRole.user.name)).willReturn(
            userMap
        )

        changeRequestSetBizService.updateParticipators(
            crSetSn,
            UpdateChangeSetParticipatorsRequest(participators = listOf(oldUser2, newUser))
        )
    }
}