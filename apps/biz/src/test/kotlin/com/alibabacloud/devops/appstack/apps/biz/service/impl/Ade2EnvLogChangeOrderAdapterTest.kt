package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.adapter.ade2.Ade2EnvLogChangeOrderAdapter
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeableEnv
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.Job
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.Task
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.TaskContext
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeDeploymentStrategyV1
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeTask
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeTaskContext
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * @author: <EMAIL>
 * @date: 2024-05-30 18:01
 * @version: Ade2EnvLogChangeOrderAdapterTest, v0.1
 **/
class Ade2EnvLogChangeOrderAdapterTest : BizServerApplicationTests() {

    @Autowired
    lateinit var ade2EnvLogChangeOrderAdapter: Ade2EnvLogChangeOrderAdapter

    @Test
    fun shouldHaveEmptyList() {
        // given
        val task = AdeTask(
            engineType = ChangeableEnv.EngineType.Ade2,
            resourceType = ResourceTypeEnum.KUBERNETES,
            name = "t1",
            sn = "123",
            strategy = AdeDeploymentStrategyV1(
                targetReplicas = 3,
                fromReplicas = 1,
                batches = 2,
                batchSteps = listOf(1, 1)
            ),
            state = Task.State.FAILED,
            context = AdeTaskContext(
                appName = "appName",
                envName = "TEST-STRATEGY",
                name = "t1",
                version = "v1",
                jobType = Job.Type.Scale,
                kind = TaskContext.Kind.DeleteEnv.name,
                group = "api",
                deployGroupName = "",
                currentPassIndex = 1
            ),
        )

        // when
        val logs = ade2EnvLogChangeOrderAdapter.buildScaleLog(task)

        // then
        assert(logs.isEmpty())
    }

}