package com.alibabacloud.devops.appstack.apps.biz.api

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.web.socket.CloseStatus
import org.springframework.web.socket.TextMessage
import org.springframework.web.socket.WebSocketHttpHeaders
import org.springframework.web.socket.WebSocketSession
import org.springframework.web.socket.client.standard.StandardWebSocketClient
import org.springframework.web.socket.handler.TextWebSocketHandler
import org.springframework.web.socket.sockjs.client.SockJsClient
import org.springframework.web.socket.sockjs.client.Transport
import org.springframework.web.socket.sockjs.client.WebSocketTransport
import java.net.URI


/**
 * <AUTHOR>
 * @date 2022-01-29
 */
@Disabled
class PodWebSocketTest{

    companion object {
        val content = "Socket-Content-for Test"
    }

    @Test
    fun testPodWebSocket() {

        val client = SockJsClient(createTransportClient())
        val webSocketHttpHeaders = WebSocketHttpHeaders()
        webSocketHttpHeaders.add(
            "Cookie",
            "cna=+/srHQ70HDQCAQAAAAA4zmU6; t=11d6a79f2560d031072f51371d1e8927; aliyun_lang=zh; aliyun_choice=CN; ORG_ID=60d54f3daccf2bbd6659f3ad; aliyun_country=CN; aliyun_site=CN; currentRegionId=cn-beijing; activeRegionId=cn-beijing; AONE_SESSION=8d14f2dd-93c2-4002-b83a-f4b224137b43; login_aliyunid_pk=; LOGIN_ALIYUN_PK_FOR_TB=262579140573491041; TEAMBITION_SESSIONID=eyJ1aWQiOiI2MWM5MmEzN2FlODA1ZGJhY2M1YjlmNzgiLCJhdXRoVXBkYXRlZCI6MTY5MTM3Nzg5ODkyNSwidXNlciI6eyJfaWQiOiI2MWM5MmEzN2FlODA1ZGJhY2M1YjlmNzgiLCJuYW1lIjoi5byX5q2icmRjLXJkYyIsImVtYWlsIjoiMTMxODUwOTIyNzFAMTYzLmNvbSIsImF2YXRhclVybCI6Imh0dHBzOi8vdGNzLWRldm9wcy5hbGl5dW5jcy5jb20vdGh1bWJuYWlsLzExMmthZGU3ZmM3YjJiZGYyZmNkYjhiMTcyYTI3ZDJmMGQyMi93LzIwMC9oLzIwMCIsInJlZ2lvbiI6IiIsImxhbmciOiIiLCJpc1JvYm90IjpmYWxzZSwib3BlbklkIjoiIiwicGhvbmVGb3JMb2dpbiI6IiIsImNyZWF0ZWQiOiIyMDIxLTEyLTI3VDAyOjUxOjM1LjU0WiJ9LCJsb2dpbkZyb20iOiIifQ==; TEAMBITION_SESSIONID.sig=j7lepGZFaAh72p_5y118weA-G14; ak_user_locale=zh_CN; teambition_lang=zh_CN; tfstk=dX8WqhtMLpL2ZK_fBuG2cZqYAC_IQLgNJW1psBUz9aQRd9BXKTSFUalQRt9C8QrPaHtXpKjPeY_-dSddnU7FaT5LRB6c8QWPL7xDQCUyz0bURabhJAkZ7VlkZwbLOLgZ745muBDZQVuwZQbhJA8FSiCGnoYjNZ6_OJPZv_XOVOdSjyUBlPS5BQBbJsYfQcaNCue_ASsgRs6ZGjZ3xyFI7E5..; l=fBIZ-galTw8mEQabBOfaFurza77OSIRYYuPzaNbMi9fPOkCB5eHFW19Yc5T6C3GVF67vR3S7jpCMBeYBcQAonxv94NZiTXkmndLHR35..; isg=BJSUerDujJd5Kh_8wAO9tpCDZdQG7bjXIt_3cy51-Z-iGTJjXfRIZS3YHRGB4fAv; login_aliyunid_csrf=f6c30ee198e944b4a271d6a672d642a5; login_aliyunid=\"kaihang.xkh @ 1005298046719350\"; login_aliyunid_ticket=CJmU0lC5eU7n*DFNmHqPpLZjkCdG6kWOjPhFovE6rhAfq1S1E2ml6JYlY4q9CyLstMknfiSc2GhOwNcWzj5bYLpKzKZ49O80KpzxYXWJ0WPzFXDzr7rhZ_Dua5Qyv2KMv85szYAdhP4\$; login_aliyunid_sc=74u48x24xL7xCj1SQ9*cYL0T_GM6j755RszRjewzItuzfC7emzt*NAJSsZPK1qzw56CirX_*9VBpfkTFdJTd577kE2FYUtVM6cD3Hfl5dBjrmJK*H1vT5ERPp2356A*R; version=ga"
        )
        val uri =
            URI("wss://devops.aliyun.com/appstack/gateway/resource-manager/ws/execPod?appName=pod-app&envName=dev&namespace=default&podName=pod-app-dev-7f8567bd7d-zrc7x&containerName=main&token=MYtUMXoqTNwN2yylq3wMYyS1y%2Bjts%2FcqBpNRPAcKLcQcVCBMOhxGJnE9MPyvss6qUfYUM6PjcN%2FEZmJlEP%2BYCrlQ%2BmigjQC2QREQGuk5Aid4vtLaSu5ehDQ0bIsZAE0nCuObdjDshF4gYElN%2FzFMbGVATTvTQCGRIASCqkDZ8jg%3D")

        val sessions = mutableListOf<WebSocketSession>()

        for (i in 1..3) {
            val session = client.doHandshake(TestWebSocketHandler(), webSocketHttpHeaders, uri).get()
            println("IsOpen: "+ session.isOpen)
            session.sendMessage(TextMessage("Text:top\n"))
            sessions.add(session)
        }

        val thread = Thread(Daemon(sessions[1]))
        thread.start()
        thread.join()
    }

    @Test
    fun testAppTemplateSync(){
        val client = SockJsClient(createTransportClient())
        val webSocketHttpHeaders = WebSocketHttpHeaders()
        webSocketHttpHeaders.add(
            "Cookie",
            "t=11d6a79f2560d031072f51371d1e8927; ORG_ID=60d54f3daccf2bbd6659f3ad; aliyun_site=CN; l=fBIZ-galTw8mEvx0BO5Z-urza77t2IOfhOVzaNbMiIEGa6Q1oFNrYNCOpyywZdtjgTfxcFKzdfRCFdhv7ka3WAkDBeYB-V_Rw8Jw-bpU-L5..; _abfpc=3a07c3786dc939ff100adef01bb00114d0650ec6_2.0; aliyun_lang=zh; currentRegionId=cn-zhangjiakou; cna=+/srHQ70HDQCAQAAAAA4zmU6; yunpk=1005298046719350; cnaui=%2522kaihang.xkh%2520%2540%25201005298046719350%2522; aui=%2522kaihang.xkh%2520%2540%25201005298046719350%2522; login_aliyunid_csrf=_csrf_tk_1081109199732202; login_aliyunid=\"kaihang.xkh @ rdc\"; login_aliyunid_ticket=3R5H3e3HY2c8q5WiJ2aXp2hv.1118SYZZQjRLQ6nfCp7Ed5oUZKCSSRzmZVLWU77JEyX3WZEzMbAgEw1WGXv2bYJqdpTzFRztpPcL1NGtfc3FJ8ou3LzsMDqE7iAHcChqSRqnLp2q9JFQRhe9odSrit1P3RoprGgLCacbZF76RHn9CdPMx88jNWyDiA4QQbeNdj7RYHHBZb7.1QDta7oezh2Q9fzdPiTD8ubz9PbSPXDSCU6RAg7LQQKjDRN13SLUHFZBTRXycVrE2; login_aliyunid_sc=3R5H3e3HY2c8q5WiJ2aXp2hw.*****************************************************LMzHHWfEH3HPQy5Gg.2mWNaj2wfv1dBLe61jS543FuEojGfFnoiE1DLZUiHH9X9uF7k17VrUUfvJDLZcBt98; login_aliyunid_pk=1599737445918100; login_current_pk=271001090080628171; LOGIN_ALIYUN_PK_FOR_TB=271001090080628171; TEAMBITION_SESSIONID=eyJ1aWQiOiI1ZWI4YjAxNzNjMzBjMzI2NTNlNGI2YTkiLCJhdXRoVXBkYXRlZCI6MTcwOTE5ODk1NzIxOCwidXNlciI6eyJfaWQiOiI1ZWI4YjAxNzNjMzBjMzI2NTNlNGI2YTkiLCJuYW1lIjoi5byX5q2iYWxpYmF5IiwiZW1haWwiOiJhY2NvdW50c181ZWI4YjAxNzgxNzk0NDAwMzZkOWU0MjJAbWFpbC50ZWFtYml0aW9uLmNvbSIsImF2YXRhclVybCI6Imh0dHBzOi8vdGNzLWRldm9wcy5hbGl5dW5jcy5jb20vdGh1bWJuYWlsLzExMms4ODI1NGM1MTAzYWYyNDU4Mjk0NzgyMWM5ZGNhZjM3MC93LzIwMC9oLzIwMCIsInJlZ2lvbiI6InVzIiwibGFuZyI6IiIsImlzUm9ib3QiOmZhbHNlLCJvcGVuSWQiOiIiLCJwaG9uZUZvckxvZ2luIjoiIiwiY3JlYXRlZCI6IjIwMjAtMDUtMTFUMDE6NTM6MjcuNTAxWiJ9LCJsb2dpbkZyb20iOiIifQ==; TEAMBITION_SESSIONID.sig=xq7KZl31ajaypxlrdS63xu13KTY; XSRF-TOKEN=bf6db00c-504f-42ff-9922-a71cbb4b60e0; ak_user_locale=zh_CN; teambition_lang=zh_CN; lastVisitedPath=apps; version=ga; isg=BJycH4Q_FU8n6ucEOOs1ztg7bbxOFUA_-zDBh3ac-wdowTFLlSZszhkyJSk5yXiX; tfstk=eWt6zHTkgIKeIBagOfMed_kh9psbDCirD-6vEKEaHGIOGsCBi17V_Gkbc6Of0dzNQETBMBbN6fstGJpAthSVQ1WYcK1HboBAMkQpgQCYBxbOp9BJgRFAHf2M-QJ8bhuG0osGmihra0ozSdjcDVcWgPvib3mQbbor4d3OIiJKaAmuY6jfN8UEMSpiOkr3DkT5rgr9IdKCCw3en6IhAxWR5HvGObhJAoQ1wdCQDgoU4_gqyrwCro6CavMQorfjUfh2a-wPSNBhC3kId5UcW9XCKvMQorbOK9rtdvNTo"
        )
        val uri = URI("wss://devops.aliyun.com/appstack/gateway/biz/ws/appTemplateSyncProgress?transactionId=123")
        val sessions = mutableListOf<WebSocketSession>()

        for (i in 1..3) {
            val session = client.doHandshake(TestWebSocketHandler(), webSocketHttpHeaders, uri).get()
            println("IsOpen: "+ session.isOpen)
            session.sendMessage(TextMessage("Text:top\n"))
            sessions.add(session)
        }

        val thread = Thread(Daemon(sessions[1]))
        thread.start()
        thread.join()
    }

    private fun createTransportClient(): List<Transport> {
        val transports: MutableList<Transport> = ArrayList(1)
        transports.add(WebSocketTransport(StandardWebSocketClient()))
        return transports
    }

    private class TestWebSocketHandler : TextWebSocketHandler() {
        override fun handleTextMessage(session: WebSocketSession, message: TextMessage) {
            println("Receive[${session.id}]: " + message.payload)
        }

        override fun afterConnectionClosed(session: WebSocketSession, status: CloseStatus) {
            println("Closed[${session.id}]")
        }

        override fun handleTransportError(session: WebSocketSession, exception: Throwable) {
            println("Error[${session.id}]")
        }
    }
}


class Daemon(
    val session : WebSocketSession
): Runnable {
    override fun run() {
        while (true){
            session.sendMessage(TextMessage("Text:ls\n"))
            Thread.sleep(1000 * 60 * 5)
        }
    }
}