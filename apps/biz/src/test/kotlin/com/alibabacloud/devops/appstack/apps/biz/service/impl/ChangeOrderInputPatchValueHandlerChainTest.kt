package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.libs.model.`do`.app.App
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderInput
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.Orchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppBuiltInOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Placeholder
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.RevisionVariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.Variable
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.RepoMeta
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR>
 * @date 2023-04-22
 */
class ChangeOrderInputPatchValueHandlerChainTest : BizServerApplicationTests() {

    @Autowired
    lateinit var chain: ChangeOrderInputPatchValueHandlerChain

    @Test
    fun test_handle_forDeploy() {
        val appName = "myapp"
        val envName = "myenv"
        val constantPlaceholder = Pair("constant.key", "constant.value")
        val imagePlaceholder = Pair("image.backend", "NULL")
        val envVariablePlaceholder = Pair("my.env.placeholder", "my.env.variable.key")
        val nullPlaceholder = Pair("null.key", "NULL")

        val placeholderList = listOf(
            Placeholder(
                constantPlaceholder.first,
                "",
                Placeholder.Type.STRING,
                constantPlaceholder.second,
                false,
                null,
                Placeholder.ValueSource.CONSTANT,
                false
            ),
            Placeholder(
                imagePlaceholder.first,
                "",
                Placeholder.Type.STRING,
                imagePlaceholder.second,
                false,
                null,
                Placeholder.ValueSource.CONSTANT,
                true
            ),
            Placeholder(
                envVariablePlaceholder.first,
                "",
                Placeholder.Type.STRING,
                envVariablePlaceholder.second,
                false,
                null,
                Placeholder.ValueSource.VARIABLE,
                true
            ),
            Placeholder(
                "envName",
                "",
                Placeholder.Type.STRING,
                "APPSTACK_ENV_NAME",
                false,
                null,
                Placeholder.ValueSource.VARIABLE,
                true
            ),
            Placeholder(
                nullPlaceholder.first,
                "Namespace",
                Placeholder.Type.STRING,
                nullPlaceholder.second,
                false,
                null,
                Placeholder.ValueSource.NULL,
                true
            )
        )

        val orchestration = AppBuiltInOrchestration(
            format = Orchestration.FormatEnum.MANIFEST,
            app = App(appName),
            sn = appName,
            name = null,
            creatorId = null,
            modifierId = null,
            gmtCreate = null,
            gmtModified = null,
            description = null,
            revision = null,
            suitableResourceTypes = listOf(ResourceTypeEnum.KUBERNETES)
        ).apply {
            this.placeholderList = placeholderList
        }

        val env = Env(
            name = envName,
            displayName = envName,
            appName = appName,
            profiles = mutableListOf(VariableGroup(type = VariableGroup.Type.APP, name = "customProfile")),
        )

        val profiles: List<RevisionVariableGroup> = listOf(
            RevisionVariableGroup(
                name = "test",
                displayName = "test",
                type = VariableGroup.Type.APP,
                revision = Revision(RepoMeta("", ""), "", "", ""),
                vars =  listOf(
                    Variable("my.env.variable.key", "my.env.variable.value", ""),
                    Variable("my.env.variable.key12", "my.env.variable.value12", "")
                )
            )
        )

        val changeOrderInput = ChangeOrderInput(
            appName = appName,
            name = "",
            type = ChangeOrder.Type.Deploy,
            description = "",
            version = "",
            envValues = mapOf(
                envName to mutableMapOf(
                    "envName" to envName,
                    imagePlaceholder.first to "myImage:tag"
                )
            )
        )

        val patchValues =
            chain.handle(env, ChangeOrder.Type.Deploy, orchestration, profiles, changeOrderInput, emptyMap())
        Assertions.assertEquals(5, patchValues.size)
        Assertions.assertEquals(constantPlaceholder.second, patchValues[constantPlaceholder.first])
        Assertions.assertEquals("myImage:tag", patchValues[imagePlaceholder.first])
        Assertions.assertEquals("", patchValues[nullPlaceholder.first])
        Assertions.assertEquals("my.env.variable.value", patchValues[envVariablePlaceholder.first])
        Assertions.assertEquals(envName, patchValues["envName"])


        val patchValuesForCustomValue =
            chain.handle(env, ChangeOrder.Type.Deploy, orchestration, profiles, changeOrderInput, mapOf(imagePlaceholder.first to "nginx:tag"))
        Assertions.assertEquals(5, patchValues.size)
        Assertions.assertEquals(constantPlaceholder.second, patchValuesForCustomValue[constantPlaceholder.first])
        Assertions.assertEquals("nginx:tag", patchValuesForCustomValue[imagePlaceholder.first])
        Assertions.assertEquals("", patchValuesForCustomValue[nullPlaceholder.first])
        Assertions.assertEquals("my.env.variable.value", patchValuesForCustomValue[envVariablePlaceholder.first])
        Assertions.assertEquals(envName, patchValuesForCustomValue["envName"])

        val patchValuesWithNullChangeOrderInput =
            chain.handle(env, ChangeOrder.Type.Deploy, orchestration, profiles, null, emptyMap())
        Assertions.assertEquals("", patchValuesWithNullChangeOrderInput[imagePlaceholder.first])
    }


}