package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.DeployGroupBizService
import com.alibabacloud.devops.appstack.libs.model.constant.Default
import org.junit.jupiter.api.MethodOrderer
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestMethodOrder
import org.springframework.beans.factory.annotation.Autowired

/**
 * @author: <EMAIL>
 * @date: 2023-06-13 12:20
 * @version: DeployGroupBizServiceImplTest, v0.1
 **/
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class DeployGroupBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var deployGroupBizService: DeployGroupBizService

    @Test
    fun findAllByInstanceTest() {
        val deployGroupVoList = deployGroupBizService.findAllByInstance(Default.POOLNAME, "mock-instance-name")
        val claimList = deployGroupVoList.first().claimList
        assert(deployGroupVoList.isNotEmpty())
        assert(claimList.isNotEmpty())
        assert(claimList.first().resourceInstance?.name == "mock-instance-name")
        assert(claimList.first().itemSnList.size == 1)
        assert(claimList.first().itemSnList.contains("itemSnMock"))
    }

}