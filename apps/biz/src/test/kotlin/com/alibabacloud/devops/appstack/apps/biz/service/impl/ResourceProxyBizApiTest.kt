package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * @author: <EMAIL>
 * @date: 2024-05-21 17:55
 * @version: ResourceProxyBizApiTest, v0.1
 **/
class ResourceProxyBizApiTest : BizServerApplicationTests() {

    @Autowired
    lateinit var resourceBizProxyBizServiceImpl: ResourceProxyBizServiceImpl

    @Test
    fun shouldHaveNoLineCoverageCroak() {
        val mockedHealthy = resourceBizProxyBizServiceImpl.probeKubernetesHealth("RI:default/none")
        assert(mockedHealthy.healthy)
    }

}