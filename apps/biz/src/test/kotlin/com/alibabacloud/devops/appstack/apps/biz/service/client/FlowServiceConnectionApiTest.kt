package com.alibabacloud.devops.appstack.apps.biz.service.client

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.client.api.FlowServiceConnectionApi
import com.alibabacloud.devops.appstack.libs.common.util.TokenUtil
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR> <EMAIL>
 * @version : FlowApiTest, v0.1
 * @date : 2022-06-10 11:51
 **/
@Disabled
class FlowServiceConnectionApiTest : BizServerApplicationTests() {

    @Autowired
    lateinit var flowServiceConnectionApi: FlowServiceConnectionApi

    @Test
    fun test() {
        val rolePermission = flowServiceConnectionApi.queryRolePermission(
            headers = TokenUtil.generateAuthHeaders("60964c32f44831d666bdd11a", "BOe4r3BBOOqMNITyRr8wDk9wIEgRA0sd"),
            roleId = "5fd1e384cd3711dd64a9cd4f"
        )
        println(rolePermission)
        val joinToString = rolePermission.data?.permissions?.map { it.code }?.joinToString(separator = ",")
        println(joinToString)
    }

    @Test
    fun token() {
        //val headers = TokenUtil.generateAuthHeaders("60964c32f44831d666bdd11a", "BOe4r3BBOOqMNITyRr8wDk9wIEgRA0sd")
        // test
        val headers = TokenUtil.generateAuthHeaders("09347fb6-79e1-4dfe-8ff4-6761ab85262c", "5f0e30e7-60c1-499a-a55b-49db1f80d222")
        println(headers["Authorization"])
    }
}