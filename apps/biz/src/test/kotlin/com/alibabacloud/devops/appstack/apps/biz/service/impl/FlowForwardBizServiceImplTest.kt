package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestStageBizService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.Header
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.apache.http.client.config.RequestConfig
import org.apache.http.config.RegistryBuilder
import org.apache.http.conn.socket.ConnectionSocketFactory
import org.apache.http.conn.socket.PlainConnectionSocketFactory
import org.apache.http.conn.ssl.NoopHostnameVerifier
import org.apache.http.conn.ssl.SSLConnectionSocketFactory
import org.apache.http.impl.client.HttpClientBuilder
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager
import org.apache.http.ssl.SSLContexts
import org.apache.http.ssl.TrustStrategy
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.kotlin.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.boot.test.mock.mockito.SpyBean
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory
import org.springframework.mock.web.MockHttpServletRequest
import org.springframework.mock.web.MockHttpServletResponse
import org.springframework.web.client.RestTemplate
import java.security.cert.X509Certificate
import java.util.LinkedHashMap

/**
 * <AUTHOR>
 * @create 2023/12/4 5:41 PM
 **/
class FlowForwardBizServiceImplTest : BizServerApplicationTests(){

    @SpyBean
    lateinit var flowForwardBizService: FlowForwardBizServiceImpl

    @MockBean
    lateinit var changeRequestStageBizService: ChangeRequestStageBizService

    @Mock
    lateinit var restTemplate: RestTemplate

    @MockBean
    lateinit var iamService: IamService

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Mock
    lateinit var flowPermissionHandlerChain: FlowPermissionHandlerChain

    @Value("\${service.flow.forward-map}")
    var flowForwardMapStr: String = "      {\n" +
            "        \"/ec/ajax/pipeline/(?<segment>[^/]*)/getPipelineBaseInfo\": {\n" +
            "          \"replacement\": \"/inner/api/pipelines/\$\\\\{segment}/baseInfo\",\n" +
            "          \"method\": \"GET\",\n" +
            "          \"permissions\": [\n" +
            "            \"tb.flow.pipeline.view\"\n" +
            "          ]\n" +
            "        },\n" +
            "        \"/ec/ajax/pipelines/(?<pipelineId>[^/]*)/stages/(?<stageId>[^/]*)/execute\": {\n" +
            "          \"replacement\": \"/inner/api/pipelines/\$\\\\{pipelineId}/stages/\$\\\\{stageId}/run\",\n" +
            "          \"method\": \"POST\",\n" +
            "          \"permissions\": [\n" +
            "            \"tb.flow.pipeline.run\"\n" +
            "          ]\n" +
            "        },\n" +
            "        \"/ec/ajax/pipelines/(?<pipelineId>[^/]*)/instances/(?<instanceId>[^/]*)/cancel\": {\n" +
            "          \"replacement\": \"/inner/api/pipelines/\$\\\\{pipelineId}/instances/\$\\\\{instanceId}/cancel\",\n" +
            "          \"method\": \"POST\",\n" +
            "          \"permissions\": [\n" +
            "            \"tb.flow.pipeline.run\"\n" +
            "          ]\n" +
            "        },\n" +
            "       \"/ec/ajax/pipelines/(?<segment>.*)\": {\n" +
            "          \"replacement\": \"/inner/api/pipelines/\$\\\\{segment}\",\n" +
            "          \"method\": \"GET\",\n" +
            "          \"permissions\": [\n" +
            "            \"tb.flow.pipeline.view\"\n" +
            "          ]\n" +
            "        },\n" +
            "        \"/flow/service/pipeline/cache/clean\": {\n" +
            "            \"replacement\": \"/inner/api/pop/pipeline/cleanPipelineCache\",\n" +
            "            \"method\": \"POST\",\n" +
            "            \"permissions\": [\n" +
            "              \"tb.flow.pipeline.edit\"\n" +
            "            ]\n" +
            "        },\n" +
            "        \"/execution-component/logList\": {\n" +
            "            \"replacement\": \"/inner/api/v2/execution-component/logList\",\n" +
            "            \"method\": \"GET\",\n" +
            "            \"permissions\": [\n" +
            "              \"tb.flow.pipeline.view\"\n" +
            "            ]\n" +
            "        },\n" +
            "        \"/execution-component/log\": {\n" +
            "            \"replacement\": \"/inner/api/v2/execution-component/log\",\n" +
            "            \"method\": \"GET\",\n" +
            "            \"permissions\": [\n" +
            "              \"tb.flow.pipeline.view\"\n" +
            "            ]\n" +
            "        },\n" +
            "      \"/execution-component/downloadLog\": {\n" +
            "            \"replacement\": \"/inner/api/v2/execution-component/downloadLog\",\n" +
            "            \"method\": \"GET\",\n" +
            "            \"permissions\": [\n" +
            "              \"tb.flow.pipeline.view\"\n" +
            "            ]\n" +
            "        },\n" +
            "        \"/execution-component/downloadLogV2\": {\n" +
            "            \"replacement\": \"/inner/api/v2/execution-component/downloadLogV2\",\n" +
            "            \"method\": \"GET\",\n" +
            "            \"permissions\": [\n" +
            "              \"tb.flow.pipeline.view\"\n" +
            "            ]\n" +
            "        },\n" +
            "        \"/execution-component/getDebugInfo\": {\n" +
            "            \"replacement\": \"/inner/api/v2/execution-component/getDebugInfo\",\n" +
            "            \"method\": \"GET\",\n" +
            "            \"permissions\": [\n" +
            "              \"tb.flow.pipeline.view\"\n" +
            "            ]\n" +
            "        }\n" +
            "      }"

    @Value("\${service.flow.url}")
    lateinit var flowUrl: String

    companion object {
        const val RESULT_STR = "{}"
    }

    @Test
    fun timeoutTest() {
        val strategy =
            TrustStrategy { _: Array<X509Certificate?>?, _: String? -> true }
        val sslContext = SSLContexts.custom().loadTrustMaterial(null, strategy).build()
        val sslsf = SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE)

        val registry = RegistryBuilder.create<ConnectionSocketFactory>()
            .register("http", PlainConnectionSocketFactory.getSocketFactory())
            .register("https", sslsf)
            .build()

        val connectManager = PoolingHttpClientConnectionManager(registry)
        connectManager.maxTotal = 50
        connectManager.defaultMaxPerRoute = 10

        val requestConfig = RequestConfig.custom()
            .setSocketTimeout(10000)
            .setConnectTimeout(10000)
            .setConnectionRequestTimeout(1000)
            .build()

        val httpClient = HttpClientBuilder.create()
            .setDefaultRequestConfig(requestConfig)
            .setConnectionManager(connectManager)
            .build()

        val factory = HttpComponentsClientHttpRequestFactory()
        factory.httpClient = httpClient

        val restTemplate = RestTemplate(factory)
        val url = "http://localhost:8080/timeout"
        try {
            //restTemplate.getForObject(url, String::class.java)
        } catch (e: Throwable) {
            logger.error(e.message, e)
        }
    }

    @Test
    fun forwardMapTest() {
        var flowForwardMap = mutableMapOf<String, ObjectNode>()
        logger.info("flowForwardMapStr: $flowForwardMapStr")
        if (flowForwardMapStr.isNotBlank() && flowForwardMap.isEmpty()) {
            try {
                val jsonNode = jacksonObjectMapper().readTree(flowForwardMapStr)
                flowForwardMap = jacksonObjectMapper().convertValue(jsonNode, object : TypeReference<LinkedHashMap<String, ObjectNode>>() {})
                logger.info("load flowForwardMap real: $flowForwardMap")
                var path = "/execution-component/logList"
                val method = "GET"
                val permissions = mutableListOf<String>()
                flowForwardMap.keys.firstOrNull { path.matches(it.toRegex()) && flowForwardMap[it]!!.get("method").asText() == method }?.let {
                    val replacement = flowForwardMap[it]!!.get("replacement").asText().replace("$\\", "$")
                    path = path.replace(it.toRegex(), replacement)
                    flowForwardMap[it]!!.get("permissions").forEach {
                        permissions.add(it.asText())
                    }
                }
                logger.info("path: $path permissions: $permissions")
            } catch (e: Throwable) {
                logger.error("failed to forwardFlow: load flowForwardMap: str: $flowForwardMapStr", e)
            }
        }
    }

    @Test
    fun testNoPermission() {
        val request = MockHttpServletRequest()
        request.servletPath = "/ec/ajax/pipelines/queryByRegion"
        request.method = "GET"
        val response = MockHttpServletResponse()
        val restResponse = ResponseEntity<String>(RESULT_STR, HttpStatus.OK)
        given(restTemplate.exchange(eq("$flowUrl/inner/api/pop/v2/pipelines/queryByRegion"), eq(HttpMethod.GET), any(), eq(String::class.java))).willReturn(restResponse)
        flowForwardBizService.restTemplate = restTemplate
        val result = flowForwardBizService.handleCommon(request, response)
        assert(RESULT_STR == jacksonObjectMapper().writeValueAsString(result))
    }

    @Test
    fun testParseFormBody() {
        val body = "test=1%2C2"
        val parsedBody = flowForwardBizService.exposeParseFormBody(body)
        assert(parsedBody["test"] == "1,2")
    }

    @Test
    fun testGetPipelineWithParams() {
        val mp: MutableMap<String, Any> = mutableMapOf(
            Header.APPSTACK_APP_NAME to "test"
        )
        val restResponse = ResponseEntity<String>(RESULT_STR, HttpStatus.OK)
        given(changeRequestStageBizService.getPipelineParams("test", "test", "test")).willReturn(mp)
        given(restTemplate.exchange(eq("$flowUrl/inner/api/pop/v2/pipelines/1234/getWithParams"), eq(HttpMethod.POST), any(), eq(String::class.java))).willReturn(restResponse)
        given(iamService.can(ProtocolType.AppStackApp, "test", Action.APP_VIEW)).willReturn(true)
        val request = MockHttpServletRequest()
        request.addHeader(Header.APPSTACK_APP_NAME, "test")
        val response = MockHttpServletResponse()
        flowForwardBizService.restTemplate = restTemplate
        val result = flowForwardBizService.getPipelineWithParams("1234", request, response)
        assert(RESULT_STR == jacksonObjectMapper().writeValueAsString(result))
    }

    @Test
    fun should_get_instance_id_by_invoking_exchange_only_once() {
        val appName = "appName"
        val releaseWorkflowSn = "releaseWorkflowSn"
        val releaseStageSn = "testCancelExecutionReleaseStageSn"
        val executionNumber = "2"
        val total = 20
        val instanceId = "instanceId"

        val releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findBrief(releaseWorkflowSn)
        val releaseStage = workflowControllerFacade.releaseStageFacade.findBrief(releaseStageSn)

        // 模拟第一次获取实例列表的响应体
        // 在实例列表中能找到实例的 instNumber 与请求的 executionNumber 相同，则可以直接获取其 id
        val firstInstancesResponseBody = """
            {
                "object": {
                    "dataList": [
                        {
                            "id": "$instanceId",
                            "instNumber": 2
                        }
                    ],
                    "total": $total
                }
            }
        """
        val firstRestResponse = ResponseEntity<String>(firstInstancesResponseBody, HttpStatus.OK)
        // 模拟鉴权通过
        doReturn(true).`when`(flowPermissionHandlerChain).handle(any(), any(), any(), any())
        flowForwardBizService.flowPermissionHandlerChain = flowPermissionHandlerChain

        // 模拟第一次获取实例列表
        given(restTemplate.exchange(
            eq("$flowUrl/inner/api/pipelines/pipelineId/instances?pageStart=0&pageSize=9"),
            eq(HttpMethod.GET),
            any(),
            eq(String::class.java)
        )).willReturn(firstRestResponse)
        flowForwardBizService.restTemplate = restTemplate

        val result = flowForwardBizService.exposeGetInstanceId(appName, releaseWorkflow, releaseStage, executionNumber)
        // 验证只获取了一次实例列表
        verify(restTemplate, times(1)).exchange(
            eq("$flowUrl/inner/api/pipelines/pipelineId/instances?pageStart=0&pageSize=9"),
            eq(HttpMethod.GET),
            any(),
            eq(String::class.java)
        )
        verify(restTemplate, never()).exchange(
            eq("$flowUrl/inner/api/pipelines/pipelineId/instances?pageStart=${total - executionNumber.toInt()}&pageSize=1"),
            eq(HttpMethod.GET),
            any(),
            eq(String::class.java)
        )
        assert(result == instanceId)
    }

    /**
     * 由于获取到的实例列表是倒序排列的，第一次获取实例列表时不一定能命中 executionNumber 对应的 instanceId
     * 但是第一次可以获取到实例总数 total，所以第二次获取实例列表时，使 pageStart = total - executionNumber 可以精确获取到需要的实例
     * 所以至多获取两次实例列表可得到 executionNumber 对应的 instanceId
     */
    @Test
    fun should_get_instance_id_by_invoking_exchange_twice() {
        val appName = "appName"
        val releaseWorkflowSn = "releaseWorkflowSn"
        val releaseStageSn = "testCancelExecutionReleaseStageSn"
        val executionNumber = "10"
        val total = 20
        val instanceId = "instanceId"

        val releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findBrief(releaseWorkflowSn)
        val releaseStage = workflowControllerFacade.releaseStageFacade.findBrief(releaseStageSn)

        // 模拟第一次获取实例列表的响应体
        // 在实例列表中不能找到与请求的 executionNumber 相同的实例 instNumber，但是可以获取实例总数 total
        val firstInstancesResponseBody = """
            {
                "object": {
                    "dataList": [],
                    "total": $total
                }
            }
        """
        // 模拟第二次获取实例列表的响应体
        // 在实例列表中能找到实例的 instNumber 与请求的 executionNumber 相同，则在第二次获取时可以得到 instanceId
        val secondInstancesResponseBody = """
            {
                "object": {
                    "dataList": [
                        {
                            "id": "$instanceId",
                            "instNumber": 10
                        }
                    ],
                    "total": $total
                }
            }
        """
        val firstRestResponse = ResponseEntity<String>(firstInstancesResponseBody, HttpStatus.OK)
        val secondRestResponse = ResponseEntity<String>(secondInstancesResponseBody, HttpStatus.OK)
        // 模拟鉴权通过
        doReturn(true).`when`(flowPermissionHandlerChain).handle(any(), any(), any(), any())
        flowForwardBizService.flowPermissionHandlerChain = flowPermissionHandlerChain

        // 模拟第一次获取实例列表
        given(restTemplate.exchange(
            eq("$flowUrl/inner/api/pipelines/pipelineId/instances?pageStart=0&pageSize=9"),
            eq(HttpMethod.GET),
            any(),
            eq(String::class.java)
        )).willReturn(firstRestResponse)
        // 模拟第二次获取实例列表（获取实例总数 total 后，根据 total 计算 pageStart 来精确获取实例）
        given(restTemplate.exchange(
            eq("$flowUrl/inner/api/pipelines/pipelineId/instances?pageStart=${total - executionNumber.toInt()}&pageSize=1"),
            eq(HttpMethod.GET),
            any(),
            eq(String::class.java)
        )).willReturn(secondRestResponse)
        flowForwardBizService.restTemplate = restTemplate

        val result = flowForwardBizService.exposeGetInstanceId(appName, releaseWorkflow, releaseStage, executionNumber)
        // 验证一共获取了两次实例列表
        verify(restTemplate, times(1)).exchange(
            eq("$flowUrl/inner/api/pipelines/pipelineId/instances?pageStart=0&pageSize=9"),
            eq(HttpMethod.GET),
            any(),
            eq(String::class.java)
        )
        verify(restTemplate, times(1)).exchange(
            eq("$flowUrl/inner/api/pipelines/pipelineId/instances?pageStart=${total - executionNumber.toInt()}&pageSize=1"),
            eq(HttpMethod.GET),
            any(),
            eq(String::class.java)
        )
        assert(result == instanceId)
    }

    @Test
    fun should_generate_forward_request_for_get_pipeline_run() {
        val method = "POST"
        val appName = "appName"
        val releaseWorkflowSn = "releaseWorkflowSn"
        val releaseStageSn = "testCancelExecutionReleaseStageSn"
        val pipelineId = "pipelineId"
        val executionNumber = "executionNumber"
        val jobId: String? = null
        val operationName = "getPipelineRun"

        val releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findBrief(releaseWorkflowSn)
        val releaseStage = workflowControllerFacade.releaseStageFacade.findBrief(releaseStageSn)

        val request = flowForwardBizService.exposeGenForwardRequest(appName, releaseWorkflow, releaseStage, pipelineId, executionNumber, jobId, operationName)

        assert(request.method == method)
        assert(request.servletPath == "/forward/flow/ec/ajax/pipeline:getPipelineRun")
        assert(request.queryString == "pipelineId=$pipelineId&pipelineRunId=$executionNumber")
    }

    @Test
    fun should_generate_forward_request_for_pass_pipeline_validate() {
        val method = "POST"
        val appName = "appName"
        val releaseWorkflowSn = "releaseWorkflowSn"
        val releaseStageSn = "testCancelExecutionReleaseStageSn"
        val pipelineId = "pipelineId"
        val executionNumber = "executionNumber"
        val jobId = "jobId"
        val operationName = "passPipelineValidate"

        val releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findBrief(releaseWorkflowSn)
        val releaseStage = workflowControllerFacade.releaseStageFacade.findBrief(releaseStageSn)

        val request = flowForwardBizService.exposeGenForwardRequest(appName, releaseWorkflow, releaseStage, pipelineId, executionNumber, jobId, operationName)

        assert(request.method == method)
        assert(request.servletPath == "/forward/flow/ec/ajax/pipeline:passPipelineValidate")
        assert(request.queryString == "pipelineId=$pipelineId&pipelineRunId=$executionNumber&jobId=$jobId")
    }
}