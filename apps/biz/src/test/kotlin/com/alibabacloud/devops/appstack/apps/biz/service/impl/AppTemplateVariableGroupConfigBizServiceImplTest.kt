package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateVariableGroupConfigBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.Profile
import com.alibabacloud.devops.appstack.libs.model.request.UpdateVariableGroupDryRunRequest
import com.alibabacloud.devops.appstack.libs.model.request.VariableTemplateBatchUpdateRequest
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.UnicastSender
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.UnicastTagEnum
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.kotlin.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean
import java.lang.Exception

class AppTemplateVariableGroupConfigBizServiceImplTest: BizServerApplicationTests() {

    @Autowired
    lateinit var appTemplateVariableGroupConfigBizService: AppTemplateVariableGroupConfigBizService

    @MockBean
    lateinit var  unicastSender: UnicastSender

    @Test
    fun batchUpdateTest(){
        val appTemplateName = "templateName"
        val request = VariableTemplateBatchUpdateRequest(
            mode = UpdateVariableGroupDryRunRequest.Mode.OVERRIDE_KEY_VALUE,
            profile = Profile(name = "dev", displayName = "开发环境变量组", vars = emptyList()),
            appNames = listOf("appName1", "appName2"),
            commitMsg = "commitMsg",
        )
        doNothing().`when`(unicastSender).send(any(), any())
        appTemplateVariableGroupConfigBizService.batchUpdate(appTemplateName, request)
        verify(unicastSender, times(1)).send(eq(UnicastTagEnum.APPTEMPLATE_VARIABLE_BATCH_UPDATE), any())

        val emptyRequest = VariableTemplateBatchUpdateRequest(
            mode = UpdateVariableGroupDryRunRequest.Mode.OVERRIDE_KEY_VALUE,
            profile = Profile(name = "dev", displayName = "开发环境变量组", vars = emptyList()),
            appNames = listOf(),
            commitMsg = "commitMsg",
        )
        try{
            appTemplateVariableGroupConfigBizService.batchUpdate(appTemplateName, emptyRequest)
            Assertions.fail<String>()
        }catch (e: Exception){
            Assertions.assertEquals(ErrorCode.AS_APP_NOT_FOUND, (e as BizException).errorEntry.code)
        }
    }
}
