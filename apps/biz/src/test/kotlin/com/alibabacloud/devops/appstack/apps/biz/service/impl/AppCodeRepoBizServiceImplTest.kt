package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.request.PaginationQuery
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.lang.Exception

class AppCodeRepoBizServiceImplTest: BizServerApplicationTests() {

    @Autowired
    lateinit var appCodeRepoBizService: AppCodeRepoBizServiceImpl

    @Test
    fun testListException(){
        val appName = "myApp"
        val query = PaginationQuery(pagination = "none", sort = "desc")
        try{
            appCodeRepoBizService.list(appName, query)
        }catch (e: Exception){
            e as BizException
            Assertions.assertEquals(ErrorCode.AS_PAGINATION_PARAM_INVALID, e.errorEntry.code)
        }
    }
}