package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AESCryptUtil
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.PodExecRefactor
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR>
 * @date 2023-08-06
 */
class EnvProxyBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var envProxyBizService: EnvProxyBizServiceImpl

    companion object {
        val env = Env(name = "myEnv", displayName = "", appName = "myApp")
        val namespace = "default"
        val containerName = "main"
    }

    @Test
    fun test_generatePodExecToken_for_pod() {
        val podName = "test_generatePodExecToken_for_pod"
        val token = envProxyBizService.generatePodExecToken(env, namespace, podName, containerName)
        val originJson = AESCryptUtil.decrypt(token.token)
        val podExecRefactor = jacksonObjectMapper().readValue<PodExecRefactor>(originJson)
        Assertions.assertEquals(podName, podExecRefactor.podName)
    }

    @Test
    fun test_generatePodExecToken_for_pod_not_found() {
        val podName = "test_generatePodExecToken_for_pod_not_found"
        try {
            envProxyBizService.generatePodExecToken(env, namespace, podName, containerName)
            Assertions.fail("Should throw Exception but Not")
        } catch (e: BizException) {
            Assertions.assertEquals(ErrorCode.AS_K8S_RESOURCE_NOT_FOUND, e.errorEntry.code)
        }

        val notExistPod = "not-exist"
        try {
            envProxyBizService.generatePodExecToken(env, namespace, notExistPod, containerName)
            Assertions.fail("Should throw Exception but Not")
        } catch (e: BizException) {
            Assertions.assertEquals(ErrorCode.AS_K8S_RESOURCE_NOT_FOUND, e.errorEntry.code)
        }
    }

    @Test
    fun test_generatePodExecToken_for_pod_through_owner() {
        val podName = "my-stateful-app-0"
        val token = envProxyBizService.generatePodExecToken(env, namespace, podName, containerName)
        val originJson = AESCryptUtil.decrypt(token.token)
        val podExecRefactor = jacksonObjectMapper().readValue<PodExecRefactor>(originJson)
        Assertions.assertEquals(podName, podExecRefactor.podName)
    }
}