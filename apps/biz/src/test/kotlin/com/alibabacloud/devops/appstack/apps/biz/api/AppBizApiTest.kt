package com.alibabacloud.devops.appstack.apps.biz.api

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.AppBaseInfoVO
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.apache.http.client.methods.HttpGet
import org.apache.http.impl.client.HttpClientBuilder
import org.apache.http.util.EntityUtils
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import java.net.URI

/**
 * <AUTHOR>
 * @date 2023-06-08
 */
class AppBizApiTest: BizServerApplicationTests()  {

    @Test
    fun test_findApp(){
        val client = HttpClientBuilder.create().build()
        val httpGet = HttpGet(URI("http://localhost:$port/api/v1/apps/myapp"))
        httpGet.addHeader("X-Tenant-Id", "647af8c6391b9f116d36b66d")
        httpGet.addHeader("X-Operator-Id", "61c92a37ae805dbacc5b9f78")
        val response = client.execute(httpGet)
        Assertions.assertEquals(200, response.statusLine.statusCode)
        val body = EntityUtils.toString(response.entity)
        val responseVO: Response<AppBaseInfoVO> = jacksonObjectMapper().readValue(body)
        Assertions.assertTrue(responseVO.success)

        val httpGetNone = HttpGet(URI("http://localhost:$port/api/v1/apps/none"))
        httpGetNone.addHeader("X-Tenant-Id", "647af8c6391b9f116d36b66d")
        httpGetNone.addHeader("X-Operator-Id", "61c92a37ae805dbacc5b9f78")
        val responseNone = client.execute(httpGetNone)
        Assertions.assertEquals(404, responseNone.statusLine.statusCode)
    }
}