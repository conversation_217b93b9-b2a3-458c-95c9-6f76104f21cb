package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.*
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.constant.SystemVariable
import com.alibabacloud.devops.appstack.libs.model.constant.WorkflowEnvVariable
import com.alibabacloud.devops.appstack.libs.model.`do`.app.*
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ConnectionType
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ServiceConnection
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.Pipeline
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStage
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageInstance
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.constant.FlowPermission
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageBriefVO
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowBriefVO
import com.alibabacloud.devops.appstack.libs.model.request.PaginationQuery
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.ExecuteReleaseStagePipelineRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.QueryReleaseStageCrMetadataRequest
import com.alibabacloud.devops.appstack.libs.model.vo.ReleaseIntegratedMetadata
import com.alibabacloud.devops.iam.exception.IamForbiddenException
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.given
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean
import java.lang.Exception

/**
 * <AUTHOR>
 * @create 2024/5/21 5:27 PM
 **/
class ChangeRequestStageBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var changeRequestStageBizService: ChangeRequestStageBizServiceImpl

    @MockBean
    lateinit var serviceConnectionBizService: ServiceConnectionBizService

    @MockBean
    lateinit var appCodeRepoBizService: AppCodeRepoBizService

    @MockBean
    lateinit var appArtifactRepoBizService: AppArtifactRepoBizService

    @MockBean
    lateinit var stageBaseService: StageBaseService

    @MockBean
    lateinit var flowPermissionHandlerChain: FlowPermissionHandlerChain

    @MockBean
    lateinit var releaseWorkflowBaseService: WorkflowBaseService

    companion object {
        const val APP_NAME = "app"
        const val REPO_URL = "repo_url"
        const val CONNECTION_ID = 1L
        const val CONNECTION_UUID = "uuid"
        const val CODE_IDENTIFIER = "code"
        const val ARTI_IDENTIFIER = "arti"
        val APP_CODE_REPO = AppCodeRepo(
            sn = "sn",
            appName = APP_NAME,
            name = "name",
            identifier = CODE_IDENTIFIER,
            repoUrl = REPO_URL,
            repoContext = CodeupRepoContext(
                repoUrl = REPO_URL,
                defaultBranch = "master",
                projectId = ""
            ),
            connectionConfig = FlowConnectionConfig(
                connectionId = CONNECTION_ID.toString()
            )
        )
        val APP_ARTIFACT_REPO = AppArtifactRepo(
            sn = "sn",
            appName = APP_NAME,
            identifier = ARTI_IDENTIFIER,
            repoContext = CustomDockerRegistryContext(
                repoUrl = REPO_URL,
                defaultVersion = "v1"
            ),
            connectionConfig = FlowConnectionConfig(
                connectionId = CONNECTION_ID.toString()
            )
        )
    }

    @BeforeEach
    fun setUp() {
        changeRequestStageBizService.appCodeRepoBizService = appCodeRepoBizService
        changeRequestStageBizService.appArtifactRepoBizService = appArtifactRepoBizService
        changeRequestStageBizService.serviceConnectionBizService = serviceConnectionBizService
    }

    @Test
    fun testHandlePipelineEnvWithEmptyID() {
        val value = "{\"serviceConnectionID\":\"\"}"
        val result = changeRequestStageBizService.exposeHandlePipelineEnv(value)
        assert(result == "{\"serviceConnectionID\":\"\"}")
    }

    @Test
    fun testHandlePipelineEnvWithID() {
        val value = "{\"serviceConnectionID\":\"${CONNECTION_ID}\"}"
        given(serviceConnectionBizService.findByIds(any())).willReturn(listOf(ServiceConnection(
            id = CONNECTION_ID,
            connectionName = "name",
            connectionType = ConnectionType.Codeup,
            uuid = CONNECTION_UUID
        )))
        val result = changeRequestStageBizService.exposeHandlePipelineEnv(value)
        assert(result == "{\"serviceConnectionID\":\"${CONNECTION_UUID}\"}")
    }

    @Test
    fun testGetPipelineParams() {
        given(appCodeRepoBizService.findAll(APP_NAME)).willReturn(listOf(APP_CODE_REPO))
        given(appArtifactRepoBizService.findAll(APP_NAME)).willReturn(listOf(APP_ARTIFACT_REPO))
        given(serviceConnectionBizService.findByIds(any())).willReturn(listOf(ServiceConnection(
            id = CONNECTION_ID,
            connectionName = "name",
            connectionType = ConnectionType.Codeup,
            uuid = CONNECTION_UUID
        )))
        val result = changeRequestStageBizService.getPipelineParams(APP_NAME, "", "")
        // 判断字段是否存在
        assert(result.contains(SystemVariable.APPSTACK_APP_NAME))
        assert(result.contains("${WorkflowEnvVariable.APPSTACK_APP_SOURCE_PREFIX}${CODE_IDENTIFIER}"))
        assert(result.contains(WorkflowEnvVariable.APPSTACK_APP_SOURCES))
        // 判断值是否正确
        assert((result[SystemVariable.APPSTACK_APP_NAME] == APP_NAME))
        // 判断应用代码源是否注入成功
        val codeMapStr = result["${WorkflowEnvVariable.APPSTACK_APP_SOURCE_PREFIX}${CODE_IDENTIFIER}"] as? String
        assert(codeMapStr != null)
        val codeJson = jacksonObjectMapper().readValue(codeMapStr, Map::class.java)
        assert(codeJson["repoUrl"] == REPO_URL)
        // 判断 serviceConnectionID是否为UUID格式
        assert(codeJson["serviceConnectionID"] == CONNECTION_UUID)
        assert(codeJson["branch"] == "master")
        assert(codeJson["type"] == "codeup")
        // 判断应用源是否注入成功
        val sourceMap = result[WorkflowEnvVariable.APPSTACK_APP_SOURCES] as? Map<String, Map<String, Any>>
        assert(sourceMap != null)
        assert(sourceMap!!.contains(CODE_IDENTIFIER))
        assert(sourceMap.contains(ARTI_IDENTIFIER))
        assert(sourceMap[ARTI_IDENTIFIER]!!["repoUrl"] == REPO_URL)
    }

    @Test
    fun testListReleaseStageInstances() {
        val appName = "myapp"
        val workflowSn = "workflowSn"
        val stageSn = "stageSn"
        val query = PaginationQuery(
            sort = "desc"
        )
        given(stageBaseService.listReleaseStageInstance(appName, workflowSn, stageSn, query)).willReturn(PageList(
            data = listOf(ReleaseStageInstance(
                sn = "sn",
                releaseStageSn = stageSn,
                engineInstanceId = "1",
                engineType = Pipeline.Type.FlowV1,
                state = ReleaseStageInstance.State.SUCCESS,
                context = ReleaseStageInstance.Context()
            )),
        ))
        val result = changeRequestStageBizService.listReleaseStageInstances(appName, workflowSn, stageSn, query)
        Assertions.assertEquals(1, result.data.size)
    }


    @Test
    fun testListReleaseStageInstanceIntegratedMetadata() {
        val appName = "myapp"
        val workflowSn = "workflowSn"
        val stageSn = "stageSn"
        val number = 1L
        val releaseBranch = "release"
        given(stageBaseService.findReleaseStageCrMetadata(appName, workflowSn, stageSn, number))
            .willReturn(ReleaseIntegratedMetadata(
                releaseStageSn = stageSn,
                releaseBranch = releaseBranch,
                releaseRevision = "sha1",
                repoUrl = "url",
                repoType = "codeup",
                crDetailList = listOf(ReleaseIntegratedMetadata.CrDetail(
                    sn = "crSn",
                    name = "crName",
                    ownerId = "ownerId",
                    branchName = "feature",
                    commitId = "sha2"
                )),
                runNumber = 1L
            ))
        val resultList = changeRequestStageBizService.listReleaseStageInstanceIntegratedMetadata(appName, workflowSn, stageSn, number)
        Assertions.assertEquals(1, resultList.size)
        Assertions.assertEquals(releaseBranch, resultList.first().releaseBranch)
    }

    @Test
    fun testFindReleaseStageCrMetadata(){
        val appName = "myapp"
        val workflowSn = "workflowSn"
        val stageSn = "stageSn"
        val number = 1L
        val releaseBranch = "release"
        val metadata=  ReleaseIntegratedMetadata(
            releaseStageSn = stageSn,
            releaseBranch = releaseBranch,
            releaseRevision = "sha1",
            repoUrl = "url",
            repoType = "codeup",
            crDetailList = listOf(ReleaseIntegratedMetadata.CrDetail(
                sn = "crSn",
                name = "crName",
                ownerId = "ownerId",
                branchName = "feature",
                commitId = "sha2"
            )),
            runNumber = 1L
        )
        given(stageBaseService.findReleaseStageCrMetadata(appName, workflowSn, stageSn, number)).willReturn(metadata)
        val result = changeRequestStageBizService.findReleaseStageCrMetadata(appName, workflowSn, stageSn, QueryReleaseStageCrMetadataRequest(engineType = Pipeline.Type.FlowV1, buildNum = number))
        Assertions.assertEquals(metadata, result)
    }

    @Test
    fun testExecutePipeline_withoutPermission() {
        val releaseWorkflowSn = "sn1"
        val releaseStageSn = "sn2"
        given(flowPermissionHandlerChain.handle(eq((FlowPermission.PERMISSION_PIPELINE_RUN)), any(), any(), any())).willReturn(false)
        given(releaseWorkflowBaseService.findBrief(eq(APP_NAME), eq(releaseWorkflowSn))).willReturn(
            ReleaseWorkflowBriefVO()
        )
        given(stageBaseService.findBrief(eq(APP_NAME), eq(releaseWorkflowSn), eq(releaseStageSn))).willReturn(
            ReleaseStageBriefVO(name = "stageName", processEngineType = Pipeline.Type.FlowV1, processEngineSn = "processEngineSn")
        )
        try{
            changeRequestStageBizService.executePipeline(APP_NAME, releaseWorkflowSn, releaseStageSn, ExecuteReleaseStagePipelineRequest(params = mutableMapOf()))
            Assertions.fail<String>("Should fail")
        }catch (e: Exception){
            Assertions.assertTrue(e is IamForbiddenException)
        }
    }
}