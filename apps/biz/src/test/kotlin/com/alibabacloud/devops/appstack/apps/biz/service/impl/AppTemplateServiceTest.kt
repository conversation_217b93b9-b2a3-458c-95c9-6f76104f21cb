package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateService
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR>
 * @date 2024-05-16
 */
class AppTemplateServiceTest: BizServerApplicationTests() {
    @Autowired
    lateinit var appTemplateService: AppTemplateService

    @Test
    fun findPresetPaginatedTest(){

        val pages = appTemplateService.findPresetPaginated("", 1, 10)
        Assertions.assertEquals(4, pages.total)
        Assertions.assertEquals(1, pages.current)
        Assertions.assertEquals(1, pages.pages)
        Assertions.assertEquals(10, pages.pageSize)
        Assertions.assertEquals(4, pages.records.size)

        val searchPages = appTemplateService.findPresetPaginated("AI", 1, 10)
        Assertions.assertEquals(2, searchPages.total)
    }

    @Test
    fun findPresetConfigTest(){
        AppTemplateServiceImpl.PRESET_TEMPLATE_NAMES.forEach { templateName ->
            AppTemplateConfig.TypeEnum.values().forEach { type ->
                val config = appTemplateService.findPresetConfig(templateName, type)
                Assertions.assertNotNull(config)
            }
        }

        val nullConfig = appTemplateService.findPresetConfig("abc", AppTemplateConfig.TypeEnum.Orchestration)
        Assertions.assertNull(nullConfig)
    }

    @Test
    fun findTest(){
        val template = appTemplateService.find(AppTemplateServiceImpl.PRESET_TEMPLATE_NAMES.first())
        Assertions.assertNotNull(template)
    }

    @Test
    fun `search app list with app templates`() {
        val map = appTemplateService.findAppTemplateBindedByAppList(
            listOf(
                "appWithTemplate",
                "appWithoutTemplate",
                "appWithPresetTemplate"
            )
        )
        assert(map.size == 2)
        assert(map.get("appWithTemplate")!!.displayName == "mock-template")
        assert(map.get("appWithPresetTemplate")!!.displayName.isNotBlank())
    }

}