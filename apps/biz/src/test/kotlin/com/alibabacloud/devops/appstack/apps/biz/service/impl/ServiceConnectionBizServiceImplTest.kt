package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.ServiceConnectionBizService
import com.alibabacloud.devops.appstack.apps.biz.service.client.api.FlowServiceConnectionApi
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ConnectionType
import com.alibabacloud.devops.appstack.libs.model.response.FlowApiResponse
import com.alibabacloud.devops.appstack.libs.model.response.Response
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Order
import org.junit.jupiter.api.Test
import org.mockito.Mock
import org.mockito.kotlin.any
import org.mockito.kotlin.given
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean

/**
 * @author: <EMAIL>
 * @date: 2022-07-08 14:29
 * @version: ServiceConnectionBizServiceImplTest, v0.1
 **/
class ServiceConnectionBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var serviceConnectionBizService: ServiceConnectionBizServiceImpl

    @MockBean
    lateinit var flowServiceConnectionApi: FlowServiceConnectionApi

    @Disabled
    @Order(10)
    @Test
    fun findAllTest() {
        val list = serviceConnectionBizService.findAll(ConnectionType.ecs)
        //  assert(list.isNotEmpty())
    }

    @Disabled
    @Order(20)
    @Test
    fun findRegionsByIdTest() {
        val list = serviceConnectionBizService.findRegionsById(1L)
        //    assert(list.isNotEmpty())
    }

    @Test
    fun findByIdsTest() {
        serviceConnectionBizService.flowServiceConnectionApi = flowServiceConnectionApi
        given(flowServiceConnectionApi.findByIds(any(), any())).willReturn(FlowApiResponse(
            true, null, null, emptyList()
        ))
        val list = serviceConnectionBizService.findByIds(listOf("1", "2"))
        assert(list.isEmpty())
    }

}