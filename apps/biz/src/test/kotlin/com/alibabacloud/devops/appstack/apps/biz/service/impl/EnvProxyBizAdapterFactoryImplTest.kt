package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.EnvProxyBizAdapterFactory
import com.alibabacloud.devops.appstack.apps.biz.service.HasMetadataVO
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeableEnv
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.TaskContext
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.fabric8.kubernetes.api.model.apps.Deployment
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR>
 * @date 2023-03-18
 */
class EnvProxyBizAdapterFactoryImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var envProxyBizAdapterFactory: EnvProxyBizAdapterFactory

    @Test
    fun test() {
        val changeOrder = jacksonObjectMapper().readValue<ChangeOrder>(
            "{\"name\":\"20230318150347-部署\",\"appName\":\"oam-8\",\"type\":\"Deploy\",\"startedAt\":1679123034000,\"endedAt\":1679123066000,\"version\":\"20230318150347-545\",\"tag\":\"oam-8::20230318150351::8167218\",\"state\":\"SUCCESS\",\"overrideFinishState\":null,\"controlMode\":\"Auto\",\"description\":null,\"jobs\":[{\"sn\":\"279e1bae4ad141269d714c3affd99650\",\"name\":\"Deployr-f-f\",\"envName\":\"r-f-f\",\"envDisplayName\":null,\"appName\":\"oam-8\",\"changeableEnvSn\":\"bd28e7348ef5448d988ed28a3062d0ab\",\"engineType\":\"Ade2\",\"resourceType\":\"KUBERNETES\",\"changeOrderSn\":\"bc20c4c4352d4842b09b7a37e40e2a2d\",\"nextSn\":null,\"priority\":0,\"startedAt\":1679123034000,\"endedAt\":1679123066000,\"state\":\"SUCCESS\",\"overrideFinishState\":null,\"controlMode\":\"Auto\",\"stages\":[{\"sn\":\"740c2236907e41f3a09b740ae3216607\",\"jobSn\":\"279e1bae4ad141269d714c3affd99650\",\"engineType\":\"Ade2\",\"resourceType\":\"KUBERNETES\",\"priority\":2,\"startedAt\":1679123034000,\"endedAt\":1679123066000,\"state\":\"SUCCESS\",\"overrideFinishState\":null,\"controlMode\":\"Auto\",\"name\":\"顺序2\",\"tasks\":[{\"sn\":\"a07eccb638f040438934222ce25ca381\",\"name\":\"oam-8-r-f-f\",\"engineType\":\"Ade2\",\"resourceType\":\"KUBERNETES\",\"jobSn\":\"279e1bae4ad141269d714c3affd99650\",\"locator\":\"oam-8-r-f-f/xinfeitest/apps/v1/Deployment\",\"stageSn\":\"740c2236907e41f3a09b740ae3216607\",\"priority\":0,\"startedAt\":1679123034000,\"endedAt\":1679123065000,\"state\":\"SUCCESS\",\"overrideFinishState\":null,\"strategyContent\":\"{\\\"batches\\\":1,\\\"batchSteps\\\":[1],\\\"targetReplicas\\\":1,\\\"fromReplicas\\\":null,\\\"batchMode\\\":\\\"Auto\\\",\\\"deployType\\\":\\\"Batch\\\",\\\"deleteDeployGroup\\\":false,\\\"restraint\\\":null}\",\"contextContent\":\"{\\\"appName\\\":\\\"oam-8\\\",\\\"envName\\\":\\\"r-f-f\\\",\\\"name\\\":\\\"oam-8-r-f-f\\\",\\\"changeOrderVersion\\\":\\\"20230318150347-545\\\",\\\"artifacts\\\":{\\\"image.backend\\\":\\\"repo-registry.cn-shanghai.cr.aliyuncs.com/yunxiao-paas/nginx:v1\\\"},\\\"version\\\":\\\"v1\\\",\\\"jobType\\\":\\\"Deploy\\\",\\\"group\\\":\\\"apps\\\",\\\"namespace\\\":\\\"xinfeitest\\\",\\\"deployGroupName\\\":\\\"k8s0330\\\",\\\"planningStatus\\\":null,\\\"kind\\\":\\\"Deployment\\\",\\\"currentPassIndex\\\":0,\\\"extendParams\\\":{},\\\"locator\\\":\\\"oam-8-r-f-f/xinfeitest/apps/v1/Deployment\\\",\\\"apiVersion\\\":\\\"apps/v1\\\"}\",\"deliveries\":\"---\\napiVersion: \\\"apps/v1\\\"\\nkind: \\\"Deployment\\\"\\nmetadata:\\n  name: \\\"oam-8-r-f-f\\\"\\n  namespace: \\\"xinfeitest\\\"\\n  labels:\\n    run: \\\"oam-8-r-f-f\\\"\\nspec:\\n  replicas: 1\\n  selector:\\n    matchLabels:\\n      run: \\\"oam-8-r-f-f\\\"\\n  template:\\n    metadata:\\n      labels:\\n        run: \\\"oam-8-r-f-f\\\"\\n    spec:\\n      containers:\\n      - name: \\\"main\\\"\\n        image: \\\"repo-registry.cn-shanghai.cr.aliyuncs.com/yunxiao-paas/nginx:v1\\\"\\n        ports:\\n        - containerPort: 8080\\n        resources:\\n          limits:\\n            cpu: 0.1\\n            memory: \\\"200Mi\\\"\\n          requests:\\n            cpu: 0.01\\n            memory: \\\"32Mi\\\"\\n\",\"spec\":\"---\\napiVersion: \\\"apps/v1\\\"\\nkind: \\\"Deployment\\\"\\nmetadata:\\n  name: \\\"oam-8-r-f-f\\\"\\n  namespace: \\\"xinfeitest\\\"\\n  labels:\\n    run: \\\"oam-8-r-f-f\\\"\\n    devops.aliyun.com/app-name: \\\"oam-8\\\"\\n    devops.aliyun.com/env-name: \\\"r-f-f\\\"\\n    devops.aliyun.com/version: \\\"20230318150347-545\\\"\\n    devops.aliyun.com/org-id: \\\"1f256c07-b180-4b8e-a14e-c7bc91fc8f22\\\"\\nspec:\\n  replicas: 1\\n  selector:\\n    matchLabels:\\n      run: \\\"oam-8-r-f-f\\\"\\n  template:\\n    metadata:\\n      labels:\\n        run: \\\"oam-8-r-f-f\\\"\\n        devops.aliyun.com/app-name: \\\"oam-8\\\"\\n        devops.aliyun.com/env-name: \\\"r-f-f\\\"\\n        devops.aliyun.com/version: \\\"20230318150347-545\\\"\\n        devops.aliyun.com/org-id: \\\"1f256c07-b180-4b8e-a14e-c7bc91fc8f22\\\"\\n    spec:\\n      containers:\\n      - name: \\\"main\\\"\\n        image: \\\"repo-registry.cn-shanghai.cr.aliyuncs.com/yunxiao-paas/nginx:v1\\\"\\n        ports:\\n        - containerPort: 8080\\n        resources:\\n          limits:\\n            cpu: 0.1\\n            memory: \\\"200Mi\\\"\\n          requests:\\n            cpu: 0.01\\n            memory: \\\"32Mi\\\"\\n\",\"engineTaskKey\":\"2018c50f-7ad0-4ec9-892e-babf6f6e3150\",\"engineTaskResultContent\":\"{\\\"deliverySn\\\":\\\"2018c50f-7ad0-4ec9-892e-babf6f6e3150\\\",\\\"state\\\":\\\"SUCCESSFUL\\\",\\\"userParam\\\":{},\\\"statistics\\\":{\\\"externalOrderId\\\":null,\\\"rolloutId\\\":null,\\\"strategy\\\":\\\"Batch\\\",\\\"currentPass\\\":0,\\\"totalPasses\\\":1,\\\"passStatus\\\":\\\"SUCCESSFUL\\\",\\\"overallStatus\\\":\\\"SUCCESSFUL\\\",\\\"passStatistics\\\":null,\\\"overallStatistics\\\":null,\\\"batchStatisticList\\\":[{\\\"updatingReplicas\\\":null,\\\"updatedReplicas\\\":1,\\\"failedReplicas\\\":null,\\\"succeedReplicas\\\":null,\\\"desiredReplicas\\\":1}],\\\"purgeStatistics\\\":null,\\\"lastPass\\\":true},\\\"podRevision\\\":{\\\"newRevisions\\\":[{\\\"name\\\":\\\"oam-8-r-f-f-v4-8f76bf65b-g78l6\\\",\\\"status\\\":\\\"Running\\\",\\\"age\\\":\\\"2023-03-18T07:04:06Z\\\",\\\"deployGroupName\\\":null,\\\"namespace\\\":\\\"xinfeitest\\\",\\\"refWorkload\\\":null,\\\"revision\\\":\\\"20230318150347-545\\\"}],\\\"oldRevisions\\\":[{\\\"name\\\":\\\"oam-8-r-f-f-v1-9b696f65-b6d7z\\\",\\\"status\\\":\\\"NotExists\\\",\\\"age\\\":\\\"2023-03-18T02:38:05Z\\\",\\\"deployGroupName\\\":null,\\\"namespace\\\":\\\"xinfeitest\\\",\\\"refWorkload\\\":null,\\\"revision\\\":\\\"20230318103725-246\\\"},{\\\"name\\\":\\\"oam-8-r-f-f-v2-ff66d567b-9l8jc\\\",\\\"status\\\":\\\"NotExists\\\",\\\"age\\\":\\\"2023-03-18T02:39:05Z\\\",\\\"deployGroupName\\\":null,\\\"namespace\\\":\\\"xinfeitest\\\",\\\"refWorkload\\\":null,\\\"revision\\\":\\\"20230318103844-113\\\"},{\\\"name\\\":\\\"oam-8-r-f-f-v3-57d7855585-ds9gg\\\",\\\"status\\\":\\\"NotExists\\\",\\\"age\\\":\\\"2023-03-18T07:00:51Z\\\",\\\"deployGroupName\\\":null,\\\"namespace\\\":\\\"xinfeitest\\\",\\\"refWorkload\\\":null,\\\"revision\\\":\\\"20230318145921-484\\\"}]},\\\"serviceBrief\\\":null,\\\"purgeStatistics\\\":null,\\\"callbackParams\\\":{},\\\"taskErrorMessages\\\":null,\\\"snapshotType\\\":\\\"PACKAGE\\\",\\\"snapshotKey\\\":\\\"1f256c07-b180-4b8e-a14e-c7bc91fc8f22a07eccb638f040438934222ce25ca381\\\",\\\"engineType\\\":\\\"Ade2\\\",\\\"resourceType\\\":\\\"KUBERNETES\\\",\\\"engineTaskKey\\\":\\\"2018c50f-7ad0-4ec9-892e-babf6f6e3150\\\"}\",\"orgId\":\"1f256c07-b180-4b8e-a14e-c7bc91fc8f22\"}]}],\"type\":\"Deploy\"}],\"sn\":\"bc20c4c4352d4842b09b7a37e40e2a2d\",\"creator\":\"7d305d70-a428-4371-88ca-8263bf0d4685\",\"gmtCreate\":1679123034000,\"sourceType\":\"CUSTOMIZE\",\"sourceSn\":\"7d305d70-a428-4371-88ca-8263bf0d4685\"}"
        )
        println(jacksonObjectMapper().writeValueAsString(changeOrder))
        val kind = jacksonObjectMapper().readValue<TaskContext>(changeOrder.jobs.first().stages.first().tasks.first().contextContent).kind
        val kindResourceService = envProxyBizAdapterFactory.get(changeOrder.jobs.first().engineType, kind)
        Assertions.assertTrue(kindResourceService is Ade2DeploymentResourceService)
        val result = kindResourceService.convertHasMetadataVOList(
            changeOrder = changeOrder,
            kind = "Deployment",
            envName = "r-f-f",
            resourceList = listOf(jacksonObjectMapper().readValue<Deployment>("{\"apiVersion\":\"apps/v1\",\"kind\":\"Deployment\",\"metadata\":{\"annotations\":{\"ade.v2/desired-replicas\":\"1\",\"ade.v2/rollout-trait\":\"{\\\"properties\\\":{\\\"targetSize\\\":1,\\\"batchPartition\\\":0,\\\"rolloutBatches\\\":[{\\\"replicas\\\":1}]},\\\"type\\\":\\\"rollout\\\"}\",\"app.oam.dev/last-applied-configuration\":\"{\\\"apiVersion\\\":\\\"apps/v1\\\",\\\"kind\\\":\\\"Deployment\\\",\\\"metadata\\\":{\\\"annotations\\\":{\\\"ade.v2/desired-replicas\\\":\\\"1\\\",\\\"ade.v2/rollout-trait\\\":\\\"{\\\\\\\"properties\\\\\\\":{\\\\\\\"targetSize\\\\\\\":1,\\\\\\\"batchPartition\\\\\\\":0,\\\\\\\"rolloutBatches\\\\\\\":[{\\\\\\\"replicas\\\\\\\":1}]},\\\\\\\"type\\\\\\\":\\\\\\\"rollout\\\\\\\"}\\\",\\\"app.oam.dev/skipGC\\\":\\\"true\\\",\\\"oam.dev/kubevela-version\\\":\\\"v1.5.10\\\"},\\\"labels\\\":{\\\"app.oam.dev/appRevision\\\":\\\"oam-8-r-f-f-d2519d2a-v3\\\",\\\"app.oam.dev/component\\\":\\\"oam-8-r-f-f\\\",\\\"app.oam.dev/name\\\":\\\"oam-8-r-f-f-d2519d2a\\\",\\\"app.oam.dev/namespace\\\":\\\"tenant-ns-1f256c07-b180-4b8e-a14e-c7bc91fc8f22\\\",\\\"app.oam.dev/resourceType\\\":\\\"WORKLOAD\\\",\\\"app.oam.dev/revision\\\":\\\"oam-8-r-f-f-v4\\\",\\\"devops.aliyun.com/app-name\\\":\\\"oam-8\\\",\\\"devops.aliyun.com/env-name\\\":\\\"r-f-f\\\",\\\"devops.aliyun.com/org-id\\\":\\\"1f256c07-b180-4b8e-a14e-c7bc91fc8f22\\\",\\\"devops.aliyun.com/version\\\":\\\"20230318150347-545\\\",\\\"oam.dev/render-hash\\\":\\\"fd67b40be63ce2c\\\",\\\"run\\\":\\\"oam-8-r-f-f\\\",\\\"workload.oam.dev/type\\\":\\\"raw\\\"},\\\"name\\\":\\\"oam-8-r-f-f-v4\\\",\\\"namespace\\\":\\\"xinfeitest\\\",\\\"ownerReferences\\\":[{\\\"apiVersion\\\":\\\"standard.oam.dev/v1alpha1\\\",\\\"blockOwnerDeletion\\\":true,\\\"controller\\\":false,\\\"kind\\\":\\\"Rollout\\\",\\\"name\\\":\\\"oam-8-r-f-f\\\",\\\"uid\\\":\\\"ba277bb8-b78c-459f-8986-f77a406d9c35\\\"}]},\\\"spec\\\":{\\\"paused\\\":true,\\\"replicas\\\":0,\\\"selector\\\":{\\\"matchLabels\\\":{\\\"run\\\":\\\"oam-8-r-f-f\\\"}},\\\"template\\\":{\\\"metadata\\\":{\\\"labels\\\":{\\\"devops.aliyun.com/app-name\\\":\\\"oam-8\\\",\\\"devops.aliyun.com/env-name\\\":\\\"r-f-f\\\",\\\"devops.aliyun.com/org-id\\\":\\\"1f256c07-b180-4b8e-a14e-c7bc91fc8f22\\\",\\\"devops.aliyun.com/version\\\":\\\"20230318150347-545\\\",\\\"run\\\":\\\"oam-8-r-f-f\\\"}},\\\"spec\\\":{\\\"containers\\\":[{\\\"image\\\":\\\"repo-registry.cn-shanghai.cr.aliyuncs.com/yunxiao-paas/nginx:v1\\\",\\\"name\\\":\\\"main\\\",\\\"ports\\\":[{\\\"containerPort\\\":8080}],\\\"resources\\\":{\\\"limits\\\":{\\\"cpu\\\":0.1,\\\"memory\\\":\\\"200Mi\\\"},\\\"requests\\\":{\\\"cpu\\\":0.01,\\\"memory\\\":\\\"32Mi\\\"}}}]}}}}\",\"app.oam.dev/skipGC\":\"true\",\"deployment.kubernetes.io/revision\":\"1\",\"oam.dev/kubevela-version\":\"v1.5.10\"},\"creationTimestamp\":\"2023-03-18T07:04:06Z\",\"generation\":3,\"labels\":{\"app.oam.dev/appRevision\":\"oam-8-r-f-f-d2519d2a-v3\",\"app.oam.dev/component\":\"oam-8-r-f-f\",\"app.oam.dev/name\":\"oam-8-r-f-f-d2519d2a\",\"app.oam.dev/namespace\":\"tenant-ns-1f256c07-b180-4b8e-a14e-c7bc91fc8f22\",\"app.oam.dev/resourceType\":\"WORKLOAD\",\"app.oam.dev/revision\":\"oam-8-r-f-f-v4\",\"devops.aliyun.com/app-name\":\"oam-8\",\"devops.aliyun.com/env-name\":\"r-f-f\",\"devops.aliyun.com/org-id\":\"1f256c07-b180-4b8e-a14e-c7bc91fc8f22\",\"devops.aliyun.com/version\":\"20230318150347-545\",\"oam.dev/render-hash\":\"fd67b40be63ce2c\",\"run\":\"oam-8-r-f-f\",\"workload.oam.dev/type\":\"raw\"},\"name\":\"oam-8-r-f-f-v4\",\"namespace\":\"xinfeitest\",\"ownerReferences\":[{\"apiVersion\":\"standard.oam.dev/v1alpha1\",\"kind\":\"Rollout\",\"blockOwnerDeletion\":true,\"controller\":false,\"name\":\"oam-8-r-f-f\",\"uid\":\"ba277bb8-b78c-459f-8986-f77a406d9c35\"}],\"resourceVersion\":\"18272217\",\"uid\":\"9443a809-4dfa-43b4-9171-3c8758427bf2\"},\"spec\":{\"progressDeadlineSeconds\":600,\"replicas\":1,\"revisionHistoryLimit\":10,\"selector\":{\"matchLabels\":{\"run\":\"oam-8-r-f-f\"}},\"strategy\":{\"rollingUpdate\":{\"maxSurge\":\"25%\",\"maxUnavailable\":\"25%\"},\"type\":\"RollingUpdate\"},\"template\":{\"metadata\":{\"labels\":{\"devops.aliyun.com/app-name\":\"oam-8\",\"devops.aliyun.com/env-name\":\"r-f-f\",\"devops.aliyun.com/org-id\":\"1f256c07-b180-4b8e-a14e-c7bc91fc8f22\",\"devops.aliyun.com/version\":\"20230318150347-545\",\"run\":\"oam-8-r-f-f\"}},\"spec\":{\"containers\":[{\"image\":\"repo-registry.cn-shanghai.cr.aliyuncs.com/yunxiao-paas/nginx:v1\",\"imagePullPolicy\":\"IfNotPresent\",\"name\":\"main\",\"ports\":[{\"containerPort\":8080,\"protocol\":\"TCP\"}],\"resources\":{\"limits\":{\"cpu\":\"100m\",\"memory\":\"200Mi\"},\"requests\":{\"cpu\":\"10m\",\"memory\":\"32Mi\"}},\"terminationMessagePath\":\"/dev/termination-log\",\"terminationMessagePolicy\":\"File\"}],\"dnsPolicy\":\"ClusterFirst\",\"restartPolicy\":\"Always\",\"schedulerName\":\"default-scheduler\",\"securityContext\":{},\"terminationGracePeriodSeconds\":30}}},\"status\":{\"availableReplicas\":1,\"conditions\":[{\"lastTransitionTime\":\"2023-03-18T07:04:06Z\",\"lastUpdateTime\":\"2023-03-18T07:04:06Z\",\"message\":\"ReplicaSet \\\"oam-8-r-f-f-v4-8f76bf65b\\\" has successfully progressed.\",\"reason\":\"NewReplicaSetAvailable\",\"status\":\"True\",\"type\":\"Progressing\"},{\"lastTransitionTime\":\"2023-03-18T07:04:09Z\",\"lastUpdateTime\":\"2023-03-18T07:04:09Z\",\"message\":\"Deployment has minimum availability.\",\"reason\":\"MinimumReplicasAvailable\",\"status\":\"True\",\"type\":\"Available\"}],\"observedGeneration\":3,\"readyReplicas\":1,\"replicas\":1,\"updatedReplicas\":1}}"))
        )
        Assertions.assertEquals(1, result.size)
        Assertions.assertEquals(1, result.first().relatedResource!!.size)
        Assertions.assertEquals(HasMetadataVO.HasMetadataVOState.MATCHED, result.first().state)
    }
}