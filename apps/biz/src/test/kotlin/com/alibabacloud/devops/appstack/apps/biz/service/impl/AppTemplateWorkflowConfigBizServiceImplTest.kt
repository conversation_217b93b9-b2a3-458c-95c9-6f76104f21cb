package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.AppTemplateConfigSyncStatusMapper
import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateConfigSyncStatusPO
import com.alibabacloud.devops.appstack.libs.model.api.workflowcontroller.mock.AppTemplateWorkflowApiMock
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.RepoMeta
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.facade.AppTemplateWorkflowFacade
import com.baomidou.mybatisplus.core.conditions.Wrapper
import com.baomidou.mybatisplus.core.metadata.IPage
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.io.Serializable

/**
 * @author: <EMAIL>
 * @date: 2024-07-09 11:41
 * @version: AppTemplateWorkflowConfigBizServiceImplTest, v0.1
 **/
class AppTemplateWorkflowConfigBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var appTemplateWorkflowConfigBizServiceImpl: AppTemplateWorkflowConfigBizServiceImpl

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    lateinit var appTemplateWorkflowFacadeBackup: AppTemplateWorkflowFacade

    lateinit var appTemplateConfigSyncStatusMapperBackup: AppTemplateConfigSyncStatusMapper

    @BeforeEach
    fun setup() {
        appTemplateWorkflowFacadeBackup = workflowControllerFacade.appTemplateWorkflowFacade
        workflowControllerFacade.appTemplateWorkflowFacade = MockAppTemplateWorkflowFacade()

        appTemplateConfigSyncStatusMapperBackup = appTemplateWorkflowConfigBizServiceImpl.appTemplateConfigSyncStatusMapper
        appTemplateWorkflowConfigBizServiceImpl.appTemplateConfigSyncStatusMapper = MockAppTemplateConfigSyncStatusMapper()
    }

    @AfterEach
    fun teardown() {
        workflowControllerFacade.appTemplateWorkflowFacade = appTemplateWorkflowFacadeBackup
        appTemplateWorkflowConfigBizServiceImpl.appTemplateConfigSyncStatusMapper = appTemplateConfigSyncStatusMapperBackup
    }

    @Test
    fun should_find_app_template_workflow_config_revisions() {
        // when
        val results = appTemplateWorkflowConfigBizServiceImpl.findPaginatedRevision("tmpl", "app", 1, 20)

        // then
        assert(results.total == 1L)
    }

}

class MockAppTemplateWorkflowFacade : AppTemplateWorkflowFacade(AppTemplateWorkflowApiMock()) {
    override fun findPaginatedRevision(
        appTemplateName: String,
        name: String,
        current: Long,
        pageSize: Long,
    ): Pagination<Revision> {
        val revision = Revision(
            repoMeta = RepoMeta("name", "type"),
            sha = "sha",
            message = "message",
            author = "author",
        )
        return Pagination(
            total = 1L,
            current = 1L,
            pageSize = 20L,
            pages = 1L,
            records = listOf(revision)
        )
    }
}

class MockAppTemplateConfigSyncStatusMapper : AppTemplateConfigSyncStatusMapper {
    override fun updateTemplateSyncStatus(
        templateName: String,
        configType: String,
        configInstanceName: String,
        appName: String,
        templateSyncStatus: String
    ) {
        TODO("Not yet implemented")
    }

    override fun updateInstanceUpdateStatus(
        templateName: String,
        configType: String,
        configInstanceName: String,
        appName: String,
        lastInstanceUpdateSeqNo: Long,
        instanceUpdateStatus: String
    ) {
        TODO("Not yet implemented")
    }

    override fun insert(entity: AppTemplateConfigSyncStatusPO?): Int {
        TODO("Not yet implemented")
    }

    override fun deleteById(id: Serializable?): Int {
        TODO("Not yet implemented")
    }

    override fun deleteById(entity: AppTemplateConfigSyncStatusPO?): Int {
        TODO("Not yet implemented")
    }

    override fun deleteByMap(columnMap: MutableMap<String, Any>?): Int {
        TODO("Not yet implemented")
    }

    override fun delete(queryWrapper: Wrapper<AppTemplateConfigSyncStatusPO>?): Int {
        TODO("Not yet implemented")
    }

    override fun deleteBatchIds(idList: MutableCollection<out Serializable>?): Int {
        TODO("Not yet implemented")
    }

    override fun updateById(entity: AppTemplateConfigSyncStatusPO?): Int {
        TODO("Not yet implemented")
    }

    override fun update(
        entity: AppTemplateConfigSyncStatusPO?,
        updateWrapper: Wrapper<AppTemplateConfigSyncStatusPO>?
    ): Int {
        TODO("Not yet implemented")
    }

    override fun selectById(id: Serializable?): AppTemplateConfigSyncStatusPO {
        TODO("Not yet implemented")
    }

    override fun selectBatchIds(idList: MutableCollection<out Serializable>?): MutableList<AppTemplateConfigSyncStatusPO> {
        TODO("Not yet implemented")
    }

    override fun selectByMap(columnMap: MutableMap<String, Any>?): MutableList<AppTemplateConfigSyncStatusPO> {
        TODO("Not yet implemented")
    }

    override fun selectCount(queryWrapper: Wrapper<AppTemplateConfigSyncStatusPO>?): Long {
        TODO("Not yet implemented")
    }

    override fun selectList(queryWrapper: Wrapper<AppTemplateConfigSyncStatusPO>?): MutableList<AppTemplateConfigSyncStatusPO> {
        val po = AppTemplateConfigSyncStatusPO()

        po.appTemplateName = "appTemplateName"
        po.appTemplateConfigType = "ReleaseWorkflow"
        po.appTemplateConfigInstanceName = "appTemplateConfigType"
        po.lastInstanceUpdateSeqNo = System.currentTimeMillis()
        po.templateSyncStatus = "{\"lastAppliedTemplateRevisionSha\":\"b5184afa97e7b9acaba9dcf97f962b80636a404f\",\"lastAppliedAppRevisionSha\":\"244e47a39554537c877f48752f6baacbfeb0164f\",\"lastSyncErrorMessage\":null}"
        po.instanceUpdateStatus = "{\"actualAppRevisionSha\":\"244e47a39554537c877f48752f6baacbfeb0164f\",\"actualAppRevisionGmtModified\":1720011407748,\"actualAppRevisionCommitMessage\":\"同步模版[x][y[ b5184afa97e7b9acaba9dcf97f962b80636a404f\",\"actualAppRevisionCommitAuthor\":\"65fc01076d032cfbba309d79\"}"
        po.appName = "app"

        return mutableListOf(po)
    }

    override fun selectMaps(queryWrapper: Wrapper<AppTemplateConfigSyncStatusPO>?): MutableList<MutableMap<String, Any>> {
        TODO("Not yet implemented")
    }

    override fun selectObjs(queryWrapper: Wrapper<AppTemplateConfigSyncStatusPO>?): MutableList<Any> {
        TODO("Not yet implemented")
    }

    override fun <P : IPage<AppTemplateConfigSyncStatusPO>?> selectPage(
        page: P,
        queryWrapper: Wrapper<AppTemplateConfigSyncStatusPO>?
    ): P {
        TODO("Not yet implemented")
    }

    override fun <P : IPage<MutableMap<String, Any>>?> selectMapsPage(
        page: P,
        queryWrapper: Wrapper<AppTemplateConfigSyncStatusPO>?
    ): P {
        TODO("Not yet implemented")
    }
}

fun MockAppTemplateConfigSyncStatusMapper.listByTemplate(
    appTemplateName: String,
    appTemplateConfigType: String,
    appTemplateConfigInstanceName: String,
): List<AppTemplateConfigSyncStatusPO> {
    val po = AppTemplateConfigSyncStatusPO()

    po.appTemplateName = appTemplateName
    po.appTemplateConfigType = appTemplateConfigType
    po.appTemplateConfigInstanceName = appTemplateConfigInstanceName
    po.lastInstanceUpdateSeqNo = System.currentTimeMillis()
    po.templateSyncStatus = "{\"lastAppliedTemplateRevisionSha\":\"b5184afa97e7b9acaba9dcf97f962b80636a404f\",\"lastAppliedAppRevisionSha\":\"244e47a39554537c877f48752f6baacbfeb0164f\",\"lastSyncErrorMessage\":null}"
    po.instanceUpdateStatus = "{\"actualAppRevisionSha\":\"244e47a39554537c877f48752f6baacbfeb0164f\",\"actualAppRevisionGmtModified\":1720011407748,\"actualAppRevisionCommitMessage\":\"同步模版[x][y[ b5184afa97e7b9acaba9dcf97f962b80636a404f\",\"actualAppRevisionCommitAuthor\":\"65fc01076d032cfbba309d79\"}"
    po.appName = "app"

    return listOf(po)
}