package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.KubernetesCheckOutputItem
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateCheckRequest
import com.fasterxml.jackson.module.kotlin.readValue
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test

import org.springframework.beans.factory.annotation.Autowired
import javax.annotation.PostConstruct

/**
 * <AUTHOR> <EMAIL>
 * @version : OrchestrationBaseBizServiceTest, v0.1
 * @date : 2023-07-26 14:18
 */
class OrchestrationBaseBizServiceTest : BizServerApplicationTests() {

    @Autowired
    lateinit var i18nMessageService: I18nMessageService

    lateinit var orchestrationBaseBizService: OrchestrationBaseBizService

    @PostConstruct
    fun postConstruct() {
        orchestrationBaseBizService = OrchestrationBaseBizService(i18nMessageService)
    }

    @Test
    fun checkInvalid() {
        val objectMapper = JacksonUtil.jacksonObjectMapper()

        val request: OrchestrationTemplateCheckRequest = objectMapper.readValue(
            javaClass.getResource("/orchestration/orchestration-template-check.json")!!.readText()
        )
        val components = request.componentList.toMutableList()
        components.add(components.last())

        val node = objectMapper.readTree(
            javaClass.getResource("/orchestration/orchestration-template-check-output.json")!!.readText()
        )
        val items = node.get("items").map { objectMapper.convertValue(it, KubernetesCheckOutputItem::class.java) }.toMutableList()
        items.add(items.last())

        val invalidList = orchestrationBaseBizService.checkInvalid(
            checkOutputItems = items,
            componentList = components,
        )
        val invalids = setOf(
            "类型为Deployment的组件[deploy, deploy, deploy, deploy]存在metadata.name重复",
            "存在重名组件deploy",
            "暂不支持多个deployment在同一个阶段内，请调整组件顺序"
        )
        Assertions.assertEquals(invalids, invalidList.map { it.message }.toSet())
    }
}