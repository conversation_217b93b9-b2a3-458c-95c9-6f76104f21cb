package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.OrgConfigItemBizService
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR> <EMAIL>
 * @version : OrgConfigItemBizServiceImplTest, v0.1
 * @date : 2024-06-17 16:54
 */
class OrgConfigItemBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var orgConfigItemBizService: OrgConfigItemBizService

    @Test
    fun findAll() {
        val list = orgConfigItemBizService.findAll()
        Assertions.assertEquals(4, list.size)
    }
}