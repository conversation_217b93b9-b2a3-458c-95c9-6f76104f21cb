package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateBizService
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.ng.ResourceInstanceQuery
import com.alibabacloud.devops.iam.constant.ProtocolType
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers.*
import org.mockito.kotlin.*
import org.mockito.kotlin.any
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.mock.mockito.MockBean

/**
 * <AUTHOR> <EMAIL>
 * @version : ResourceBizServiceImplTest, v0.1
 * @date : 2023-04-20 12:00
 */
class ResourceBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var resourceBizServiceImpl: ResourceBizServiceImpl

    @MockBean
    lateinit var iamService: IamServiceImpl

    @MockBean
    lateinit var auditLogService: AuditLogService

    @MockBean
    lateinit var appTemplateBizService: AppTemplateBizService

    @Test
    fun test() {
        var instance = resourceBizServiceImpl.findInstance("mock-pool-name", "mock-instance-name")
        Assertions.assertEquals("mock-kubeConfig", instance.contextMap["content"])
        instance = resourceBizServiceImpl.findInstanceWithEditPermission("mock-pool-name", "mock-instance-name")
        Assertions.assertEquals("mock-kubeConfig", instance.contextMap["content"])
        instance = resourceBizServiceImpl.findInstanceWithViewPermission("mock-pool-name", "mock-instance-name")
        Assertions.assertEquals(null, instance.contextMap["content"])
    }

    @Test
    fun findAllInstanceCanUse_WithoutCan_ReturnsFilteredInstances() {
        // Arrange
        val poolName = "testPool"
        val type = "testType"
        val userId = AuthThreadContext.getUserId()!!
        val tenant = AuthThreadContext.getTenant()!!

        given(iamService.can(ProtocolType.AppStack, "any", Action.ORG_RESOURCE_MANAGE)).willReturn(false)
        given(iamService.getTeamPlayer(tenant, userId)).willReturn(emptyList())
        given(iamService.getGroupPlayer(tenant, userId)).willReturn(emptyList())
        given(iamService.searchResourceWithPolicy(
            any(), anyList(), anyString(), anyString(), anyInt(), anyInt()
        )).willReturn(listOf("mock"))

        // Act
        val result = resourceBizServiceImpl.findAllInstanceCanUse(poolName, type)

        // Assert
        verify(iamService).can(any(), anyString(), any())
        verify(iamService).getTeamPlayer(tenant, userId)
        verify(iamService).getGroupPlayer(tenant, userId)
        verify(iamService).searchResourceWithPolicy(
            any(), anyList(), anyString(), anyString(), anyInt(), anyInt()
        )
        Assertions.assertEquals(0, result.size)
    }

    @Test
    fun test_delete_resourceInstance(){
        val poolName = "pool1"
        val instanceName = "instance1"
        doNothing().`when`(auditLogService).log(any(), any())
        given(appTemplateBizService.searchByNames(any())).willReturn(emptyMap())
        resourceBizServiceImpl.deleteResourceInstance(poolName, instanceName)
        verify(auditLogService, times(1)).log(any(), any())
    }

    @Test
    fun test_delete_resourceInstance_forbidden_dueTo_related_others(){
        val poolName = "pool1"
        val instanceName = "instance1"
        doNothing().`when`(auditLogService).log(any(), any())
        given(appTemplateBizService.searchByNames(any())).willReturn(mapOf("a" to AppTemplate(name = "a", displayName = "aa")))
        try {
            resourceBizServiceImpl.deleteResourceInstance(poolName, instanceName)
        }catch (e: Exception){
            Assertions.assertEquals(ErrorCode.AS_RES_INST_RELATED_APP_TEMPLATE_OR_ENV, (e as BizException).errorEntry.code)
        }

    }

    @Test
    fun listInstances_query_not_pass(){
        val query = ResourceInstanceQuery(pagination = "xx", orderBy = "id")
        try{
            resourceBizServiceImpl.listInstances("default", query)
        }catch (e: BizException){
            Assertions.assertEquals(ErrorCode.AS_PAGINATION_PARAM_INVALID, e.errorEntry.code)
        }
    }

}