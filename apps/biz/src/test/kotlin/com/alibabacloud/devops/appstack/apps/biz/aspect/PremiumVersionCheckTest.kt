package com.alibabacloud.devops.appstack.apps.biz.aspect

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import org.aspectj.lang.JoinPoint
import org.aspectj.lang.Signature
import org.aspectj.lang.reflect.SourceLocation
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired


/**
 * @author: <EMAIL>
 * @date: 2023-06-09 16:10
 * @version: PremiumVersionCheckTest, v0.1
 **/
class PremiumVersionCheckTest : BizServerApplicationTests() {

    @Autowired
    lateinit var premiumVersionCheck: PremiumVersionCheck

    @Test
    fun testPass() {
        assertDoesNotThrow {
            premiumVersionCheck.preCheck(
                MockJoinPoint()
            )
        }
    }

    class MockJoinPoint : JoinPoint {
        override fun toShortString(): String {
            TODO("Not yet implemented")
        }

        override fun toLongString(): String {
            TODO("Not yet implemented")
        }

        override fun getThis(): Any {
            TODO("Not yet implemented")
        }

        override fun getTarget(): Any {
            TODO("Not yet implemented")
        }

        override fun getArgs(): Array<Any> {
            TODO("Not yet implemented")
        }

        override fun getSignature(): Signature {
            TODO("Not yet implemented")
        }

        override fun getSourceLocation(): SourceLocation {
            TODO("Not yet implemented")
        }

        override fun getKind(): String {
            TODO("Not yet implemented")
        }

        override fun getStaticPart(): JoinPoint.StaticPart {
            TODO("Not yet implemented")
        }
    }
}