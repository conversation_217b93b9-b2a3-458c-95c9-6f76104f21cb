package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.BizServerApplicationTests
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateConfigBizService
import com.alibabacloud.devops.appstack.apps.biz.service.impl.AppTemplateServiceImpl.Companion.PRESET_TEMPLATE_NAMES
import com.alibabacloud.devops.appstack.libs.model.`do`.app.*
import com.alibabacloud.devops.appstack.libs.model.`do`.apptemplate.ArtifactRepoTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.apptemplate.CodeRepoTemplate
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.SourceConfigurationUpsertRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateConfigRequest
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.junit.jupiter.api.*
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR>
 * @create 2024/4/25 11:43 AM
 **/
@TestMethodOrder(MethodOrderer.OrderAnnotation::class)
class AppTemplateConfigBizServiceImplTest : BizServerApplicationTests() {

    @Autowired
    lateinit var appTemplateConfigBizService: AppTemplateConfigBizService


    companion object {
        val MOCK_APPTEMPLATE_NAME = "mock-template"
        val CODE_REPO_TEMPLATES = listOf(CodeRepoTemplate(
            name = "test",
            repoUrl = "http://test.git",
            identifier = "test",
            repoContext = CodeupRepoContext(projectId = "0"),
            connectionConfig = FlowConnectionConfig("")
        ))
        val ARTIFACT_REPO_TEMPLATES = listOf(ArtifactRepoTemplate(
            identifier = "test",
            repoContext = CustomDockerRegistryContext(repoUrl = "", defaultVersion = ""),
            connectionConfig = FlowConnectionConfig("")
        ))
    }

    @Test
    @Order(10)
    fun createTest() {
        val config = appTemplateConfigBizService.upsert(MOCK_APPTEMPLATE_NAME, AppTemplateConfig.TypeEnum.Source, UpsertAppTemplateConfigRequest(
            modeSetting = SourceConfigModeSetting(),
            configuration = SourceConfigurationUpsertRequest(
                codeRepos = CODE_REPO_TEMPLATES,
                artifactRepos = ARTIFACT_REPO_TEMPLATES
            )
        ))
        assert(config.type == AppTemplateConfig.TypeEnum.Source)
        assert(config.modeSetting is SourceConfigModeSetting)
        assert(config.configuration is SourceConfiguration)
        val configuration = config.configuration as SourceConfiguration
        Assertions.assertEquals(jacksonObjectMapper().writeValueAsString(configuration.codeRepos),
            jacksonObjectMapper().writeValueAsString(CODE_REPO_TEMPLATES))
        Assertions.assertEquals(jacksonObjectMapper().writeValueAsString(configuration.artifactRepos),
            jacksonObjectMapper().writeValueAsString(ARTIFACT_REPO_TEMPLATES))
    }

    @Test
    @Order(20)
    fun findTest() {
        val config = appTemplateConfigBizService.find(MOCK_APPTEMPLATE_NAME, AppTemplateConfig.TypeEnum.Source)
        assert(config != null)
        assert(config!!.type == AppTemplateConfig.TypeEnum.Source)
        assert(config.modeSetting is SourceConfigModeSetting)
        assert(config.configuration is SourceConfiguration)
        val configuration = config.configuration as SourceConfiguration
        Assertions.assertEquals(jacksonObjectMapper().writeValueAsString(configuration.codeRepos),
            jacksonObjectMapper().writeValueAsString(CODE_REPO_TEMPLATES))
        Assertions.assertEquals(jacksonObjectMapper().writeValueAsString(configuration.artifactRepos),
            jacksonObjectMapper().writeValueAsString(ARTIFACT_REPO_TEMPLATES))
    }

    @Test
    @Order(20)
    fun findPresetTemplateTest() {
        val config = appTemplateConfigBizService.find(PRESET_TEMPLATE_NAMES[0], AppTemplateConfig.TypeEnum.Source)
        assert(config != null)
        assert(config!!.type == AppTemplateConfig.TypeEnum.Source)
        assert(config.modeSetting is SourceConfigModeSetting)
        assert(config.configuration is SourceConfiguration)
    }

    @Test
    @Order(30)
    fun updateTest() {
        val config = appTemplateConfigBizService.upsert(MOCK_APPTEMPLATE_NAME, AppTemplateConfig.TypeEnum.Source, UpsertAppTemplateConfigRequest(
            modeSetting = SourceConfigModeSetting(),
            configuration = SourceConfigurationUpsertRequest(
                codeRepos = CODE_REPO_TEMPLATES,
                artifactRepos = emptyList()
            )
        ))
        assert(config.type == AppTemplateConfig.TypeEnum.Source)
        assert(config.modeSetting is SourceConfigModeSetting)
        assert(config.configuration is SourceConfiguration)
        val configuration = config.configuration as SourceConfiguration
        Assertions.assertEquals(jacksonObjectMapper().writeValueAsString(configuration.codeRepos),
            jacksonObjectMapper().writeValueAsString(CODE_REPO_TEMPLATES))
        Assertions.assertEquals(configuration.artifactRepos.size, 0)
    }

    @Test
    @Order(40)
    fun deleteTest() {
        var config = appTemplateConfigBizService.find(MOCK_APPTEMPLATE_NAME, AppTemplateConfig.TypeEnum.Source)
        assert(config != null)
        assert(config!!.sn != null)
        appTemplateConfigBizService.delete(MOCK_APPTEMPLATE_NAME, AppTemplateConfig.TypeEnum.Source, config!!.sn!!)
        config = appTemplateConfigBizService.find(MOCK_APPTEMPLATE_NAME, AppTemplateConfig.TypeEnum.Source)
        assert(config == null)
    }
}