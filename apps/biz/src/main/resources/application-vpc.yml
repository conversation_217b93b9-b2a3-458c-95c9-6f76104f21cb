logging:
  config: classpath:log4j2.xml
server:
  port: ${SERVER_PORT:8080}
rocketmq:
  enabled: false
devops:
  appstack:
    core:
      base-url: http://127.0.0.1:8060/api/v1/
#      base-url: http://appstack-core/api/v1/
      mock: false
    change-controller:
      #      base-url: http://127.0.0.1:8070/api/v1/
      base-url: http://appstack-change-controller/api/v1/
      mock: false
    broadcast:
      type: MOCK
      accessKey: MOCK
      secretKey: MOCK
      nameSrvAddr: MOCK
      topic: MOCK
      groupId: MOCK
  iam:
    mock: false
    enable: true
    #    base-url: ${IAM_BASE_URL:http://127.0.0.1:8888/api/v1/}
    base-url: http://appstack-iam/api/v1/
    filter-url-pattern:
      - /api/*
      - /inner/api/*
    timeout: 10000
    cache-lifetime: 5
    #  for yunxiao v5
    environment: vpc
    apiHost: http://devops-nezha.yunxiao.svc.cluster.local:9080
    appId: 09347fb6-79e1-4dfe-8ff4-6761ab85262c
    appSecret: 5f0e30e7-60c1-499a-a55b-49db1f80d222
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
spring:
  datasource:
    #    url: *****************************************************************************
    #    username: dev
    #    password: 123456
#    url: ************************************************************************************************************************************
    url: ***************************************************************************************************************************************************
    username: root
    password: YxRoot#500
    driver-class-name: com.mysql.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
  #  config:
  #    import: iam.yml
  jackson:
    deserialization:
      fail-on-unknown-properties: false
service:
  flow-execution:
    url: ${FLOW_URL:http://aone-execution-component.flow}
    callback-url: ${FLOW_CALLBACK_URL:http://appstack-web.appstack/openapi/task/agent-install/callback}
    secret: ${FLOW_SECRET:Ex62MR1uhRiZXFVV}
    exec-shell: exec-shell-staging
  flow-service-connection:
    url: ${FLOW_SERVER_URL:http://rdc-tb-web.flow}
  org:
    apiHost: ${SSO_API_HOST:http://devops-nezha.yunxiao.svc.cluster.local:9080}
    appId: ${TB_APP_ID:09347fb6-79e1-4dfe-8ff4-6761ab85262c}
    appSecret: ${TB_APP_SECRET:5f0e30e7-60c1-499a-a55b-49db1f80d222}
    environment: ${CONFIG_ENVIRONMENT:vpc}

mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: isDeleted  # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: unix_timestamp() # 逻辑已删除值(默认为 1)
      logic-not-delete-value: N # 逻辑未删除值(默认为 0)
    banner: false

tb:
  sso:
    appId: ${TB_APP_ID:09347fb6-79e1-4dfe-8ff4-6761ab85262c}
    appSecret: ${TB_APP_SECRET:5f0e30e7-60c1-499a-a55b-49db1f80d222}

oneapi:
  accessKey: 612303b65d430304db5dfb8b
  accessSecret: EspfQ_905X-qErdakNu4N8UH

yunxiao:
  web:
    disable-yunxiao-locale-resolver=true