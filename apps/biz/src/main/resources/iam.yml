devops:
  iam:
    enable: ${IAM_ENABLE:true}
    mock: ${IAM_MOCK:false}
    base-url: ${IAM_BASE_URL:http://iam.devops-stack.svc.cluster.local/api/v1/}
    black-list: ${IAM_BLACK_LIST:}
    white-list: ${IAM_WHITE_LIST:}
    filter-url-pattern:
      - /api/*
      - /inner/api/*
      - /openapi/*
      - /forward/*
      - /innerapi/*
    timeout: ${IAM_TIMEOUT:500}
    cache-lifetime: -1
    #  for yunxiao v5
    environment: ${CONFIG_ENVIRONMENT:aliyun}
    appId: ${TB_APP_ID:5d2749f3-b520-4b6f-a8ff-2b9219d4af43}
    appSecret: ${TB_APP_SECRET:e58352d2-69cd-465e-8d7b-eaca9d46e3d3}
    apiHost: ${SSO_API_HOST:http://devops-nezha.yunxiao.svc.cluster.local:9080}