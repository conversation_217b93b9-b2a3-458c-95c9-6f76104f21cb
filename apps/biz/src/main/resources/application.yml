logging:
  config: classpath:log4j2.xml
devops:
  appstack:
    notice: ${APPSTACK_NOTICE:[]}
    oam:
      image:
        repository: ${APPSTACK_OAM_IMAGE_REPOSITORY:oamdev/vela-rollout}
        tag: ${APPSTACK_OAM_IMAGE_TAG:v1.2.1}
        pullSecretName: ${APPSTACK_OAM_IMAGE_SECRET_NAME:""}
    kruise:
      image:
        repository: ${APPSTACK_KRUISE_IMAGE_REPOSITORY:rdc-2020-registry.cn-hangzhou.cr.aliyuncs.com/alibabacloud-appstack-oam/vela-core}
        tag: ${APPSTACK_KRUISE_IMAGE_TAG:v0.4.0-krd}
        pullSecretName: ${APPSTACK_OAM_IMAGE_SECRET_NAME:""}
    domain_with_schema: ${APPSTACK_DOMAIN_WITH_SCHEMA:https://devops.aliyun.com}
    baseDomain: ${APPSTACK_BASE_DOMAIN:aliyun.com}
    core:
      base-url: ${APPSTACK_CORE_BASE_URL:http://appstack-core/api/v1/}
    web:
      base-url: ${APPSTACK_WEB_BASE_URL:http://appstack-web}
    change-controller:
      base-url: ${APPSTACK_CHANGE_CONTROLLER_BASE_URL:http://appstack-change-controller/api/v1/}
    resource-manager:
      base-url: ${APPSTACK_RESOURCE_MANAGER_BASE_URL:http://appstack-resource-manager-ng/api/v3/}
    integration:
      base-url: ${APPSTACK_INTEGRATION_BASE_URL:http://appstack-integration/api/v1/}
    environment: ${CONFIG_ENVIRONMENT:aliyun}
    broadcast:
      type: MQ
      accessKey: ${MQ_ACCESS_KEY:mockKey}
      secretKey: ${MQ_SECRET_KEY:mockSecret}
      nameSrvAddr: ${MQ_NAME_SRV_ADDR:mockSrvAddr}
      topic: ${BROADCAST_MQ_TOPIC:mockTopic}
      groupId: ${BROADCAST_MQ_GROUP_ID:mockGroupId}
    unicast:
      type: MQ
      accessKey: ${MQ_ACCESS_KEY:mockKey}
      secretKey: ${MQ_SECRET_KEY:mockSecret}
      nameSrvAddr: ${MQ_NAME_SRV_ADDR:mockSrvAddr}
      topic: ${UNICAST_MQ_TOPIC:mockTopic}
      groupId: ${UNICAST_MQ_GROUP_ID:mockGroupId}
    internal-adapter:
      baseUrl: http://appstack-aliyun-internal-adapter
    lock:
      enabled: true
      type: dbv2
springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  packages-to-scan: com.alibabacloud.devops.appstack.apps.biz.api.rest.ng
frontend:
  tb:
    sso:
      appId: ${TB_APP_ID:60964c32f44831d666bdd11a}
      appSecret: ${TB_APP_SECRET:BOe4r3BBOOqMNITyRr8wDk9wIEgRA0sd}
  environment: ${CONFIG_ENVIRONMENT:aliyun}
spring:
  application:
    name: appstack-biz
  datasource:
    biz:
      driver-class-name: com.mysql.jdbc.Driver
      url: ${JDBC_URL}
      username: ${JDBC_USERNAME}
      password: ${JDBC_PASSWORD}
      encrypt-type: ${DATABASE_ACCOUNT_ENCRYPT_TYPE:SM2}
      encrypted-pubkey: ${DATABASE_ACCOUNT_ENCRYPTED_PUBKEY:04f02bd31c7bb3877c63cc02537edda9c4b08bcc4eff33b059ff392b865ed3ba51fc2ae293d4ba927494e46d493899e835ce9ad319e3c7f552fb2245c92da3fb97}
      maxActive: ${JDBC_MAX_ACTIVE:8}
  config:
    import: iam.yml
  jackson:
    deserialization:
      fail-on-unknown-properties: false
management:
  endpoint:
    prometheus:
      enabled: true
  endpoints:
    web:
      exposure:
        include: "*"
        exclude: "shutdown"
  metrics:
    tags:
      application: "appstack-biz"
      product: "appstack"
service:
  flow:
    build-node-group: ${SERVICE_FLOW_BUILD_NODE_GROUP:K8S-19}
    url: ${FLOW_SERVER_URL:https://pre-devops.aliyuncs.com}
    forward-map: >-
      {
       "/ec/ajax/pipelines/queryByRegion": {
         "replacement": "/inner/api/pop/v2/pipelines/queryByRegion",
         "method": "GET",
         "permissions": [
           "NO_PERMISSION"
         ]
       },
        "/ec/ajax/pipeline/(?<segment>[^/]*)/getPipelineBaseInfo": {
          "replacement": "/inner/api/pipelines/$\\{segment}/baseInfo",
          "method": "GET",
          "permissions": [
            "tb.flow.pipeline.view"
          ]
        },
        "/ec/ajax/pipelines/(?<pipelineId>[^/]*)/stages/(?<stageId>[^/]*)/execute": {
          "replacement": "/inner/api/pipelines/$\\{pipelineId}/stages/$\\{stageId}/run",
          "method": "POST",
          "permissions": [
            "tb.flow.pipeline.run"
          ]
        },
        "/ec/ajax/pipelines/(?<pipelineId>[^/]*)/instances/(?<instanceId>[^/]*)/cancel": {
          "replacement": "/inner/api/pipelines/$\\{pipelineId}/instances/$\\{instanceId}/cancel",
          "method": "POST",
          "permissions": [
            "tb.flow.pipeline.run"
          ]
        },
        "/ec/ajax/pipeline:getPipelineRun": {
          "replacement": "/inner/api/pop/pipeline/run",
          "method": "POST",
          "permissions": [
            "tb.flow.pipeline.view"
          ]
        },
        "/ec/ajax/pipeline:passPipelineValidate": {
          "replacement": "/inner/api/pop/pipeline/pipelineValidate/pass",
          "method": "POST",
          "permissions": [
            "tb.flow.pipeline.run"
          ]
        },
        "/ec/ajax/pipeline:refusePipelineValidate": {
          "replacement": "/inner/api/pop/pipeline/pipelineValidate/refuse",
          "method": "POST",
          "permissions": [
            "tb.flow.pipeline.run"
          ]
        },
       "/ec/ajax/pipelines/(?<segment>.*)": {
          "replacement": "/inner/api/pipelines/$\\{segment}",
          "method": "GET",
          "permissions": [
            "tb.flow.pipeline.view"
          ]
        },
        "/flow/service/pipeline/cache/clean": {
            "replacement": "/inner/api/pop/pipeline/cleanPipelineCache",
            "method": "POST",
            "permissions": [
              "tb.flow.pipeline.edit"
            ]
        },
        "/execution-component/logList": {
            "replacement": "/inner/api/v2/execution-component/logList",
            "method": "GET",
            "permissions": [
              "tb.flow.pipeline.view"
            ]
        },
        "/execution-component/log": {
            "replacement": "/inner/api/v2/execution-component/log",
            "method": "GET",
            "permissions": [
              "tb.flow.pipeline.view"
            ]
        },
      "/execution-component/downloadLog": {
            "replacement": "/inner/api/v2/execution-component/downloadLog",
            "method": "GET",
            "permissions": [
              "tb.flow.pipeline.view"
            ]
        },
        "/execution-component/downloadLogV2": {
            "replacement": "/inner/api/v2/execution-component/downloadLogV2",
            "method": "GET",
            "permissions": [
              "tb.flow.pipeline.view"
            ]
        },
        "/execution-component/getDebugInfo": {
            "replacement": "/inner/api/v2/getDebugInfo",
            "method": "GET",
            "permissions": [
              "tb.flow.pipeline.view"
            ]
        }
      }
    operation-map: >-
      {
        "cancelExecutionReleaseStage": {
            "method": "POST",
            "servletPath": "/forward/flow/ec/ajax/pipelines/$\\{pipelineId}/instances/$\\{instanceId}/cancel",
            "queryString": ""
        },
        "getPipelineRun": {
            "method": "POST",
            "servletPath": "/forward/flow/ec/ajax/pipeline:getPipelineRun",
            "queryString": "pipelineId=$\\{pipelineId}&pipelineRunId=$\\{pipelineRunId}"
        },
        "passPipelineValidate": {
            "method": "POST",
            "servletPath": "/forward/flow/ec/ajax/pipeline:passPipelineValidate",
            "queryString": "pipelineId=$\\{pipelineId}&pipelineRunId=$\\{pipelineRunId}&jobId=$\\{jobId}"
        },
        "refusePipelineValidate": {
            "method": "POST",
            "servletPath": "/forward/flow/ec/ajax/pipeline:refusePipelineValidate",
            "queryString": "pipelineId=$\\{pipelineId}&pipelineRunId=$\\{pipelineRunId}&jobId=$\\{jobId}"
        }
      }
  flow-execution:
    url: ${FLOW_URL:https://pre-devops.aliyuncs.com}
    callback-url: ${FLOW_CALLBACK_URL:https://devops.aliyun.com/appstack/callbackapi/task/agent-install/callback}
    secret: ${FLOW_SECRET:GkeHi9ULuyJT8KRC}
    exec-shell: exec-shell-staging
  flow-service-connection:
    url: ${FLOW_SERVER_URL:https://pre-devops.aliyuncs.com}
  codeup:
    url: ${SERVER_CODEUP_URL:https://codeup-api.aliyun.com}
    clientSecret: ${SERVER_CODEUP_CLIENT_SECRET:e433294edea39cae7e78708b134}
    clientId: ${SERVER_CODEUP_CLIENT_ID:bc7e0b3c00a3e58f46f623}
  v3codeup:
    clientSecret: ${SERVER_V3CODEUP_CLIENT_SECRET:e55cf75fb4b887fe91fb4cb98}
    clientId: ${SERVER_V3CODEUP_CLIENT_ID:9138d494047858bb110428f94}
  projex:
    base-url: ${PROJEX_BASE_URL:https://devops.aliyun.com}
    mock: ${PROJEX_MOCK:false}
    secret: ${PROJEX_SECRET:f56d14f9ef5fb7952bf613739918633a}
  org:
    apiHost: ${SSO_API_HOST:http://api-devops.aliyun-inc.com/tbs}
    appId: ${TB_APP_ID:60964c32f44831d666bdd11a}
    appSecret: ${TB_APP_SECRET:BOe4r3BBOOqMNITyRr8wDk9wIEgRA0sd}
    environment: ${CONFIG_ENVIRONMENT:aliyun}
mybatis-plus:
  global-config:
    db-config:
      logic-delete-field: isDeleted  # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: unix_timestamp() # 逻辑已删除值(默认为 1)
      logic-not-delete-value: N # 逻辑未删除值(默认为 0)
    banner: false
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志
feign:
  client:
    config:
      default:
        connectTimeout: 500
        readTimeout: 500
tb:
  sso:
    appId: ${TB_APP_ID:60964c32f44831d666bdd11a}
    appSecret: ${TB_APP_SECRET:BOe4r3BBOOqMNITyRr8wDk9wIEgRA0sd}
    apiHost: ${SSO_API_HOST:http://api-devops.aliyun-inc.com/tbs}

yunxiao:
  web:
    disable-yunxiao-locale-resolver=true
  config-center:
    locator-enabled: false
    refresher-enabled: false
  # using yunxiao-promtheus sdk，only using MonitorableProducerFactory
  prometheus:
    enable: false
    enable-standalone-exporter: false
innerapi:
  apps:
    appstack-biz:
      secret: ${BIZ_SECRET:63a15dasdfqer4069c12b}
oss:
  endpoint: ${OSS_ENDPOINT:oss-cn-hangzhou.aliyuncs.com}
  accesskey:
    id: ${OSS_AK_ID:mock}
    secret: ${OSS_AK_SECRET:mock}
  bucket:
    name: ${OSS_BUCKET_NAME:appstack-migrate}

ratelimit:
  forced: off
  selective:
    targets: 6434c75298298b900fda51ec

ahas:
  namespace: default
project:
  name: appstack-biz

ade2:
  kruise-enforce:
    black-orgs: ${KRUISE_ENFORCE_BLACK_ORGS:none}
    ratio-limit: ${KRUISE_ENFORCE_RATIO:0}
