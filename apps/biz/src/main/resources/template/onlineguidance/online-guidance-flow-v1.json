[{"name": "oxs", "stepList": [{"name": "CODE_REPO_CONFIG", "state": "INIT"}, {"name": "DEPLOY_CONFIG", "state": "INIT"}, {"name": "ENV_CONFIG", "state": "INIT"}, {"name": "WORKFLOW_CONFIG", "state": "INIT"}, {"name": "APP_PUBLISH", "state": "INIT"}]}, {"name": "<PERSON><PERSON><PERSON>", "stepList": [{"name": "CODE_REPO_CONFIG", "state": "INIT"}, {"name": "DEPLOY_CONFIG", "state": "INIT"}, {"name": "ENV_CONFIG", "state": "INIT"}, {"name": "WORKFLOW_CONFIG", "state": "INIT"}, {"name": "APP_PUBLISH", "state": "INIT"}]}, {"name": "vpc", "stepList": [{"name": "CODE_REPO_CONFIG", "state": "INIT"}, {"name": "DEPLOY_CONFIG", "state": "INIT"}, {"name": "ENV_CONFIG", "state": "INIT"}, {"name": "WORKFLOW_CONFIG", "state": "INIT"}, {"name": "APP_PUBLISH", "state": "INIT"}]}]