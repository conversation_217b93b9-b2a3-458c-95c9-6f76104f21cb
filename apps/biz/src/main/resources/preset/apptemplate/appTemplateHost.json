{"template": {"name": "host-app-template", "displayName": "Java 主机应用示例模板", "description": "主机部署编排应用模板，预置主机部署脚本模板，预置四套环境、四套变量组，预置主机构建部署标准研发流程"}, "configs": [{"sn": "1b4c7e8ca2264407a13ed0345ef08116", "appTemplateName": "host-app-template", "type": "Orchestration", "modeSetting": {"configType": "Orchestration", "modes": {}}, "configuration": {"type": "Orchestration", "orchestrations": [{"storageType": "BUILTIN", "format": "MANIFEST", "suitableResourceTypes": ["HOST"], "sn": "host-app-template@HOST", "revision": {"sha": "5c333199a1e329b5a26ec7572c2a2cac68790d8f", "message": "初始化", "author": "cac390bf-d746-46f5-b2cb-b97442c55001", "commitTime": "2023-10-25T13:25:23.530+00:00"}, "name": "host-app-template", "creator": "cac390bf-d746-46f5-b2cb-b97442c55001", "gmtCreate": "2023-10-25T13:25:24.000+00:00", "modifier": "cac390bf-d746-46f5-b2cb-b97442c55001", "gmtModified": "2023-10-25T13:25:24.000+00:00", "description": "", "placeholderList": [{"name": "artifact.demoapp", "description": "制品包地址（通常来自构建阶段产物，部署时动态输入地址）", "type": "string", "value": "NULL", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": true, "rsType": "HOST"}, {"name": "appName", "description": "应用名", "type": "string", "value": "APPSTACK_APP_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "HOST"}, {"name": "envName", "description": "环境名", "type": "string", "value": "APPSTACK_ENV_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "HOST"}], "componentList": [{"name": "{{ .AppStack.appName }}", "kind": "Application", "description": "", "content": "apiVersion: core.oam.dev/v1beta1\nkind: Application\nmetadata:\n  name: '{{ .AppStack.appName }}-{{ .AppStack.envName }}'\nspec:\n  components:\n    - name: '{{ .AppStack.appName }}-{{ .AppStack.envName }}'\n      type: hybrid-workload\n      properties:\n        specialization: non-containerized\n        bootstrap:\n          - type: http-direct\n            fromUrl: '{{ .AppStack.artifact.demoapp }}'\n            toPath: /package.tgz\n        packages:\n          - type: local\n            name: '{{ .AppStack.appName }}-{{ .AppStack.envName }}'\n            path: '# 请输入制品包在你主机上的绝对路径，示例：/home/<USER>/app/package.tgz'\n            executedBy: 'admin'\n            start:\n              command: |-\n                # 一个典型的启动脚本逻辑如下：先将制品包解压缩到指定目录中，再执行启动文件（通常在代码中维护，如示例中的deploy.sh）。\n                # mkdir -p /home/<USER>/application\n                # tar zxvf /home/<USER>/app/package.tgz -C /home/<USER>/application/\n                # sh /home/<USER>/application/deploy.sh start\n            stop:\n              command: |-\n                # 一个典型的停止脚本逻辑如下：先查找到服务进程，再kill服务进程。\n                # sh /home/<USER>/application/deploy.sh stop\n            healthCheck:\n              command: |-\n                # sh /home/<USER>/application/deploy.sh health-check\n            descriptors:\n              source: local\n      traits: []", "priority": 1, "type": "HOST"}], "groupNameMap": {}}], "revision": null}}, {"sn": "2c6d613dc24144f99908001dda1bc445", "appTemplateName": "host-app-template", "type": "VariableGroup", "modeSetting": {"configType": "VariableGroup"}, "configuration": {"type": "VariableGroup", "profileMap": {"dev": {"name": "dev", "displayName": "开发环境变量组", "vars": [{"key": "port", "value": "80", "description": "端口"}]}, "test": {"name": "test", "displayName": "测试环境变量组", "vars": [{"key": "port", "value": "80", "description": "端口"}]}, "prepub": {"name": "prepub", "displayName": "预发环境变量组", "vars": [{"key": "port", "value": "80", "description": "端口"}]}, "production": {"name": "production", "displayName": "生产环境变量组", "vars": [{"key": "port", "value": "80", "description": "端口"}]}}, "revision": {"repoMeta": {"name": "k8s-app-template", "type": "variableTemplate"}, "sha": "e22021e90c65b175ec99f26b2c727520beb9ffa7", "message": "null", "author": "cac390bf-d746-46f5-b2cb-b97442c55001", "refs": [], "commitTime": 1698200105753}}}, {"sn": "707cf7f35f114b2590144b041f0d10eb", "appTemplateName": "host-app-template", "type": "Env", "modeSetting": {"configType": "Env"}, "configuration": {"type": "Env", "envs": [{"name": "dev", "displayName": "开发环境", "labels": [{"namespace": "default", "name": "envType", "value": "dev", "displayName": "环境级别", "displayValue": "开发环境", "extraMap": {}}], "profileName": "dev", "deployType": null, "resourcePoolName": null, "deployGroupName": null, "description": "开发环境"}, {"name": "test", "displayName": "测试环境", "labels": [{"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}], "profileName": "test", "deployType": null, "resourcePoolName": null, "deployGroupName": null, "description": "测试环境"}, {"name": "prepub", "displayName": "预发环境", "labels": [{"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "profileName": "prepub", "deployType": null, "resourcePoolName": null, "deployGroupName": null, "description": "预发环境"}, {"name": "production", "displayName": "生产环境", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "profileName": "production", "deployType": null, "resourcePoolName": null, "deployGroupName": null, "description": "生产环境"}]}}, {"sn": "b65b16899cd54b93963a6fc64e0f19d1", "appTemplateName": "host-app-template", "type": "ReleaseWorkflow", "modeSetting": {"configType": "ReleaseWorkflow"}, "configuration": {"type": "ReleaseWorkflow", "appTemplateWorkflowList": [{"sn": "b25fd354eb034f13a5103aeeee31dac1", "name": "标准研发流程", "appTemplateName": "标准研发流程", "releaseStageTemplate": [{"sn": "测试阶段", "name": "测试阶段", "labels": [{"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Java主机应用标准研发流程模板-测试阶段-发布流水线", "owner": "ed99ca55-1fe1-4ad3-9fc3-9314edf0e64b", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: 构建\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Java 构建上传\n            task: execution-component-production@1\n            identifier: '33_1675156796800'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version: jdk1.8\n              steps:\n                - name: Java 构建\n                  stepType: java_build\n                  stepIdentifier: '33_1675156796800__34_1675156796801'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                - name: 构建物上传\n                  stepType: tb_oss_upload\n                  stepIdentifier: '33_1675156796800__35_1675156796801'\n                  uploadType: 'PACKAGE_FILE_STORAGE'\n                  SERVICE_CONNECTION_ID: ''\n                  REPO_ID: ''\n                  ARTIFACT: 'Artifacts_${PIPELINE_ID}'\n                  ARTIFACT_VERSION: '${BUILD_NUMBER}'\n                  SHOULD_PACKAGE: true\n                  ARCHIVE_PATH: target/\n                  INCLUDE_PATH_IN_ARTIFACT: false\n                  freeInTaskGroupModeFields: []\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - description: 自定义产出物名称，定义后在部署组件使用\n                displayName: '制品名称.Artifacts_${PIPELINE_ID}'\n                export: true\n                identifier: 33_1675156796800__35_1675156796801__ARTIFACT\n                name: >-\n                  33_1675156796800__35_1675156796801.dynamic_output.Artifacts_${PIPELINE_ID}\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: AppStack部署\n            task: execution-component-production@1\n            identifier: '36_1675156796802'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '36_1675156796802__37_1675156796802'\n                  APP_NAME: ''\n                  ENV_NAME: ''\n                  ACREE_TIP: ''\n                  IMAGE_LIST: ''\n                  AUTO_SUBMIT: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": ""}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "ON", "ciType": "CR", "specificBranch": null, "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}}, {"sn": "预发阶段", "name": "预发阶段", "labels": [{"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Java主机应用标准研发流程模板-预发阶段-发布流水线", "owner": "ed99ca55-1fe1-4ad3-9fc3-9314edf0e64b", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: 构建\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Java 构建上传\n            task: execution-component-production@1\n            identifier: '38_1675156807964'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version: jdk1.8\n              steps:\n                - name: Java 构建\n                  stepType: java_build\n                  stepIdentifier: '38_1675156807964__39_1675156807964'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                - name: 构建物上传\n                  stepType: tb_oss_upload\n                  stepIdentifier: '38_1675156807964__40_1675156807964'\n                  uploadType: 'PACKAGE_FILE_STORAGE'\n                  SERVICE_CONNECTION_ID: ''\n                  REPO_ID: ''\n                  ARTIFACT: 'Artifacts_${PIPELINE_ID}'\n                  ARTIFACT_VERSION: '${BUILD_NUMBER}'\n                  SHOULD_PACKAGE: true\n                  ARCHIVE_PATH: target/\n                  INCLUDE_PATH_IN_ARTIFACT: false\n                  freeInTaskGroupModeFields: []\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - description: 自定义产出物名称，定义后在部署组件使用\n                displayName: '制品名称.Artifacts_${PIPELINE_ID}'\n                export: true\n                identifier: 38_1675156807964__40_1675156807964__ARTIFACT\n                name: >-\n                  38_1675156807964__40_1675156807964.dynamic_output.Artifacts_${PIPELINE_ID}\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: AppStack部署\n            task: execution-component-production@1\n            identifier: '41_1675156807967'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '41_1675156807967__42_1675156807967'\n                  APP_NAME: ''\n                  ENV_NAME: ''\n                  ACREE_TIP: ''\n                  IMAGE_LIST: ''\n                  AUTO_SUBMIT: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": ""}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "ON", "ciType": "CR", "specificBranch": null, "validateConfigState": "ON", "validateStageSnList": ["测试阶段"], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}}, {"sn": "生产阶段", "name": "生产阶段", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Java主机应用标准研发流程模板-生产阶段-发布流水线", "owner": "ed99ca55-1fe1-4ad3-9fc3-9314edf0e64b", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: 构建\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Java 构建上传\n            task: execution-component-production@1\n            identifier: '45_1675156829877'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version: jdk1.8\n              steps:\n                - name: Java 构建\n                  stepType: java_build\n                  stepIdentifier: '45_1675156829877__46_1675156829877'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                - name: 构建物上传\n                  stepType: tb_oss_upload\n                  stepIdentifier: '45_1675156829877__47_1675156829878'\n                  uploadType: 'PACKAGE_FILE_STORAGE'\n                  SERVICE_CONNECTION_ID: ''\n                  REPO_ID: ''\n                  ARTIFACT: 'Artifacts_${PIPELINE_ID}'\n                  ARTIFACT_VERSION: '${BUILD_NUMBER}'\n                  SHOULD_PACKAGE: true\n                  ARCHIVE_PATH: target/\n                  INCLUDE_PATH_IN_ARTIFACT: false\n                  freeInTaskGroupModeFields: []\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - description: 自定义产出物名称，定义后在部署组件使用\n                displayName: '制品名称.Artifacts_${PIPELINE_ID}'\n                export: true\n                identifier: 45_1675156829877__47_1675156829878__ARTIFACT\n                name: >-\n                  45_1675156829877__47_1675156829878.dynamic_output.Artifacts_${PIPELINE_ID}\n                jobIdentifier: '45_1675156829877'\n            freeInTaskGroupModeFields: []\n  - name: 人工卡点\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 人工卡点\n            task: APPSTACK_DIY_VALIDATE_TB_PROD@1\n            identifier: '48_1675156829878'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              appId: '${INPUTS.appId}'\n              mixFlowInstId: '${INPUTS.mixFlowInstId}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              validatorMethod: or\n              validatorType: user\n              validatorUser: ''\n              validatorRole: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: AppStack部署\n            task: execution-component-production@1\n            identifier: '49_1675156829878'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '49_1675156829878__50_1675156829878'\n                  APP_NAME: ''\n                  ENV_NAME: ''\n                  ACREE_TIP: ''\n                  IMAGE_LIST: ''\n                  AUTO_SUBMIT: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 合并主干\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 合并代码\n            task: execution-component-production@1\n            identifier: '14_1675156852137'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 代码合并\n                  stepType: git-merge\n                  stepIdentifier: '14_1675156852137__15_1675156852138'\n                  GIT_BRANCH: master\n                  DELETE_ORIGIN_BRANCH: false\n                  DELETE_MATCHED_BRANCH: false\n                  BRANCH_FILTER: ''\n                  freeInTaskGroupModeFields: []\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 关闭变更\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 关闭变更\n            task: execution-component-production@1\n            identifier: '16_1675156873226'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 关闭变更\n                  stepType: appstack-close-change-request-production\n                  stepIdentifier: '16_1675156873226__17_1675156873226'\n                  freeInTaskGroupModeFields: []\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": ""}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "ON", "ciType": "CR", "specificBranch": null, "validateConfigState": "ON", "validateStageSnList": ["测试阶段", "预发阶段"], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}}], "fromRevisionSha": "", "message": "", "order": "1"}]}}]}