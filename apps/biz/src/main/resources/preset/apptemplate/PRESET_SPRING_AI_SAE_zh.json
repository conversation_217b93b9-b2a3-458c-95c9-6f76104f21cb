{"template": {"name": "PRESET_SPRING_AI_SAE", "displayName": "SAE 部署 AI 应用体验模板", "cover": "https://img.alicdn.com/imgextra/i1/O1CN01MYCeF61VRkYopejI8_!!6000000002650-55-tps-200-200.svg", "description": "{\"text\":\"本应用模板演示如何使用云效 AppStack + SAE（Serverless 应用引擎）快速构建部署一个 AI 聊天问答助手，开启你的第一个 AI 应用研发，让 Java 开发者像使用 Spring 开发普通应用一样开发 AI 应用。详见：https://atomgit.com/appstack-example/spring-cloud-ai/blob/master/README.md\",\"content\":[\"root\",{},[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"color\":\"rgb(0, 0, 0)\",\"fonts\":{\"ascii\":\"PingFang SC\",\"hAnsi\":\"PingFang SC\",\"cs\":\"PingFang SC\",\"eastAsia\":\"PingFang SC\"},\"sz\":12,\"szUnit\":\"pt\",\"data-type\":\"leaf\"},\"本应用模板演示如何使用云效 AppStack + SAE（Serverless 应用引擎）快速构建部署一个 AI 聊天问答助手，开启你的第一个 AI 应用研发，让 Java 开发者像使用 Spring 开发普通应用一样开发 AI 应用。详见：https://atomgit.com/appstack-example/spring-cloud-ai/blob/master/README.md\"]]]]}"}, "configs": [{"sn": "633c574f60fd40e7bef00f9f19af7f23", "appTemplateName": "PRESET_SPRING_AI_SAE", "type": "VariableGroup", "modeSetting": {"configType": "VariableGroup"}, "configuration": {"type": "VariableGroup", "profileMap": {}, "revision": {"repoMeta": {"name": "PRESET_SPRING_AI_SAE", "type": "variableTemplate"}, "sha": "ff6c0ecf7e66c9af2cbdc8793989d7a1391f2dee", "message": "删除变量组 生产环境变量组", "author": "646dabc05ccc4ad1bc2a9e2f", "refs": [], "commitTime": 1718872255498}, "branchInfo": {"head": {"repoMeta": {"name": "PRESET_SPRING_AI_SAE", "type": "variableTemplate"}, "sha": "ff6c0ecf7e66c9af2cbdc8793989d7a1391f2dee", "message": "删除变量组 生产环境变量组", "author": "646dabc05ccc4ad1bc2a9e2f", "refs": [], "commitTime": 1718872255498}, "repoMeta": {"name": "PRESET_SPRING_AI_SAE", "type": "variableTemplate"}, "name": "master", "startRevisionSha": "", "fromBranch": ""}}}, {"sn": "a8455fbbb0da4ce980f2950b3e3b2522", "appTemplateName": "PRESET_SPRING_AI_SAE", "type": "Orchestration", "modeSetting": {"configType": "Orchestration", "modes": {"default_builtin": "Initialization"}}, "configuration": {"type": "Orchestration", "orchestrations": [], "revision": {"repoMeta": {"name": "PRESET_SPRING_AI_SAE", "type": "appTemplateOrchestration"}, "sha": "3451548579aeafce21a843e49c0ac4970c7e7b37", "message": "删除 主机部署编排", "author": "646dabc05ccc4ad1bc2a9e2f", "refs": [], "commitTime": 1718872114623}, "branchInfo": {"head": {"repoMeta": {"name": "PRESET_SPRING_AI_SAE", "type": "appTemplateOrchestration"}, "sha": "3451548579aeafce21a843e49c0ac4970c7e7b37", "message": "删除 主机部署编排", "author": "646dabc05ccc4ad1bc2a9e2f", "refs": [], "commitTime": 1718872114623}, "repoMeta": {"name": "PRESET_SPRING_AI_SAE", "type": "appTemplateOrchestration"}, "name": "master", "startRevisionSha": "", "fromBranch": ""}}}, {"sn": "78e982fe59d54dd6ad29afc678b58cd4", "appTemplateName": "PRESET_SPRING_AI_SAE", "type": "ReleaseWorkflow", "modeSetting": {"configType": "ReleaseWorkflow", "modes": {}}, "configuration": {"type": "ReleaseWorkflow", "appTemplateWorkflowList": [{"sn": "7497dc12-2f8e-4b7c-b0f4-716a574c8fdf", "name": "标准研发流程", "appTemplateName": "PRESET_SPRING_AI_SAE", "releaseStageTemplate": [{"sn": "测试阶段", "name": "测试阶段", "labels": [{"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Spring AI 应用模板-Spring AI-Spring AI-发布流水线", "owner": "64a3940486937ebe0ce9bb7a", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}],\"globalParams\":[{\"encrypted\":false,\"key\":\"AI_API_KEY\",\"masked\":false,\"runningConfig\":true,\"value\":\"请输入你的DashScope模型API-KEY\"},{\"encrypted\":false,\"key\":\"SERVER_PORT\",\"masked\":false,\"runningConfig\":true,\"value\":\"8080\"}]}", "flow": "schema: tb\npipeline:\n  - name: 测试\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Java 代码扫描\n            task: execution-component-production@20\n            identifier: '12_1715827592350'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java 代码规约扫描\n                  stepType: p3c\n                  stepIdentifier: '12_1715827592350__13_1715827592351'\n                  JDK_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  INCREASE: false\n                  USE_CUSTOM_RULE: false\n                  RULE_SET: >-\n                    ali-comment.xml,ali-concurrent.xml,ali-constant.xml,ali-exception.xml,ali-flowcontrol.xml,ali-naming.xml,ali-oop.xml,ali-orm.xml,ali-other.xml,ali-set.xml\n                  RULE_DIR: .\n                  SUB_DIR: ''\n                  EXCLUSION: test/\n                  CHECK_REDLINES: ''\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 构建\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 应用镜像构建\n            task: execution-component-production@20\n            identifier: '10_1715397594667'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version: jdk1.8\n              steps:\n                - name: Java 构建\n                  stepType: java_build\n                  stepIdentifier: '10_1715397594667__12_1715397594669'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -s settings.xml -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                  isOpen: false\n                - name: 镜像构建并推送至阿里云镜像仓库个人版\n                  stepType: docker_build_sc_production\n                  stepIdentifier: '10_1715397594667__10_1718872316142'\n                  freeInTaskGroupModeFields:\n                    - ARGS\n                  SERVICE_CONNECTION_ID: 627686\n                  DOCKER_REGION: ''\n                  artifact: artifact\n                  ACREE_TIP: ''\n                  DOCKER_REPOSITORY: ''\n                  DOCKER_TAG: '${DATETIME}'\n                  EXTRA_TAGS: ''\n                  BUILDKIT_VERSION: v0.8.0\n                  DOCKER_FILE_PATH: Dockerfile\n                  CONTEXT_PATH: ''\n                  NO_CACHE: false\n                  ARGS: >-\n                    {\"AI_API_KEY\":\"${AI_API_KEY}\",\"AI_API_KEY.type\":\"custom\",\"SERVER_PORT\":\"${SERVER_PORT}\",\"SERVER_PORT.type\":\"custom\"}\n                  JSONEncoding: true\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - name: '10_1715397594667__10_1718872316142.dynamic_output.${DATETIME}'\n                displayName: '标签.${DATETIME}'\n                type: artifact\n                identifier: 10_1715397594667__10_1718872316142__DOCKER_TAG\n                description: Docker镜像Tag\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715397594667'\n              - name: 10_1715397594667__10_1718872316142.DOCKER_OUTPUT\n                displayName: 镜像构建并推送至阿里云镜像仓库个人版.镜像公网地址\n                type: artifact\n                identifier: 10_1715397594667__10_1718872316142__DOCKER_OUTPUT\n                description: ''\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715397594667'\n              - name: 10_1715397594667__10_1718872316142.DOCKER_OUTPUT_VPC\n                displayName: 镜像构建并推送至阿里云镜像仓库个人版.镜像VPC地址\n                type: artifact\n                identifier: 10_1715397594667__10_1718872316142__DOCKER_OUTPUT_VPC\n                description: ''\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715397594667'\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Serverless(SAE) 应用发布-测试环境\n            task: execution-component-production@20\n            identifier: '10_1718872427614'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 阿里SAE部署\n                  stepType: flow-sae\n                  stepIdentifier: '10_1718872427614__11_1718872427614'\n                  SERVICE_CONNECTION_ID: ''\n                  REGION_ID: ''\n                  SAE_APP_NAMESPACE: ''\n                  APPLICATION_ID: ''\n                  PACKAGE: ''\n                  USE_ACR_EE: false\n                  ACR_EE_ID: ''\n                  StrategyType: BatchUpdate\n                  GrayNumber: ''\n                  ReleaseType: auto\n                  Batch: 1\n                  BatchWaitTime: 0\n                  minReadyInstances: 1\n                  freeInTaskGroupModeFields:\n                    - PACKAGE\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "nbzeqWPcks8i0AcnZDmy", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "pipelineYaml": "schema: tb\npipeline:\n  - name: 测试\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Java 代码扫描\n            task: execution-component-production@20\n            identifier: '12_1715827592350'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java 代码规约扫描\n                  stepType: p3c\n                  stepIdentifier: '12_1715827592350__13_1715827592351'\n                  JDK_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  INCREASE: false\n                  USE_CUSTOM_RULE: false\n                  RULE_SET: >-\n                    ali-comment.xml,ali-concurrent.xml,ali-constant.xml,ali-exception.xml,ali-flowcontrol.xml,ali-naming.xml,ali-oop.xml,ali-orm.xml,ali-other.xml,ali-set.xml\n                  RULE_DIR: .\n                  SUB_DIR: ''\n                  EXCLUSION: test/\n                  CHECK_REDLINES: ''\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 构建\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 应用镜像构建\n            task: execution-component-production@20\n            identifier: '10_1715397594667'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version: jdk1.8\n              steps:\n                - name: Java 构建\n                  stepType: java_build\n                  stepIdentifier: '10_1715397594667__12_1715397594669'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -s settings.xml -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                  isOpen: false\n                - name: 镜像构建并推送至阿里云镜像仓库个人版\n                  stepType: docker_build_sc_production\n                  stepIdentifier: '10_1715397594667__10_1718872316142'\n                  freeInTaskGroupModeFields:\n                    - ARGS\n                  SERVICE_CONNECTION_ID: 627686\n                  DOCKER_REGION: ''\n                  artifact: artifact\n                  ACREE_TIP: ''\n                  DOCKER_REPOSITORY: ''\n                  DOCKER_TAG: '${DATETIME}'\n                  EXTRA_TAGS: ''\n                  BUILDKIT_VERSION: v0.8.0\n                  DOCKER_FILE_PATH: Dockerfile\n                  CONTEXT_PATH: ''\n                  NO_CACHE: false\n                  ARGS: >-\n                    {\"AI_API_KEY\":\"${AI_API_KEY}\",\"AI_API_KEY.type\":\"custom\",\"SERVER_PORT\":\"${SERVER_PORT}\",\"SERVER_PORT.type\":\"custom\"}\n                  JSONEncoding: true\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - name: '10_1715397594667__10_1718872316142.dynamic_output.${DATETIME}'\n                displayName: '标签.${DATETIME}'\n                type: artifact\n                identifier: 10_1715397594667__10_1718872316142__DOCKER_TAG\n                description: Docker镜像Tag\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715397594667'\n              - name: 10_1715397594667__10_1718872316142.DOCKER_OUTPUT\n                displayName: 镜像构建并推送至阿里云镜像仓库个人版.镜像公网地址\n                type: artifact\n                identifier: 10_1715397594667__10_1718872316142__DOCKER_OUTPUT\n                description: ''\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715397594667'\n              - name: 10_1715397594667__10_1718872316142.DOCKER_OUTPUT_VPC\n                displayName: 镜像构建并推送至阿里云镜像仓库个人版.镜像VPC地址\n                type: artifact\n                identifier: 10_1715397594667__10_1718872316142__DOCKER_OUTPUT_VPC\n                description: ''\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715397594667'\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Serverless(SAE) 应用发布-测试环境\n            task: execution-component-production@20\n            identifier: '10_1718872427614'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 阿里SAE部署\n                  stepType: flow-sae\n                  stepIdentifier: '10_1718872427614__11_1718872427614'\n                  SERVICE_CONNECTION_ID: ''\n                  REGION_ID: ''\n                  SAE_APP_NAMESPACE: ''\n                  APPLICATION_ID: ''\n                  PACKAGE: ''\n                  USE_ACR_EE: false\n                  ACR_EE_ID: ''\n                  StrategyType: BatchUpdate\n                  GrayNumber: ''\n                  ReleaseType: auto\n                  Batch: 1\n                  BatchWaitTime: 0\n                  minReadyInstances: 1\n                  freeInTaskGroupModeFields:\n                    - PACKAGE\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": []}, {"sn": "预发阶段", "name": "预发阶段", "labels": [{"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Spring AI 应用体验模板-标准研发流程-预发阶段-发布流水线", "owner": "646dabc05ccc4ad1bc2a9e2f", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: 构建\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 应用镜像构建\n            task: execution-component-production@20\n            identifier: '10_1715827733104'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version: jdk1.8\n              steps:\n                - name: 执行命令\n                  stepType: exec-shell\n                  stepIdentifier: '10_1715827733104__10_1718872533548'\n                  freeInTaskGroupModeFields:\n                    - ARTIFACTS\n                  command: |\n                    # input your command here\n                    echo hello,world!\n                  ARTIFACTS: '{}'\n                  JSONEncoding: true\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 预发环境部署\n            task: execution-component-production@20\n            identifier: '12_1718872568898'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version1: pre-jdk1.62\n              steps:\n                - name: 执行命令\n                  stepType: exec-shell\n                  stepIdentifier: '12_1718872568898__13_1718872568898'\n                  command: |\n                    # input your command here\n                    echo hello,world!\n                  ARTIFACTS: '{}'\n                  JSONEncoding: true\n                  freeInTaskGroupModeFields:\n                    - ARTIFACTS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "Fdv4niL3QcnB4RTXlzzS", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "pipelineYaml": "schema: tb\npipeline:\n  - name: 构建\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 应用镜像构建\n            task: execution-component-production@20\n            identifier: '10_1715827733104'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version: jdk1.8\n              steps:\n                - name: 执行命令\n                  stepType: exec-shell\n                  stepIdentifier: '10_1715827733104__10_1718872533548'\n                  freeInTaskGroupModeFields:\n                    - ARTIFACTS\n                  command: |\n                    # input your command here\n                    echo hello,world!\n                  ARTIFACTS: '{}'\n                  JSONEncoding: true\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 预发环境部署\n            task: execution-component-production@20\n            identifier: '12_1718872568898'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version1: pre-jdk1.62\n              steps:\n                - name: 执行命令\n                  stepType: exec-shell\n                  stepIdentifier: '12_1718872568898__13_1718872568898'\n                  command: |\n                    # input your command here\n                    echo hello,world!\n                  ARTIFACTS: '{}'\n                  JSONEncoding: true\n                  freeInTaskGroupModeFields:\n                    - ARTIFACTS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": []}, {"sn": "生产阶段", "name": "生产阶段", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Spring AI 应用体验模板-标准研发流程-生产阶段-发布流水线", "owner": "646dabc05ccc4ad1bc2a9e2f", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: 发布审核\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 发布审核\n            task: APPSTACK_DIY_VALIDATE_TB_PROD@1\n            identifier: '14_1715828084056'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              appId: '${INPUTS.appId}'\n              mixFlowInstId: '${INPUTS.mixFlowInstId}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              validatorMethod: or\n              validatorType: role\n              validatorUser: ''\n              validatorRole: 620321f1a705818587432d56\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 生产环境部署\n            task: execution-component-production@20\n            identifier: '14_1718872648413'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version1: pre-jdk1.62\n              steps:\n                - name: 执行命令\n                  stepType: exec-shell\n                  stepIdentifier: '14_1718872648413__15_1718872648413'\n                  command: |\n                    # input your command here\n                    echo hello,world!\n                  ARTIFACTS: '{}'\n                  JSONEncoding: true\n                  freeInTaskGroupModeFields:\n                    - ARTIFACTS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "nlWaonQ3AWWO0dzAuJ4Q", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "pipelineYaml": "schema: tb\npipeline:\n  - name: 发布审核\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 发布审核\n            task: APPSTACK_DIY_VALIDATE_TB_PROD@1\n            identifier: '14_1715828084056'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              appId: '${INPUTS.appId}'\n              mixFlowInstId: '${INPUTS.mixFlowInstId}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              validatorMethod: or\n              validatorType: role\n              validatorUser: ''\n              validatorRole: 620321f1a705818587432d56\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 生产环境部署\n            task: execution-component-production@20\n            identifier: '14_1718872648413'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version1: pre-jdk1.62\n              steps:\n                - name: 执行命令\n                  stepType: exec-shell\n                  stepIdentifier: '14_1718872648413__15_1718872648413'\n                  command: |\n                    # input your command here\n                    echo hello,world!\n                  ARTIFACTS: '{}'\n                  JSONEncoding: true\n                  freeInTaskGroupModeFields:\n                    - ARTIFACTS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": []}], "fromRevisionSha": "1fe0474cf216817299794422af0f48cb7f4150fc", "message": "修改研发流程阶段[测试阶段]流水线", "order": "1"}]}}, {"sn": "3f8f13db9977420a897294261c20e500", "appTemplateName": "PRESET_SPRING_AI_SAE", "type": "Env", "modeSetting": {"configType": "Env"}, "configuration": {"type": "Env", "envs": []}}, {"sn": "25906c6e6c834707bbf8fef8756310ab", "appTemplateName": "PRESET_SPRING_AI_SAE", "type": "Source", "modeSetting": {"configType": "Source"}, "configuration": {"type": "Source", "codeRepos": [{"name": "spring-cloud-ai", "repoUrl": "https://atomgit.com/appstack-example/spring-cloud-ai.git", "identifier": "spring_cloud_ai", "repoContext": {"repoUrl": "https://atomgit.com/appstack-example/spring-cloud-ai.git", "defaultBranch": "master", "repoType": "GIT"}, "connectionConfig": {"connectionId": "", "connectionType": "FLOW"}}], "artifactRepos": []}}]}