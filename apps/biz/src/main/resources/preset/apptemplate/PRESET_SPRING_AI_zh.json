{"template": {"name": "PRESET_SPRING_AI", "displayName": "Spring AI 应用体验模板", "cover": "https://img.alicdn.com/imgextra/i4/O1CN01Vk4ufi1MXOsZVSaxZ_!!6000000001444-55-tps-200-200.svg", "description": "本应用模板演示如何使用云效 AppStack 快速构建部署一个 AI 聊天问答助手，开启你的第一个 AI 应用研发，让 Java 开发者像使用 Spring 开发普通应用一样开发 AI 应用。详见：https://atomgit.com/appstack-example/spring-cloud-ai/blob/master/README.md"}, "configs": [{"sn": "f0a3a34403a342fe9e055e5b926a9042", "appTemplateName": "PRESET_SPRING_AI", "type": "Source", "modeSetting": {"configType": "Source"}, "configuration": {"type": "Source", "codeRepos": [{"name": "spring-cloud-ai", "repoUrl": "https://atomgit.com/appstack-example/spring-cloud-ai.git", "identifier": "spring_cloud_ai", "repoContext": {"repoUrl": "https://atomgit.com/appstack-example/spring-cloud-ai.git", "defaultBranch": "master", "repoType": "GIT"}, "connectionConfig": {"connectionId": "", "connectionType": "FLOW"}}], "artifactRepos": []}}, {"sn": "8fc02cdbea04497c943fe169ca88c0b7", "appTemplateName": "PRESET_SPRING_AI", "type": "Env", "modeSetting": {"configType": "Env"}, "configuration": {"type": "Env", "envs": [{"name": "test-env", "displayName": "测试环境", "labels": [{"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "test-variables", "displayName": "测试环境变量组", "type": "TEMPLATE"}], "deployType": "HOST", "resourcePoolName": "default", "deployGroupName": null, "description": "测试环境，通常用于开发过程中日常测试", "spec": {"migrateState": null, "oamRebuildState": null, "withoutOam": null, "rolloutStrategy": [{"locator": "*", "batches": null, "batchSteps": null, "timeOutMS": 600000, "targetReplicas": null, "batchMode": "ConfirmFirstBatch", "deployType": "Recreate"}], "replicasManagement": "SYSTEM"}}, {"name": "pre-env", "displayName": "预发环境", "labels": [{"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "pre-variables", "displayName": "预发环境变量组", "type": "TEMPLATE"}], "deployType": "HOST", "resourcePoolName": "default", "deployGroupName": null, "description": "预发环境，通常用于发布前的验收", "spec": {"migrateState": null, "oamRebuildState": null, "withoutOam": null, "rolloutStrategy": [{"locator": "*", "batches": null, "batchSteps": null, "timeOutMS": 600000, "targetReplicas": null, "batchMode": "ConfirmFirstBatch", "deployType": "Recreate"}], "replicasManagement": "SYSTEM"}}, {"name": "prod-env", "displayName": "生产环境", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "prod-variables", "displayName": "生产环境变量组", "type": "TEMPLATE"}], "deployType": "HOST", "resourcePoolName": "default", "deployGroupName": null, "description": "生产环境，最终为用户提供服务的线上环境", "spec": {"migrateState": null, "oamRebuildState": null, "withoutOam": null, "rolloutStrategy": [{"locator": "*", "batches": null, "batchSteps": null, "timeOutMS": 600000, "targetReplicas": null, "batchMode": "ConfirmFirstBatch", "deployType": "Recreate"}], "replicasManagement": "SYSTEM"}}]}}, {"sn": "02221c4669634968bd794bcf13620ade", "appTemplateName": "PRESET_SPRING_AI", "type": "ReleaseWorkflow", "modeSetting": {"configType": "ReleaseWorkflow", "modes": {}}, "configuration": {"type": "ReleaseWorkflow", "appTemplateWorkflowList": [{"sn": "9a622933-6e27-4ab7-ada7-55c358b414e7", "name": "标准研发流程", "appTemplateName": "PRESET_SPRING_AI", "releaseStageTemplate": [{"sn": "测试阶段", "name": "测试阶段", "labels": [{"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Spring AI 应用模板-Spring AI-Spring AI-发布流水线", "owner": "64a3940486937ebe0ce9bb7a", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}],\"globalParams\":[]}", "flow": "schema: tb\npipeline:\n  - name: 测试\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Java 代码扫描\n            task: execution-component-production@20\n            identifier: '12_1715827592350'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java 代码规约扫描\n                  stepType: p3c\n                  stepIdentifier: '12_1715827592350__13_1715827592351'\n                  JDK_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  INCREASE: false\n                  USE_CUSTOM_RULE: false\n                  RULE_SET: >-\n                    ali-comment.xml,ali-concurrent.xml,ali-constant.xml,ali-exception.xml,ali-flowcontrol.xml,ali-naming.xml,ali-oop.xml,ali-orm.xml,ali-other.xml,ali-set.xml\n                  RULE_DIR: .\n                  SUB_DIR: ''\n                  EXCLUSION: test/\n                  CHECK_REDLINES: ''\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 构建\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 应用构建\n            task: execution-component-production@20\n            identifier: '10_1715397594667'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version: jdk1.8\n              steps:\n                - name: Java 构建\n                  stepType: java_build\n                  stepIdentifier: '10_1715397594667__12_1715397594669'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -s settings.xml -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                - name: 构建物上传\n                  stepType: tb_oss_upload\n                  stepIdentifier: '10_1715397594667__13_1715397594670'\n                  uploadType: PUBLIC_STORAGE\n                  SERVICE_CONNECTION_ID: ''\n                  REPO_ID: ''\n                  ARTIFACT: 'Artifacts_${PIPELINE_ID}'\n                  ARTIFACT_VERSION: '${DATETIME}'\n                  SHOULD_PACKAGE: true\n                  ARCHIVE_PATH: target/\n                  INCLUDE_PATH_IN_ARTIFACT: false\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - name: >-\n                  10_1715397594667__13_1715397594670.dynamic_output.Artifacts_${PIPELINE_ID}\n                displayName: '制品名称.Artifacts_${PIPELINE_ID}'\n                type: artifact\n                identifier: 10_1715397594667__13_1715397594670__ARTIFACT\n                description: 自定义产出物名称，定义后在部署组件使用\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715397594667'\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: AppStack部署-测试环境\n            task: execution-component-production@20\n            identifier: '14_1715397646449'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '14_1715397646449__15_1715397646450'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: test-env\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: package\n                      value: >-\n                        ${10_1715397594667.10_1715397594667__13_1715397594670.dynamic_output.Artifacts_${PIPELINE_ID}}\n                      type: upstream\n                  AUTO_SUBMIT: true\n                  AUTO_SUBMIT_LATEST_FAILED_CHECK: true\n                  AUTO_SUBMIT_ORCHESTRATION_CHANGED_CHECK: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 活动校验\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 活动校验\n            task: execution-component-production@20\n            identifier: '12_1716549029698'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version1: pre-jdk1.62\n              steps:\n                - name: 执行命令\n                  stepType: exec-shell\n                  stepIdentifier: '12_1716549029698__13_1716549029698'\n                  command: >\n                    #!/bin/bash\n\n                    set -e\n\n\n                    # 系统提供参数，从流水线上下文获取\n\n                    echo [INFO] 系统变量\n\n                    echo [INFO] PIPELINE_ID=$PIPELINE_ID       # 流水线ID\n\n                    echo [INFO] PIPELINE_NAME=$PIPELINE_NAME   # 流水线名称\n\n                    echo [INFO] BUILD_NUMBER=$BUILD_NUMBER     # 流水线运行实例编号\n\n                    echo [INFO] EMPLOYEE_ID=$EMPLOYEE_ID       # 触发流水线用户ID\n\n                    echo [INFO] WORK_SPACE=$WORK_SPACE         #\n                    /root/workspace容器中目录\n\n                    echo [INFO] PROJECT_DIR=$PROJECT_DIR       #\n                    代码库根路径，默认为/root/workspace/code\n\n                    echo [INFO] PLUGIN_DIR=$PLUGIN_DIR         #\n                    插件路径，默认为/root/plugins\n\n                    echo [INFO] BUILD_JOB_ID=$BUILD_JOB_ID     # build-service\n                    任务ID\n\n                    echo [INFO] STEP_ID=$stepIdentifier        # yml 步骤ID\n\n                    echo [INFO] CREATOR_ALIYUN_PK=$CREATOR_ALIYUN_PK\n\n                    echo [INFO] INGRESS_HOST=$INGRESS_HOST\n\n\n                    curl --location\n                    \"https://devops.aliyun.com/appstack/hook/api/ucc/report?aliyunPk=$CREATOR_ALIYUN_PK&userId=$EMPLOYEE_ID&pipelineId=$PIPELINE_ID\"\n\n\n                    if [[ $? == 0 ]]\n\n                    then\n                      echo [INFO] 注册云效体验活动成功\n                    else\n                      echo [ERROR] 注册云效体验活动失败\n                      exit 1\n                    fi\n                  ARTIFACTS: '{}'\n                  JSONEncoding: true\n                  freeInTaskGroupModeFields:\n                    - ARTIFACTS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: none\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "nbzeqWPcks8i0AcnZDmy", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": [{"name": "test-variables", "displayName": "测试环境变量组", "type": "TEMPLATE"}]}, {"sn": "预发阶段", "name": "预发阶段", "labels": [{"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Spring AI 应用体验模板-标准研发流程-预发阶段-发布流水线", "owner": "646dabc05ccc4ad1bc2a9e2f", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: 构建\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 应用构建\n            task: execution-component-production@20\n            identifier: '10_1715827733104'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version: jdk1.8\n              steps:\n                - name: Java 构建\n                  stepType: java_build\n                  stepIdentifier: '10_1715827733104__11_1715827733104'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -s settings.xml -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                - name: 构建物上传\n                  stepType: tb_oss_upload\n                  stepIdentifier: '10_1715827733104__12_1715827733107'\n                  uploadType: PUBLIC_STORAGE\n                  SERVICE_CONNECTION_ID: ''\n                  REPO_ID: ''\n                  ARTIFACT: 'Artifacts_${PIPELINE_ID}'\n                  ARTIFACT_VERSION: '${DATETIME}'\n                  SHOULD_PACKAGE: true\n                  ARCHIVE_PATH: target/\n                  INCLUDE_PATH_IN_ARTIFACT: false\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - name: >-\n                  10_1715827733104__12_1715827733107.dynamic_output.Artifacts_${PIPELINE_ID}\n                displayName: '制品名称.Artifacts_${PIPELINE_ID}'\n                type: artifact\n                identifier: 10_1715827733104__12_1715827733107__ARTIFACT\n                description: 自定义产出物名称，定义后在部署组件使用\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715827733104'\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: AppStack部署-预发环境\n            task: execution-component-production@20\n            identifier: '13_1715827733108'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '13_1715827733108__14_1715827733109'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: pre-env\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: package\n                      value: >-\n                        ${10_1715827733104.10_1715827733104__12_1715827733107.dynamic_output.Artifacts_${PIPELINE_ID}}\n                      type: upstream\n                  AUTO_SUBMIT: true\n                  AUTO_SUBMIT_LATEST_FAILED_CHECK: true\n                  AUTO_SUBMIT_ORCHESTRATION_CHANGED_CHECK: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "Fdv4niL3QcnB4RTXlzzS", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": [{"name": "pre-variables", "displayName": "预发环境变量组", "type": "TEMPLATE"}]}, {"sn": "生产阶段", "name": "生产阶段", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Spring AI 应用体验模板-标准研发流程-生产阶段-发布流水线", "owner": "646dabc05ccc4ad1bc2a9e2f", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: 发布审核\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 发布审核\n            task: APPSTACK_DIY_VALIDATE_TB_PROD@1\n            identifier: '14_1715828084056'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              appId: '${INPUTS.appId}'\n              mixFlowInstId: '${INPUTS.mixFlowInstId}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              validatorMethod: or\n              validatorType: role\n              validatorUser: ''\n              validatorRole: 6628c0ce9f4372aeee9caf3e\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: AppStack部署-生产环境\n            task: execution-component-production@20\n            identifier: '17_1715827986100'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '17_1715827986100__18_1715827986100'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: prod-env\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: package\n                      value: 替换为预发构建产物\n                      type: custom\n                  AUTO_SUBMIT: true\n                  AUTO_SUBMIT_LATEST_FAILED_CHECK: true\n                  AUTO_SUBMIT_ORCHESTRATION_CHANGED_CHECK: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "nlWaonQ3AWWO0dzAuJ4Q", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": [{"name": "prod-variables", "displayName": "生产环境变量组", "type": "TEMPLATE"}]}], "fromRevisionSha": "fec0f66123bc6ee00b93b8a6749a31b48dff1638", "message": "修改研发流程阶段[测试阶段]流水线", "order": "1"}]}}, {"sn": "9dbede952ee74f5083ddc3dc7ebf140b", "appTemplateName": "PRESET_SPRING_AI", "type": "Orchestration", "modeSetting": {"configType": "Orchestration", "modes": {"default_builtin": "Initialization"}}, "configuration": {"type": "Orchestration", "orchestrations": [{"storageType": "BUILTIN", "format": "MANIFEST", "suitableResourceTypes": ["HOST"], "sn": "PRESET_SPRING_AI@HOST", "revision": {"sha": "428afe7c6490949bcac28460517e4671f64a9e00", "message": "修改编排模板", "author": "646dabc05ccc4ad1bc2a9e2f", "commitTime": "2024-05-16T03:29:12.848+00:00"}, "name": "PRESET_SPRING_AI", "creatorId": "646dabc05ccc4ad1bc2a9e2f", "gmtCreate": "2024-05-16T02:04:41.000+00:00", "modifierId": "646dabc05ccc4ad1bc2a9e2f", "gmtModified": "2024-05-16T03:29:13.000+00:00", "description": null, "labelPolicy": "FROM_LABEL_BINDING", "labelList": [{"namespace": "default", "name": "envType", "value": "dev", "displayName": "环境级别", "displayValue": "开发环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "placeholderList": [{"name": "artifact.package", "description": "制品包地址（通常来自构建阶段产物，部署时动态输入地址）", "type": "string", "value": "NULL", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": true, "rsType": "HOST"}, {"name": "appName", "description": "应用名", "type": "string", "value": "APPSTACK_APP_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "HOST"}, {"name": "envName", "description": "环境名", "type": "string", "value": "APPSTACK_ENV_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "HOST"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "请替换为你的阿里云DashScope API-KEY，申请步骤详见：https://help.aliyun.com/zh/dashscope/developer-reference/activate-dashscope-and-create-an-api-key", "type": "string", "value": "api-key", "overridable": true, "rule": null, "valueSource": "VARIABLE", "predefined": false, "rsType": "HOST"}, {"name": "serverPort", "description": "请输入服务的端口", "type": "number", "value": "serverPort", "overridable": true, "rule": null, "valueSource": "VARIABLE", "predefined": false, "rsType": "HOST"}], "componentList": [{"name": "{{ .AppStack.appName }}", "kind": "Application", "description": "", "content": "apiVersion: core.oam.dev/v1beta1\nkind: Application\nmetadata:\n  name: '{{ .AppStack.appName }}-{{ .AppStack.envName }}'\nspec:\n  components:\n    - name: '{{ .AppStack.appName }}-{{ .AppStack.envName }}'\n      type: hybrid-workload\n      properties:\n        specialization: non-containerized\n        bootstrap:\n          - type: http-direct\n            fromUrl: '{{ .AppStack.artifact.package }}'\n            toPath: /tmp/package.tgz\n        packages:\n          - type: local\n            name: '{{ .AppStack.appName }}-{{ .AppStack.envName }}'\n            path: '# 请输入制品包在你主机上的绝对路径，示例：/home/<USER>/app/package.tgz'\n            executedBy: root\n            start:\n              command: |-\n                mkdir -p ~/appstack/ai-demo\n                cd ~/appstack\n                tar zxvf /tmp/package.tgz -C ai-demo/\n                # 安装 java17，如果非 centos 系统，需要调整脚本或者自行安装 java17\n                java -version >version 2>&1 || echo \"failed to find java version\"\n                version=`cat version | head -n 1 | grep \"17\\.\"` || echo \"failed to parse java version\"\n                if [ -z \"$version\" ]; then\n                  # 未安装 java 17 版本\n                  # 判断 java17 是否安装到本地路径\n                  if [ -e jdk/jdk17/bin/java ]; then\n                     echo \"find java17 in ~/appstack/jdk/jdk17/bin/java\"\n                     JAVA=jdk/jdk17/bin/java\n                  else \n                     # 下载 java17\n                     wget -O jdk17.tar.gz \"https://mirrors.tuna.tsinghua.edu.cn/Adoptium/17/jdk/x64/linux/OpenJDK17U-jdk_x64_linux_hotspot_17.0.11_9.tar.gz\"\n                     mkdir -p jdk\n                     tar -zxvf jdk17.tar.gz -C jdk\n                     mv jdk/* jdk/jdk17\n                     JAVA=jdk/jdk17/bin/java\n                  fi\n                else\n                # 已安装 java 17 版本\n                  echo \"already install java17\"\n                  JAVA=java\n                fi\n                echo \"java: $JAVA\"\n                # 设置应用所需的环境变量\n                export AI_API_KEY={{ .Values.apiKey }}\n                export SERVER_PORT={{ .Values.serverPort }}\n                # 默认只有一个应用 jar 包，如果不只一个需要调整脚本\n                nohup $JAVA -jar ai-demo/*.jar > ai-demo/application.log 2>&1 &\n                echo \"application start\"\n            stop:\n              command: |-\n                # 默认只有一个应用 java 进程，如果不只一个需要调整脚本\n                pid=$(ps -ef | grep java | grep -v grep | awk '{print $2}')\n                if [ -z $pid ]; then\n                    echo \"no java application\"\n                else\n                    echo \"kill java application pid: $pid\"\n                    kill -9 $pid\n                fi\n            descriptors:\n              source: local\n      traits: []\n", "priority": 1, "type": "HOST"}], "groupNameMap": {}}], "revision": {"repoMeta": {"name": "PRESET_SPRING_AI", "type": "appTemplateOrchestration"}, "sha": "428afe7c6490949bcac28460517e4671f64a9e00", "message": "修改编排模板", "author": "646dabc05ccc4ad1bc2a9e2f", "refs": [], "commitTime": 1715830152848}, "branchInfo": {"head": {"repoMeta": {"name": "PRESET_SPRING_AI", "type": "appTemplateOrchestration"}, "sha": "428afe7c6490949bcac28460517e4671f64a9e00", "message": "修改编排模板", "author": "646dabc05ccc4ad1bc2a9e2f", "refs": [], "commitTime": 1715830152848}, "repoMeta": {"name": "PRESET_SPRING_AI", "type": "appTemplateOrchestration"}, "name": "master", "startRevisionSha": "", "fromBranch": ""}}}, {"sn": "2cbd7e2a6f814755b6653284725c47af", "appTemplateName": "PRESET_SPRING_AI", "type": "VariableGroup", "modeSetting": {"configType": "VariableGroup"}, "configuration": {"type": "VariableGroup", "profileMap": {"test-variables": {"name": "test-variables", "displayName": "测试环境变量组", "vars": [{"key": "api-key", "value": "sk-a3d73b1709bf4a178c28ed7c8bxxxxxx", "description": "请替换成你的有效阿里云 DashScope API-KEY：https://help.aliyun.com/zh/dashscope/developer-reference/activate-dashscope-and-create-an-api-key"}, {"key": "serverPort", "value": "8080", "description": "服务端口"}]}, "pre-variables": {"name": "pre-variables", "displayName": "预发环境变量组", "vars": [{"key": "api-key", "value": "sk-a3d73b1709bf4a178c28ed7c8bxxxxxx", "description": "请替换成你的有效阿里云 DashScope API-KEY：https://help.aliyun.com/zh/dashscope/developer-reference/activate-dashscope-and-create-an-api-key"}, {"key": "serverPort", "value": "8080", "description": "服务端口"}]}, "prod-variables": {"name": "prod-variables", "displayName": "生产环境变量组", "vars": [{"key": "api-key", "value": "sk-a3d73b1709bf4a178c28ed7c8bxxxxxx", "description": "请替换成你的有效阿里云 DashScope API-KEY：https://help.aliyun.com/zh/dashscope/developer-reference/activate-dashscope-and-create-an-api-key"}, {"key": "serverPort", "value": "8080", "description": "服务端口"}]}}, "revision": {"repoMeta": {"name": "PRESET_SPRING_AI", "type": "variableTemplate"}, "sha": "68233f0df00076d81466129e17adc72c3a48890a", "message": "新增变量组生产环境变量组", "author": "646dabc05ccc4ad1bc2a9e2f", "refs": [], "commitTime": 1715827217758}, "branchInfo": {"head": {"repoMeta": {"name": "PRESET_SPRING_AI", "type": "variableTemplate"}, "sha": "68233f0df00076d81466129e17adc72c3a48890a", "message": "新增变量组生产环境变量组", "author": "646dabc05ccc4ad1bc2a9e2f", "refs": [], "commitTime": 1715827217758}, "repoMeta": {"name": "PRESET_SPRING_AI", "type": "variableTemplate"}, "name": "master", "startRevisionSha": "", "fromBranch": ""}}}]}