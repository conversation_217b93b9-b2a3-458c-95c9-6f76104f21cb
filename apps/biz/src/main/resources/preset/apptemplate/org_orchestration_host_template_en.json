{"componentList": [{"name": "{{ .AppStack.appName }}", "type": "HOST", "kind": "Application", "content": "apiVersion: core.oam.dev/v1beta1\nkind: Application\nmetadata:\n  name: '{{ .AppStack.appName }}-{{ .AppStack.envName }}'\nspec:\n  components:\n    - name: '{{ .AppStack.appName }}-{{ .AppStack.envName }}'\n      type: hybrid-workload\n      properties:\n        specialization: non-containerized\n        bootstrap:\n          - type: http-direct\n            fromUrl: '{{ .AppStack.artifact.demoapp }}'\n            toPath: /package.tgz\n        packages:\n          - type: local\n            name: '{{ .AppStack.appName }}-{{ .AppStack.envName }}'\n            path: '# Example：/home/<USER>/app/package.tgz'\n            executedBy: 'admin'\n            start:\n              command: |-\n                # mkdir -p /home/<USER>/application\n                # tar zxvf /home/<USER>/app/package.tgz -C /home/<USER>/application/\n                # sh /home/<USER>/application/deploy.sh start\n            stop:\n              command: |-\n                # sh /home/<USER>/application/deploy.sh stop\n            healthCheck:\n              command: |-\n                # sh /home/<USER>/application/deploy.sh health-check\n            descriptors:\n              source: local\n      traits: []"}], "placeholderList": [{"name": "artifact.demoapp", "description": "Artifact url", "type": "string", "value": "NULL", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": true, "rsType": "HOST"}, {"name": "appName", "description": "App name", "type": "string", "value": "APPSTACK_APP_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "HOST"}, {"name": "envName", "description": "Env name", "type": "string", "value": "APPSTACK_ENV_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "HOST"}]}