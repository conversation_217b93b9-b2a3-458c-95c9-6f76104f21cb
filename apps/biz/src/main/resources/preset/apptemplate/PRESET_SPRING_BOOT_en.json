{"template": {"name": "PRESET_SPRING_BOOT", "displayName": "Spring-boot Application", "cover": "https://img.alicdn.com/imgextra/i3/O1CN01FdRgIw1TjUeBBG8Ir_!!6000000002418-55-tps-200-200.svg", "description": "This application template demonstrates the construction and deployment process of the spring-boot application, based on K8s deployment."}, "configs": [{"sn": "71079fbcbd024f0cb2e99199d3f9e30a", "appTemplateName": "PRESET_SPRING_BOOT", "type": "VariableGroup", "modeSetting": {"configType": "VariableGroup"}, "configuration": {"type": "VariableGroup", "profileMap": {"dev": {"name": "dev", "displayName": "<PERSON>", "vars": [{"key": "namespace", "value": "default", "description": "namespace"}]}, "test": {"name": "test", "displayName": "Test Vars", "vars": [{"key": "namespace", "value": "default", "description": "namespace"}]}, "prepub": {"name": "prepub", "displayName": "Prepub Vars", "vars": [{"key": "namespace", "value": "default", "description": "namespace"}]}, "production": {"name": "production", "displayName": "Production Vars", "vars": [{"key": "namespace", "value": "default", "description": "namespace"}]}}, "revision": {"repoMeta": {"name": "PRESET_SPRING_BOOT", "type": "variableTemplate"}, "sha": "71644eef8b31549cff5ca2feaef10c53d2f1a655", "message": "修改 生产环境变量组 名称", "author": "61c92a37ae805dbacc5b9f78", "refs": [], "commitTime": 1722246976416}, "branchInfo": {"head": {"repoMeta": {"name": "PRESET_SPRING_BOOT", "type": "variableTemplate"}, "sha": "71644eef8b31549cff5ca2feaef10c53d2f1a655", "message": "修改 生产环境变量组 名称", "author": "61c92a37ae805dbacc5b9f78", "refs": [], "commitTime": 1722246976416}, "repoMeta": {"name": "PRESET_SPRING_BOOT", "type": "variableTemplate"}, "name": "master", "startRevisionSha": "", "fromBranch": ""}}}, {"sn": "cdf77934f1464966993aaa92ba7a9bb4", "appTemplateName": "PRESET_SPRING_BOOT", "type": "Orchestration", "modeSetting": {"configType": "Orchestration", "modes": {"default_builtin": "Synchronization"}}, "configuration": {"type": "Orchestration", "orchestrations": [{"storageType": "BUILTIN", "format": "MANIFEST", "suitableResourceTypes": ["KUBERNETES"], "sn": "PRESET_SPRING_BOOT@KUBERNETES", "revision": {"sha": "688e8f4424fdc9689113c8dce1710c87c5147f73", "message": "修改编排模板", "author": "61c92a37ae805dbacc5b9f78", "commitTime": "2024-07-29T09:54:41.838+00:00"}, "name": "PRESET_SPRING_BOOT", "creatorId": "61c92a37ae805dbacc5b9f78", "gmtCreate": "2024-07-29T09:53:01.000+00:00", "modifierId": "61c92a37ae805dbacc5b9f78", "gmtModified": "2024-07-29T09:54:42.000+00:00", "description": null, "labelPolicy": "FROM_LABEL_BINDING", "labelList": [{"namespace": "default", "name": "envType", "value": "dev", "displayName": "环境级别", "displayValue": "开发环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "placeholderList": [{"name": "image.backend", "description": "Image", "type": "string", "value": "NULL", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": true, "rsType": "KUBERNETES"}, {"name": "appName", "description": "应用名", "type": "string", "value": "APPSTACK_APP_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "KUBERNETES"}, {"name": "envName", "description": "环境名", "type": "string", "value": "APPSTACK_ENV_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "KUBERNETES"}, {"name": "namespace", "description": "namespace", "type": "string", "value": "default", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "cpuLimit", "description": "cpu limit", "type": "string", "value": "1", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "memoryLimit", "description": "memory limit", "type": "string", "value": "1024Mi", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "cpuRequest", "description": "cpu request", "type": "string", "value": "0.01", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "memoryRequest", "description": "memory request", "type": "string", "value": "32Mi", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}], "componentList": [{"name": "demo-service", "kind": "Service", "description": "服务访问策略", "content": "---\napiVersion: v1\nkind: Service\nmetadata:\n  name: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  namespace: {{ .Values.namespace }}\nspec:\n  selector:\n    run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  ports:\n    - protocol: TCP\n      port: 80\n      targetPort: 8080", "priority": 1, "type": "KUBERNETES"}, {"name": "demo-deployment", "kind": "Deployment", "description": "无状态应用", "content": "---\napiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  labels:\n    run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  namespace: {{ .Values.namespace }}\nspec:\n  replicas: 1\n  selector:\n    matchLabels:\n      run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  template:\n    metadata:\n      labels:\n        run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n    spec:\n      containers:\n        - name: main", "priority": 2, "type": "KUBERNETES"}], "groupNameMap": {}}], "revision": {"repoMeta": {"name": "PRESET_SPRING_BOOT", "type": "appTemplateOrchestration"}, "sha": "688e8f4424fdc9689113c8dce1710c87c5147f73", "message": "修改编排模板", "author": "61c92a37ae805dbacc5b9f78", "refs": [], "commitTime": 1722246881838}, "branchInfo": {"head": {"repoMeta": {"name": "PRESET_SPRING_BOOT", "type": "appTemplateOrchestration"}, "sha": "688e8f4424fdc9689113c8dce1710c87c5147f73", "message": "修改编排模板", "author": "61c92a37ae805dbacc5b9f78", "refs": [], "commitTime": 1722246881838}, "repoMeta": {"name": "PRESET_SPRING_BOOT", "type": "appTemplateOrchestration"}, "name": "master", "startRevisionSha": "", "fromBranch": ""}}}, {"sn": "0236ab477d224e4783e0d949e7934bf7", "appTemplateName": "PRESET_SPRING_BOOT", "type": "ReleaseWorkflow", "modeSetting": {"configType": "ReleaseWorkflow", "modes": {"标准研发流程": "Initialization"}}, "configuration": {"type": "ReleaseWorkflow", "appTemplateWorkflowList": [{"sn": "5d9044ec-6992-43be-b558-26e99e53efcf", "name": "Standard", "appTemplateName": "PRESET_SPRING_BOOT", "releaseStageTemplate": [{"sn": "Test", "name": "Test", "labels": [{"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Test", "owner": "ed99ca55-1fe1-4ad3-9fc3-9314edf0e64b", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: Test\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Maven Unit Test\n            task: execution-component-production@20\n            identifier: '10_1715832711076'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Maven Unit Test\n                  stepType: java-maven-unit-test\n                  stepIdentifier: '10_1715832711076__11_1715832711076'\n                  TEST_REPORT_PATH: target/site\n                  TEST_REPORT_INDEX: surefire-report.html\n                  CI_RUNTIME_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  command: |\n                    # maven test default command\n                    mvn -B test -Dmaven.test.failure.ignore=true\n                    mvn surefire-report:report-only\n                    mvn site -DgenerateReports=false\n                  CHECK_REDLINES: ''\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n      - driven: AUTO\n        jobs:\n          - displayName: Java Code Scan\n            task: execution-component-production@20\n            identifier: '12_1715832714878'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java P3C Scan\n                  stepType: p3c\n                  stepIdentifier: '12_1715832714878__13_1715832714878'\n                  JDK_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  INCREASE: false\n                  USE_CUSTOM_RULE: false\n                  RULE_SET: >-\n                    ali-comment.xml,ali-concurrent.xml,ali-constant.xml,ali-exception.xml,ali-flowcontrol.xml,ali-naming.xml,ali-oop.xml,ali-orm.xml,ali-other.xml,ali-set.xml\n                  RULE_DIR: .\n                  SUB_DIR: ''\n                  EXCLUSION: test/\n                  CHECK_REDLINES: ''\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: Build\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Build\n            task: execution-component-production@1\n            identifier: '15_1675155917344'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java Build\n                  stepType: java_build\n                  stepIdentifier: '15_1675155917344__16_1675155917345'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                - name: Image Build & Image Push\n                  stepType: custom_docker_build\n                  stepIdentifier: '15_1675155917344__17_1675155917347'\n                  artifact: artifact\n                  DOCKER_DESTINATION: 请替换成你的镜像仓库地址\n                  authType: usernamepassword\n                  SERVICE_CONNECTION_ID: ''\n                  DOCKER_USER: 请替换成你的镜像仓库用户名\n                  DOCKER_PASSWORD: '123456'\n                  DOCKER_FILE_PATH: Dockerfile\n                  CONTEXT_PATH: ''\n                  NO_CACHE: false\n                  ARGS: '{}'\n                  JSONEncoding: true\n                  INSECURE_REGISTRY: false\n                  freeInTaskGroupModeFields:\n                    - ARGS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              clone_option: all\n              selected_sources: ''\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n            plugins: []\n            output:\n              - name: 15_1675155917344__17_1675155917347.dynamic_output.请替换成你的镜像仓库地址\n                displayName: 镜像仓库地址.请替换成你的镜像仓库地址\n                type: artifact\n                identifier: 15_1675155917344__17_1675155917347__DOCKER_DESTINATION\n                description: 自定义镜像仓库地址\n                export: true\n                ref: steps\n                jobIdentifier: '15_1675155917344'\n            freeInTaskGroupModeFields: []\n  - name: Deploy\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Deploy Test Env\n            task: execution-component-production@1\n            identifier: '18_1675155917349'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '18_1675155917349__19_1675155917350'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: test\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: backend\n                      value: >-\n                        ${15_1675155917344.15_1675155917344__17_1675155917347.dynamic_output.请替换成你的镜像仓库地址}\n                      type: upstream\n                  AUTO_SUBMIT: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              clone_option: all\n              selected_sources: ''\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "BmHXozCGAN8T75STIU3j", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "pipelineYaml": "schema: tb\npipeline:\n  - name: Test\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Maven Unit Test\n            task: execution-component-production@20\n            identifier: '10_1715832711076'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Maven Unit Test\n                  stepType: java-maven-unit-test\n                  stepIdentifier: '10_1715832711076__11_1715832711076'\n                  TEST_REPORT_PATH: target/site\n                  TEST_REPORT_INDEX: surefire-report.html\n                  CI_RUNTIME_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  command: |\n                    # maven test default command\n                    mvn -B test -Dmaven.test.failure.ignore=true\n                    mvn surefire-report:report-only\n                    mvn site -DgenerateReports=false\n                  CHECK_REDLINES: ''\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n      - driven: AUTO\n        jobs:\n          - displayName: Java Code Scan\n            task: execution-component-production@20\n            identifier: '12_1715832714878'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java P3C Scan\n                  stepType: p3c\n                  stepIdentifier: '12_1715832714878__13_1715832714878'\n                  JDK_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  INCREASE: false\n                  USE_CUSTOM_RULE: false\n                  RULE_SET: >-\n                    ali-comment.xml,ali-concurrent.xml,ali-constant.xml,ali-exception.xml,ali-flowcontrol.xml,ali-naming.xml,ali-oop.xml,ali-orm.xml,ali-other.xml,ali-set.xml\n                  RULE_DIR: .\n                  SUB_DIR: ''\n                  EXCLUSION: test/\n                  CHECK_REDLINES: ''\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: Build\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Build\n            task: execution-component-production@1\n            identifier: '15_1675155917344'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java Build\n                  stepType: java_build\n                  stepIdentifier: '15_1675155917344__16_1675155917345'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                - name: Image Build & Image Push\n                  stepType: custom_docker_build\n                  stepIdentifier: '15_1675155917344__17_1675155917347'\n                  artifact: artifact\n                  DOCKER_DESTINATION: 请替换成你的镜像仓库地址\n                  authType: usernamepassword\n                  SERVICE_CONNECTION_ID: ''\n                  DOCKER_USER: 请替换成你的镜像仓库用户名\n                  DOCKER_PASSWORD: '123456'\n                  DOCKER_FILE_PATH: Dockerfile\n                  CONTEXT_PATH: ''\n                  NO_CACHE: false\n                  ARGS: '{}'\n                  JSONEncoding: true\n                  INSECURE_REGISTRY: false\n                  freeInTaskGroupModeFields:\n                    - ARGS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              clone_option: all\n              selected_sources: ''\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n            plugins: []\n            output:\n              - name: 15_1675155917344__17_1675155917347.dynamic_output.请替换成你的镜像仓库地址\n                displayName: 镜像仓库地址.请替换成你的镜像仓库地址\n                type: artifact\n                identifier: 15_1675155917344__17_1675155917347__DOCKER_DESTINATION\n                description: 自定义镜像仓库地址\n                export: true\n                ref: steps\n                jobIdentifier: '15_1675155917344'\n            freeInTaskGroupModeFields: []\n  - name: Deploy\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Deploy Test Env\n            task: execution-component-production@1\n            identifier: '18_1675155917349'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '18_1675155917349__19_1675155917350'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: test\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: backend\n                      value: >-\n                        ${15_1675155917344.15_1675155917344__17_1675155917347.dynamic_output.请替换成你的镜像仓库地址}\n                      type: upstream\n                  AUTO_SUBMIT: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              clone_option: all\n              selected_sources: ''\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": []}, {"sn": "Prepub", "name": "Prepub", "labels": [{"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Prepub", "owner": "ed99ca55-1fe1-4ad3-9fc3-9314edf0e64b", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: Build\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Build\n            task: execution-component-production@1\n            identifier: '20_1675156443208'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java Build\n                  stepType: java_build\n                  stepIdentifier: '20_1675156443208__21_1675156443209'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                - name: Image Build & Image Push\n                  stepType: custom_docker_build\n                  stepIdentifier: '20_1675156443208__22_1675156443211'\n                  artifact: artifact\n                  DOCKER_DESTINATION: '# Replace your Image'\n                  authType: usernamepassword\n                  SERVICE_CONNECTION_ID: ''\n                  DOCKER_USER: '# Replace your account'\n                  DOCKER_PASSWORD: '123456'\n                  DOCKER_FILE_PATH: Dockerfile\n                  CONTEXT_PATH: ''\n                  NO_CACHE: false\n                  ARGS: '{}'\n                  JSONEncoding: true\n                  INSECURE_REGISTRY: false\n                  freeInTaskGroupModeFields:\n                    - ARGS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              clone_option: all\n              selected_sources: ''\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n            plugins: []\n            output:\n              - name: >-\n                  20_1675156443208__22_1675156443211.dynamic_output.# Replace\n                  your Image\n                displayName: 镜像仓库地址.# Replace your Image\n                type: artifact\n                identifier: 20_1675156443208__22_1675156443211__DOCKER_DESTINATION\n                description: 自定义镜像仓库地址\n                export: true\n                ref: steps\n                jobIdentifier: '20_1675156443208'\n            freeInTaskGroupModeFields: []\n  - name: Deploy\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Deploy Prepub Env\n            task: execution-component-production@1\n            identifier: '23_1675156443212'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '23_1675156443212__24_1675156443213'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: pre\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: backend\n                      value: >-\n                        ${20_1675156443208.20_1675156443208__22_1675156443211.dynamic_output.#\n                        Replace your Image}\n                      type: upstream\n                  AUTO_SUBMIT: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              clone_option: all\n              selected_sources: ''\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n            plugins: []\n            output:\n              - name: >-\n                  23_1675156443212__24_1675156443213.dynamic_output.${APPSTACK_APP_NAME}\n                displayName: '应用.${APPSTACK_APP_NAME}'\n                type: artifact\n                identifier: 23_1675156443212__24_1675156443213__APP_NAME\n                description: 部署单\n                export: true\n                ref: steps\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "lXBCBVuo5ijkb3xECooX", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "pipelineYaml": "schema: tb\npipeline:\n  - name: Build\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Build\n            task: execution-component-production@1\n            identifier: '20_1675156443208'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java Build\n                  stepType: java_build\n                  stepIdentifier: '20_1675156443208__21_1675156443209'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                - name: Image Build & Image Push\n                  stepType: custom_docker_build\n                  stepIdentifier: '20_1675156443208__22_1675156443211'\n                  artifact: artifact\n                  DOCKER_DESTINATION: '# Replace your Image'\n                  authType: usernamepassword\n                  SERVICE_CONNECTION_ID: ''\n                  DOCKER_USER: '# Replace your account'\n                  DOCKER_PASSWORD: '123456'\n                  DOCKER_FILE_PATH: Dockerfile\n                  CONTEXT_PATH: ''\n                  NO_CACHE: false\n                  ARGS: '{}'\n                  JSONEncoding: true\n                  INSECURE_REGISTRY: false\n                  freeInTaskGroupModeFields:\n                    - ARGS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              clone_option: all\n              selected_sources: ''\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n            plugins: []\n            output:\n              - name: >-\n                  20_1675156443208__22_1675156443211.dynamic_output.# Replace\n                  your Image\n                displayName: 镜像仓库地址.# Replace your Image\n                type: artifact\n                identifier: 20_1675156443208__22_1675156443211__DOCKER_DESTINATION\n                description: 自定义镜像仓库地址\n                export: true\n                ref: steps\n                jobIdentifier: '20_1675156443208'\n            freeInTaskGroupModeFields: []\n  - name: Deploy\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Deploy Prepub Env\n            task: execution-component-production@1\n            identifier: '23_1675156443212'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '23_1675156443212__24_1675156443213'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: pre\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: backend\n                      value: >-\n                        ${20_1675156443208.20_1675156443208__22_1675156443211.dynamic_output.#\n                        Replace your Image}\n                      type: upstream\n                  AUTO_SUBMIT: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              clone_option: all\n              selected_sources: ''\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n            plugins: []\n            output:\n              - name: >-\n                  23_1675156443212__24_1675156443213.dynamic_output.${APPSTACK_APP_NAME}\n                displayName: '应用.${APPSTACK_APP_NAME}'\n                type: artifact\n                identifier: 23_1675156443212__24_1675156443213__APP_NAME\n                description: 部署单\n                export: true\n                ref: steps\n            freeInTaskGroupModeFields: []\n", "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "ON", "validateStageSnList": ["测试阶段"], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": []}, {"sn": "Production", "name": "Production", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Production", "owner": "ed99ca55-1fe1-4ad3-9fc3-9314edf0e64b", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: Validation\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Validation\n            task: APPSTACK_DIY_VALIDATE_TB_PROD@1\n            identifier: '30_1675156498088'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              appId: '${INPUTS.appId}'\n              mixFlowInstId: '${INPUTS.mixFlowInstId}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              validatorMethod: or\n              validatorType: role\n              validatorUser: null\n              validatorRole: 66067a82c778866622c0f135\n              timeoutTime: '0'\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: Deploy\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Deploy Production Env\n            task: execution-component-production@1\n            identifier: '31_1675156498088'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '31_1675156498088__32_1675156498089'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: prod\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: backend\n                      value: '# Replace your Image'\n                      type: custom\n                  AUTO_SUBMIT: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              clone_option: all\n              selected_sources: ''\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n            plugins: []\n            output:\n              - name: >-\n                  31_1675156498088__32_1675156498089.dynamic_output.${APPSTACK_APP_NAME}\n                displayName: '应用.${APPSTACK_APP_NAME}'\n                type: artifact\n                identifier: 31_1675156498088__32_1675156498089__APP_NAME\n                description: 部署单\n                export: true\n                ref: steps\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "t1MnbqhHBFbUCzwY9b5O", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "pipelineYaml": "schema: tb\npipeline:\n  - name: Validation\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Validation\n            task: APPSTACK_DIY_VALIDATE_TB_PROD@1\n            identifier: '30_1675156498088'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              appId: '${INPUTS.appId}'\n              mixFlowInstId: '${INPUTS.mixFlowInstId}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              validatorMethod: or\n              validatorType: role\n              validatorUser: null\n              validatorRole: 66067a82c778866622c0f135\n              timeoutTime: '0'\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: Deploy\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Deploy Production Env\n            task: execution-component-production@1\n            identifier: '31_1675156498088'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '31_1675156498088__32_1675156498089'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: prod\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: backend\n                      value: '# Replace your Image'\n                      type: custom\n                  AUTO_SUBMIT: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              clone_option: all\n              selected_sources: ''\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n            plugins: []\n            output:\n              - name: >-\n                  31_1675156498088__32_1675156498089.dynamic_output.${APPSTACK_APP_NAME}\n                displayName: '应用.${APPSTACK_APP_NAME}'\n                type: artifact\n                identifier: 31_1675156498088__32_1675156498089__APP_NAME\n                description: 部署单\n                export: true\n                ref: steps\n            freeInTaskGroupModeFields: []\n", "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "ON", "validateStageSnList": ["测试阶段", "预发阶段"], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": []}], "fromRevisionSha": "ed9f5c2191697ebff0899660eda4813327b3287a", "message": "修改研发流程阶段[Production]流水线", "order": "1"}]}}, {"sn": "0ed983b4e1d446d0bb3666c0d7a42ff1", "appTemplateName": "PRESET_SPRING_BOOT", "type": "Env", "modeSetting": {"configType": "Env"}, "configuration": {"type": "Env", "envs": [{"name": "dev", "displayName": "<PERSON>", "labels": [{"namespace": "default", "name": "envType", "value": "dev", "displayName": "环境级别", "displayValue": "开发环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "dev", "displayName": "<PERSON>", "type": "TEMPLATE"}], "deployType": "KUBERNETES", "resourcePoolName": "default", "deployGroupName": null, "description": "<PERSON>", "spec": {"migrateState": null, "oamRebuildState": null, "withoutOam": null, "rolloutStrategy": [{"locator": "*", "batches": null, "batchSteps": null, "timeOutMS": 600000, "targetReplicas": null, "batchMode": "ConfirmFirstBatch", "deployType": "<PERSON><PERSON>"}], "replicasManagement": "SYSTEM"}}, {"name": "test", "displayName": "Test", "labels": [{"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "test", "displayName": "Test Vars", "type": "TEMPLATE"}], "deployType": "KUBERNETES", "resourcePoolName": "default", "deployGroupName": null, "description": "Test", "spec": {"migrateState": null, "oamRebuildState": null, "withoutOam": null, "rolloutStrategy": [{"locator": "*", "batches": null, "batchSteps": null, "timeOutMS": 600000, "targetReplicas": null, "batchMode": "ConfirmFirstBatch", "deployType": "<PERSON><PERSON>"}], "replicasManagement": "SYSTEM"}}, {"name": "pre", "displayName": "Prepub", "labels": [{"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "prepub", "displayName": "Prepub Vars", "type": "TEMPLATE"}], "deployType": "KUBERNETES", "resourcePoolName": "default", "deployGroupName": null, "description": "Prepub", "spec": {"migrateState": null, "oamRebuildState": null, "withoutOam": null, "rolloutStrategy": [{"locator": "*", "batches": null, "batchSteps": null, "timeOutMS": 600000, "targetReplicas": null, "batchMode": "ConfirmFirstBatch", "deployType": "<PERSON><PERSON>"}], "replicasManagement": "SYSTEM"}}, {"name": "prod", "displayName": "Production", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "production", "displayName": "Production Vars", "type": "TEMPLATE"}], "deployType": "KUBERNETES", "resourcePoolName": "default", "deployGroupName": null, "description": "Production", "spec": {"migrateState": null, "oamRebuildState": null, "withoutOam": null, "rolloutStrategy": [{"locator": "*", "batches": null, "batchSteps": null, "timeOutMS": 600000, "targetReplicas": null, "batchMode": "ConfirmFirstBatch", "deployType": "<PERSON><PERSON>"}], "replicasManagement": "SYSTEM"}}]}}, {"sn": "59373c64eada4ea39ab90e842841db52", "appTemplateName": "PRESET_SPRING_BOOT", "type": "Source", "modeSetting": {"configType": "Source"}, "configuration": {"type": "Source", "codeRepos": [{"name": "spring-boot", "repoUrl": "https://atomgit.com/appstack-example/spring-boot.git", "identifier": "spring_boot", "repoContext": {"repoUrl": "https://atomgit.com/appstack-example/spring-boot.git", "defaultBranch": "master", "repoType": "GIT"}, "connectionConfig": {"connectionId": "", "connectionType": "FLOW"}}], "artifactRepos": []}}]}