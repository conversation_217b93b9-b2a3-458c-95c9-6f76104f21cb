{"template": {"name": "PRESET_SPRING_AI", "displayName": "Spring AI Application", "cover": "https://img.alicdn.com/imgextra/i4/O1CN01Vk4ufi1MXOsZVSaxZ_!!6000000001444-55-tps-200-200.svg", "description": "This application template demonstrates how to use Yunxiao AppStack to quickly build and deploy an AI chat question-and-answer assistant, start your first AI application development, and allow Java developers to develop AI applications just like using Spring to develop ordinary applications. See more：https://atomgit.com/appstack-example/spring-cloud-ai/blob/master/README.md"}, "configs": [{"sn": "609e4dc1a93f43748718d3b6dc2fca2e", "appTemplateName": "PRESET_SPRING_AI", "type": "Source", "modeSetting": {"configType": "Source"}, "configuration": {"type": "Source", "codeRepos": [{"name": "spring-cloud-ai", "repoUrl": "https://atomgit.com/appstack-example/spring-cloud-ai.git", "identifier": "spring_cloud_ai", "repoContext": {"repoUrl": "https://atomgit.com/appstack-example/spring-cloud-ai.git", "defaultBranch": "master", "repoType": "GIT"}, "connectionConfig": {"connectionId": "", "connectionType": "FLOW"}}], "artifactRepos": []}}, {"sn": "3ca08070c5bd4e4aaca5b80006c6f0e0", "appTemplateName": "PRESET_SPRING_AI", "type": "Env", "modeSetting": {"configType": "Env"}, "configuration": {"type": "Env", "envs": [{"name": "test-env", "displayName": "Test Env", "labels": [{"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "test-variables", "displayName": "Test Vars", "type": "TEMPLATE"}], "deployType": "HOST", "resourcePoolName": "default", "deployGroupName": null, "description": "Test Env", "spec": {"migrateState": null, "oamRebuildState": null, "withoutOam": null, "rolloutStrategy": [{"locator": "*", "batches": null, "batchSteps": null, "timeOutMS": 600000, "targetReplicas": null, "batchMode": "ConfirmFirstBatch", "deployType": "Recreate"}], "replicasManagement": "SYSTEM"}}, {"name": "pre-env", "displayName": "Prepub Env", "labels": [{"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "pre-variables", "displayName": "Prepub Vars", "type": "TEMPLATE"}], "deployType": "HOST", "resourcePoolName": "default", "deployGroupName": null, "description": "Prepub Env", "spec": {"migrateState": null, "oamRebuildState": null, "withoutOam": null, "rolloutStrategy": [{"locator": "*", "batches": null, "batchSteps": null, "timeOutMS": 600000, "targetReplicas": null, "batchMode": "ConfirmFirstBatch", "deployType": "Recreate"}], "replicasManagement": "SYSTEM"}}, {"name": "prod-env", "displayName": "Production Env", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "prod-variables", "displayName": "Production Vars", "type": "TEMPLATE"}], "deployType": "HOST", "resourcePoolName": "default", "deployGroupName": null, "description": "Production Env", "spec": {"migrateState": null, "oamRebuildState": null, "withoutOam": null, "rolloutStrategy": [{"locator": "*", "batches": null, "batchSteps": null, "timeOutMS": 600000, "targetReplicas": null, "batchMode": "ConfirmFirstBatch", "deployType": "Recreate"}], "replicasManagement": "SYSTEM"}}]}}, {"sn": "12c48763f31f4ca289c23251aaf220cf", "appTemplateName": "PRESET_SPRING_AI", "type": "ReleaseWorkflow", "modeSetting": {"configType": "ReleaseWorkflow", "modes": {}}, "configuration": {"type": "ReleaseWorkflow", "appTemplateWorkflowList": [{"sn": "6bab1ab2-db57-4ed1-ba29-7d434168290d", "name": "Standard", "appTemplateName": "PRESET_SPRING_AI", "releaseStageTemplate": [{"sn": "Test", "name": "Test", "labels": [{"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Test", "owner": "64a3940486937ebe0ce9bb7a", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}],\"globalParams\":[]}", "flow": "schema: tb\npipeline:\n  - name: Test\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Java Code Scan\n            task: execution-component-production@20\n            identifier: '12_1715827592350'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java P3C\n                  stepType: p3c\n                  stepIdentifier: '12_1715827592350__13_1715827592351'\n                  JDK_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  INCREASE: false\n                  USE_CUSTOM_RULE: false\n                  RULE_SET: >-\n                    ali-comment.xml,ali-concurrent.xml,ali-constant.xml,ali-exception.xml,ali-flowcontrol.xml,ali-naming.xml,ali-oop.xml,ali-orm.xml,ali-other.xml,ali-set.xml\n                  RULE_DIR: .\n                  SUB_DIR: ''\n                  EXCLUSION: test/\n                  CHECK_REDLINES: ''\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: Build\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Build\n            task: execution-component-production@20\n            identifier: '10_1715397594667'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version: jdk1.8\n              steps:\n                - name: Java Build\n                  stepType: java_build\n                  stepIdentifier: '10_1715397594667__12_1715397594669'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -s settings.xml -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                - name: Artifact Upload\n                  stepType: tb_oss_upload\n                  stepIdentifier: '10_1715397594667__13_1715397594670'\n                  uploadType: PUBLIC_STORAGE\n                  SERVICE_CONNECTION_ID: ''\n                  REPO_ID: ''\n                  ARTIFACT: 'Artifacts_${PIPELINE_ID}'\n                  ARTIFACT_VERSION: '${DATETIME}'\n                  SHOULD_PACKAGE: true\n                  ARCHIVE_PATH: target/\n                  INCLUDE_PATH_IN_ARTIFACT: false\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - name: >-\n                  10_1715397594667__13_1715397594670.dynamic_output.Artifacts_${PIPELINE_ID}\n                displayName: '制品名称.Artifacts_${PIPELINE_ID}'\n                type: artifact\n                identifier: 10_1715397594667__13_1715397594670__ARTIFACT\n                description: 自定义产出物名称，定义后在部署组件使用\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715397594667'\n            freeInTaskGroupModeFields: []\n  - name: Deploy\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Deploy\n            task: execution-component-production@20\n            identifier: '14_1715397646449'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '14_1715397646449__15_1715397646450'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: test-env\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: package\n                      value: >-\n                        ${10_1715397594667.10_1715397594667__13_1715397594670.dynamic_output.Artifacts_${PIPELINE_ID}}\n                      type: upstream\n                  AUTO_SUBMIT: true\n                  AUTO_SUBMIT_LATEST_FAILED_CHECK: true\n                  AUTO_SUBMIT_ORCHESTRATION_CHANGED_CHECK: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: Validation\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Validation\n            task: execution-component-production@20\n            identifier: '12_1716549029698'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version1: pre-jdk1.62\n              steps:\n                - name: 执行命令\n                  stepType: exec-shell\n                  stepIdentifier: '12_1716549029698__13_1716549029698'\n                  command: >\n                    #!/bin/bash\n\n                    set -e\n\n\n                    # 系统提供参数，从流水线上下文获取\n\n                    echo [INFO] 系统变量\n\n                    echo [INFO] PIPELINE_ID=$PIPELINE_ID       # 流水线ID\n\n                    echo [INFO] PIPELINE_NAME=$PIPELINE_NAME   # 流水线名称\n\n                    echo [INFO] BUILD_NUMBER=$BUILD_NUMBER     # 流水线运行实例编号\n\n                    echo [INFO] EMPLOYEE_ID=$EMPLOYEE_ID       # 触发流水线用户ID\n\n                    echo [INFO] WORK_SPACE=$WORK_SPACE         #\n                    /root/workspace容器中目录\n\n                    echo [INFO] PROJECT_DIR=$PROJECT_DIR       #\n                    代码库根路径，默认为/root/workspace/code\n\n                    echo [INFO] PLUGIN_DIR=$PLUGIN_DIR         #\n                    插件路径，默认为/root/plugins\n\n                    echo [INFO] BUILD_JOB_ID=$BUILD_JOB_ID     # build-service\n                    任务ID\n\n                    echo [INFO] STEP_ID=$stepIdentifier        # yml 步骤ID\n\n                    echo [INFO] CREATOR_ALIYUN_PK=$CREATOR_ALIYUN_PK\n\n                    echo [INFO] INGRESS_HOST=$INGRESS_HOST\n\n\n                    curl --location\n                    \"https://devops.aliyun.com/appstack/hook/api/ucc/report?aliyunPk=$CREATOR_ALIYUN_PK&userId=$EMPLOYEE_ID&pipelineId=$PIPELINE_ID\"\n\n\n                    if [[ $? == 0 ]]\n\n                    then\n                      echo [INFO] 注册云效体验活动成功\n                    else\n                      echo [ERROR] 注册云效体验活动失败\n                      exit 1\n                    fi\n                  ARTIFACTS: '{}'\n                  JSONEncoding: true\n                  freeInTaskGroupModeFields:\n                    - ARTIFACTS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: none\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "nbzeqWPcks8i0AcnZDmy", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "pipelineYaml": "schema: tb\npipeline:\n  - name: Test\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Java Code Scan\n            task: execution-component-production@20\n            identifier: '12_1715827592350'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java P3C\n                  stepType: p3c\n                  stepIdentifier: '12_1715827592350__13_1715827592351'\n                  JDK_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  INCREASE: false\n                  USE_CUSTOM_RULE: false\n                  RULE_SET: >-\n                    ali-comment.xml,ali-concurrent.xml,ali-constant.xml,ali-exception.xml,ali-flowcontrol.xml,ali-naming.xml,ali-oop.xml,ali-orm.xml,ali-other.xml,ali-set.xml\n                  RULE_DIR: .\n                  SUB_DIR: ''\n                  EXCLUSION: test/\n                  CHECK_REDLINES: ''\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: Build\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Build\n            task: execution-component-production@20\n            identifier: '10_1715397594667'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version: jdk1.8\n              steps:\n                - name: Java Build\n                  stepType: java_build\n                  stepIdentifier: '10_1715397594667__12_1715397594669'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -s settings.xml -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                - name: Artifact Upload\n                  stepType: tb_oss_upload\n                  stepIdentifier: '10_1715397594667__13_1715397594670'\n                  uploadType: PUBLIC_STORAGE\n                  SERVICE_CONNECTION_ID: ''\n                  REPO_ID: ''\n                  ARTIFACT: 'Artifacts_${PIPELINE_ID}'\n                  ARTIFACT_VERSION: '${DATETIME}'\n                  SHOULD_PACKAGE: true\n                  ARCHIVE_PATH: target/\n                  INCLUDE_PATH_IN_ARTIFACT: false\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - name: >-\n                  10_1715397594667__13_1715397594670.dynamic_output.Artifacts_${PIPELINE_ID}\n                displayName: '制品名称.Artifacts_${PIPELINE_ID}'\n                type: artifact\n                identifier: 10_1715397594667__13_1715397594670__ARTIFACT\n                description: 自定义产出物名称，定义后在部署组件使用\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715397594667'\n            freeInTaskGroupModeFields: []\n  - name: Deploy\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Deploy\n            task: execution-component-production@20\n            identifier: '14_1715397646449'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '14_1715397646449__15_1715397646450'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: test-env\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: package\n                      value: >-\n                        ${10_1715397594667.10_1715397594667__13_1715397594670.dynamic_output.Artifacts_${PIPELINE_ID}}\n                      type: upstream\n                  AUTO_SUBMIT: true\n                  AUTO_SUBMIT_LATEST_FAILED_CHECK: true\n                  AUTO_SUBMIT_ORCHESTRATION_CHANGED_CHECK: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: Validation\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Validation\n            task: execution-component-production@20\n            identifier: '12_1716549029698'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version1: pre-jdk1.62\n              steps:\n                - name: 执行命令\n                  stepType: exec-shell\n                  stepIdentifier: '12_1716549029698__13_1716549029698'\n                  command: >\n                    #!/bin/bash\n\n                    set -e\n\n\n                    # 系统提供参数，从流水线上下文获取\n\n                    echo [INFO] 系统变量\n\n                    echo [INFO] PIPELINE_ID=$PIPELINE_ID       # 流水线ID\n\n                    echo [INFO] PIPELINE_NAME=$PIPELINE_NAME   # 流水线名称\n\n                    echo [INFO] BUILD_NUMBER=$BUILD_NUMBER     # 流水线运行实例编号\n\n                    echo [INFO] EMPLOYEE_ID=$EMPLOYEE_ID       # 触发流水线用户ID\n\n                    echo [INFO] WORK_SPACE=$WORK_SPACE         #\n                    /root/workspace容器中目录\n\n                    echo [INFO] PROJECT_DIR=$PROJECT_DIR       #\n                    代码库根路径，默认为/root/workspace/code\n\n                    echo [INFO] PLUGIN_DIR=$PLUGIN_DIR         #\n                    插件路径，默认为/root/plugins\n\n                    echo [INFO] BUILD_JOB_ID=$BUILD_JOB_ID     # build-service\n                    任务ID\n\n                    echo [INFO] STEP_ID=$stepIdentifier        # yml 步骤ID\n\n                    echo [INFO] CREATOR_ALIYUN_PK=$CREATOR_ALIYUN_PK\n\n                    echo [INFO] INGRESS_HOST=$INGRESS_HOST\n\n\n                    curl --location\n                    \"https://devops.aliyun.com/appstack/hook/api/ucc/report?aliyunPk=$CREATOR_ALIYUN_PK&userId=$EMPLOYEE_ID&pipelineId=$PIPELINE_ID\"\n\n\n                    if [[ $? == 0 ]]\n\n                    then\n                      echo [INFO] 注册云效体验活动成功\n                    else\n                      echo [ERROR] 注册云效体验活动失败\n                      exit 1\n                    fi\n                  ARTIFACTS: '{}'\n                  JSONEncoding: true\n                  freeInTaskGroupModeFields:\n                    - ARTIFACTS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: none\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": [{"name": "test-variables", "displayName": "Test Vars", "type": "TEMPLATE"}]}, {"sn": "Prepub", "name": "Prepub", "labels": [{"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Prepub", "owner": "646dabc05ccc4ad1bc2a9e2f", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: Build\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Build\n            task: execution-component-production@20\n            identifier: '10_1715827733104'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version: jdk1.8\n              steps:\n                - name: Java Build\n                  stepType: java_build\n                  stepIdentifier: '10_1715827733104__11_1715827733104'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -s settings.xml -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                - name: Artifact Upload\n                  stepType: tb_oss_upload\n                  stepIdentifier: '10_1715827733104__12_1715827733107'\n                  uploadType: PUBLIC_STORAGE\n                  SERVICE_CONNECTION_ID: ''\n                  REPO_ID: ''\n                  ARTIFACT: 'Artifacts_${PIPELINE_ID}'\n                  ARTIFACT_VERSION: '${DATETIME}'\n                  SHOULD_PACKAGE: true\n                  ARCHIVE_PATH: target/\n                  INCLUDE_PATH_IN_ARTIFACT: false\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - name: >-\n                  10_1715827733104__12_1715827733107.dynamic_output.Artifacts_${PIPELINE_ID}\n                displayName: '制品名称.Artifacts_${PIPELINE_ID}'\n                type: artifact\n                identifier: 10_1715827733104__12_1715827733107__ARTIFACT\n                description: 自定义产出物名称，定义后在部署组件使用\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715827733104'\n            freeInTaskGroupModeFields: []\n  - name: Deploy\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Deploy\n            task: execution-component-production@20\n            identifier: '13_1715827733108'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '13_1715827733108__14_1715827733109'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: pre-env\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: package\n                      value: >-\n                        ${10_1715827733104.10_1715827733104__12_1715827733107.dynamic_output.Artifacts_${PIPELINE_ID}}\n                      type: upstream\n                  AUTO_SUBMIT: true\n                  AUTO_SUBMIT_LATEST_FAILED_CHECK: true\n                  AUTO_SUBMIT_ORCHESTRATION_CHANGED_CHECK: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "Fdv4niL3QcnB4RTXlzzS", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "pipelineYaml": "schema: tb\npipeline:\n  - name: Build\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Build\n            task: execution-component-production@20\n            identifier: '10_1715827733104'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version: jdk1.8\n              steps:\n                - name: Java Build\n                  stepType: java_build\n                  stepIdentifier: '10_1715827733104__11_1715827733104'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -s settings.xml -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                - name: Artifact Upload\n                  stepType: tb_oss_upload\n                  stepIdentifier: '10_1715827733104__12_1715827733107'\n                  uploadType: PUBLIC_STORAGE\n                  SERVICE_CONNECTION_ID: ''\n                  REPO_ID: ''\n                  ARTIFACT: 'Artifacts_${PIPELINE_ID}'\n                  ARTIFACT_VERSION: '${DATETIME}'\n                  SHOULD_PACKAGE: true\n                  ARCHIVE_PATH: target/\n                  INCLUDE_PATH_IN_ARTIFACT: false\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - name: >-\n                  10_1715827733104__12_1715827733107.dynamic_output.Artifacts_${PIPELINE_ID}\n                displayName: '制品名称.Artifacts_${PIPELINE_ID}'\n                type: artifact\n                identifier: 10_1715827733104__12_1715827733107__ARTIFACT\n                description: 自定义产出物名称，定义后在部署组件使用\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715827733104'\n            freeInTaskGroupModeFields: []\n  - name: Deploy\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Deploy\n            task: execution-component-production@20\n            identifier: '13_1715827733108'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '13_1715827733108__14_1715827733109'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: pre-env\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: package\n                      value: >-\n                        ${10_1715827733104.10_1715827733104__12_1715827733107.dynamic_output.Artifacts_${PIPELINE_ID}}\n                      type: upstream\n                  AUTO_SUBMIT: true\n                  AUTO_SUBMIT_LATEST_FAILED_CHECK: true\n                  AUTO_SUBMIT_ORCHESTRATION_CHANGED_CHECK: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": [{"name": "pre-variables", "displayName": "Prepub Vars", "type": "TEMPLATE"}]}, {"sn": "Production", "name": "Production", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Production", "owner": "646dabc05ccc4ad1bc2a9e2f", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: Validation\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Validation\n            task: APPSTACK_DIY_VALIDATE_TB_PROD@1\n            identifier: '14_1715828084056'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              appId: '${INPUTS.appId}'\n              mixFlowInstId: '${INPUTS.mixFlowInstId}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              validatorMethod: or\n              validatorType: role\n              validatorUser: ''\n              validatorRole: 6628c0ce9f4372aeee9caf3e\n              timeoutTime: '0'\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: deploy\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Deploy\n            task: execution-component-production@20\n            identifier: '17_1715827986100'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '17_1715827986100__18_1715827986100'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: prod-env\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: package\n                      value: '# Replace with previous Artifact'\n                      type: custom\n                  AUTO_SUBMIT: true\n                  AUTO_SUBMIT_LATEST_FAILED_CHECK: true\n                  AUTO_SUBMIT_ORCHESTRATION_CHANGED_CHECK: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - name: >-\n                  17_1715827986100__18_1715827986100.dynamic_output.${APPSTACK_APP_NAME}\n                displayName: '应用.${APPSTACK_APP_NAME}'\n                type: artifact\n                identifier: 17_1715827986100__18_1715827986100__APP_NAME\n                description: 部署单\n                export: true\n                ref: steps\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "nlWaonQ3AWWO0dzAuJ4Q", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "pipelineYaml": "schema: tb\npipeline:\n  - name: Validation\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Validation\n            task: APPSTACK_DIY_VALIDATE_TB_PROD@1\n            identifier: '14_1715828084056'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              appId: '${INPUTS.appId}'\n              mixFlowInstId: '${INPUTS.mixFlowInstId}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              validatorMethod: or\n              validatorType: role\n              validatorUser: ''\n              validatorRole: 6628c0ce9f4372aeee9caf3e\n              timeoutTime: '0'\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: deploy\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Deploy\n            task: execution-component-production@20\n            identifier: '17_1715827986100'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '17_1715827986100__18_1715827986100'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: prod-env\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: package\n                      value: '# Replace with previous Artifact'\n                      type: custom\n                  AUTO_SUBMIT: true\n                  AUTO_SUBMIT_LATEST_FAILED_CHECK: true\n                  AUTO_SUBMIT_ORCHESTRATION_CHANGED_CHECK: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - name: >-\n                  17_1715827986100__18_1715827986100.dynamic_output.${APPSTACK_APP_NAME}\n                displayName: '应用.${APPSTACK_APP_NAME}'\n                type: artifact\n                identifier: 17_1715827986100__18_1715827986100__APP_NAME\n                description: 部署单\n                export: true\n                ref: steps\n            freeInTaskGroupModeFields: []\n", "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": [{"name": "prod-variables", "displayName": "Production Vars", "type": "TEMPLATE"}]}], "fromRevisionSha": "21be3929a6b9b0d481bc9830af23803b98a6a902", "message": "修改研发流程阶段[Production]基本信息", "order": "1"}]}}, {"sn": "bf410bfe5213449fa96cd923e563b1a8", "appTemplateName": "PRESET_SPRING_AI", "type": "Orchestration", "modeSetting": {"configType": "Orchestration", "modes": {"default_builtin": "Initialization"}}, "configuration": {"type": "Orchestration", "orchestrations": [{"storageType": "BUILTIN", "format": "MANIFEST", "suitableResourceTypes": ["HOST"], "sn": "PRESET_SPRING_AI@HOST", "revision": {"sha": "b32659cc0b67fe856b30734c4be699d3409b667e", "message": "修改编排模板", "author": "61c92a37ae805dbacc5b9f78", "commitTime": "2024-07-29T10:46:35.236+00:00"}, "name": "PRESET_SPRING_AI", "creatorId": "61c92a37ae805dbacc5b9f78", "gmtCreate": "2024-07-29T09:53:07.000+00:00", "modifierId": "61c92a37ae805dbacc5b9f78", "gmtModified": "2024-07-29T10:46:35.000+00:00", "description": null, "labelPolicy": "FROM_LABEL_BINDING", "labelList": [{"namespace": "default", "name": "envType", "value": "dev", "displayName": "环境级别", "displayValue": "开发环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "placeholderList": [{"name": "artifact.package", "description": "Artifact package", "type": "string", "value": "NULL", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": true, "rsType": "HOST"}, {"name": "appName", "description": "应用名", "type": "string", "value": "APPSTACK_APP_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "HOST"}, {"name": "envName", "description": "环境名", "type": "string", "value": "APPSTACK_ENV_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "HOST"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "# Please replace it with your Alibaba Cloud DashScope API-KEY. For detailed application steps, see: https://help.aliyun.com/zh/dashscope/developer-reference/activate-dashscope-and-create-an-api-key", "type": "string", "value": "api-key", "overridable": true, "rule": null, "valueSource": "VARIABLE", "predefined": false, "rsType": "HOST"}, {"name": "serverPort", "description": "# Please enter the service port", "type": "number", "value": "serverPort", "overridable": true, "rule": null, "valueSource": "VARIABLE", "predefined": false, "rsType": "HOST"}], "componentList": [{"name": "{{ .AppStack.appName }}", "kind": "Application", "description": "", "content": "apiVersion: core.oam.dev/v1beta1\nkind: Application\nmetadata:\n  name: '{{ .AppStack.appName }}-{{ .AppStack.envName }}'\nspec:\n  components:\n    - name: '{{ .AppStack.appName }}-{{ .AppStack.envName }}'\n      type: hybrid-workload\n      properties:\n        specialization: non-containerized\n        bootstrap:\n          - type: http-direct\n            fromUrl: '{{ .AppStack.artifact.package }}'\n            toPath: /tmp/package.tgz\n        packages:\n          - type: local\n            name: '{{ .AppStack.appName }}-{{ .AppStack.envName }}'\n            path: '# 请输入制品包在你主机上的绝对路径，示例：/home/<USER>/app/package.tgz'\n            executedBy: root\n            start:\n              command: |-\n                mkdir -p ~/appstack/ai-demo\n                cd ~/appstack\n                tar zxvf /tmp/package.tgz -C ai-demo/\n                java -version >version 2>&1 || echo \"failed to find java version\"\n                version=`cat version | head -n 1 | grep \"17\\.\"` || echo \"failed to parse java version\"\n                if [ -z \"$version\" ]; then\n                  if [ -e jdk/jdk17/bin/java ]; then\n                     echo \"find java17 in ~/appstack/jdk/jdk17/bin/java\"\n                     JAVA=jdk/jdk17/bin/java\n                  else \n                     wget -O jdk17.tar.gz \"https://mirrors.tuna.tsinghua.edu.cn/Adoptium/17/jdk/x64/linux/OpenJDK17U-jdk_x64_linux_hotspot_17.0.11_9.tar.gz\"\n                     mkdir -p jdk\n                     tar -zxvf jdk17.tar.gz -C jdk\n                     mv jdk/* jdk/jdk17\n                     JAVA=jdk/jdk17/bin/java\n                  fi\n                else\n                  echo \"already install java17\"\n                  JAVA=java\n                fi\n                echo \"java: $JAVA\"\n                export AI_API_KEY={{ .Values.apiKey }}\n                export SERVER_PORT={{ .Values.serverPort }}\n                nohup $JAVA -jar ai-demo/*.jar > ai-demo/application.log 2>&1 &\n                echo \"application start\"\n            stop:\n              command: |-\n                pid=$(ps -ef | grep java | grep -v grep | awk '{print $2}')\n                if [ -z $pid ]; then\n                    echo \"no java application\"\n                else\n                    echo \"kill java application pid: $pid\"\n                    kill -9 $pid\n                fi\n            descriptors:\n              source: local\n      traits: []\n", "priority": 1, "type": "HOST"}], "groupNameMap": {}}], "revision": {"repoMeta": {"name": "PRESET_SPRING_AI", "type": "appTemplateOrchestration"}, "sha": "b32659cc0b67fe856b30734c4be699d3409b667e", "message": "修改编排模板", "author": "61c92a37ae805dbacc5b9f78", "refs": [], "commitTime": 1722249995236}, "branchInfo": {"head": {"repoMeta": {"name": "PRESET_SPRING_AI", "type": "appTemplateOrchestration"}, "sha": "b32659cc0b67fe856b30734c4be699d3409b667e", "message": "修改编排模板", "author": "61c92a37ae805dbacc5b9f78", "refs": [], "commitTime": 1722249995236}, "repoMeta": {"name": "PRESET_SPRING_AI", "type": "appTemplateOrchestration"}, "name": "master", "startRevisionSha": "", "fromBranch": ""}}}, {"sn": "511003b717ea4518b1d02939f5e3d18d", "appTemplateName": "PRESET_SPRING_AI", "type": "VariableGroup", "modeSetting": {"configType": "VariableGroup"}, "configuration": {"type": "VariableGroup", "profileMap": {"test-variables": {"name": "test-variables", "displayName": "Test Vars", "vars": [{"key": "api-key", "value": "sk-a3d73b1709bf4a178c28ed7c8bxxxxxx", "description": "Replace it with your API-KEY：https://help.aliyun.com/zh/dashscope/developer-reference/activate-dashscope-and-create-an-api-key"}, {"key": "serverPort", "value": "8080", "description": "Service Port"}]}, "pre-variables": {"name": "pre-variables", "displayName": "Prepub Vars", "vars": [{"key": "api-key", "value": "sk-a3d73b1709bf4a178c28ed7c8bxxxxxx", "description": "Replace it with your API-KEY：https://help.aliyun.com/zh/dashscope/developer-reference/activate-dashscope-and-create-an-api-key"}, {"key": "serverPort", "value": "8080", "description": "Service Port"}]}, "prod-variables": {"name": "prod-variables", "displayName": "Production Vars", "vars": [{"key": "api-key", "value": "sk-a3d73b1709bf4a178c28ed7c8bxxxxxx", "description": "Replace it with your API-KEY：https://help.aliyun.com/zh/dashscope/developer-reference/activate-dashscope-and-create-an-api-key"}, {"key": "serverPort", "value": "8080", "description": "Service Port"}]}}, "revision": {"repoMeta": {"name": "PRESET_SPRING_AI", "type": "variableTemplate"}, "sha": "c128686db1cf4c00e1743eed6da39e97f00cccbf", "message": "更新变量", "author": "61c92a37ae805dbacc5b9f78", "refs": [], "commitTime": 1722250138599}, "branchInfo": {"head": {"repoMeta": {"name": "PRESET_SPRING_AI", "type": "variableTemplate"}, "sha": "c128686db1cf4c00e1743eed6da39e97f00cccbf", "message": "更新变量", "author": "61c92a37ae805dbacc5b9f78", "refs": [], "commitTime": 1722250138599}, "repoMeta": {"name": "PRESET_SPRING_AI", "type": "variableTemplate"}, "name": "master", "startRevisionSha": "", "fromBranch": ""}}}]}