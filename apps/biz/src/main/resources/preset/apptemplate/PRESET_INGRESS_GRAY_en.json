{"template": {"name": "PRESET_INGRESS_GRAY", "displayName": "Gray release Application", "cover": "https://img.alicdn.com/imgextra/i1/O1CN01dtrC4q1VkWnWpQ0ep_!!6000000002691-55-tps-200-200.svg", "description": "{\"text\":\"Grayscale release template based on nginx-ingress-controller. Before using this template, please make sure you have the following resources:    ● Available ACK cluster (version 1.22 and above is recommended), and nginx ingress controller is installed in the cluster    ● ACR image repository with upload permission, and prepare publicly accessible image addresses in advance    How to use:    1. Create an application based on this template and enter the application page    2. Click the &quot;Environment&quot; menu, click ACK grayscale environment and ACK production environment respectively, and associate these two environments with the available ACK cluster    3. Click &quot;Settings / R&amp;D process settings&quot;, edit the production stage pipeline YAML, and replace the following:    ● &lt;acr-registry&gt;, replace with the correct ACR address, such as registry.cn-zhangjiakou.aliyuncs.com/docker007/demo-go-echo    ● &lt;acr-region&gt;, replace with the correct ACR warehouse region, such as cn-zhangjiakou    ● &lt;acr-service-connection&gt;, replace with the correct ACR service connection. You can open    https://devops.aliyun.com/appstack/setting/serviceConnection    to create an ACR service connection and replace the created service connection ID into the YAML.    ● &lt;user-id&gt;, replace with your Alibaba Cloud account ID. You can open    https://aliyun.com    and click the user information in the upper right corner to view and obtain the account ID.    4. Save the production stage pipeline.    5. Click the &quot;R&amp;D process&quot; menu to run the R&amp;D process.    6. Get the IP address of ingress from the ACK cluster.    7. Add a line to the local /etc/hosts file and replace &lt;ingress ip&gt; with the IP address obtained in the previous step:    &lt;ingress ip&gt; go.demo.prod    8. You can access the grayscale environment through curl    http://go.demo.prod/version    -H &quot;_env: grey&quot; and the production environment through curl    http://go.demo.prod/version\",\"content\":[\"root\",{},[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"Grayscale release template based on nginx-ingress-controller. Before using this template, please make sure you have the following resources:\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"● Available ACK cluster (version 1.22 and above is recommended), and nginx ingress controller is installed in the cluster\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"● ACR image repository with upload permission, and prepare publicly accessible image addresses in advance\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"How to use:\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"1. Create an application based on this template and enter the application page\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"2. Click the \\\"Environment\\\" menu, click ACK grayscale environment and ACK production environment respectively, and associate these two environments with the available ACK cluster\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"3. Click \\\"Settings / R&D process settings\\\", edit the production stage pipeline YAML, and replace the following:\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"● <acr-registry>, replace with the correct ACR address, such as registry.cn-zhangjiakou.aliyuncs.com/docker007/demo-go-echo\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"● <acr-region>, replace with the correct ACR warehouse region, such as cn-zhangjiakou\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"● <acr-service-connection>, replace with the correct ACR service connection. You can open \"]],[\"a\",{\"href\":\"https://devops.aliyun.com/appstack/setting/serviceConnection\"},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"https://devops.aliyun.com/appstack/setting/serviceConnection\"]]],[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\" to create an ACR service connection and replace the created service connection ID into the YAML.\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"● <user-id>, replace with your Alibaba Cloud account ID. You can open \"]],[\"a\",{\"href\":\"https://aliyun.com\"},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"https://aliyun.com\"]]],[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\" and click the user information in the upper right corner to view and obtain the account ID.\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"4. Save the production stage pipeline.\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"5. Click the \\\"R&D process\\\" menu to run the R&D process.\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"6. Get the IP address of ingress from the ACK cluster.\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"7. Add a line to the local /etc/hosts file and replace <ingress ip> with the IP address obtained in the previous step:\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"<ingress ip> go.demo.prod\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"8. You can access the grayscale environment through curl \"]],[\"a\",{\"href\":\"http://go.demo.prod/version\"},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"http://go.demo.prod/version\"]]],[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\" -H \\\"_env: grey\\\" and the production environment through curl \"]],[\"a\",{\"href\":\"http://go.demo.prod/version\"},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"http://go.demo.prod/version\"]]],[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"\"]]]]}"}, "configs": [{"sn": "2505e9b2b5964a32846d76050c0f89fa", "appTemplateName": "PRESET_INGRESS_GRAY", "type": "Source", "modeSetting": {"configType": "Source"}, "configuration": {"type": "Source", "codeRepos": [{"name": "demo-go-echo", "repoUrl": "https://atomgit.com/feiyuw/demo-go-echo.git", "identifier": "demo_go_echo", "repoContext": {"repoUrl": "https://atomgit.com/feiyuw/demo-go-echo.git", "defaultBranch": "master", "repoType": "GIT"}, "connectionConfig": {"connectionId": "", "connectionType": "FLOW"}}], "artifactRepos": []}}, {"sn": "fb4d2bdfe97044fd9602a15737c02936", "appTemplateName": "PRESET_INGRESS_GRAY", "type": "Env", "modeSetting": {"configType": "Env"}, "configuration": {"type": "Env", "envs": [{"name": "grey", "displayName": "Gray Env", "labels": [{"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "grey", "displayName": "<PERSON>", "type": "TEMPLATE"}], "deployType": "KUBERNETES", "resourcePoolName": "default", "deployGroupName": null, "description": "", "spec": {"migrateState": null, "oamRebuildState": null, "withoutOam": null, "rolloutStrategy": [{"locator": "*", "batches": null, "batchSteps": null, "timeOutMS": 600000, "targetReplicas": null, "batchMode": "ConfirmFirstBatch", "deployType": "Rolling"}], "replicasManagement": "USER"}}, {"name": "ack-prod", "displayName": "Production Env", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "prod", "displayName": "Production Vars", "type": "TEMPLATE"}], "deployType": "KUBERNETES", "resourcePoolName": "default", "deployGroupName": null, "description": "", "spec": {"migrateState": null, "oamRebuildState": null, "withoutOam": null, "rolloutStrategy": [{"locator": "*", "batches": null, "batchSteps": null, "timeOutMS": 600000, "targetReplicas": null, "batchMode": "ConfirmFirstBatch", "deployType": "Rolling"}], "replicasManagement": "USER"}}]}}, {"sn": "44378728eadb4e17ab4821a56f7257ff", "appTemplateName": "PRESET_INGRESS_GRAY", "type": "ReleaseWorkflow", "modeSetting": {"configType": "ReleaseWorkflow", "modes": {"标准流程": "Synchronization"}}, "configuration": {"type": "ReleaseWorkflow", "appTemplateWorkflowList": [{"sn": "d15087a3-9286-47e2-8fe9-b67389346b64", "name": "Standard", "appTemplateName": "PRESET_INGRESS_GRAY", "releaseStageTemplate": [{"sn": "Production", "name": "Production", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "pipeline", "owner": "5e96a034afcc954c89849976", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "stages:\n  grey:\n    name: Deploy grey and verify\n    jobs:\n      grey_deploy_job:\n        name: deploy grey\n        component: AppStackFlowDeploy\n        with:\n          application: '${APPSTACK_APP_NAME}'\n          environment: grey\n          artifacts:\n            - label: backend\n              value: >-\n                registry.cn-zhangjiakou.aliyuncs.com/docker007/demo-go-echo:grey.v1.1.0\n          autoDeploy: true\n      grey_validate:\n        name: grey verify\n        component: ManualValidate\n        needs:\n          - grey_deploy_job\n        with:\n          validatorType: pipelineRoles\n          validatorPipelineRoles: member\n  deploy:\n    name: Deploy Production\n    jobs:\n      ack_deploy_job:\n        name: deploy production\n        component: AppStackFlowDeploy\n        condition: |\n          succeed('grey.grey_validate')\n        with:\n          application: '${APPSTACK_APP_NAME}'\n          environment: ack-prod\n          artifacts:\n            - label: backend\n              value: >-\n                registry.cn-zhangjiakou.aliyuncs.com/docker007/demo-go-echo:prod.v1.1.0\n          autoDeploy: true\n  validate:\n    name: Production verify\n    jobs:\n      prod_validate:\n        name: verify\n        component: ManualValidate\n        needs:\n          - deploy.ack_deploy_job\n        condition: |\n          succeed('deploy.ack_deploy_job') && !skipped('deploy.ack_deploy_job')\n        with:\n          validatorType: pipelineRoles\n          validatorPipelineRoles: member\n  cleanup:\n    name: Cleanup\n    jobs:\n      cleanup_grey_env_job:\n        name: clean grey env up\n        component: AppStackCleanEnv\n        needs:\n          - grey.grey_validate\n          - validate.prod_validate\n        condition: |\n          failed('grey.grey_validate') || !skipped('validate.prod_validate')\n        with:\n          application: '${APPSTACK_APP_NAME}'\n          environment: grey\n          deleteEnv: cleanEnv\n  report:\n    name: Report\n    jobs:\n      command_job:\n        name: command\n        needs:\n          - deploy.ack_deploy_job\n        condition: |\n          succeed('deploy.ack_deploy_job') && !skipped('deploy.ack_deploy_job')\n        steps:\n          command_step:\n            name: command\n            step: Command\n            with:\n              run: >\n                #!/bin/bash\n\n                set -e\n\n\n                # 系统提供参数，从流水线上下文获取\n\n                echo [INFO] 系统变量\n\n                echo [INFO] PIPELINE_ID=$PIPELINE_ID       # 流水线ID\n\n                echo [INFO] PIPELINE_NAME=$PIPELINE_NAME   # 流水线名称\n\n                echo [INFO] BUILD_NUMBER=$BUILD_NUMBER     # 流水线运行实例编号\n\n                echo [INFO] EMPLOYEE_ID=$EMPLOYEE_ID       # 触发流水线用户ID\n\n                echo [INFO] WORK_SPACE=$WORK_SPACE         #\n                /root/workspace容器中目录\n\n                echo [INFO] PROJECT_DIR=$PROJECT_DIR       #\n                代码库根路径，默认为/root/workspace/code\n\n                echo [INFO] PLUGIN_DIR=$PLUGIN_DIR         #\n                插件路径，默认为/root/plugins\n\n                echo [INFO] BUILD_JOB_ID=$BUILD_JOB_ID     # build-service 任务ID\n\n                echo [INFO] STEP_ID=$stepIdentifier        # yml 步骤ID\n\n                echo [INFO] CREATOR_ALIYUN_PK=$CREATOR_ALIYUN_PK\n\n                echo [INFO] INGRESS_HOST=$INGRESS_HOST\n\n\n                curl --location\n                \"https://devops.aliyun.com/appstack/hook/api/ucc/report?aliyunPk=$CREATOR_ALIYUN_PK&userId=$EMPLOYEE_ID&pipelineId=$PIPELINE_ID\"\n\n\n                if [[ $? == 0 ]]\n\n                then\n                  echo [INFO] 注册云效体验活动成功\n                else\n                  echo [ERROR] 注册云效体验活动失败\n                  exit 1\n                fi\n", "sources": "[]", "webhook": "azJUxSVmme2HBLA5dRqg", "doValidate": false}, "originPipelineId": 1, "type": "PIPELINEASCODE", "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineType": "FlowV2", "engineSn": null, "pipelineYaml": "stages:\n  grey:\n    name: Deploy grey and verify\n    jobs:\n      grey_deploy_job:\n        name: deploy grey\n        component: AppStackFlowDeploy\n        with:\n          application: '${APPSTACK_APP_NAME}'\n          environment: grey\n          artifacts:\n            - label: backend\n              value: >-\n                registry.cn-zhangjiakou.aliyuncs.com/docker007/demo-go-echo:grey.v1.1.0\n          autoDeploy: true\n      grey_validate:\n        name: grey verify\n        component: ManualValidate\n        needs:\n          - grey_deploy_job\n        with:\n          validatorType: pipelineRoles\n          validatorPipelineRoles: member\n  deploy:\n    name: Deploy Production\n    jobs:\n      ack_deploy_job:\n        name: deploy production\n        component: AppStackFlowDeploy\n        condition: |\n          succeed('grey.grey_validate')\n        with:\n          application: '${APPSTACK_APP_NAME}'\n          environment: ack-prod\n          artifacts:\n            - label: backend\n              value: >-\n                registry.cn-zhangjiakou.aliyuncs.com/docker007/demo-go-echo:prod.v1.1.0\n          autoDeploy: true\n  validate:\n    name: Production verify\n    jobs:\n      prod_validate:\n        name: verify\n        component: ManualValidate\n        needs:\n          - deploy.ack_deploy_job\n        condition: |\n          succeed('deploy.ack_deploy_job') && !skipped('deploy.ack_deploy_job')\n        with:\n          validatorType: pipelineRoles\n          validatorPipelineRoles: member\n  cleanup:\n    name: Cleanup\n    jobs:\n      cleanup_grey_env_job:\n        name: clean grey env up\n        component: AppStackCleanEnv\n        needs:\n          - grey.grey_validate\n          - validate.prod_validate\n        condition: |\n          failed('grey.grey_validate') || !skipped('validate.prod_validate')\n        with:\n          application: '${APPSTACK_APP_NAME}'\n          environment: grey\n          deleteEnv: cleanEnv\n  report:\n    name: Report\n    jobs:\n      command_job:\n        name: command\n        needs:\n          - deploy.ack_deploy_job\n        condition: |\n          succeed('deploy.ack_deploy_job') && !skipped('deploy.ack_deploy_job')\n        steps:\n          command_step:\n            name: command\n            step: Command\n            with:\n              run: >\n                #!/bin/bash\n\n                set -e\n\n\n                # 系统提供参数，从流水线上下文获取\n\n                echo [INFO] 系统变量\n\n                echo [INFO] PIPELINE_ID=$PIPELINE_ID       # 流水线ID\n\n                echo [INFO] PIPELINE_NAME=$PIPELINE_NAME   # 流水线名称\n\n                echo [INFO] BUILD_NUMBER=$BUILD_NUMBER     # 流水线运行实例编号\n\n                echo [INFO] EMPLOYEE_ID=$EMPLOYEE_ID       # 触发流水线用户ID\n\n                echo [INFO] WORK_SPACE=$WORK_SPACE         #\n                /root/workspace容器中目录\n\n                echo [INFO] PROJECT_DIR=$PROJECT_DIR       #\n                代码库根路径，默认为/root/workspace/code\n\n                echo [INFO] PLUGIN_DIR=$PLUGIN_DIR         #\n                插件路径，默认为/root/plugins\n\n                echo [INFO] BUILD_JOB_ID=$BUILD_JOB_ID     # build-service 任务ID\n\n                echo [INFO] STEP_ID=$stepIdentifier        # yml 步骤ID\n\n                echo [INFO] CREATOR_ALIYUN_PK=$CREATOR_ALIYUN_PK\n\n                echo [INFO] INGRESS_HOST=$INGRESS_HOST\n\n\n                curl --location\n                \"https://devops.aliyun.com/appstack/hook/api/ucc/report?aliyunPk=$CREATOR_ALIYUN_PK&userId=$EMPLOYEE_ID&pipelineId=$PIPELINE_ID\"\n\n\n                if [[ $? == 0 ]]\n\n                then\n                  echo [INFO] 注册云效体验活动成功\n                else\n                  echo [ERROR] 注册云效体验活动失败\n                  exit 1\n                fi\n"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "SPECIFIC_BRANCH", "specificBranch": "master", "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": []}], "fromRevisionSha": "7cb0ec099f798e9caf38b5e959416aa42e279f0a", "message": "修改研发流程阶段[Production]流水线", "order": "1"}]}}, {"sn": "b9e6833662d141d6be7dd6e9e3d06ed9", "appTemplateName": "PRESET_INGRESS_GRAY", "type": "Orchestration", "modeSetting": {"configType": "Orchestration", "modes": {"default_builtin": "Synchronization"}}, "configuration": {"type": "Orchestration", "orchestrations": [{"storageType": "BUILTIN", "format": "MANIFEST", "suitableResourceTypes": ["KUBERNETES"], "sn": "PRESET_INGRESS_GRAY@KUBERNETES", "revision": {"sha": "d5887d81ac15399484ae0e45493d25d984a74f94", "message": "修改编排模板", "author": "61c92a37ae805dbacc5b9f78", "commitTime": "2024-07-29T11:00:24.321+00:00"}, "name": "PRESET_INGRESS_GRAY", "creatorId": "61c92a37ae805dbacc5b9f78", "gmtCreate": "2024-07-29T09:53:14.000+00:00", "modifierId": "61c92a37ae805dbacc5b9f78", "gmtModified": "2024-07-29T11:00:24.000+00:00", "description": null, "labelPolicy": "FROM_LABEL_BINDING", "labelList": [{"namespace": "default", "name": "envType", "value": "dev", "displayName": "环境级别", "displayValue": "开发环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "placeholderList": [{"name": "image.backend", "description": null, "type": "string", "value": "NULL", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": true, "rsType": "KUBERNETES"}, {"name": "appName", "description": "应用名", "type": "string", "value": "APPSTACK_APP_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "KUBERNETES"}, {"name": "envName", "description": "环境名", "type": "string", "value": "APPSTACK_ENV_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "KUBERNETES"}, {"name": "namespace", "description": null, "type": "string", "value": "namespace", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": false, "rsType": "KUBERNETES"}, {"name": "replicas", "description": null, "type": "string", "value": "2", "overridable": false, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "cpuLimit", "description": null, "type": "string", "value": "1", "overridable": false, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "memoryLimit", "description": null, "type": "string", "value": "256Mi", "overridable": false, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "cpuRequest", "description": null, "type": "string", "value": "0.01", "overridable": false, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "memoryRequest", "description": null, "type": "string", "value": "32Mi", "overridable": false, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "host", "description": null, "type": "string", "value": "host", "overridable": true, "rule": null, "valueSource": "VARIABLE", "predefined": false, "rsType": "KUBERNETES"}], "componentList": [{"name": "svc", "kind": "Service", "description": "", "content": "---\napiVersion: v1\nkind: Service\nmetadata:\n  name: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  namespace: {{ .Values.namespace }}\nspec:\n  selector:\n    run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  ports:\n    - protocol: TCP\n      port: 80\n      targetPort: 8080", "priority": 1, "type": "KUBERNETES"}, {"name": "deploy", "kind": "Deployment", "description": "", "content": "---\napiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  labels:\n    run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  namespace: {{ .Values.namespace }}\nspec:\n  replicas: {{ .Values.replicas }}\n  selector:\n    matchLabels:\n      run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  template:\n    metadata:\n      labels:\n        run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n    spec:\n      containers:\n        - name: main\n          image: {{ .AppStack.image.backend }}\n          ports:\n            - containerPort: 8080\n          resources:\n            limits:\n              cpu: {{ .Values.cpuLimit }}\n              memory: {{ .Values.memoryLimit }}\n            requests:\n              cpu: {{ .Values.cpuRequest }}\n              memory: {{ .Values.memoryRequest }}", "priority": 2, "type": "KUBERNETES"}, {"name": "ingress", "kind": "Ingress", "description": "", "content": "---\napiVersion: networking.k8s.io/v1\nkind: Ingress\nmetadata:\n  name: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  namespace: {{ .Values.namespace }}\n{{ if eq .AppStack.envName \"grey\" }}\n  annotations:\n    # Enable Canary。\n    nginx.ingress.kubernetes.io/canary: \"true\"\n    # Header with _env。\n    nginx.ingress.kubernetes.io/canary-by-header: \"_env\"\n    # The request will be routed to the new version service only when the value of the request header _env is grey.\n    nginx.ingress.kubernetes.io/canary-by-header-value: \"grey\"\n{{ end }}\nspec:\n  rules:\n  - host: {{ .Values.host }}\n    http:\n      paths:\n      - path: /\n        pathType: Prefix\n        backend:\n          service:\n            name: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n            port:\n              number: 80", "priority": 3, "type": "KUBERNETES"}], "groupNameMap": {}}], "revision": {"repoMeta": {"name": "PRESET_INGRESS_GRAY", "type": "appTemplateOrchestration"}, "sha": "d5887d81ac15399484ae0e45493d25d984a74f94", "message": "修改编排模板", "author": "61c92a37ae805dbacc5b9f78", "refs": [], "commitTime": 1722250824321}, "branchInfo": {"head": {"repoMeta": {"name": "PRESET_INGRESS_GRAY", "type": "appTemplateOrchestration"}, "sha": "d5887d81ac15399484ae0e45493d25d984a74f94", "message": "修改编排模板", "author": "61c92a37ae805dbacc5b9f78", "refs": [], "commitTime": 1722250824321}, "repoMeta": {"name": "PRESET_INGRESS_GRAY", "type": "appTemplateOrchestration"}, "name": "master", "startRevisionSha": "", "fromBranch": ""}}}, {"sn": "9904e5400a1f481aa7b4074a4e54c795", "appTemplateName": "PRESET_INGRESS_GRAY", "type": "VariableGroup", "modeSetting": {"configType": "VariableGroup"}, "configuration": {"type": "VariableGroup", "profileMap": {"prod": {"name": "prod", "displayName": "Production Vars", "vars": [{"key": "namespace", "value": "demo-prod", "description": ""}, {"key": "host", "value": "go.demo.prod", "description": ""}]}, "grey": {"name": "grey", "displayName": "<PERSON>", "vars": [{"key": "namespace", "value": "demo-pre", "description": ""}, {"key": "host", "value": "go.demo.prod", "description": ""}]}}, "revision": {"repoMeta": {"name": "PRESET_INGRESS_GRAY", "type": "variableTemplate"}, "sha": "8d56ac2eb8527b0fa57c2d1c25da4b4dcf21a1ce", "message": "修改 灰度环境变量组 名称", "author": "61c92a37ae805dbacc5b9f78", "refs": [], "commitTime": 1722250872297}, "branchInfo": {"head": {"repoMeta": {"name": "PRESET_INGRESS_GRAY", "type": "variableTemplate"}, "sha": "8d56ac2eb8527b0fa57c2d1c25da4b4dcf21a1ce", "message": "修改 灰度环境变量组 名称", "author": "61c92a37ae805dbacc5b9f78", "refs": [], "commitTime": 1722250872297}, "repoMeta": {"name": "PRESET_INGRESS_GRAY", "type": "variableTemplate"}, "name": "master", "startRevisionSha": "", "fromBranch": ""}}}]}