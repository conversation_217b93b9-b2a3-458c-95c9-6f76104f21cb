{"template": {"name": "PRESET_SPRING_AI_SAE", "displayName": "AI Application with SAE", "cover": "https://img.alicdn.com/imgextra/i1/O1CN01MYCeF61VRkYopejI8_!!6000000002650-55-tps-200-200.svg", "description": "{\"text\":\"This application template demonstrates how to use Yunxiao AppStack + SAE (Serverless Application Engine) to quickly build and deploy an AI chat question-and-answer assistant, start your first AI application development, and allow Java developers to develop AI applications just like using Spring to develop ordinary applications. See more：https://atomgit.com/appstack-example/spring-cloud-ai/blob/master/README.md\",\"content\":[\"root\",{},[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"color\":\"rgb(0, 0, 0)\",\"fonts\":{\"ascii\":\"PingFang SC\",\"hAnsi\":\"PingFang SC\",\"cs\":\"PingFang SC\",\"eastAsia\":\"PingFang SC\"},\"sz\":12,\"szUnit\":\"pt\",\"data-type\":\"leaf\"},\"This application template demonstrates how to use Yunxiao AppStack + SAE (Serverless Application Engine) to quickly build and deploy an AI chat question-and-answer assistant, start your first AI application development, and allow Java developers to develop AI applications just like using Spring to develop ordinary applications. See more：https://atomgit.com/appstack-example/spring-cloud-ai/blob/master/README.md\"]]]]}"}, "configs": [{"sn": "fc8f3b31d76748a1a58b6954f177c781", "appTemplateName": "PRESET_SPRING_AI_SAE", "type": "Source", "modeSetting": {"configType": "Source"}, "configuration": {"type": "Source", "codeRepos": [{"name": "spring-cloud-ai", "repoUrl": "https://atomgit.com/appstack-example/spring-cloud-ai.git", "identifier": "spring_cloud_ai", "repoContext": {"repoUrl": "https://atomgit.com/appstack-example/spring-cloud-ai.git", "defaultBranch": "master", "repoType": "GIT"}, "connectionConfig": {"connectionId": "", "connectionType": "FLOW"}}], "artifactRepos": []}}, {"sn": "4e420558383e42379462d85ea2736ad4", "appTemplateName": "PRESET_SPRING_AI_SAE", "type": "Env", "modeSetting": {"configType": "Env"}, "configuration": {"type": "Env", "envs": []}}, {"sn": "a30613c9e11446a6b103568e13fa6a96", "appTemplateName": "PRESET_SPRING_AI_SAE", "type": "ReleaseWorkflow", "modeSetting": {"configType": "ReleaseWorkflow", "modes": {}}, "configuration": {"type": "ReleaseWorkflow", "appTemplateWorkflowList": [{"sn": "3b53e380-e489-4a8a-bc98-dcb94fcb3966", "name": "Standard", "appTemplateName": "PRESET_SPRING_AI_SAE", "releaseStageTemplate": [{"sn": "Test", "name": "Test", "labels": [{"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Test", "owner": "64a3940486937ebe0ce9bb7a", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}],\"globalParams\":[]}", "flow": "schema: tb\npipeline:\n  - name: Test\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Java Code Scan\n            task: execution-component-production@20\n            identifier: '12_1715827592350'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java P3C Scan\n                  stepType: p3c\n                  stepIdentifier: '12_1715827592350__13_1715827592351'\n                  JDK_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  INCREASE: false\n                  USE_CUSTOM_RULE: false\n                  RULE_SET: >-\n                    ali-comment.xml,ali-concurrent.xml,ali-constant.xml,ali-exception.xml,ali-flowcontrol.xml,ali-naming.xml,ali-oop.xml,ali-orm.xml,ali-other.xml,ali-set.xml\n                  RULE_DIR: .\n                  SUB_DIR: ''\n                  EXCLUSION: test/\n                  CHECK_REDLINES: ''\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: Build\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Image Build\n            task: execution-component-production@20\n            identifier: '10_1715397594667'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version: jdk1.8\n              steps:\n                - name: Java Build\n                  stepType: java_build\n                  stepIdentifier: '10_1715397594667__12_1715397594669'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -s settings.xml -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                - name: Image Build\n                  stepType: docker_build_sc_production\n                  stepIdentifier: '10_1715397594667__10_1718872316142'\n                  freeInTaskGroupModeFields:\n                    - ARGS\n                  SERVICE_CONNECTION_ID: 627686\n                  DOCKER_REGION: ''\n                  artifact: artifact\n                  ACREE_TIP: ''\n                  DOCKER_REPOSITORY: ''\n                  DOCKER_TAG: '${DATETIME}'\n                  EXTRA_TAGS: ''\n                  BUILDKIT_VERSION: v0.8.0\n                  DOCKER_FILE_PATH: Dockerfile\n                  CONTEXT_PATH: ''\n                  NO_CACHE: false\n                  ARGS: >-\n                    {\"AI_API_KEY\":\"${AI_API_KEY}\",\"AI_API_KEY.type\":\"custom\",\"SERVER_PORT\":\"${SERVER_PORT}\",\"SERVER_PORT.type\":\"custom\"}\n                  JSONEncoding: true\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - name: '10_1715397594667__10_1718872316142.dynamic_output.${DATETIME}'\n                displayName: '标签.${DATETIME}'\n                type: artifact\n                identifier: 10_1715397594667__10_1718872316142__DOCKER_TAG\n                description: Docker镜像Tag\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715397594667'\n              - name: 10_1715397594667__10_1718872316142.DOCKER_OUTPUT\n                displayName: Image Build.镜像公网地址\n                type: artifact\n                identifier: 10_1715397594667__10_1718872316142__DOCKER_OUTPUT\n                description: ''\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715397594667'\n              - name: 10_1715397594667__10_1718872316142.DOCKER_OUTPUT_VPC\n                displayName: Image Build.镜像VPC地址\n                type: artifact\n                identifier: 10_1715397594667__10_1718872316142__DOCKER_OUTPUT_VPC\n                description: ''\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715397594667'\n            freeInTaskGroupModeFields: []\n  - name: Deploy\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Serverless(SAE) Deploy\n            task: execution-component-production@20\n            identifier: '10_1718872427614'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 阿里SAE部署\n                  stepType: flow-sae\n                  stepIdentifier: '10_1718872427614__11_1718872427614'\n                  SERVICE_CONNECTION_ID: ''\n                  REGION_ID: ''\n                  SAE_APP_NAMESPACE: ''\n                  APPLICATION_ID: ''\n                  PACKAGE: ''\n                  USE_ACR_EE: false\n                  ACR_EE_ID: ''\n                  StrategyType: BatchUpdate\n                  GrayNumber: ''\n                  ReleaseType: auto\n                  Batch: 1\n                  BatchWaitTime: 0\n                  minReadyInstances: 1\n                  freeInTaskGroupModeFields:\n                    - PACKAGE\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "nbzeqWPcks8i0AcnZDmy", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineType": "FlowV1", "engineSn": null, "pipelineYaml": "schema: tb\npipeline:\n  - name: Test\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Java Code Scan\n            task: execution-component-production@20\n            identifier: '12_1715827592350'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java P3C Scan\n                  stepType: p3c\n                  stepIdentifier: '12_1715827592350__13_1715827592351'\n                  JDK_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  INCREASE: false\n                  USE_CUSTOM_RULE: false\n                  RULE_SET: >-\n                    ali-comment.xml,ali-concurrent.xml,ali-constant.xml,ali-exception.xml,ali-flowcontrol.xml,ali-naming.xml,ali-oop.xml,ali-orm.xml,ali-other.xml,ali-set.xml\n                  RULE_DIR: .\n                  SUB_DIR: ''\n                  EXCLUSION: test/\n                  CHECK_REDLINES: ''\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: Build\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Image Build\n            task: execution-component-production@20\n            identifier: '10_1715397594667'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              version: jdk1.8\n              steps:\n                - name: Java Build\n                  stepType: java_build\n                  stepIdentifier: '10_1715397594667__12_1715397594669'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk17\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -s settings.xml -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                - name: Image Build\n                  stepType: docker_build_sc_production\n                  stepIdentifier: '10_1715397594667__10_1718872316142'\n                  freeInTaskGroupModeFields:\n                    - ARGS\n                  SERVICE_CONNECTION_ID: 627686\n                  DOCKER_REGION: ''\n                  artifact: artifact\n                  ACREE_TIP: ''\n                  DOCKER_REPOSITORY: ''\n                  DOCKER_TAG: '${DATETIME}'\n                  EXTRA_TAGS: ''\n                  BUILDKIT_VERSION: v0.8.0\n                  DOCKER_FILE_PATH: Dockerfile\n                  CONTEXT_PATH: ''\n                  NO_CACHE: false\n                  ARGS: >-\n                    {\"AI_API_KEY\":\"${AI_API_KEY}\",\"AI_API_KEY.type\":\"custom\",\"SERVER_PORT\":\"${SERVER_PORT}\",\"SERVER_PORT.type\":\"custom\"}\n                  JSONEncoding: true\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - name: '10_1715397594667__10_1718872316142.dynamic_output.${DATETIME}'\n                displayName: '标签.${DATETIME}'\n                type: artifact\n                identifier: 10_1715397594667__10_1718872316142__DOCKER_TAG\n                description: Docker镜像Tag\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715397594667'\n              - name: 10_1715397594667__10_1718872316142.DOCKER_OUTPUT\n                displayName: Image Build.镜像公网地址\n                type: artifact\n                identifier: 10_1715397594667__10_1718872316142__DOCKER_OUTPUT\n                description: ''\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715397594667'\n              - name: 10_1715397594667__10_1718872316142.DOCKER_OUTPUT_VPC\n                displayName: Image Build.镜像VPC地址\n                type: artifact\n                identifier: 10_1715397594667__10_1718872316142__DOCKER_OUTPUT_VPC\n                description: ''\n                export: true\n                ref: steps\n                jobIdentifier: '10_1715397594667'\n            freeInTaskGroupModeFields: []\n  - name: Deploy\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Serverless(SAE) Deploy\n            task: execution-component-production@20\n            identifier: '10_1718872427614'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 阿里SAE部署\n                  stepType: flow-sae\n                  stepIdentifier: '10_1718872427614__11_1718872427614'\n                  SERVICE_CONNECTION_ID: ''\n                  REGION_ID: ''\n                  SAE_APP_NAMESPACE: ''\n                  APPLICATION_ID: ''\n                  PACKAGE: ''\n                  USE_ACR_EE: false\n                  ACR_EE_ID: ''\n                  StrategyType: BatchUpdate\n                  GrayNumber: ''\n                  ReleaseType: auto\n                  Batch: 1\n                  BatchWaitTime: 0\n                  minReadyInstances: 1\n                  freeInTaskGroupModeFields:\n                    - PACKAGE\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": []}], "fromRevisionSha": "c1cde28cb3354132c4d79016666ab09f22cd2489", "message": "删除研发流程阶段[Production]", "order": "1"}]}}, {"sn": "70e1a1eaf7f54e6ca1407dc1542ef175", "appTemplateName": "PRESET_SPRING_AI_SAE", "type": "Orchestration", "modeSetting": {"configType": "Orchestration", "modes": {"default_builtin": "Initialization"}}, "configuration": {"type": "Orchestration", "orchestrations": [], "revision": null, "branchInfo": null}}, {"sn": "5bbe8c3b763644a2b6d448946d486a7a", "appTemplateName": "PRESET_SPRING_AI_SAE", "type": "VariableGroup", "modeSetting": {"configType": "VariableGroup"}, "configuration": {"type": "VariableGroup", "profileMap": {}, "revision": {"repoMeta": {"name": "PRESET_SPRING_AI_SAE", "type": "variableTemplate"}, "sha": "948ef6e324ca81cd12ea152ffc69c5117c786c68", "message": "初始化", "author": "61c92a37ae805dbacc5b9f78", "refs": [], "commitTime": 1722246790528}, "branchInfo": {"head": {"repoMeta": {"name": "PRESET_SPRING_AI_SAE", "type": "variableTemplate"}, "sha": "948ef6e324ca81cd12ea152ffc69c5117c786c68", "message": "初始化", "author": "61c92a37ae805dbacc5b9f78", "refs": [], "commitTime": 1722246790528}, "repoMeta": {"name": "PRESET_SPRING_AI_SAE", "type": "variableTemplate"}, "name": "master", "startRevisionSha": "", "fromBranch": ""}}}]}