{"template": {"name": "PRESET_SPRING_BOOT", "displayName": "spring-boot 应用体验模板", "cover": "https://img.alicdn.com/imgextra/i3/O1CN01FdRgIw1TjUeBBG8Ir_!!6000000002418-55-tps-200-200.svg", "description": "本应用模板演示 spring-boot 应用的构建、部署流程，基于 K8s 部署。"}, "configs": [{"sn": "abf3188732ca4e6cb18bd140339ae71f", "appTemplateName": "PRESET_SPRING_BOOT", "type": "VariableGroup", "modeSetting": {"configType": "VariableGroup"}, "configuration": {"type": "VariableGroup", "profileMap": {"dev": {"name": "dev", "displayName": "开发环境变量组", "vars": [{"key": "namespace", "value": "default", "description": "命名空间"}]}, "test": {"name": "test", "displayName": "测试环境变量组", "vars": [{"key": "namespace", "value": "default", "description": "命名空间"}]}, "prepub": {"name": "prepub", "displayName": "预发环境变量组", "vars": [{"key": "namespace", "value": "default", "description": "命名空间"}]}, "production": {"name": "production", "displayName": "生产环境变量组", "vars": [{"key": "namespace", "value": "default", "description": "命名空间"}]}}, "revision": {"repoMeta": {"name": "PRESET_SPRING_BOOT", "type": "variableTemplate"}, "sha": "37d8a7200cec89f7a9a2b8aebb1d42906e1c18c8", "message": "初始化", "author": "646dabc05ccc4ad1bc2a9e2f", "refs": [], "commitTime": 1715832365443}, "branchInfo": {"head": {"repoMeta": {"name": "PRESET_SPRING_BOOT", "type": "variableTemplate"}, "sha": "37d8a7200cec89f7a9a2b8aebb1d42906e1c18c8", "message": "初始化", "author": "646dabc05ccc4ad1bc2a9e2f", "refs": [], "commitTime": 1715832365443}, "repoMeta": {"name": "PRESET_SPRING_BOOT", "type": "variableTemplate"}, "name": "master", "startRevisionSha": "", "fromBranch": ""}}}, {"sn": "5dd7f1801de84f208ea26d9a48e5838b", "appTemplateName": "PRESET_SPRING_BOOT", "type": "Orchestration", "modeSetting": {"configType": "Orchestration", "modes": {"default_builtin": "Synchronization"}}, "configuration": {"type": "Orchestration", "orchestrations": [{"storageType": "BUILTIN", "format": "MANIFEST", "suitableResourceTypes": ["KUBERNETES"], "sn": "PRESET_SPRING_BOOT@KUBERNETES", "revision": {"sha": "3f9f45efe4c3053c99e5301042f531573fb124e9", "message": "null", "author": "646dabc05ccc4ad1bc2a9e2f", "commitTime": "2024-05-16T04:06:05.594+00:00"}, "name": "PRESET_SPRING_BOOT", "creatorId": "646dabc05ccc4ad1bc2a9e2f", "gmtCreate": "2024-05-16T04:06:06.000+00:00", "modifierId": "646dabc05ccc4ad1bc2a9e2f", "gmtModified": "2024-05-16T04:06:06.000+00:00", "description": null, "labelPolicy": "FROM_LABEL_BINDING", "labelList": [{"namespace": "default", "name": "envType", "value": "dev", "displayName": "环境级别", "displayValue": "开发环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "placeholderList": [{"name": "image.backend", "description": "后端服务镜像", "type": "string", "value": "NULL", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": true, "rsType": "KUBERNETES"}, {"name": "appName", "description": "应用名", "type": "string", "value": "APPSTACK_APP_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "KUBERNETES"}, {"name": "envName", "description": "环境名", "type": "string", "value": "APPSTACK_ENV_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "KUBERNETES"}, {"name": "namespace", "description": "命名空间", "type": "string", "value": "default", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "cpuLimit", "description": "CPU限制", "type": "string", "value": "1", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "memoryLimit", "description": "内存限制", "type": "string", "value": "1024Mi", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "cpuRequest", "description": "CPU请求", "type": "string", "value": "0.01", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "memoryRequest", "description": "内存请求", "type": "string", "value": "32Mi", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}], "componentList": [{"name": "demo-service", "kind": "Service", "description": "服务访问策略", "content": "---\napiVersion: v1\nkind: Service\nmetadata:\n  name: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  # 命名空间配置：\n  # 建议每个环境使用不同的Kubernetes集群命名空间，以便达到环境隔离效果\n  # 部署时云效会将占位符{{ .Values.namespace }}替换成右侧设置的实际值\n  namespace: {{ .Values.namespace }}\nspec:\n  selector:\n    run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  ports:\n    - protocol: TCP\n      port: 80\n      targetPort: 8080", "priority": 1, "type": "KUBERNETES"}, {"name": "demo-deployment", "kind": "Deployment", "description": "无状态应用", "content": "---\napiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  labels:\n    run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  # 命名空间配置：\n  # 建议每个环境使用不同的Kubernetes集群命名空间，以便达到环境隔离效果\n  # 部署时云效会将占位符{{ .Values.namespace }}替换成右侧设置的实际值\n  namespace: {{ .Values.namespace }}\nspec:\n  replicas: 1\n  selector:\n    matchLabels:\n      run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  template:\n    metadata:\n      labels:\n        run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n    spec:\n      containers:\n        - name: main\n          # 镜像配置：\n          # 部署时云效会将预置占位符{{ .AppStack.image.backend }}替换成实际部署时的镜像\n          # 支持在新建部署单时手动输入镜像地址，也支持接收流水线的上游构建产物\n          # 另外，在右侧镜像占位符处添加多个镜像，可以支持SideCar或InitContainer等多容器场景\n          image: {{ .AppStack.image.backend }}\n          # 端口配置：\n          ports:\n            - containerPort: 8080\n          # 资源规格配置：\n          # 当不同环境有不同的CPU或内存资源规格要求时，可以定义占位符搭配变量组使用\n          # 如，设置{{ .Values.cpuLimit }}占位符，部署时云效会将占位符{{ .Values.cpuLimit }}替换成右侧设置的实际值\n          resources:\n            limits:\n              cpu: {{ .Values.cpuLimit }}\n              memory: {{ .Values.memoryLimit }}\n            requests:\n              cpu: {{ .Values.cpuRequest }}\n              memory: {{ .Values.memoryRequest }}\n          # 生命周期配置：\n          #lifecycle:\n          #  preStop:\n          #    exec:\n          #      command: [ \"/bin/bash\", \"-c\", \"sleep 10\" ]\n          # 探活配置：\n          #livenessProbe:\n          #  initialDelaySeconds: 10\n          #  failureThreshold: 3\n          #  periodSeconds: 5\n          #  successThreshold: 1\n          #  timeoutSeconds: 2\n          #  httpGet:\n          #    scheme: HTTP\n          #    path: /health\n          #    port: 7002\n          # 就绪探测配置：\n          #readinessProbe:\n          #  initialDelaySeconds: 60\n          #  failureThreshold: 4\n          #  periodSeconds: 10\n          #  successThreshold: 1\n          #  timeoutSeconds: 3\n          #  httpGet:\n          #    scheme: HTTP\n          #    port: 7002\n          #    path: /health", "priority": 2, "type": "KUBERNETES"}], "groupNameMap": {}}], "revision": {"repoMeta": {"name": "PRESET_SPRING_BOOT", "type": "appTemplateOrchestration"}, "sha": "3f9f45efe4c3053c99e5301042f531573fb124e9", "message": "null", "author": "646dabc05ccc4ad1bc2a9e2f", "refs": [], "commitTime": 1715832365594}, "branchInfo": {"head": {"repoMeta": {"name": "PRESET_SPRING_BOOT", "type": "appTemplateOrchestration"}, "sha": "3f9f45efe4c3053c99e5301042f531573fb124e9", "message": "null", "author": "646dabc05ccc4ad1bc2a9e2f", "refs": [], "commitTime": 1715832365594}, "repoMeta": {"name": "PRESET_SPRING_BOOT", "type": "appTemplateOrchestration"}, "name": "master", "startRevisionSha": "", "fromBranch": ""}}}, {"sn": "40b4992f12354a098b83975feca7aebe", "appTemplateName": "PRESET_SPRING_BOOT", "type": "ReleaseWorkflow", "modeSetting": {"configType": "ReleaseWorkflow", "modes": {"标准研发流程": "Initialization"}}, "configuration": {"type": "ReleaseWorkflow", "appTemplateWorkflowList": [{"sn": "e07e0f5a-2f8d-4af9-906a-9937916daa1b", "name": "标准研发流程", "appTemplateName": "PRESET_SPRING_BOOT", "releaseStageTemplate": [{"sn": "测试阶段", "name": "测试阶段", "labels": [{"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Java K8S 应用标准研发流程模板-测试阶段-发布流水线", "owner": "ed99ca55-1fe1-4ad3-9fc3-9314edf0e64b", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: 测试\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: Maven 单元测试\n            task: execution-component-production@20\n            identifier: '10_1715832711076'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Maven 单元测试\n                  stepType: java-maven-unit-test\n                  stepIdentifier: '10_1715832711076__11_1715832711076'\n                  TEST_REPORT_PATH: target/site\n                  TEST_REPORT_INDEX: surefire-report.html\n                  CI_RUNTIME_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  command: |\n                    # maven test default command\n                    mvn -B test -Dmaven.test.failure.ignore=true\n                    mvn surefire-report:report-only\n                    mvn site -DgenerateReports=false\n                  CHECK_REDLINES: ''\n                  freeInTaskGroupModeFields: []\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n      - driven: AUTO\n        jobs:\n          - displayName: Java 代码扫描\n            task: execution-component-production@20\n            identifier: '12_1715832714878'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java 代码规约扫描\n                  stepType: p3c\n                  stepIdentifier: '12_1715832714878__13_1715832714878'\n                  JDK_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  INCREASE: false\n                  USE_CUSTOM_RULE: false\n                  RULE_SET: >-\n                    ali-comment.xml,ali-concurrent.xml,ali-constant.xml,ali-exception.xml,ali-flowcontrol.xml,ali-naming.xml,ali-oop.xml,ali-orm.xml,ali-other.xml,ali-set.xml\n                  RULE_DIR: .\n                  SUB_DIR: ''\n                  EXCLUSION: test/\n                  CHECK_REDLINES: ''\n                  freeInTaskGroupModeFields: []\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 构建\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 镜像构建并推送\n            task: execution-component-production@1\n            identifier: '15_1675155917344'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java 构建\n                  stepType: java_build\n                  stepIdentifier: '15_1675155917344__16_1675155917345'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                - name: 镜像构建并推送至自定义镜像仓库\n                  stepType: custom_docker_build\n                  stepIdentifier: '15_1675155917344__17_1675155917347'\n                  artifact: artifact\n                  DOCKER_DESTINATION: 请替换成你的镜像仓库地址\n                  authType: usernamepassword\n                  SERVICE_CONNECTION_ID: ''\n                  DOCKER_USER: 请替换成你的镜像仓库用户名\n                  DOCKER_PASSWORD: '123456'\n                  DOCKER_FILE_PATH: Dockerfile\n                  CONTEXT_PATH: ''\n                  NO_CACHE: false\n                  ARGS: '{}'\n                  JSONEncoding: true\n                  INSECURE_REGISTRY: false\n                  freeInTaskGroupModeFields:\n                    - ARGS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              clone_option: all\n              selected_sources: ''\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n            plugins: []\n            output:\n              - name: 15_1675155917344__17_1675155917347.dynamic_output.请替换成你的镜像仓库地址\n                displayName: 镜像仓库地址.请替换成你的镜像仓库地址\n                type: artifact\n                identifier: 15_1675155917344__17_1675155917347__DOCKER_DESTINATION\n                description: 自定义镜像仓库地址\n                export: true\n                ref: steps\n                jobIdentifier: '15_1675155917344'\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: AppStack部署-测试环境\n            task: execution-component-production@1\n            identifier: '18_1675155917349'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '18_1675155917349__19_1675155917350'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: test\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: backend\n                      value: >-\n                        ${15_1675155917344.15_1675155917344__17_1675155917347.dynamic_output.请替换成你的镜像仓库地址}\n                      type: upstream\n                  AUTO_SUBMIT: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              clone_option: all\n              selected_sources: ''\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "BmHXozCGAN8T75STIU3j", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": []}, {"sn": "预发阶段", "name": "预发阶段", "labels": [{"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Java K8S 应用标准研发流程模板-预发阶段-发布流水线", "owner": "ed99ca55-1fe1-4ad3-9fc3-9314edf0e64b", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: 构建\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 镜像构建并推送\n            task: execution-component-production@1\n            identifier: '20_1675156443208'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java 构建\n                  stepType: java_build\n                  stepIdentifier: '20_1675156443208__21_1675156443209'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                  invalidStepVersion: false\n                - name: 镜像构建并推送至自定义镜像仓库\n                  stepType: custom_docker_build\n                  stepIdentifier: '20_1675156443208__22_1675156443211'\n                  artifact: artifact\n                  DOCKER_DESTINATION: 请替换成你的镜像仓库地址\n                  authType: usernamepassword\n                  SERVICE_CONNECTION_ID: ''\n                  DOCKER_USER: 请替换成你的镜像仓库用户名\n                  DOCKER_PASSWORD: '123456'\n                  DOCKER_FILE_PATH: Dockerfile\n                  CONTEXT_PATH: ''\n                  NO_CACHE: false\n                  ARGS: '{}'\n                  JSONEncoding: true\n                  INSECURE_REGISTRY: false\n                  freeInTaskGroupModeFields:\n                    - ARGS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              clone_option: all\n              selected_sources: ''\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n            plugins: []\n            output:\n              - name: 20_1675156443208__22_1675156443211.dynamic_output.请替换成你的镜像仓库地址\n                displayName: 镜像仓库地址.请替换成你的镜像仓库地址\n                type: artifact\n                identifier: 20_1675156443208__22_1675156443211__DOCKER_DESTINATION\n                description: 自定义镜像仓库地址\n                export: true\n                ref: steps\n                jobIdentifier: '20_1675156443208'\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: AppStack部署-预发环境\n            task: execution-component-production@1\n            identifier: '23_1675156443212'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '23_1675156443212__24_1675156443213'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: pre\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: backend\n                      value: >-\n                        ${20_1675156443208.20_1675156443208__22_1675156443211.dynamic_output.请替换成你的镜像仓库地址}\n                      type: upstream\n                  AUTO_SUBMIT: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              clone_option: all\n              selected_sources: ''\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "lXBCBVuo5ijkb3xECooX", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "ON", "validateStageSnList": ["测试阶段"], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": []}, {"sn": "生产阶段", "name": "生产阶段", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Java K8S 应用标准研发流程模板-生产阶段-发布流水线", "owner": "ed99ca55-1fe1-4ad3-9fc3-9314edf0e64b", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: 发布审核\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 发布审核\n            task: APPSTACK_DIY_VALIDATE_TB_PROD@1\n            identifier: '30_1675156498088'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              appId: '${INPUTS.appId}'\n              mixFlowInstId: '${INPUTS.mixFlowInstId}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              validatorMethod: or\n              validatorType: role\n              validatorUser: null\n              validatorRole: 66067a82c778866622c0f135\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: AppStack部署-生产环境\n            task: execution-component-production@1\n            identifier: '31_1675156498088'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '31_1675156498088__32_1675156498089'\n                  APP_NAME: '${APPSTACK_APP_NAME}'\n                  ENV_NAME: prod\n                  ACREE_TIP: ''\n                  IMAGE_LIST:\n                    - label: backend\n                      value: 请替换成预发构建好的镜像\n                      type: custom\n                  AUTO_SUBMIT: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n                  invalidStepVersion: false\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: K8S-4\n              clone_option: all\n              selected_sources: ''\n              capabilities: 'Linux,amd64'\n              buildEnvironment: container\n              specifyContainerImageId: >-\n                build-steps-public-registry.cn-beijing.cr.aliyuncs.com/build-steps/alinux3:ffdb23fd-2024-02-26-17-42-35\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": "t1MnbqhHBFbUCzwY9b5O", "doValidate": false}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "CR", "specificBranch": null, "validateConfigState": "ON", "validateStageSnList": ["测试阶段", "预发阶段"], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": []}], "fromRevisionSha": "6bec5329df0c611c577520fc16b7696579d48217", "message": "修改研发流程阶段[测试阶段]流水线", "order": "1"}]}}, {"sn": "a9c89669a71748ce944d6c845c14e776", "appTemplateName": "PRESET_SPRING_BOOT", "type": "Env", "modeSetting": {"configType": "Env"}, "configuration": {"type": "Env", "envs": [{"name": "dev", "displayName": "开发环境", "labels": [{"namespace": "default", "name": "envType", "value": "dev", "displayName": "环境级别", "displayValue": "开发环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "dev", "displayName": "开发环境变量组", "type": "TEMPLATE"}], "deployType": "KUBERNETES", "resourcePoolName": null, "deployGroupName": null, "description": "开发环境"}, {"name": "test", "displayName": "测试环境", "labels": [{"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "test", "displayName": "测试环境变量组", "type": "TEMPLATE"}], "deployType": "KUBERNETES", "resourcePoolName": null, "deployGroupName": null, "description": "测试环境"}, {"name": "pre", "displayName": "预发环境", "labels": [{"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "prepub", "displayName": "预发环境变量组", "type": "TEMPLATE"}], "deployType": "KUBERNETES", "resourcePoolName": null, "deployGroupName": null, "description": "预发环境"}, {"name": "prod", "displayName": "生产环境", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "production", "displayName": "生产环境变量组", "type": "TEMPLATE"}], "deployType": "KUBERNETES", "resourcePoolName": null, "deployGroupName": null, "description": "生产环境"}]}}, {"sn": "095f93a2e82c4b1888d2a4539cf5f6c6", "appTemplateName": "PRESET_SPRING_BOOT", "type": "Source", "modeSetting": {"configType": "Source"}, "configuration": {"type": "Source", "codeRepos": [{"name": "spring-boot", "repoUrl": "https://atomgit.com/appstack-example/spring-boot.git", "identifier": "spring_boot", "repoContext": {"repoUrl": "https://atomgit.com/appstack-example/spring-boot.git", "defaultBranch": "master", "repoType": "GIT"}, "connectionConfig": {"connectionId": "", "connectionType": "FLOW"}}], "artifactRepos": []}}]}