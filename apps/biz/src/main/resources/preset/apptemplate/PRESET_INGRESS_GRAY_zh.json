{"template": {"name": "PRESET_INGRESS_GRAY", "displayName": "灰度发布应用模板", "cover": "https://img.alicdn.com/imgextra/i1/O1CN01dtrC4q1VkWnWpQ0ep_!!6000000002691-55-tps-200-200.svg", "description": "{\"text\":\"基于nginx-ingress-controller的灰度发布模板，使用该模板前，请确保您有如下资源：      可用的ACK集群（建议1.22及以上版本），且在集群中有安装nginx ingress controller      有上传权限的ACR镜像仓库，并提前准备好可公开访问的镜像地址      使用方法：      基于本模板创建应用，进入应用页面      点击“环境”菜单，分别点击ACK灰度环境和ACK生产环境，将这两个环境关联到可用的ACK集群上      点击“设置 / 研发流程设置”，编辑生产阶段流水线YAML，将其中的：        &lt;acr-registry&gt;，替换为正确的ACR地址，如 registry.cn-zhangjiakou.aliyuncs.com/docker007/demo-go-echo      &lt;acr-region&gt;，替换为正确的ACR仓库region，如 cn-zhangjiakou      &lt;acr-service-connection&gt;，替换为正确的ACR服务连接，可打开   https://devops.aliyun.com/appstack/setting/serviceConnection    创建ACR服务连接，并将创建好的服务连接ID替换到YAML中      &lt;user-id&gt;，替换为您的阿里云账号ID，可打开https://aliyun.com，点击右上角用户信息，查看并获取账号ID        保存生产阶段流水线      点击“研发流程”菜单，运行研发流程      从ACK集群上获取ingress的IP地址      在本地/etc/hosts文件中加入一行，把&lt;ingress ip&gt;替换为上一步获取的IP地址：      &lt;ingress ip&gt;   go.demo.prod      通过 curl    http://go.demo.prod/version    -H &quot;_env:grey&quot; 即可访问灰度环境，通过  curl    http://go.demo.prod/version    即可访问生产环境\",\"content\":[\"root\",{},[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"基于nginx-ingress-controller的灰度发布模板，使用该模板前，请确保您有如下资源：\"]]],[\"p\",{\"list\":{\"listId\":\"fj7lc3z4bn\",\"level\":0,\"isOrdered\":false,\"isTaskList\":false,\"listStyleType\":\"SCIR_ECIR_SREC\",\"symbolStyle\":{},\"listStyle\":{\"format\":\"bullet\",\"text\":\"●\",\"align\":\"left\"},\"hideSymbol\":false},\"ind\":{\"left\":0}},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"可用的ACK集群（建议1.22及以上版本），且在集群中有安装nginx ingress controller\"]]],[\"p\",{\"list\":{\"listId\":\"fj7lc3z4bn\",\"level\":0,\"isOrdered\":false,\"isTaskList\":false,\"isChecked\":false,\"listStyleType\":\"SCIR_ECIR_SREC\",\"symbolStyle\":{},\"listStyle\":{\"format\":\"bullet\",\"text\":\"●\",\"align\":\"left\"},\"hideSymbol\":false,\"extraData\":{}},\"ind\":{\"left\":0}},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"有上传权限的ACR镜像仓库，并提前准备好可公开访问的镜像地址\"]]],[\"p\",{\"ind\":{\"left\":0}},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"使用方法：\"]]],[\"p\",{\"ind\":{\"left\":0},\"list\":{\"listId\":\"nlp1702v16c\",\"level\":0,\"isOrdered\":true,\"isTaskList\":false,\"listStyleType\":\"DEC_LEN_LROM_P\",\"symbolStyle\":{},\"listStyle\":{\"format\":\"decimal\",\"text\":\"%1.\",\"align\":\"left\"},\"hideSymbol\":false}},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"基于本模板创建应用，进入应用页面\"]]],[\"p\",{\"ind\":{\"left\":0},\"list\":{\"listId\":\"nlp1702v16c\",\"level\":0,\"isOrdered\":true,\"isTaskList\":false,\"isChecked\":false,\"listStyleType\":\"DEC_LEN_LROM_P\",\"symbolStyle\":{},\"listStyle\":{\"format\":\"decimal\",\"text\":\"%1.\",\"align\":\"left\"},\"hideSymbol\":false,\"extraData\":{}}},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"点击“环境”菜单，分别点击ACK灰度环境和ACK生产环境，将这两个环境关联到可用的ACK集群上\"]]],[\"p\",{\"ind\":{\"left\":0},\"list\":{\"listId\":\"nlp1702v16c\",\"level\":0,\"isOrdered\":true,\"isTaskList\":false,\"isChecked\":false,\"listStyleType\":\"DEC_LEN_LROM_P\",\"symbolStyle\":{},\"listStyle\":{\"format\":\"decimal\",\"text\":\"%1.\",\"align\":\"left\"},\"hideSymbol\":false,\"extraData\":{}}},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"点击“设置 / 研发流程设置”，编辑生产阶段流水线YAML，将其中的：\"]]],[\"p\",{\"ind\":{\"left\":32},\"list\":{\"listId\":\"8we3g7jmf7\",\"level\":0,\"isOrdered\":false,\"isTaskList\":false,\"listStyleType\":\"SCIR_ECIR_SREC\",\"symbolStyle\":{},\"listStyle\":{\"format\":\"bullet\",\"text\":\"●\",\"align\":\"left\"},\"hideSymbol\":false}},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"<acr-registry>，替换为正确的ACR地址，如 registry.cn-zhangjiakou.aliyuncs.com/docker007/demo-go-echo\"]]],[\"p\",{\"ind\":{\"left\":32},\"list\":{\"listId\":\"8we3g7jmf7\",\"level\":0,\"isOrdered\":false,\"isTaskList\":false,\"isChecked\":false,\"listStyleType\":\"SCIR_ECIR_SREC\",\"symbolStyle\":{},\"listStyle\":{\"format\":\"bullet\",\"text\":\"●\",\"align\":\"left\"},\"hideSymbol\":false,\"extraData\":{}}},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"<acr-region>，替换为正确的ACR仓库region，如 cn-zhangjiakou\"]]],[\"p\",{\"ind\":{\"left\":32},\"list\":{\"listId\":\"8we3g7jmf7\",\"level\":0,\"isOrdered\":false,\"isTaskList\":false,\"isChecked\":false,\"listStyleType\":\"SCIR_ECIR_SREC\",\"symbolStyle\":{},\"listStyle\":{\"format\":\"bullet\",\"text\":\"●\",\"align\":\"left\"},\"hideSymbol\":false,\"extraData\":{}}},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"<acr-service-connection>，替换为正确的ACR服务连接，可打开\"]],[\"a\",{\"href\":\"https://devops.aliyun.com/appstack/setting/serviceConnection\"},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"https://devops.aliyun.com/appstack/setting/serviceConnection\"]]],[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\" 创建ACR服务连接，并将创建好的服务连接ID替换到YAML中\"]]],[\"p\",{\"ind\":{\"left\":32},\"list\":{\"listId\":\"8we3g7jmf7\",\"level\":0,\"isOrdered\":false,\"isTaskList\":false,\"isChecked\":false,\"listStyleType\":\"SCIR_ECIR_SREC\",\"symbolStyle\":{},\"listStyle\":{\"format\":\"bullet\",\"text\":\"●\",\"align\":\"left\"},\"hideSymbol\":false,\"extraData\":{}}},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"<user-id>，替换为您的阿里云账号ID，可打开https://aliyun.com，点击右上角用户信息，查看并获取账号ID\"]]],[\"p\",{\"ind\":{\"left\":0},\"list\":{\"listId\":\"nlp1702v16c\",\"level\":0,\"isOrdered\":true,\"isTaskList\":false,\"isChecked\":false,\"listStyleType\":\"DEC_LEN_LROM_P\",\"symbolStyle\":{},\"listStyle\":{\"format\":\"decimal\",\"text\":\"%1.\",\"align\":\"left\"},\"hideSymbol\":false,\"extraData\":{}}},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"保存生产阶段流水线\"]]],[\"p\",{\"ind\":{\"left\":0},\"list\":{\"listId\":\"nlp1702v16c\",\"level\":0,\"isOrdered\":true,\"isTaskList\":false,\"isChecked\":false,\"listStyleType\":\"DEC_LEN_LROM_P\",\"symbolStyle\":{},\"listStyle\":{\"format\":\"decimal\",\"text\":\"%1.\",\"align\":\"left\"},\"hideSymbol\":false,\"extraData\":{}}},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"点击“研发流程”菜单，运行研发流程\"]]],[\"p\",{\"ind\":{\"left\":0},\"list\":{\"listId\":\"nlp1702v16c\",\"level\":0,\"isOrdered\":true,\"isTaskList\":false,\"isChecked\":false,\"listStyleType\":\"DEC_LEN_LROM_P\",\"symbolStyle\":{},\"listStyle\":{\"format\":\"decimal\",\"text\":\"%1.\",\"align\":\"left\"},\"hideSymbol\":false,\"extraData\":{}}},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"从ACK集群上获取ingress的IP地址\"]]],[\"p\",{\"ind\":{\"left\":0},\"list\":{\"listId\":\"nlp1702v16c\",\"level\":0,\"isOrdered\":true,\"isTaskList\":false,\"isChecked\":false,\"listStyleType\":\"DEC_LEN_LROM_P\",\"symbolStyle\":{},\"listStyle\":{\"format\":\"decimal\",\"text\":\"%1.\",\"align\":\"left\"},\"hideSymbol\":false,\"extraData\":{}}},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"在本地/etc/hosts文件中加入一行，把<ingress ip>替换为上一步获取的IP地址：\"]]],[\"p\",{},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"<ingress ip>   go.demo.prod\"]]],[\"p\",{\"list\":{\"listId\":\"nlp1702v16c\",\"level\":0,\"isOrdered\":true,\"isTaskList\":false,\"isChecked\":false,\"listStyleType\":\"DEC_LEN_LROM_P\",\"symbolStyle\":{},\"listStyle\":{\"format\":\"decimal\",\"text\":\"%1.\",\"align\":\"left\"},\"hideSymbol\":false,\"extraData\":{}},\"ind\":{\"left\":0}},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"通过 curl \"]],[\"a\",{\"href\":\"http://go.demo.prod/version\"},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"http://go.demo.prod/version\"]]],[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\" -H \\\"_env: grey\\\" 即可访问灰度环境，通过  curl \"]],[\"a\",{\"href\":\"http://go.demo.prod/version\"},[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\"http://go.demo.prod/version\"]]],[\"span\",{\"data-type\":\"text\"},[\"span\",{\"data-type\":\"leaf\"},\" 即可访问生产环境\"]]]]}"}, "configs": [{"sn": "6b17d048f02b458e94c5738e4d782a4d", "appTemplateName": "PRESET_INGRESS_GRAY", "type": "ReleaseWorkflow", "modeSetting": {"configType": "ReleaseWorkflow", "modes": {"标准流程": "Synchronization"}}, "configuration": {"type": "ReleaseWorkflow", "appTemplateWorkflowList": [{"sn": "eeed353e-1665-4b54-bd8e-6a7c7660db33", "name": "标准流程", "appTemplateName": "PRESET_INGRESS_GRAY", "releaseStageTemplate": [{"sn": "生产环境", "name": "生产环境", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "灰度发布应用模板-标准流程-生产环境-发布流水线", "owner": "5e96a034afcc954c89849976", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "stages:\n  grey:\n    name: 灰度环境部署并验证\n    jobs:\n      grey_deploy_job:\n        name: ACK灰度部署\n        component: AppStackFlowDeploy\n        with:\n          application: '${APPSTACK_APP_NAME}'\n          environment: grey\n          artifacts:\n            - label: backend\n              value: >-\n                registry.cn-zhangjiakou.aliyuncs.com/docker007/demo-go-echo:grey.v1.1.0\n          autoDeploy: true\n      grey_validate:\n        name: 灰度验证\n        component: ManualValidate\n        needs:\n          - grey_deploy_job\n        with:\n          validatorType: pipelineRoles\n          validatorPipelineRoles: member\n  deploy:\n    name: 生产环境部署\n    jobs:\n      ack_deploy_job:\n        name: ACK生产部署\n        component: AppStackFlowDeploy\n        condition: |\n          succeed('grey.grey_validate')\n        with:\n          application: '${APPSTACK_APP_NAME}'\n          environment: ack-prod\n          artifacts:\n            - label: backend\n              value: >-\n                registry.cn-zhangjiakou.aliyuncs.com/docker007/demo-go-echo:prod.v1.1.0\n          autoDeploy: true\n  validate:\n    name: 生产环境验证\n    jobs:\n      prod_validate:\n        name: 生产验证\n        component: ManualValidate\n        needs:\n          - deploy.ack_deploy_job\n        condition: |\n          succeed('deploy.ack_deploy_job') && !skipped('deploy.ack_deploy_job')\n        with:\n          validatorType: pipelineRoles\n          validatorPipelineRoles: member\n  cleanup:\n    name: 清理环境\n    jobs:\n      cleanup_grey_env_job:\n        name: 清理灰度环境\n        component: AppStackCleanEnv\n        needs:\n          - grey.grey_validate\n          - validate.prod_validate\n        condition: |\n          failed('grey.grey_validate') || !skipped('validate.prod_validate')\n        with:\n          application: '${APPSTACK_APP_NAME}'\n          environment: grey\n          deleteEnv: cleanEnv\n  report:\n    name: 活动上报\n    jobs:\n      command_job:\n        name: 执行命令\n        needs:\n          - deploy.ack_deploy_job\n        condition: |\n          succeed('deploy.ack_deploy_job') && !skipped('deploy.ack_deploy_job')\n        steps:\n          command_step:\n            name: 执行命令\n            step: Command\n            with:\n              run: >\n                #!/bin/bash\n\n                set -e\n\n\n                # 系统提供参数，从流水线上下文获取\n\n                echo [INFO] 系统变量\n\n                echo [INFO] PIPELINE_ID=$PIPELINE_ID       # 流水线ID\n\n                echo [INFO] PIPELINE_NAME=$PIPELINE_NAME   # 流水线名称\n\n                echo [INFO] BUILD_NUMBER=$BUILD_NUMBER     # 流水线运行实例编号\n\n                echo [INFO] EMPLOYEE_ID=$EMPLOYEE_ID       # 触发流水线用户ID\n\n                echo [INFO] WORK_SPACE=$WORK_SPACE         #\n                /root/workspace容器中目录\n\n                echo [INFO] PROJECT_DIR=$PROJECT_DIR       #\n                代码库根路径，默认为/root/workspace/code\n\n                echo [INFO] PLUGIN_DIR=$PLUGIN_DIR         #\n                插件路径，默认为/root/plugins\n\n                echo [INFO] BUILD_JOB_ID=$BUILD_JOB_ID     # build-service 任务ID\n\n                echo [INFO] STEP_ID=$stepIdentifier        # yml 步骤ID\n\n                echo [INFO] CREATOR_ALIYUN_PK=$CREATOR_ALIYUN_PK\n\n                echo [INFO] INGRESS_HOST=$INGRESS_HOST\n\n\n                curl --location\n                \"https://devops.aliyun.com/appstack/hook/api/ucc/report?aliyunPk=$CREATOR_ALIYUN_PK&userId=$EMPLOYEE_ID&pipelineId=$PIPELINE_ID\"\n\n\n                if [[ $? == 0 ]]\n\n                then\n                  echo [INFO] 注册云效体验活动成功\n                else\n                  echo [ERROR] 注册云效体验活动失败\n                  exit 1\n                fi\n", "sources": "[]", "webhook": "azJUxSVmme2HBLA5dRqg", "doValidate": false}, "originPipelineId": 1, "type": "PIPELINEASCODE", "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "pipelineYaml": "stages:\n  grey:\n    name: 灰度环境部署并验证\n    jobs:\n      grey_deploy_job:\n        name: ACK灰度部署\n        component: AppStackFlowDeploy\n        with:\n          application: '${APPSTACK_APP_NAME}'\n          environment: grey\n          artifacts:\n            - label: backend\n              value: >-\n                registry.cn-zhangjiakou.aliyuncs.com/docker007/demo-go-echo:grey.v1.1.0\n          autoDeploy: true\n      grey_validate:\n        name: 灰度验证\n        component: ManualValidate\n        needs:\n          - grey_deploy_job\n        with:\n          validatorType: pipelineRoles\n          validatorPipelineRoles: member\n  deploy:\n    name: 生产环境部署\n    jobs:\n      ack_deploy_job:\n        name: ACK生产部署\n        component: AppStackFlowDeploy\n        condition: |\n          succeed('grey.grey_validate')\n        with:\n          application: '${APPSTACK_APP_NAME}'\n          environment: ack-prod\n          artifacts:\n            - label: backend\n              value: >-\n                registry.cn-zhangjiakou.aliyuncs.com/docker007/demo-go-echo:prod.v1.1.0\n          autoDeploy: true\n  validate:\n    name: 生产环境验证\n    jobs:\n      prod_validate:\n        name: 生产验证\n        component: ManualValidate\n        needs:\n          - deploy.ack_deploy_job\n        condition: |\n          succeed('deploy.ack_deploy_job') && !skipped('deploy.ack_deploy_job')\n        with:\n          validatorType: pipelineRoles\n          validatorPipelineRoles: member\n  cleanup:\n    name: 清理环境\n    jobs:\n      cleanup_grey_env_job:\n        name: 清理灰度环境\n        component: AppStackCleanEnv\n        needs:\n          - grey.grey_validate\n          - validate.prod_validate\n        condition: |\n          failed('grey.grey_validate') || !skipped('validate.prod_validate')\n        with:\n          application: '${APPSTACK_APP_NAME}'\n          environment: grey\n          deleteEnv: cleanEnv\n  report:\n    name: 活动上报\n    jobs:\n      command_job:\n        name: 执行命令\n        needs:\n          - deploy.ack_deploy_job\n        condition: |\n          succeed('deploy.ack_deploy_job') && !skipped('deploy.ack_deploy_job')\n        steps:\n          command_step:\n            name: 执行命令\n            step: Command\n            with:\n              run: >\n                #!/bin/bash\n\n                set -e\n\n\n                # 系统提供参数，从流水线上下文获取\n\n                echo [INFO] 系统变量\n\n                echo [INFO] PIPELINE_ID=$PIPELINE_ID       # 流水线ID\n\n                echo [INFO] PIPELINE_NAME=$PIPELINE_NAME   # 流水线名称\n\n                echo [INFO] BUILD_NUMBER=$BUILD_NUMBER     # 流水线运行实例编号\n\n                echo [INFO] EMPLOYEE_ID=$EMPLOYEE_ID       # 触发流水线用户ID\n\n                echo [INFO] WORK_SPACE=$WORK_SPACE         #\n                /root/workspace容器中目录\n\n                echo [INFO] PROJECT_DIR=$PROJECT_DIR       #\n                代码库根路径，默认为/root/workspace/code\n\n                echo [INFO] PLUGIN_DIR=$PLUGIN_DIR         #\n                插件路径，默认为/root/plugins\n\n                echo [INFO] BUILD_JOB_ID=$BUILD_JOB_ID     # build-service 任务ID\n\n                echo [INFO] STEP_ID=$stepIdentifier        # yml 步骤ID\n\n                echo [INFO] CREATOR_ALIYUN_PK=$CREATOR_ALIYUN_PK\n\n                echo [INFO] INGRESS_HOST=$INGRESS_HOST\n\n\n                curl --location\n                \"https://devops.aliyun.com/appstack/hook/api/ucc/report?aliyunPk=$CREATOR_ALIYUN_PK&userId=$EMPLOYEE_ID&pipelineId=$PIPELINE_ID\"\n\n\n                if [[ $? == 0 ]]\n\n                then\n                  echo [INFO] 注册云效体验活动成功\n                else\n                  echo [ERROR] 注册云效体验活动失败\n                  exit 1\n                fi\n", "engineType": "FlowV2"}, "stageRuleConfig": {"type": "CR", "state": "OFF", "ciType": "SPECIFIC_BRANCH", "specificBranch": "master", "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}, "variableGroups": []}], "fromRevisionSha": "7ee1d4dba87747cf39382c1126ce581582b425d0", "message": "修改研发流程阶段[生产环境]流水线", "order": "1"}]}}, {"sn": "f3c4dd9bceed4ff88af16ded5a437bd0", "appTemplateName": "PRESET_INGRESS_GRAY", "type": "Orchestration", "modeSetting": {"configType": "Orchestration", "modes": {"default_builtin": "Synchronization"}}, "configuration": {"type": "Orchestration", "orchestrations": [{"storageType": "BUILTIN", "format": "MANIFEST", "suitableResourceTypes": ["KUBERNETES"], "sn": "PRESET_INGRESS_GRAY@KUBERNETES", "revision": {"sha": "92ba84f3e94dce0a40a25f6bdc48d5dd7357849a", "message": "修改编排模板", "author": "5e96a034afcc954c89849976", "commitTime": "2024-07-02T04:19:31.271+00:00"}, "name": "PRESET_INGRESS_GRAY", "creatorId": "5e96a034afcc954c89849976", "gmtCreate": "2024-07-02T04:09:35.000+00:00", "modifierId": "5e96a034afcc954c89849976", "gmtModified": "2024-07-02T04:19:31.000+00:00", "description": null, "labelPolicy": "FROM_LABEL_BINDING", "labelList": [{"namespace": "default", "name": "envType", "value": "dev", "displayName": "环境级别", "displayValue": "开发环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}, {"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "placeholderList": [{"name": "image.backend", "description": null, "type": "string", "value": "NULL", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": true, "rsType": "KUBERNETES"}, {"name": "appName", "description": "应用名", "type": "string", "value": "APPSTACK_APP_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "KUBERNETES"}, {"name": "envName", "description": "环境名", "type": "string", "value": "APPSTACK_ENV_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "KUBERNETES"}, {"name": "namespace", "description": null, "type": "string", "value": "namespace", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": false, "rsType": "KUBERNETES"}, {"name": "replicas", "description": null, "type": "string", "value": "2", "overridable": false, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "cpuLimit", "description": null, "type": "string", "value": "1", "overridable": false, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "memoryLimit", "description": null, "type": "string", "value": "256Mi", "overridable": false, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "cpuRequest", "description": null, "type": "string", "value": "0.01", "overridable": false, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "memoryRequest", "description": null, "type": "string", "value": "32Mi", "overridable": false, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "host", "description": null, "type": "string", "value": "host", "overridable": true, "rule": null, "valueSource": "VARIABLE", "predefined": false, "rsType": "KUBERNETES"}], "componentList": [{"name": "svc", "kind": "Service", "description": "", "content": "---\napiVersion: v1\nkind: Service\nmetadata:\n  name: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  namespace: {{ .Values.namespace }}\nspec:\n  selector:\n    run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  ports:\n    - protocol: TCP\n      port: 80\n      targetPort: 8080", "priority": 1, "type": "KUBERNETES"}, {"name": "deploy", "kind": "Deployment", "description": "", "content": "---\napiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  labels:\n    run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  namespace: {{ .Values.namespace }}\nspec:\n  replicas: {{ .Values.replicas }}\n  selector:\n    matchLabels:\n      run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  template:\n    metadata:\n      labels:\n        run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n    spec:\n      containers:\n        - name: main\n          image: {{ .AppStack.image.backend }}\n          ports:\n            - containerPort: 8080\n          resources:\n            limits:\n              cpu: {{ .Values.cpuLimit }}\n              memory: {{ .Values.memoryLimit }}\n            requests:\n              cpu: {{ .Values.cpuRequest }}\n              memory: {{ .Values.memoryRequest }}", "priority": 2, "type": "KUBERNETES"}, {"name": "ingress", "kind": "Ingress", "description": "", "content": "---\napiVersion: networking.k8s.io/v1\nkind: Ingress\nmetadata:\n  name: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  namespace: {{ .Values.namespace }}\n{{ if eq .AppStack.envName \"grey\" }}\n  annotations:\n    # 开启Canary。\n    nginx.ingress.kubernetes.io/canary: \"true\"\n    # 请求头为_env。\n    nginx.ingress.kubernetes.io/canary-by-header: \"_env\"\n    # 请求头_env的值为grey时，请求才会被路由到新版本服务中。\n    nginx.ingress.kubernetes.io/canary-by-header-value: \"grey\"\n{{ end }}\nspec:\n  rules:\n  - host: {{ .Values.host }}\n    http:\n      paths:\n      - path: /\n        pathType: Prefix\n        backend:\n          service:\n            name: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n            port:\n              number: 80", "priority": 3, "type": "KUBERNETES"}], "groupNameMap": {}}], "revision": {"repoMeta": {"name": "PRESET_INGRESS_GRAY", "type": "appTemplateOrchestration"}, "sha": "92ba84f3e94dce0a40a25f6bdc48d5dd7357849a", "message": "修改编排模板", "author": "5e96a034afcc954c89849976", "refs": [], "commitTime": 1719893971271}, "branchInfo": {"head": {"repoMeta": {"name": "PRESET_INGRESS_GRAY", "type": "appTemplateOrchestration"}, "sha": "92ba84f3e94dce0a40a25f6bdc48d5dd7357849a", "message": "修改编排模板", "author": "5e96a034afcc954c89849976", "refs": [], "commitTime": 1719893971271}, "repoMeta": {"name": "PRESET_INGRESS_GRAY", "type": "appTemplateOrchestration"}, "name": "master", "startRevisionSha": "", "fromBranch": ""}}}, {"sn": "9e0956c3ab6c4e7d8e552616e1230c7c", "appTemplateName": "PRESET_INGRESS_GRAY", "type": "VariableGroup", "modeSetting": {"configType": "VariableGroup"}, "configuration": {"type": "VariableGroup", "profileMap": {"prod": {"name": "prod", "displayName": "生产环境变量组", "vars": [{"key": "namespace", "value": "demo-prod", "description": ""}, {"key": "host", "value": "go.demo.prod", "description": ""}]}, "grey": {"name": "grey", "displayName": "灰度环境变量组", "vars": [{"key": "namespace", "value": "demo-pre", "description": ""}, {"key": "host", "value": "go.demo.prod", "description": ""}]}}, "revision": {"repoMeta": {"name": "PRESET_INGRESS_GRAY", "type": "variableTemplate"}, "sha": "3f8ba1e85ee1f9915f79bafb24e6c1e3183741e4", "message": "删除变量组 公共变量组", "author": "5e96a034afcc954c89849976", "refs": [], "commitTime": 1719997937555}, "branchInfo": {"head": {"repoMeta": {"name": "PRESET_INGRESS_GRAY", "type": "variableTemplate"}, "sha": "3f8ba1e85ee1f9915f79bafb24e6c1e3183741e4", "message": "删除变量组 公共变量组", "author": "5e96a034afcc954c89849976", "refs": [], "commitTime": 1719997937555}, "repoMeta": {"name": "PRESET_INGRESS_GRAY", "type": "variableTemplate"}, "name": "master", "startRevisionSha": "", "fromBranch": ""}}}, {"sn": "fd196ced03794d879c392144685a5d62", "appTemplateName": "PRESET_INGRESS_GRAY", "type": "Source", "modeSetting": {"configType": "Source"}, "configuration": {"type": "Source", "codeRepos": [{"name": "demo-go-echo", "repoUrl": "https://atomgit.com/feiyuw/demo-go-echo.git", "identifier": "demo_go_echo", "repoContext": {"repoUrl": "https://atomgit.com/feiyuw/demo-go-echo.git", "defaultBranch": "master", "repoType": "GIT"}, "connectionConfig": {"connectionId": "", "connectionType": "FLOW"}}], "artifactRepos": []}}, {"sn": "a404626561674eacb5724f10def3df38", "appTemplateName": "PRESET_INGRESS_GRAY", "type": "Env", "modeSetting": {"configType": "Env"}, "configuration": {"type": "Env", "envs": [{"name": "grey", "displayName": "ACK灰度环境", "labels": [{"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "grey", "displayName": "灰度环境变量组", "type": "TEMPLATE"}], "deployType": "KUBERNETES", "resourcePoolName": "default", "deployGroupName": null, "description": "", "spec": {"migrateState": null, "oamRebuildState": null, "withoutOam": null, "rolloutStrategy": [{"locator": "*", "batches": null, "batchSteps": null, "timeOutMS": 600000, "targetReplicas": null, "batchMode": "ConfirmFirstBatch", "deployType": "Rolling"}], "replicasManagement": "USER"}}, {"name": "ack-prod", "displayName": "ACK生产环境", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "profileName": null, "variableGroups": [{"name": "prod", "displayName": "生产环境变量组", "type": "TEMPLATE"}], "deployType": "KUBERNETES", "resourcePoolName": "default", "deployGroupName": null, "description": "", "spec": {"migrateState": null, "oamRebuildState": null, "withoutOam": null, "rolloutStrategy": [{"locator": "*", "batches": null, "batchSteps": null, "timeOutMS": 600000, "targetReplicas": null, "batchMode": "ConfirmFirstBatch", "deployType": "Rolling"}], "replicasManagement": "USER"}}]}}]}