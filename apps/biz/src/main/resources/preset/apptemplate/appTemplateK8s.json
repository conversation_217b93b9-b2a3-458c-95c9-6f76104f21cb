{"template": {"name": "k8s-app-template", "displayName": "Java K8s 应用示例模板", "description": "K8s 部署编排应用模板，预置Service + Deployment 应用编排模板，预置四套环境、四套变量组，预置K8s镜像构建部署标准研发流程"}, "configs": [{"sn": "5d645b42677540d1ae0a5d94f3d34ecf", "appTemplateName": "k8s-app-template", "type": "Orchestration", "modeSetting": {"configType": "Orchestration", "modes": {}}, "configuration": {"type": "Orchestration", "orchestrations": [{"storageType": "BUILTIN", "format": "MANIFEST", "suitableResourceTypes": ["KUBERNETES"], "sn": "k8s-app-template@KUBERNETES", "revision": {"sha": "edb7083759b1d95a00746a0ad91a4c9d0fed70d4", "message": "test_bdf6b214c1f0", "author": "cac390bf-d746-46f5-b2cb-b97442c55001", "commitTime": "2023-10-25T02:15:06.903+00:00"}, "name": "Demo Service/Deployment", "creator": "cac390bf-d746-46f5-b2cb-b97442c55001", "gmtCreate": "2023-10-25T02:15:06.000+00:00", "modifier": "cac390bf-d746-46f5-b2cb-b97442c55001", "gmtModified": "2023-10-25T02:15:07.000+00:00", "description": "test_203d969b235b", "placeholderList": [{"name": "image.backend", "description": "后端服务镜像", "type": "string", "value": "NULL", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": true, "rsType": "KUBERNETES"}, {"name": "appName", "description": "应用名", "type": "string", "value": "APPSTACK_APP_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "KUBERNETES"}, {"name": "envName", "description": "环境名", "type": "string", "value": "APPSTACK_ENV_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "KUBERNETES"}, {"name": "namespace", "description": "命名空间", "type": "string", "value": "default", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "cpuLimit", "description": "CPU限制", "type": "string", "value": "1", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "memoryLimit", "description": "内存限制", "type": "string", "value": "1024Mi", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "cpuRequest", "description": "CPU请求", "type": "string", "value": "0.01", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}, {"name": "memoryRequest", "description": "内存请求", "type": "string", "value": "32Mi", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": false, "rsType": "KUBERNETES"}], "componentList": [{"name": "demo-service", "kind": "Service", "description": "服务访问策略", "content": "---\napiVersion: v1\nkind: Service\nmetadata:\n  name: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  # 命名空间配置：\n  # 建议每个环境使用不同的Kubernetes集群命名空间，以便达到环境隔离效果\n  # 部署时云效会将占位符{{ .Values.namespace }}替换成右侧设置的实际值\n  namespace: {{ .Values.namespace }}\nspec:\n  selector:\n    run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  ports:\n    - protocol: TCP\n      port: 80\n      targetPort: 8080", "priority": 1, "type": "KUBERNETES"}, {"name": "demo-deployment", "kind": "Deployment", "description": "无状态应用", "content": "---\napiVersion: apps/v1\nkind: Deployment\nmetadata:\n  name: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  labels:\n    run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  # 命名空间配置：\n  # 建议每个环境使用不同的Kubernetes集群命名空间，以便达到环境隔离效果\n  # 部署时云效会将占位符{{ .Values.namespace }}替换成右侧设置的实际值\n  namespace: {{ .Values.namespace }}\nspec:\n  replicas: 1\n  selector:\n    matchLabels:\n      run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n  template:\n    metadata:\n      labels:\n        run: {{ .AppStack.appName }}-{{ .AppStack.envName }}\n    spec:\n      containers:\n        - name: main\n          # 镜像配置：\n          # 部署时云效会将预置占位符{{ .AppStack.image.backend }}替换成实际部署时的镜像\n          # 支持在新建部署单时手动输入镜像地址，也支持接收流水线的上游构建产物\n          # 另外，在右侧镜像占位符处添加多个镜像，可以支持SideCar或InitContainer等多容器场景\n          image: {{ .AppStack.image.backend }}\n          # 端口配置：\n          ports:\n            - containerPort: 8080\n          # 资源规格配置：\n          # 当不同环境有不同的CPU或内存资源规格要求时，可以定义占位符搭配变量组使用\n          # 如，设置{{ .Values.cpuLimit }}占位符，部署时云效会将占位符{{ .Values.cpuLimit }}替换成右侧设置的实际值\n          resources:\n            limits:\n              cpu: {{ .Values.cpuLimit }}\n              memory: {{ .Values.memoryLimit }}\n            requests:\n              cpu: {{ .Values.cpuRequest }}\n              memory: {{ .Values.memoryRequest }}\n          # 生命周期配置：\n          #lifecycle:\n          #  preStop:\n          #    exec:\n          #      command: [ \"/bin/bash\", \"-c\", \"sleep 10\" ]\n          # 探活配置：\n          #livenessProbe:\n          #  initialDelaySeconds: 10\n          #  failureThreshold: 3\n          #  periodSeconds: 5\n          #  successThreshold: 1\n          #  timeoutSeconds: 2\n          #  httpGet:\n          #    scheme: HTTP\n          #    path: /health\n          #    port: 7002\n          # 就绪探测配置：\n          #readinessProbe:\n          #  initialDelaySeconds: 60\n          #  failureThreshold: 4\n          #  periodSeconds: 10\n          #  successThreshold: 1\n          #  timeoutSeconds: 3\n          #  httpGet:\n          #    scheme: HTTP\n          #    port: 7002\n          #    path: /health", "priority": 2, "type": "KUBERNETES"}], "groupNameMap": {}}], "revision": null}}, {"sn": "2c6d613dc24144f99908001dda1bc445", "appTemplateName": "k8s-app-template", "type": "VariableGroup", "modeSetting": {"configType": "VariableGroup"}, "configuration": {"type": "VariableGroup", "profileMap": {"dev": {"name": "dev", "displayName": "开发环境变量组", "vars": [{"key": "namespace", "value": "default", "description": "命名空间"}]}, "test": {"name": "test", "displayName": "测试环境变量组", "vars": [{"key": "namespace", "value": "default", "description": "命名空间"}]}, "prepub": {"name": "prepub", "displayName": "预发环境变量组", "vars": [{"key": "namespace", "value": "default", "description": "命名空间"}]}, "production": {"name": "production", "displayName": "生产环境变量组", "vars": [{"key": "namespace", "value": "default", "description": "命名空间"}]}}, "revision": {"repoMeta": {"name": "k8s-app-template", "type": "variableTemplate"}, "sha": "e22021e90c65b175ec99f26b2c727520beb9ffa7", "message": "null", "author": "cac390bf-d746-46f5-b2cb-b97442c55001", "refs": [], "commitTime": 1698200105753}}}, {"sn": "707cf7f35f114b2590144b041f0d10eb", "appTemplateName": "k8s-app-template", "type": "Env", "modeSetting": {"configType": "Env"}, "configuration": {"type": "Env", "envs": [{"name": "dev", "displayName": "开发环境", "labels": [{"namespace": "default", "name": "envType", "value": "dev", "displayName": "环境级别", "displayValue": "开发环境", "extraMap": {}}], "profileName": "dev", "deployType": null, "resourcePoolName": null, "deployGroupName": null, "description": "开发环境"}, {"name": "test", "displayName": "测试环境", "labels": [{"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}], "profileName": "test", "deployType": null, "resourcePoolName": null, "deployGroupName": null, "description": "测试环境"}, {"name": "prepub", "displayName": "预发环境", "labels": [{"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "profileName": "prepub", "deployType": null, "resourcePoolName": null, "deployGroupName": null, "description": "预发环境"}, {"name": "production", "displayName": "生产环境", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "profileName": "production", "deployType": null, "resourcePoolName": null, "deployGroupName": null, "description": "生产环境"}]}}, {"sn": "b65b16899cd54b93963a6fc64e0f19d1", "appTemplateName": "k8s-app-template", "type": "ReleaseWorkflow", "modeSetting": {"configType": "ReleaseWorkflow"}, "configuration": {"type": "ReleaseWorkflow", "appTemplateWorkflowList": [{"sn": "b25fd354eb034f13a5103aeeee31dac1", "name": "标准研发流程", "appTemplateName": "标准研发流程", "releaseStageTemplate": [{"sn": "测试阶段", "name": "测试阶段", "labels": [{"namespace": "default", "name": "envType", "value": "test", "displayName": "环境级别", "displayValue": "测试环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Java K8S 应用标准研发流程模板-测试阶段-发布流水线", "owner": "ed99ca55-1fe1-4ad3-9fc3-9314edf0e64b", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: 构建\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 镜像构建并推送至自定义镜像仓库\n            task: execution-component-production@1\n            identifier: '15_1675155917344'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java 构建\n                  stepType: java_build\n                  stepIdentifier: '15_1675155917344__16_1675155917345'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                - name: 镜像构建并推送至自定义镜像仓库\n                  stepType: custom_docker_build\n                  stepIdentifier: '15_1675155917344__17_1675155917347'\n                  artifact: artifact\n                  DOCKER_DESTINATION: ''\n                  authType: serviceConnection\n                  SERVICE_CONNECTION_ID: ''\n                  DOCKER_USER: ''\n                  DOCKER_PASSWORD: ''\n                  DOCKER_FILE_PATH: Dockerfile\n                  CONTEXT_PATH: ''\n                  NO_CACHE: false\n                  ARGS: '{}'\n                  JSONEncoding: true\n                  INSECURE_REGISTRY: false\n                  freeInTaskGroupModeFields:\n                    - ARGS\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - name: 15_1675155917344__17_1675155917347.dynamic_output.\n                displayName: 镜像仓库地址.\n                type: artifact\n                identifier: 15_1675155917344__17_1675155917347__DOCKER_DESTINATION\n                description: 自定义镜像仓库地址\n                export: true\n                ref: steps\n                jobIdentifier: '15_1675155917344'\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: AppStack部署\n            task: execution-component-production@1\n            identifier: '18_1675155917349'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '18_1675155917349__19_1675155917350'\n                  APP_NAME: ''\n                  ENV_NAME: ''\n                  ACREE_TIP: ''\n                  IMAGE_LIST: ''\n                  AUTO_SUBMIT: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": ""}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "ON", "ciType": "CR", "specificBranch": null, "validateConfigState": "OFF", "validateStageSnList": [], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}}, {"sn": "预发阶段", "name": "预发阶段", "labels": [{"namespace": "default", "name": "envType", "value": "prepub", "displayName": "环境级别", "displayValue": "预发环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Java K8S 应用标准研发流程模板-预发阶段-发布流水线", "owner": "ed99ca55-1fe1-4ad3-9fc3-9314edf0e64b", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: 构建\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 镜像构建并推送至自定义镜像仓库\n            task: execution-component-production@1\n            identifier: '20_1675156443208'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java 构建\n                  stepType: java_build\n                  stepIdentifier: '20_1675156443208__21_1675156443209'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                - name: 镜像构建并推送至自定义镜像仓库\n                  stepType: custom_docker_build\n                  stepIdentifier: '20_1675156443208__22_1675156443211'\n                  artifact: artifact\n                  DOCKER_DESTINATION: ''\n                  authType: serviceConnection\n                  SERVICE_CONNECTION_ID: ''\n                  DOCKER_USER: ''\n                  DOCKER_PASSWORD: ''\n                  DOCKER_FILE_PATH: Dockerfile\n                  CONTEXT_PATH: ''\n                  NO_CACHE: false\n                  ARGS: '{}'\n                  JSONEncoding: true\n                  INSECURE_REGISTRY: false\n                  freeInTaskGroupModeFields:\n                    - ARGS\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - name: 20_1675156443208__22_1675156443211.dynamic_output.\n                displayName: 镜像仓库地址.\n                type: artifact\n                identifier: 20_1675156443208__22_1675156443211__DOCKER_DESTINATION\n                description: 自定义镜像仓库地址\n                export: true\n                ref: steps\n                jobIdentifier: '20_1675156443208'\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: AppStack部署\n            task: execution-component-production@1\n            identifier: '23_1675156443212'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '23_1675156443212__24_1675156443213'\n                  APP_NAME: ''\n                  ENV_NAME: ''\n                  ACREE_TIP: ''\n                  IMAGE_LIST: ''\n                  AUTO_SUBMIT: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": ""}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "ON", "ciType": "CR", "specificBranch": null, "validateConfigState": "ON", "validateStageSnList": ["测试阶段"], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}}, {"sn": "生产阶段", "name": "生产阶段", "labels": [{"namespace": "default", "name": "envType", "value": "production", "displayName": "环境级别", "displayValue": "生产环境", "extraMap": {}}], "pipeline": {"pipeline": {"name": "Java K8S 应用标准研发流程模板-生产阶段-发布流水线", "owner": "ed99ca55-1fe1-4ad3-9fc3-9314edf0e64b", "pipelineConfigVo": {"triggerVoList": [{"type": "MANUAL"}], "settings": "{\"executeScope\":\"\",\"caches\":[{\"directory\":\"/root/.m2\",\"desc\":\"maven依赖缓存\",\"disable\":false},{\"directory\":\"/root/.gradle/caches\",\"desc\":\"gradle依赖缓存\",\"disable\":false},{\"directory\":\"/root/.npm\",\"desc\":\"npm依赖全局缓存\",\"disable\":false},{\"directory\":\"/root/.yarn\",\"desc\":\"yarn依赖全局缓存\",\"disable\":false},{\"directory\":\"/go/pkg/mod\",\"desc\":\"go mod缓存\",\"disable\":false},{\"directory\":\"/root/.cache\",\"desc\":\"其它缓存\",\"disable\":false}]}", "flow": "schema: tb\npipeline:\n  - name: 构建\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 镜像构建并推送至自定义镜像仓库\n            task: execution-component-production@1\n            identifier: '27_1675156498084'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: Java 构建\n                  stepType: java_build\n                  stepIdentifier: '27_1675156498084__28_1675156498085'\n                  version: jdk1.8\n                  CI_RUNTIME_VERSION: jdk1.8\n                  MAVEN_VERSION: 3.5.2\n                  command: >\n                    # maven build default command\n\n                    mvn -B clean package -Dmaven.test.skip=true\n                    -Dautoconfig.skip\n\n\n                    # gradle build default command\n\n                    # ./gradlew build\n\n\n                    # ant build default command\n\n                    # ant\n                  freeInTaskGroupModeFields: []\n                - name: 镜像构建并推送至自定义镜像仓库\n                  stepType: custom_docker_build\n                  stepIdentifier: '27_1675156498084__29_1675156498086'\n                  artifact: artifact\n                  DOCKER_DESTINATION: ''\n                  authType: serviceConnection\n                  SERVICE_CONNECTION_ID: ''\n                  DOCKER_USER: ''\n                  DOCKER_PASSWORD: ''\n                  DOCKER_FILE_PATH: Dockerfile\n                  CONTEXT_PATH: ''\n                  NO_CACHE: false\n                  ARGS: '{}'\n                  JSONEncoding: true\n                  INSECURE_REGISTRY: false\n                  freeInTaskGroupModeFields:\n                    - ARGS\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output:\n              - name: 27_1675156498084__29_1675156498086.dynamic_output.\n                displayName: 镜像仓库地址.\n                type: artifact\n                identifier: 27_1675156498084__29_1675156498086__DOCKER_DESTINATION\n                description: 自定义镜像仓库地址\n                export: true\n                ref: steps\n                jobIdentifier: '27_1675156498084'\n            freeInTaskGroupModeFields: []\n  - name: 人工卡点\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 人工卡点\n            task: APPSTACK_DIY_VALIDATE_TB_PROD@1\n            identifier: '30_1675156498088'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              appId: '${INPUTS.appId}'\n              mixFlowInstId: '${INPUTS.mixFlowInstId}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              validatorMethod: or\n              validatorType: user\n              validatorUser: ''\n              validatorRole: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 部署\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: AppStack部署\n            task: execution-component-production@1\n            identifier: '31_1675156498088'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 云效 AppStack 部署\n                  stepType: appstack-flow-deploy\n                  stepIdentifier: '31_1675156498088__32_1675156498089'\n                  APP_NAME: ''\n                  ENV_NAME: ''\n                  ACREE_TIP: ''\n                  IMAGE_LIST: ''\n                  AUTO_SUBMIT: true\n                  CUSTOM_PARAMS: ''\n                  freeInTaskGroupModeFields:\n                    - IMAGE_LIST\n                    - CUSTOM_PARAMS\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 合并主干\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 合并主干\n            task: execution-component-production@1\n            identifier: '10_1675156523833'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 代码合并\n                  stepType: git-merge\n                  stepIdentifier: '10_1675156523833__11_1675156523833'\n                  GIT_BRANCH: master\n                  DELETE_ORIGIN_BRANCH: false\n                  DELETE_MATCHED_BRANCH: false\n                  BRANCH_FILTER: ''\n                  freeInTaskGroupModeFields: []\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n  - name: 关闭变更\n    stages:\n      - driven: AUTO\n        jobs:\n          - displayName: 关闭变更\n            task: execution-component-production@1\n            identifier: '12_1675156563010'\n            templateType: task\n            templateSign: ''\n            templateBatchUpdate: 'N'\n            extraInfo: ''\n            params:\n              steps:\n                - name: 关闭变更\n                  stepType: appstack-close-change-request-production\n                  stepIdentifier: '12_1675156563010__13_1675156563010'\n                  freeInTaskGroupModeFields: []\n              ENGINE_PIPELINE_NAME: '${INPUTS.ENGINE_PIPELINE_NAME}'\n              ENGINE_PIPELINE_ID: '${INPUTS.ENGINE_PIPELINE_ID}'\n              ENGINE_PIPELINE_INST_ID: '${INPUTS.ENGINE_PIPELINE_INST_ID}'\n              ENGINE_PIPELINE_INST_NUMBER: '${INPUTS.ENGINE_PIPELINE_INST_NUMBER}'\n              buildNodeGroup: ''\n              clone_option: all\n              selected_sources: ''\n            plugins: []\n            output: []\n            freeInTaskGroupModeFields: []\n", "sources": "[]", "webhook": ""}, "originPipelineId": 1, "envId": "", "tagIds": "", "tagList": [], "envObj": [], "members": ""}, "refObjectList": [], "plugins": null, "engineSn": null, "engineType": "FlowV1"}, "stageRuleConfig": {"type": "CR", "state": "ON", "ciType": "CR", "specificBranch": null, "validateConfigState": "ON", "validateStageSnList": ["测试阶段", "预发阶段"], "changeRequestsSecurityAuditCheck": null, "changeRequestsCodeReviewCheck": null, "changeFreeControl": null}}], "fromRevisionSha": "", "message": "", "order": "1"}]}}]}