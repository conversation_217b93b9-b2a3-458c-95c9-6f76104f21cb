{"componentList": [{"name": "{{ .AppStack.appName }}", "type": "HOST", "kind": "Application", "content": "apiVersion: core.oam.dev/v1beta1\nkind: Application\nmetadata:\n  name: '{{ .AppStack.appName }}-{{ .AppStack.envName }}'\nspec:\n  components:\n    - name: '{{ .AppStack.appName }}-{{ .AppStack.envName }}'\n      type: hybrid-workload\n      properties:\n        specialization: non-containerized\n        bootstrap:\n          - type: http-direct\n            fromUrl: '{{ .AppStack.artifact.demoapp }}'\n            toPath: /package.tgz\n        packages:\n          - type: local\n            name: '{{ .AppStack.appName }}-{{ .AppStack.envName }}'\n            path: '# 请输入制品包在你主机上的绝对路径，示例：/home/<USER>/app/package.tgz'\n            executedBy: 'admin'\n            start:\n              command: |-\n                # 一个典型的启动脚本逻辑如下：先将制品包解压缩到指定目录中，再执行启动文件（通常在代码中维护，如示例中的deploy.sh）。\n                # mkdir -p /home/<USER>/application\n                # tar zxvf /home/<USER>/app/package.tgz -C /home/<USER>/application/\n                # sh /home/<USER>/application/deploy.sh start\n            stop:\n              command: |-\n                # 一个典型的停止脚本逻辑如下：先查找到服务进程，再kill服务进程。\n                # sh /home/<USER>/application/deploy.sh stop\n            healthCheck:\n              command: |-\n                # sh /home/<USER>/application/deploy.sh health-check\n            descriptors:\n              source: local\n      traits: []"}], "placeholderList": [{"name": "artifact.demoapp", "description": "制品包地址（通常来自构建阶段产物，部署时动态输入地址）", "type": "string", "value": "NULL", "overridable": true, "rule": null, "valueSource": "CONSTANT", "predefined": true, "rsType": "HOST"}, {"name": "appName", "description": "应用名", "type": "string", "value": "APPSTACK_APP_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "HOST"}, {"name": "envName", "description": "环境名", "type": "string", "value": "APPSTACK_ENV_NAME", "overridable": false, "rule": null, "valueSource": "VARIABLE", "predefined": true, "rsType": "HOST"}]}