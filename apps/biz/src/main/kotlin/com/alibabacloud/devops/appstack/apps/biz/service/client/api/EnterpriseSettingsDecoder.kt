package com.alibabacloud.devops.appstack.apps.biz.service.client.api


import com.github.lianjiatech.retrofit.spring.boot.core.ErrorDecoder
import okhttp3.Request
import okhttp3.Response
import org.springframework.stereotype.Component
import retrofit2.HttpException
import retrofit2.Response as Retrofit2Response

/**
 * @author: <EMAIL>
 * @date: 2022-03-24 17:13
 * @version: EnterpriseSettingsDecoder, v0.1
 **/
@Component
class EnterpriseSettingsDecoder : ErrorDecoder {

    override fun invalidRespDecode(request: Request?, response: Response?): RuntimeException? {
        if (response?.code() == 409) {
            val error = Retrofit2Response.error<Response>(response.body()!!, response)
            return HttpException(error)
        }
        return super.invalidRespDecode(request, response)
    }
}
