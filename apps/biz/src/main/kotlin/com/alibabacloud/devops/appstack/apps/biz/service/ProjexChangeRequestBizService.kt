package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.response.cr.ProjexWorkItem

/**
 * @author: <EMAIL>
 * @date: 2022-08-20 11:16
 * @version: ProjexChangeRequestBizService, v0.1
 **/
interface ProjexChangeRequestBizService {

    fun recommend(): List<ProjexWorkItem>

    fun searchByCrSn(appName: String, crSn: String): List<ProjexWorkItem>

    fun searchByNameOrId(query: String): List<ProjexWorkItem>

    fun bindCrToWorkItem(workItemId: String, crSn: String): Boolean

    fun unbindCrToWorkItem(workItemId: String, crSn: String): Boolean
}