package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.AppVariableGroupsBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.Variable
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.request.ProfileRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateVariableGroupDryRunRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.VariableStorageVO
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * @author: <EMAIL>
 * @date: 2021-12-31 14:12
 * @version: VariableBizApi, v0.1
 **/
@Tag(name = "Variable", description = "变量 API")
@RestController
@RequestMapping("/api/v1")
open class VariableBizApi {

    @Autowired
    lateinit var appVariableGroupsBizService: AppVariableGroupsBizService

    @PostMapping("/apps/{appName}/variableGroups")
    fun createAppVariableGroup(
        @PathVariable appName: String,
        @RequestBody profileRequest: ProfileRequest
    ): Response<VariableStorageVO> {
        return Response.success(appVariableGroupsBizService.createProfile(appName, profileRequest))
    }

    @PutMapping("/apps/{appName}/variableGroups/{variableGroupName}")
    fun updateAppVariableGroup(
        @PathVariable appName: String,
        @PathVariable variableGroupName: String,
        @RequestBody profileRequest: ProfileRequest
    ): Response<VariableStorageVO> {
        profileRequest.name = variableGroupName
        return Response.success(appVariableGroupsBizService.updateProfile(appName, profileRequest))
    }

    @DeleteMapping("/apps/{appName}/variableGroups/{variableGroupName}")
    fun deleteAppVariableGroup(
        @PathVariable appName: String,
        @PathVariable variableGroupName: String,
    ): Response<Unit> {
        return Response.success(appVariableGroupsBizService.deleteProfile(appName, variableGroupName))
    }

    @GetMapping("/apps/{appName}/variableGroups")
    fun findAppVariableGroups(@PathVariable appName: String): Response<VariableStorageVO> {
        return Response.success(appVariableGroupsBizService.findAllProfiles(appName))
    }

    @GetMapping("/apps/{appName}/variableGroups/revisions")
    fun findAppVariableGroupsRevision(
        @PathVariable("appName") appName: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long
    ): Response<Pagination<Revision>> {
        return Response.success(appVariableGroupsBizService.findProfilesRevisions(appName, current, pageSize))
    }

    @GetMapping("/apps/{appName}/variableGroups/revisions/{revisionSha}")
    fun findAppVariableGroupsByRevisionSha(
        @PathVariable("appName") appName: String,
        @PathVariable("revisionSha") revisionSha: String
    ): Response<VariableStorageVO> {
        return Response.success(appVariableGroupsBizService.findProfilesByRevisionSha(appName, revisionSha))
    }


    @GetMapping("/apps/{appName}/variableGroups/tags/{tagName}")
    fun findVarByTag(
        @PathVariable("appName") appName: String,
        @PathVariable("tagName") tagName: String
    ): Response<VariableStorageVO> {
        return Response.success(appVariableGroupsBizService.findProfilesByTag(appName, tagName))
    }

    @GetMapping("/apps/{appName}/variableGroups/revisions:diff")
    fun compareVariable(
        @PathVariable("appName") appName: String,
        @RequestParam("beforeRevisionSha") beforeRevisionSha: String,
        @RequestParam("afterRevisionSha") afterRevisionSha: String
    ): Response<Triple<VariableStorageVO, VariableStorageVO, List<DiffItem<String>>>> {
        return Response.success(appVariableGroupsBizService.compare(appName, beforeRevisionSha, afterRevisionSha))
    }

    @PostMapping("/apps/{appName}/variableGroups/{variableGroupName}:dryrun")
    fun updateVariableGroupDryRun(
        @PathVariable appName: String,
        @PathVariable variableGroupName: String,
        @RequestBody request: UpdateVariableGroupDryRunRequest
    ): Response<List<Variable>> {
        return Response.success(appVariableGroupsBizService.updateDryrun(appName, variableGroupName, request))
    }

}