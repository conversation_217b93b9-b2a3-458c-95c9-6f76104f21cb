package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.CodeProxyBizService
import com.alibabacloud.devops.appstack.libs.code.spring.boot.starter.service.CodeService
import com.alibabacloud.devops.appstack.libs.model.`do`.app.ConnectionConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.RepoContext
import com.alibabacloud.devops.appstack.libs.model.response.cr.GitBranchInfo
import com.alibabacloud.devops.appstack.libs.model.response.cr.GitProject
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2022-11-26
 */
@Service
class CodeProxyBizServiceImpl : CodeProxyBizService {

    @Autowired
    lateinit var codeService: CodeService

    override fun searchBranches(
        repoContext: RepoContext,
        connectionConfig: ConnectionConfig,
        search: String
    ): List<GitBranchInfo> {
        return codeService.searchBranches(repoContext, connectionConfig, search)
    }

    override fun findBranch(
        repoContext: RepoContext,
        connectionConfig: ConnectionConfig,
        branchName: String,
    ): GitBranchInfo {
        return codeService.findBranch(repoContext, connectionConfig, branchName)
    }

    override fun searchProjects(
        repoContext: RepoContext,
        connectionConfig: ConnectionConfig,
        search: String
    ): List<GitProject> {
        return codeService.searchProjects(repoContext, connectionConfig, search)
    }
}