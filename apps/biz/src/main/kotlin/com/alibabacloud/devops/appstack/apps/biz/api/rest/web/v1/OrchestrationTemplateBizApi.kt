package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.OrchestrationTemplateBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.InvalidChangeItem
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.template.OrchestrationTemplate
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateCheckRequest
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateCopyRequest
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateCreateRequest
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateUpdateRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * @author: <EMAIL>
 * @date: 2021-12-15 20:54
 * @version: AppOrchestrationBizApi, v0.1
 **/
@Tag(name = "OrgOrchestrationTemplate", description = "企业编排模板相关 API")
@RestController
@RequestMapping("/api/v1/templates")
open class OrchestrationTemplateBizApi {

    @Autowired
    lateinit var orchestrationTemplateBizService: OrchestrationTemplateBizService

    @GetMapping("/orchestrations:all")
    fun findAllOrchestrationTemplate(
        @RequestParam(value = "scopes") scopes: List<OrchestrationTemplate.TemplateScopeEnum>
    ): Response<List<OrchestrationTemplate>> {
        return Response.success(orchestrationTemplateBizService.findAll(scopes))
    }

    @PostMapping("/orchestrations")
    fun createOrchestrationTemplate(@RequestBody orchestrationTemplateCreateRequest: OrchestrationTemplateCreateRequest): Response<OrchestrationTemplate> {
        return Response.success(orchestrationTemplateBizService.create(request = orchestrationTemplateCreateRequest))
    }

    @PostMapping("/orchestrations:copy")
    fun copy(@RequestBody templateCopyRequest: OrchestrationTemplateCopyRequest): Response<OrchestrationTemplate> {
        return Response.success(orchestrationTemplateBizService.copy(requestTemplate = templateCopyRequest))
    }

    @PutMapping("/{sn}")
    fun updateOrchestrationTemplate(
        @PathVariable("sn") sn: String,
        @RequestBody orchestrationTemplateUpdateRequest: OrchestrationTemplateUpdateRequest
    ): Response<OrchestrationTemplate> {
        return Response.success(
            orchestrationTemplateBizService.update(
                sn = sn,
                templateUpdateRequest = orchestrationTemplateUpdateRequest
            )
        )
    }

    @DeleteMapping("/{sn}")
    fun deleteOrchestrationTemplate(
        @PathVariable("sn") sn: String
    ): Response<Boolean> {
        return Response.success(orchestrationTemplateBizService.delete(sn))
    }

    @PostMapping("/orchestrations:check")
    fun check(
        @RequestBody request:OrchestrationTemplateCheckRequest
    ): Response<List<InvalidChangeItem>> {
        return Response.success(orchestrationTemplateBizService.check(request))
    }

}