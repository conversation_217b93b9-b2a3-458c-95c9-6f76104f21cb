package com.alibabacloud.devops.appstack.apps.biz.model.po

import com.alibabacloud.devops.appstack.libs.common.annotation.Comment
import com.alibabacloud.devops.appstack.libs.common.model.BasePO
import com.baomidou.mybatisplus.annotation.TableName
import lombok.Data
import javax.persistence.Column
import javax.persistence.Index
import javax.persistence.Table

/**
 * <AUTHOR>
 * @create 2023/10/18 2:56 PM
 **/
@TableName("app_template_usage_relations")
@Comment("AppTemplateUsageRelations")
@Table(
    indexes = [
        Index(unique = true, columnList = "org_id(64),is_deleted(32),app_template_name(64),app_name(64)"),
        Index(columnList = "org_id(64),is_deleted(32),app_name(64)")
    ]
)
@Data
class AppTemplateUsageRelationPO : BasePO() {

    @Comment("应用模版名")
    @Column(columnDefinition = "varchar(255)", nullable = false)
    lateinit var appTemplateName: String

    @Comment("应用名")
    @Column(columnDefinition = "varchar(255)", nullable = false)
    lateinit var appName: String

}