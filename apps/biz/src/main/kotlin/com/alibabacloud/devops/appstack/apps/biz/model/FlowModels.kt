package com.alibabacloud.devops.appstack.apps.biz.model


data class RunCommand(
    var command: String,
    var environments: Map<String, String>
)

data class JobStatus(
    var status: String,
    var startTime: Long?,
    var endTime: Long?
)

data class JobLog(
    var last: Int,
    var more: Boolean,
    var downloadURI: String?,
    var logs: String?
)

data class JobCallback(
    var id: Long,
    var regionId: Long?,
    var requestId: String?,
    var status: String
)

enum class FlowTaskStatus {
    INIT,
    PENDING,
    SUCCESS,
    RUNNING,
    CANCELLING,
    FAILED,
    CANCELED,
    SKIP
}

data class FlowRole(
    var id: String,
    var name: String,
    var level: Int,
    var isDefault: Boolean,
    var permissions: List<FlowRolePermission>,
)

data class FlowRolePermission(
    var code: String,
    var name: String,
    var enabled: Boolean,
)