package com.alibabacloud.devops.appstack.apps.biz.api.rest.hook.org

import com.alibabacloud.devops.appstack.apps.biz.model.OrgEventRequest
import com.alibabacloud.devops.appstack.apps.biz.model.OrgV5EventRequest
import com.alibabacloud.devops.appstack.apps.biz.service.OrgEventService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR> <EMAIL>
 * @version : OrgCallbackApi, v0.1
 * @date : 2021-11-29 21:34
 **/
@Slf4k
@Tag(name = "OrgCallback", description = "基础服务回调相关 API")
@RestController
@RequestMapping("/callback/org","/callbackapi/org")
class OrgCallbackApi {

    @Autowired
    lateinit var orgEventService: OrgEventService

    @Autowired
    lateinit var objectMapper: ObjectMapper

    @Operation(summary = "基础服务事件处理，for公有云")
    @PostMapping("/event")
    fun eventHandler(
        @RequestHeader("X-TB-Signature") signature: String,
        @RequestHeader("X-TB-Event") eventCode: String,
        @RequestHeader("X-TB-EventID") eventId: String,
        @RequestBody body: OrgEventRequest,
    ) {
        logger.info("signature: {}", signature)
        // TODO check signature
        orgEventService.handleEvent(eventCode, eventId, body)
    }

    @Operation(summary = "v5基础服务事件处理，for专有云")
    @PostMapping("/vpc/event")
    fun vpcEventHandler(
        @RequestHeader("X-Yunxiao-Webhook-Event") eventType: String,
        @RequestHeader("X-Yunxiao-Webhook-EventID") eventId: String,
        @RequestHeader("X-Yunxiao-Webhook-Sign") signature: String,
        @RequestBody body: String,
    ) {
        logger.info("signature: {}", signature)
        logger.info("vpc event request body: $body")
        val v5EventRequest = objectMapper.readValue<OrgV5EventRequest>(body)
        // TODO check signature
        orgEventService.handleV5Event(eventType, eventId, v5EventRequest)
    }
}