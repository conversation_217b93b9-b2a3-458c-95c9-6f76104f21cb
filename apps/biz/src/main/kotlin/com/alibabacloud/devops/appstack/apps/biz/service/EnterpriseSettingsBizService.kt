package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.request.KubeConfigVO
import com.alibabacloud.devops.appstack.libs.model.request.UpsertAliyunAuthAccountRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpsertKubeConfigRequest
import com.alibabacloud.devops.appstack.libs.model.vo.AliyunAuthAccountVO

/**
 * @author: <EMAIL>
 * @date: 2022-01-27 11:40
 * @version: EnterpriseSettingsBizService, v0.1
 **/
interface EnterpriseSettingsBizService {
    fun findAllAliyunAuthAccounts(): List<AliyunAuthAccountVO>
    fun createAliyunAuthAccount(request: UpsertAliyunAuthAccountRequest): AliyunAuthAccountVO?
    fun updateAliyunAuthAccount(request: UpsertAliyunAuthAccountRequest): AliyunAuthAccountVO?
    fun deleteAliyunAuthAccount(id: Long)

    fun findAllKubeConfig(): List<KubeConfigVO>
    fun createKubeConfig(upsertKubeConfigRequest: UpsertKubeConfigRequest): KubeConfigVO
    fun updateKubeConfig(id: Long, upsertKubeConfigRequest: UpsertKubeConfigRequest): KubeConfigVO?
    fun deleteKubeConfig(id: Long)
}