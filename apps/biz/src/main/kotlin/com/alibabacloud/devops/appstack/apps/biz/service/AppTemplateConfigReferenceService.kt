package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.apps.biz.model.AppTemplateConfigReference
import com.alibabacloud.devops.appstack.apps.biz.model.AppTemplateNameAndConfig

/**
 * <AUTHOR>
 * @date 2023-11-09
 */
interface AppTemplateConfigReferenceService {

    fun reBind(appTemplateName: String, appTemplateConfigSn: String, references: List<AppTemplateConfigReference>)

    fun deleteAll(appTemplateName: String, appTemplateConfigSn: String): Int

    fun findAppTemplateNameAndConfigByReference(reference: AppTemplateConfigReference): List<AppTemplateNameAndConfig>
}

