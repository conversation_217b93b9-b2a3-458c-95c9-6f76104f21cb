package com.alibabacloud.devops.appstack.apps.biz.service.facade

import com.alibabacloud.devops.appstack.apps.biz.service.client.api.ChangeOrderApi
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.util.checkBizWithCode
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.event.ActionLog
import com.alibabacloud.devops.appstack.libs.model.request.EnvActionLogRequest
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * 环境操作日志先继续复用原 change-order 功能，之后可能会考虑使用其他的操作日志系统
 * <AUTHOR>
 * @date 2021-12-31
 */
@Service
@Slf4k
open class EnvActionLogFacade {

    @Autowired
    lateinit var changeOrderApi: ChangeOrderApi

    fun recordEnvCreateAction(appName: String, envName: String, result: Boolean = true, msg: String? = "") {
        logger.info("recordEnvCreateAction for app[$appName] env[$envName] result[$result]")
        val response =
            changeOrderApi.recordEnvCreateAction(request = EnvActionLogRequest(appName, envName, result, msg))
        if (!response.success) {
            logger.error("recordEnvCreateAction for app[$appName] env[$envName] result[$result] fail")
        }
    }

    fun recordEnvUpdateAction(appName: String, envName: String, result: Boolean = true, msg: String? = "") {
        logger.info("recordEnvUpdateAction for app[$appName] env[$envName] result[$result]")
        val response =
            changeOrderApi.recordEnvUpdateAction(request = EnvActionLogRequest(appName, envName, result, msg))
        if (!response.success) {
            logger.error("recordEnvUpdateAction for app[$appName] env[$envName] result[$result] fail")
        }
    }

    fun recordEnvDeleteAction(appName: String, envName: String, result: Boolean = true, msg: String? = "") {
        logger.info("recordEnvDeleteAction for app[$appName] env[$envName] result[$result]")
        val response =
            changeOrderApi.recordEnvDeleteAction(request = EnvActionLogRequest(appName, envName, result, msg))
        if (!response.success) {
            logger.error("recordEnvDeleteAction for app[$appName] env[$envName] result[$result] fail")
        }
    }

    fun recordEnvLockAction(appName: String, envName: String, result: Boolean = true, msg: String? = "") {
        logger.info("recordEnvLockAction for app[$appName] env[$envName] result[$result]")
        val response = changeOrderApi.recordEnvLockAction(request = EnvActionLogRequest(appName, envName, result, msg))
        if (!response.success) {
            logger.error("recordEnvLockAction for app[$appName] env[$envName] result[$result] fail")
        }
    }

    fun recordEnvUnlockAction(appName: String, envName: String, result: Boolean = true, msg: String? = "") {
        logger.info("recordEnvUnlockAction for app[$appName] env[$envName] result[$result]")
        val response =
            changeOrderApi.recordEnvUnlockAction(request = EnvActionLogRequest(appName, envName, result, msg))
        if (!response.success) {
            logger.error("recordEnvUnlockAction for app[$appName] env[$envName] result[$result] fail")
        }
    }

    fun findPaginatedEnvActionLog(
        appName: String,
        envName: String,
        pageSize: Long,
        current: Long
    ): Pagination<ActionLog> {
        logger.info("findPaginatedEnvActionLog for env[$envName] in app[$appName]")
        val response = changeOrderApi.findPaginatedEnvActionLog(
            appName = appName,
            envName = envName,
            pageSize = pageSize,
            current = current
        )
        checkBizWithCode(response.success) { ErrorCode.AS_ENV_ACTION_LOG_FIND_FAILED }
        return response.data!!
    }
}