package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Component
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.template.OrchestrationTemplate
import com.alibabacloud.devops.appstack.libs.model.constant.VarVO
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * @author: <EMAIL>
 * @date: 2021-12-15 20:54
 * @version: AppOrchestrationBizApi, v0.1
 **/
@Tag(name = "Global", description = "全局相关 API")
@RequestMapping("/api/v1/global")
@RestController
open class GlobalBizApi {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Operation(summary = "获取系统变量")
    @GetMapping("/vars")
    fun findAllSystemVar(): Response<List<VarVO>> {
        return Response.success(coreFacades.globalFacade.findAllSystemVar())
    }

    @Operation(summary = "获取系统预置组件模板")
    @GetMapping("/template/components")
    fun findAllComponentTemplates(@RequestParam("appName", required = false) appName: String?): Response<List<Component>> {
        return Response.success(coreFacades.globalFacade.findAllComponentTemplates(appName))
    }

}