package com.alibabacloud.devops.appstack.apps.biz.config

import com.alibabacloud.devops.appstack.apps.biz.api.advice.BizExceptionAdvice
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Timer
import org.apache.commons.logging.LogFactory
import org.springframework.boot.actuate.metrics.web.servlet.WebMvcTags
import org.springframework.web.filter.OncePerRequestFilter
import org.springframework.web.servlet.DispatcherServlet
import javax.servlet.FilterChain
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 * <AUTHOR>
 * @date 2023-06-08
 */
class WebMetricsFilter(
    val registry: MeterRegistry
) : OncePerRequestFilter() {

    private val log = LogFactory.getLog(WebMetricsFilter::class.java)
    private val BIZ_API_METRICS_NAME = "appstack_biz_http_request"

    override fun shouldNotFilterAsyncDispatch(): Boolean {
        return false
    }

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse?,
        filterChain: FilterChain
    ) {
        val timingContext = TimingContext.get(request) ?: startAndAttachTimingContext(request)
        try {
            filterChain.doFilter(request, response)
            if (!request.isAsyncStarted) {
                val errorCode = fetchErrorCode(request)
                record(timingContext, request, response, errorCode)
            }
        } catch (ex: Exception) {
            record(timingContext, request, response, ex.javaClass.simpleName)
            throw ex
        }
    }

    private fun startAndAttachTimingContext(request: HttpServletRequest): TimingContext {
        val timerSample = Timer.start(registry)
        val timingContext = TimingContext(timerSample)
        timingContext.attachTo(request)
        return timingContext
    }

    private fun fetchErrorCode(request: HttpServletRequest): String? {
        var errorCode = (request.getAttribute(BizExceptionAdvice.ERROR_ATTRIBUTE) as ErrorCode?)?.name
        if (errorCode == null) {
            errorCode =
                (request.getAttribute(DispatcherServlet.EXCEPTION_ATTRIBUTE) as Throwable?)?.javaClass?.simpleName
        }
        return errorCode
    }

    private fun record(
        timingContext: TimingContext, request: HttpServletRequest, response: HttpServletResponse?, errorCode: String?
    ) {
        try {
            val timerSample = timingContext.timerSample
            timerSample.stop(getTimer(request, response, errorCode))
        } catch (ex: java.lang.Exception) {
            log.warn("Failed to record timer metrics", ex)
        }
    }

    private fun getTimer(
        request: HttpServletRequest, response: HttpServletResponse?, errorCode: String?
    ) = Timer.builder(BIZ_API_METRICS_NAME)
        .tag("method", request.method)
        .tag("uri", WebMvcTags.uri(request, response, true).value)
        .tag("errorCode", errorCode ?: "None")
        .tag("status", if (response != null) response.status.toString() else "UNKNOWN")
        .register(registry)
}

private class TimingContext(val timerSample: Timer.Sample) {

    fun attachTo(request: HttpServletRequest) {
        request.setAttribute(ATTRIBUTE, this)
    }

    companion object {
        private val ATTRIBUTE = TimingContext::class.java.name

        fun get(request: HttpServletRequest): TimingContext? {
            return request.getAttribute(ATTRIBUTE) as TimingContext?
        }
    }
}