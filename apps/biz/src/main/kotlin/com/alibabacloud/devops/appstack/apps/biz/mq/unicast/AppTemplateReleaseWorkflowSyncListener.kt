package com.alibabacloud.devops.appstack.apps.biz.mq.unicast

import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateService
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestWorkflowBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.filter.TraceFilter
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.config.IamContext
import com.alibabacloud.devops.appstack.libs.lock.spring.boot.starter.DistributedLockService
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfigSyncStatus
import com.alibabacloud.devops.appstack.libs.model.`do`.apptemplate.AppTemplateSyncProgress
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ga.AppTemplateWorkflowRevision
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AppTemplateReleaseWorkflowSyncAudit
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.ga.toRequest
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.BroadcastSender
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.UnicastListener
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.AppTemplateReleaseWorkflowSyncBody
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.AppTemplateSyncProgressBody
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.BroadcastTagEnum
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.UnicastTagEnum
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.alibabacloud.devops.iam.exception.IamForbiddenException
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.apache.commons.lang3.StringUtils
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2024-02-17
 */
@Slf4k
@Service
class AppTemplateReleaseWorkflowSyncListener : UnicastListener {

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var appTemplateService: AppTemplateService

    @Autowired
    lateinit var i18nMessageService: I18nMessageService

    @Autowired
    lateinit var distributedLockService: DistributedLockService

    @Autowired
    lateinit var broadcastSender: BroadcastSender

    @Autowired
    lateinit var changeRequestWorkflowBizService: ChangeRequestWorkflowBizService

    @Value("\${devops.appstack.unicast.groupId}")
    lateinit var groupId: String

    @Value("\${devops.appstack.unicast.topic}")
    lateinit var topic: String

    @Autowired
    lateinit var auditLogService: AuditLogService

    override fun expectedGroupId(): String {
        return groupId
    }

    override fun expectedTopic(): String {
        return topic
    }

    override fun expectedTag(): UnicastTagEnum {
        return UnicastTagEnum.APPTEMPLATE_RELEASE_WORKFLOW_SYNC
    }

    override fun handle(body: ByteArray) {
        logger.info("Receive AppTemplateReleaseWorkflowSync content: ${String(body)}")
        val content = jacksonObjectMapper().readValue(body, AppTemplateReleaseWorkflowSyncBody::class.java)
        AuthThreadContext.setTenant(content.tenant)
        AuthThreadContext.setUserId(content.operator)
        AuthThreadContext.setTraceId(content.traceId)
        AuthThreadContext.setUserIP(content.ip)
        AuthThreadContext.setLocale(content.locale)
        MDC.put(TraceFilter.TRACE_CONTEXT_KEY, content.traceId)
        IamContext.setTenant(content.tenant)
        IamContext.setOperator(content.operator)
        // 1. 锁: 根据处理任务的多少，动态调整锁的时长
        var expirationMillSeconds = content.appNames.size * 1000L
        if (expirationMillSeconds < 60 * 1000) {
            expirationMillSeconds = 60 * 1000
        }
        val releaseKey = distributedLockService.tryLockWithExpiration(
            method = "AppTemplateReleaseWorkflowSyncListener",
            param = content.transactionId,
            expirationMillSeconds = expirationMillSeconds
        )
        if (StringUtils.isBlank(releaseKey)) {
            logger.info("Handle message for AppTemplateReleaseWorkflowSync skipped, lock fail")
            throw Exception("Lock fail, not handle")
        }
        val appTemplate = appTemplateService.find(content.appTemplateName)

        // 2. 遍历同步（先检查、再同步）
        val templateReleaseWorkflows =
            workflowControllerFacade.appTemplateWorkflowFacade.findByName(content.appTemplateName, content.refSn, null)
        content.appNames.forEachIndexed { index, appName ->
            try {
                val errorMessage =
                    syncAppReleaseWorkflow(
                        content.appTemplateName,
                        templateReleaseWorkflows,
                        appName,
                        content.commitMsg
                    )
                broadcastSender.send(
                    BroadcastTagEnum.APP_TEMPLATE_SYNC_PROGRESS,
                    jacksonObjectMapper().writeValueAsBytes(
                        AppTemplateSyncProgressBody(
                            transactionId = content.transactionId,
                            progress = AppTemplateSyncProgress(
                                totalCount = content.appNames.size,
                                currentIndex = index+1,
                                appSyncResult = AppTemplateSyncProgress.AppSyncResult(
                                    appName = appName,
                                    success = StringUtils.isBlank(errorMessage),
                                    errorMessage = errorMessage
                                )
                            )
                        )
                    )
                )
                auditLogService.log(OrgEventType.APP_TEMPLATE_SYNC_RELEASE_WORKFLOW_HOOK, AppTemplateReleaseWorkflowSyncAudit(
                    target = AuditTarget(id = content.appTemplateName, name = appTemplate.displayName),
                    appTemplateWorkflowName = templateReleaseWorkflows.content.name,
                    appName = appName,
                    success = StringUtils.isBlank(errorMessage)
                ))
                auditLogService.log(OrgEventType.APP_TEMPLATE_SYNC_RELEASE_WORKFLOW_AUDIT, AppTemplateReleaseWorkflowSyncAudit(
                    target = AuditTarget(id = content.appTemplateName, name = appTemplate.displayName),
                    appTemplateWorkflowName = templateReleaseWorkflows.content.name,
                    appName = appName,
                    success = StringUtils.isBlank(errorMessage)
                ))
            } catch (e: Exception) {
                logger.error(
                    "Loop sync app release workflow fail for appName[$appName], templateName[${content.appTemplateName}]",
                    e
                )
            }
        }
        // 3. 释放锁
        distributedLockService.releaseLock(releaseKey!!)
    }

    /**
     * @return 如果是null，说明同步成功/无需同步；如果不是null，则是异常信息
     */
    private fun syncAppReleaseWorkflow(
        templateName: String,
        appTemplateWorkflowRevision: AppTemplateWorkflowRevision,
        appName: String,
        commitMsg: String,
    ): String? {
        val configSyncStatus = appTemplateService.findConfigSyncStatus(
            templateName,
            AppTemplateConfig.TypeEnum.ReleaseWorkflow,
            appTemplateWorkflowRevision.content.name,
            appName
        )
        // 如果缺失 AppTemplateConfigSyncStatus ，则跳过同步
        // AppTemplateConfigSyncStatus 删除的时机是应用被删除了，如果缺失意味着异步处理过程中应用被删除了
        if (configSyncStatus == null) {
            logger.error("AppReleaseWorkflow sync skip, missing AppTemplateConfigSyncStatus. AppName[$appName], templatename[$templateName], workflowName[${appTemplateWorkflowRevision.content.name}]")
            return null
        }

        val templateSyncStatus = AppTemplateConfigSyncStatus.TemplateSyncStatus()
        templateSyncStatus.lastAppliedTemplateRevisionSha =
            configSyncStatus.templateSyncStatus?.lastAppliedTemplateRevisionSha
        templateSyncStatus.lastAppliedAppRevisionSha = configSyncStatus.templateSyncStatus?.lastAppliedAppRevisionSha
        try {
            val appWorkflowSn = changeRequestWorkflowBizService.findAllBrief(appName)
                .firstOrNull { it.name == appTemplateWorkflowRevision.content.name }?.sn
            val appRevisionSha = if (null == appWorkflowSn) {
                null
            } else {
                changeRequestWorkflowBizService.findWithRevision(appName, appWorkflowSn)?.revision?.sha
            }

            val templateSynchronizedRevisionEqual =
                appTemplateWorkflowRevision.revision.sha == configSyncStatus.templateSyncStatus?.lastAppliedTemplateRevisionSha
            val appSynchronizedRevisionEqual =
                appRevisionSha == configSyncStatus.templateSyncStatus?.lastAppliedAppRevisionSha
            if (appRevisionSha != null && templateSynchronizedRevisionEqual && appSynchronizedRevisionEqual) {
                logger.info("AppReleaseWorkflow sync skip, already synchronized. AppName[$appName], templatename[$templateName], workflowName[${appTemplateWorkflowRevision.content.name}]")
                return null
            }

            // 应用如果还这个研发流程，先初始化
            val request =
                appTemplateWorkflowRevision.content.toRequest(commitMsg, appTemplateWorkflowRevision.revision.sha)
            val appRevision = if (null == appWorkflowSn) {
                logger.info(
                    "syncAppReleaseWorkflow workflowBaseService.create appName: $appName request:${
                        jacksonObjectMapper().writeValueAsString(
                            request
                        )
                    }"
                )
                changeRequestWorkflowBizService.create(appName, request)
            } else {
                logger.info(
                    "syncAppReleaseWorkflow workflowBaseService.update appName: $appName appWorkflowSn:$appWorkflowSn request:${
                        jacksonObjectMapper().writeValueAsString(
                            request
                        )
                    }"
                )
                changeRequestWorkflowBizService.update(appName, appWorkflowSn, request)
            }

            templateSyncStatus.lastAppliedTemplateRevisionSha = appTemplateWorkflowRevision.revision.sha
            templateSyncStatus.lastAppliedAppRevisionSha = appRevision.revision.sha
        } catch (e: Exception) {
            logger.error(
                "AppReleaseWorkflow sync failed. AppName[$appName], templateName[$templateName], workflowName[${appTemplateWorkflowRevision.content.name}]",
                e
            )
            templateSyncStatus.lastSyncErrorMessage = if (e is BizException) {
                // 同步研发阶段过程中出错，返回详情
                if (e.errorEntry.code == ErrorCode.AS_RELEASE_WORKFLOW_UPSERT_WITH_TEMPLATE_FAIELD) {
                    i18nMessageService.getErrorAdvice(e)
                } else {
                    i18nMessageService.getErrorMessage(e)
                }
            } else if (e is IamForbiddenException) {
                i18nMessageService.getErrorMessage(BizException(errorCode = ErrorCode.AS_PERMISSION_DENIED))
            } else {
                i18nMessageService.getErrorMessage(BizException(errorCode = ErrorCode.AS_UNKNOWN))
            }
        } finally {
            appTemplateService.updateTemplateSyncStatus(
                templateName,
                AppTemplateConfig.TypeEnum.ReleaseWorkflow,
                appTemplateWorkflowRevision.content.name,
                appName,
                templateSyncStatus
            )
        }
        return templateSyncStatus.lastSyncErrorMessage
    }


}