package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestWorkflowBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowBriefVO
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowRevision
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.CreateReleaseWorkflowBizRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.SortReleaseWorkflowRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.UpdateReleaseWorkflowRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @date 2022-06-26
 */
@Tag(name = "ReleaseWorkflow", description = "研发流程相关 API")
@RestController
@RequestMapping("/api/v1")
open class ChangeRequestWorkflowBizApi {

    @Autowired
    lateinit var changeRequestWorkflowBizService: ChangeRequestWorkflowBizService

    @Operation(summary = "创建研发流程")
    @PostMapping("/apps/{appName}/releaseWorkflows")
    fun createReleaseWorkflow(
        @PathVariable appName: String,
        @RequestBody request: CreateReleaseWorkflowBizRequest
    ): Response<ReleaseWorkflow> {
        return Response.success(changeRequestWorkflowBizService.create(appName = appName, request = request))
    }

    @Operation(summary = "查找所有研发流程")
    @GetMapping("/apps/{appName}/releaseWorkflows")
    fun findAllReleaseWorkflow(
        @PathVariable appName: String,
        @RequestParam(required = false) type: ReleaseWorkflow.TypeEnum?,
    ): Response<List<ReleaseWorkflow>> {
        return Response.success(changeRequestWorkflowBizService.findAll(appName))
    }

    @Operation(summary = "查找所有研发流程-不带阶段信息")
    @GetMapping("/apps/{appName}/releaseWorkflows:withoutStages")
    fun findAllReleaseWorkflowBrief(
        @PathVariable appName: String,
        @RequestParam(required = false) type: ReleaseWorkflow.TypeEnum?,
    ): Response<List<ReleaseWorkflowBriefVO>> {
        return Response.success(changeRequestWorkflowBizService.findAllBrief(appName))
    }

    @Operation(summary = "查找研发流程")
    @GetMapping("/apps/{appName}/releaseWorkflows/{sn}")
    fun findReleaseWorkflow(
        @PathVariable appName: String, @PathVariable sn: String
    ): Response<ReleaseWorkflow> {
        return Response.success(changeRequestWorkflowBizService.find(appName = appName, sn = sn))
    }

    @Operation(summary = "查看研发流程版本")
    @GetMapping("/apps/{appName}/releaseWorkflows/{sn}:revision")
    fun getReleaseWorkflowRevision(
        @PathVariable appName: String, @PathVariable sn: String
    ): Response<ReleaseWorkflowRevision?> {
        return Response.success(changeRequestWorkflowBizService.findWithRevision(appName, sn))
    }

    @Operation(summary = "查看研发流程版本内容")
    @GetMapping("/apps/{appName}/releaseWorkflows/{sn}/revisions/{sha}")
    fun getReleaseWorkflowYamlBySha(
        @PathVariable appName: String, @PathVariable sn: String, @PathVariable sha: String
    ): Response<String> {
        return Response.success(changeRequestWorkflowBizService.findYamlBySha(appName, sn, sha))
    }

    @Operation(summary = "分页查找研发流程版本列表")
    @GetMapping("/apps/{appName}/releaseWorkflows/{sn}/revisions")
    fun findReleaseWorkflowRevisionPaginated(
        @PathVariable appName: String, @PathVariable sn: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<Revision>> {
        return Response.success(changeRequestWorkflowBizService.listRevisionPaginated(appName, sn, current, pageSize))
    }

    @Operation(summary = "研发流程版本比较")
    @GetMapping("/apps/{appName}/releaseWorkflows/{sn}/compare")
    fun compareReleaseWorkflowRevision(
        @PathVariable appName: String, @PathVariable sn: String,
        @RequestParam beforeRevisionSha: String,
        @RequestParam afterRevisionSha: String,
    ): Response<Triple<String, String, List<DiffItem<String>>>> {
        return Response.success(changeRequestWorkflowBizService.compare(appName, sn, beforeRevisionSha, afterRevisionSha))
    }


    @Operation(summary = "修改研发流程")
    @PutMapping("/apps/{appName}/releaseWorkflows/{sn}")
    fun updateReleaseWorkflow(
        @PathVariable appName: String, @PathVariable sn: String,
        @RequestBody updateReleaseWorkflowRequest: UpdateReleaseWorkflowRequest
    ): Response<ReleaseWorkflow> {
        return Response.success(
            changeRequestWorkflowBizService.update(
                appName = appName,
                sn = sn,
                updateReleaseWorkflowRequest = updateReleaseWorkflowRequest
            )
        )
    }

    @Operation(summary = "删除研发流程")
    @DeleteMapping("/apps/{appName}/releaseWorkflows/{sn}")
    fun deleteReleaseWorkflow(
        @PathVariable appName: String, @PathVariable sn: String
    ): Response<Unit> {
        changeRequestWorkflowBizService.delete(appName = appName, sn = sn)
        return Response.success()
    }

    @Operation(summary = "调整研发流程顺序")
    @PutMapping("/apps/{appName}/releaseWorkflows:order")
    fun sort(
        @PathVariable appName: String,
        @RequestBody request: SortReleaseWorkflowRequest
    ): Response<List<ReleaseWorkflow>> {
        return Response.success(changeRequestWorkflowBizService.sort(appName = appName, request = request))
    }

}