package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibaba.yunxiao.framework.config.source.PropertySourceParser
import com.alibabacloud.devops.appstack.apps.biz.model.RunCommand
import com.alibabacloud.devops.appstack.apps.biz.model.po.AgentPO
import com.alibabacloud.devops.appstack.apps.biz.service.client.api.ConfigCenterApi
import com.alibabacloud.devops.appstack.apps.biz.service.client.api.ConfigCenterQuery
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.model.response.FlowApiResponse
import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import java.util.*

/**
 * <AUTHOR> <EMAIL>
 * @version : AgentServiceVpcImpl, v0.1
 * @date : 2022-09-27 17:47
 **/
@Service
@ConditionalOnProperty(name = ["devops.appstack.environment"], havingValue = "vpc", matchIfMissing = true)
open class AgentServiceVpcImpl : AgentServiceImpl() {

    @Value("\${devops.appstack.domain_with_schema}")
    lateinit var domainWithSchema: String

    @Value("\${devops.appstack.oam.image.repository}")
    lateinit var oamImageRepository: String

    @Value("\${devops.appstack.oam.image.tag}")
    lateinit var oamImageTag: String

    @Value("\${devops.appstack.oam.image.pullSecretName}")
    lateinit var oamImagePullSecretName: String

    @Value("\${devops.appstack.kruise.image.repository}")
    lateinit var kruiseImageRepository: String

    @Value("\${devops.appstack.kruise.image.tag}")
    lateinit var kruiseImageTag: String

    @Value("\${tb.sso.appId}")
    lateinit var appId: String

    @Value("\${tb.sso.appSecret}")
    lateinit var appSecret: String

    @Value("\${service.flow.build-node-group}")
    lateinit var buildNodeGroup: String

    @Autowired
    lateinit var configCenterApi: ConfigCenterApi

    companion object {
        fun genAppToken(appId: String, appSecret: String): String? {
            if (StringUtils.isEmpty(appId) || StringUtils.isEmpty(
                    appSecret
                )
            ) {
                return null
            }
            val algorithm = Algorithm.HMAC256(appSecret)
            val timestamp = System.currentTimeMillis()
            val issuedAt = Date(timestamp)
            val expiresAt = Date(timestamp + 1 * 3600 * 1000L)
            return JWT.create()
                .withClaim("appId", appId)
                .withIssuedAt(issuedAt)
                .withExpiresAt(expiresAt)
                .sign(algorithm)
        }
    }

    override fun callFlowExecuteJob(
        agent: AgentPO,
        configFile: String,
        timestamp: String,
        sign: String,
    ): FlowApiResponse<Long> {
        fetchOamValues()
        val agentInstallScriptUrl = agent.installScript!!.replace("APPSTACK_DOMAIN_WITH_SCHEMA", domainWithSchema)
        return flowApi.executeJob(
            genFlowHeaders(),
            covertCommandToParams(
                RunCommand(
                    "curl -k -L -o- $agentInstallScriptUrl | bash",
                    mapOf(
                        "KUBE_CONFIG" to configFile,
                        "APPSTACK_DOMAIN_WITH_SCHEMA" to domainWithSchema,
                        "OAM_IMAGE_REPOSITORY" to oamImageRepository,
                        "OAM_IMAGE_TAG" to oamImageTag,
                        "OAM_IMAGE_PULL_SECRET_NAME" to oamImagePullSecretName,
                        "KRUISE_IMAGE_REPOSITORY" to kruiseImageRepository,
                        "KRUISE_IMAGE_TAG" to kruiseImageTag,
                    )
                )
            ),
            "BUILD_ENGINE",
            generateCallBackUrl(),
            FLOW_CALLER,
            timestamp, sign
        )
    }

    override fun covertCommandToParams(command: RunCommand): String {
        val step = HashMap<String, String>()
        step.putAll(command.environments)
        step.putAll(
            mapOf(
                "stepType" to flowExecutionConfiguration.execStep,
                "stepIdentifier" to "whatever",
                "command" to command.command
            )
        )
        return jacksonObjectMapper().writeValueAsString(mapOf("steps" to listOf(step), "buildNodeGroup" to buildNodeGroup))
    }

    private fun fetchOamValues() {
        try {
            val content = configCenterApi.listConfigs(
                mapOf(
                    "Authorization" to "Bearer " + genAppToken(appId, appSecret)
                ),
                ConfigCenterQuery(appId)
            )
            logger.info("Fetch configs from config-center: $content")
            val map = PropertySourceParser.parser(content)
            logger.info("Parse configs from config-center: $map")
            map["devops.appstack.oam.image.repository"]?.let {
                logger.info("Replace oamImageRepository with $oamImageRepository")
                oamImageRepository = map["devops.appstack.oam.image.repository"] as String
            }
            map["devops.appstack.oam.image.tag"]?.let {
                logger.info("Replace oamImageTag with $oamImageTag")
                oamImageTag = map["devops.appstack.oam.image.tag"] as String
            }
            map["devops.appstack.oam.image.pullSecretName"]?.let {
                logger.info("Replace oamImagePullSecretName with $oamImagePullSecretName")
                oamImagePullSecretName = map["devops.appstack.oam.image.pullSecretName"] as String
            }
            map["devops.appstack.kruise.image.repository"]?.let {
                logger.info("Replace kruiseImageRepository with $kruiseImageRepository")
                kruiseImageRepository = map["devops.appstack.kruise.image.repository"] as String
            }
            map["devops.appstack.kruise.image.tag"]?.let {
                logger.info("Replace kruiseImageTag with $kruiseImageTag")
                kruiseImageTag = map["devops.appstack.kruise.image.tag"] as String
            }
        } catch (e: Exception) {
            logger.error("fetch configs from config-center error", e)
        }
    }

}