package com.alibabacloud.devops.appstack.apps.biz.service.impl.handler.deploy

import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderInputPatchValueHandler
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderInput
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppBuiltInOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Placeholder
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.RevisionVariableGroup
import org.springframework.stereotype.Service

/**
 * 提取编排中的常量
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Service
class OrchestrationChangeOrderInputPatchValueHandlerForDeploy : ChangeOrderInputPatchValueHandler {

    override fun handle(
        env: Env,
        orchestration: AppOrchestration?,
        profiles: List<RevisionVariableGroup>,
        stableChangeOrderInput: ChangeOrderInput?,
        customPatchValues: Map<String, String>,
        result: MutableMap<String, String>
    ) {
        orchestration?.let {
            orchestration as AppBuiltInOrchestration
            orchestration.placeholderList.filter { it.valueSource == Placeholder.ValueSource.CONSTANT }.forEach {
                result[it.name] = it.value
            }
        }
    }

    override fun suitableDeployType() = listOf(ChangeOrder.Type.Deploy)

    override fun order() = 0
}