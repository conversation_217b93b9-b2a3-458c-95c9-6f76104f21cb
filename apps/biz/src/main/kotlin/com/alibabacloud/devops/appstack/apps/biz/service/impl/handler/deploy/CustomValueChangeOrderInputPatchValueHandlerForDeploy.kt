package com.alibabacloud.devops.appstack.apps.biz.service.impl.handler.deploy

import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderInputPatchValueHandler
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderInput
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.RevisionVariableGroup
import org.springframework.stereotype.Service

/**
 * 用户指定的值，覆盖一切
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Service
class CustomValueChangeOrderInputPatchValueHandlerForDeploy : ChangeOrderInputPatchValueHandler {

    override fun handle(
        env: Env,
        orchestration: AppOrchestration?,
        profiles: List<RevisionVariableGroup>,
        stableChangeOrderInput: ChangeOrderInput?,
        customPatchValues: Map<String, String>,
        result: MutableMap<String, String>
    ) {
        customPatchValues.forEach {
            result[it.key] = it.value
        }
    }

    override fun suitableDeployType() = listOf(ChangeOrder.Type.Deploy)

    override fun order() = 4
}