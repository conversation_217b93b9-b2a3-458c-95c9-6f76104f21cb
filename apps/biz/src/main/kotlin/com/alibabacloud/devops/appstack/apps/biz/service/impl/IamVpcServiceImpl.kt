package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibaba.aone.framework.tbs.app.sdk.group.GroupService
import com.alibaba.aone.framework.tbs.app.sdk.model.Group
import com.alibaba.aone.framework.tbs.app.sdk.model.GroupMember
import com.alibaba.aone.framework.tbs.app.sdk.model.Member
import com.alibaba.aone.framework.tbs.app.sdk.model.Team
import com.alibaba.aone.framework.tbs.app.sdk.org.OrgService
import com.alibaba.aone.framework.tbs.app.sdk.permission.PermissionService
import com.alibaba.aone.framework.tbs.app.sdk.role.RoleService
import com.alibaba.aone.framework.tbs.app.sdk.team.TeamService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.config.IamContext
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.MemberRole
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.RolePolicy
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.PolicyOption
import com.alibabacloud.devops.iam.model.ResourcePlayer
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.request.CanRequest
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import feign.FeignException
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import org.springframework.util.StringUtils

/**
 * <AUTHOR> <EMAIL>
 * @version : IamVpcServiceImpl, v0.1
 * @date : 2022-08-18 14:52
 **/
@Service
@ConditionalOnProperty(name = ["devops.iam.environment"], havingValue = "vpc", matchIfMissing = false)
class IamVpcServiceImpl : IamServiceImpl() {

    // 根据情况是否开启
    private val enableGroupAndTeamCheck = true

    override fun findPlayerRoles(
        protocolType: ProtocolType,
        resourceName: String,
    ): Map<ResourcePlayer, MutableList<Role>> {
        val playerRoles = iamClient.resourceApi.find(protocolType, resourceName).convertToPlayerRolesMap()
//        val userIdList = playerRoles.keys.mapNotNull { if (it.type == SubjectType.User) it.id else null }
        val groupIdList = playerRoles.keys.mapNotNull { if (it.type == SubjectType.Group) it.id else null }
        val teamIdList = playerRoles.keys.mapNotNull { if (it.type == SubjectType.Team) it.id else null }
//        val memberMap = batchGetOrganizationMembers(userIdList).associateBy { it.userId }
        val groupMap = batchGetGroups(groupIdList).associateBy { it.id }
        val teamMap = batchGetTeams(teamIdList).associateBy { it.id }
        playerRoles.keys.forEach {
            when(it.type) {
//                SubjectType.User -> {
//                    val member = memberMap[it.id]
//                    it.displayName = member?.name ?: ""
//                    it.description = member?.userInfo?.name ?: ""
//                    it.avatar = member?.userInfo?.avatarUrl ?: ""
//                }
                SubjectType.Group -> {
                    val group = groupMap[it.id]
                    it.displayName = group?.name ?: ""
                    it.description = group?.description ?: ""
                }
                SubjectType.Team -> {
                    val team = teamMap[it.id]
                    it.displayName = team?.name ?: ""
                }
                else -> {}
            }
        }
        return playerRoles
    }

    override fun countResource(
        protocolType: ProtocolType,
        subjectId: String,
        subjectType: SubjectType,
        search: String?
    ): Int {
        return if (subjectType == SubjectType.User) {
            val players = allPlayersByUser(userId = subjectId)
            iamClient.resourceApi.countBySubjects(protocolType, search, players)
        } else {
            super.countResource(protocolType, subjectId, subjectType, search)
        }
    }

    override fun searchResourceName(
        protocolType: ProtocolType,
        subjectId: String,
        subjectType: SubjectType,
        search: String?,
        page: Int,
        size: Int,
    ): List<String> {
        return if (subjectType == SubjectType.User) {
            val players = allPlayersByUser(userId = subjectId)
            iamClient.resourceApi.searchPaginated(protocolType, search, page, size, players)
        } else {
            super.searchResourceName(protocolType, subjectId, subjectType, search, page, size)
        }
    }

    override fun can(protocolType: ProtocolType, resourceName: String, action: Action): Boolean {
        return if (protocolType == ProtocolType.AppStack) {
            PermissionService.checkOrganizationPermission(
                AuthUtil.getTenant(),
                AuthUtil.getUserId(),
                action.v5code
            ).success
        } else {
            val userPermissionCheck = super.can(protocolType, resourceName, action)
            return if (userPermissionCheck) {
                true
            } else if (enableGroupAndTeamCheck) {
                checkPermissionByGroupsAndTeams(protocolType, resourceName, action)
            } else false
        }
    }

    override fun can(
        protocolType: ProtocolType,
        resourceName: String,
        action: Action,
        abacProtocolType: ProtocolType,
        canRequest: CanRequest,
    ): Boolean {
        val userPermissionCheck = iamClient.resourceApi.can(
            protocolType,
            resourceName,
            action.code,
            canRequest,
            abacProtocolType,
            SubjectType.User,
            IamContext.getOperator()
        )
        return if (userPermissionCheck) {
            true
        } else if (enableGroupAndTeamCheck) {
            checkPermissionByGroupsAndTeams(protocolType, resourceName, action, canRequest, abacProtocolType)
        } else {
            false
        }
    }

    override fun fetchRolePermission(protocolType: ProtocolType, filter: List<Action>): List<RolePolicy> {
        val codes = filter.map { it.v5code }
        return if (protocolType == ProtocolType.AppStack) {
            val orgRoleList = RoleService.getRoleList(AuthUtil.getTenant(), true)
            orgRoleList.map { orgRole ->
                RolePolicy(
                    role = Role().apply {
                        protocol = ProtocolType.AppStack.toString()
                        name = orgRole.id
                    },
                    policyOptions = orgRole.permissions.filter {
                        codes.contains(it)
                    }.map { permission ->
                        val action = Action.v5CodeOf(permission)
                        PolicyOption().apply {
                            protocol = ProtocolType.AppStack.toString()
                            name = action.code
                            groupName = action.groupName
                            displayName = action.displayName

                        }
                    }
                )
            }
        } else {
            super.fetchRolePermission(protocolType, filter)
        }
    }

    override fun fetchOrgMembers(): List<MemberRole> {
        return getAllMember(orgId = AuthUtil.getTenant()).filter { !it.isDisabled }
            .map { orgMember ->
                var roleList = orgMember.rolesInfo.map { roleInfo ->
                    Role().apply {
                        protocol = ProtocolType.AppStack.toString()
                        name = roleInfo.id
                        displayName = roleInfo.name
                    }
                }
                if (roleList.isEmpty()) {
                    // 冗余处理，防止有脏数据
                    roleList = listOf(Role().apply {
                        protocol = ProtocolType.AppStack.toString()
                        name = orgMember.roleId
                        displayName = orgMember.roleId
                    })
                }
                MemberRole(
                    id = orgMember.userId,
                    type = SubjectType.User,
                    displayName = orgMember.name,
                    roleList = roleList
                )
            }
    }

    override fun checkPlayersExist(players: List<ResourcePlayerRequest>): Boolean {
        val userIdList = players.mapNotNull { if (it.type == SubjectType.User) it.id else null }
        val groupIdList = players.mapNotNull { if (it.type == SubjectType.Group) it.id else null }
        val teamIdList = players.mapNotNull { if (it.type == SubjectType.Team) it.id else null }
        return try {
            batchGetOrganizationMembers(userIdList).associateBy { it.userId }
            batchGetGroups(groupIdList).associateBy { it.id }
            batchGetTeams(teamIdList).associateBy { it.id }
            true
        } catch (e: Throwable) {
            logger.error("failed to checkPlayersExist: players: $players", e)
            false
        }
    }

    override fun checkUserExist(userId: String) {
        // 校验用户存在性
        try {
            // 校验企业下是否有该成员
            OrgService.getMemberByUserId(AuthUtil.getTenant(), userId) ?: throw BizException(ErrorCode.AS_BASE_USER_NOT_FOUND)
        } catch (e: FeignException) {
            logger.error("create app failed with FeignException: msg=${e.message ?: "null"}", e)
            throw if (e.status() == 404 || e.status() == 400) {
                BizException(ErrorCode.AS_BASE_USER_NOT_FOUND)
            } else {
                e
            }
        } catch (e: Throwable) {
            logger.error("create app failed: msg=${e.message ?: "null"}", e)
            throw BizException(ErrorCode.AS_BASE_USER_NOT_FOUND)
        }
    }

    override fun getGroupPlayer(orgId: String, userId: String): List<ResourcePlayerRequest> {
        return GroupService.listJoinedGroups(
            AuthUtil.getTenant(),
            AuthUtil.getUserId(),
            null,
            null,
            null
        ).result.map { ResourcePlayerRequest(it.id, SubjectType.Group) }
    }

    override fun getTeamPlayer(orgId: String, userId: String, containSuperior: Boolean): List<ResourcePlayerRequest> {
        return OrgService.getMemberByUserId(AuthUtil.getTenant(), AuthUtil.getUserId()).teams.map {
            ResourcePlayerRequest(
                it.id,
                SubjectType.Team
            )
        }
    }

    override fun listUserIdsByTeam(orgId: String, teamId: String): List<String> {
        val list = mutableListOf<Member>()
        var nextToken: String? = ""
        do {
            val result = TeamService.getTeamMembers(orgId, teamId, nextToken, 100, true)
            nextToken = result.nextPageToken
            list.addAll(result.result)
        } while (StringUtils.hasText(nextToken))
        return list.map { it.userId }
    }

    override fun listUserIdsByGroup(orgId: String, groupId: String): List<String> {
        val list = mutableListOf<GroupMember>()
        var nextToken: String? = ""
        do {
            val result = GroupService.listGroupMembers(orgId, groupId, null, nextToken, 100)
            nextToken = result.nextPageToken
            list.addAll(result.result)
        } while (StringUtils.hasText(nextToken))
        return list.map { it.userId }
    }

    private fun getAllMember(orgId: String): List<Member> {
        val list = mutableListOf<Member>()
        var nextToken: String? = ""
        do {
            val response = OrgService.getOrganizationMembers(orgId, nextToken, 100, null, null, false, false)
            list.addAll(response.result)
            nextToken = response.nextPageToken
        } while (StringUtils.hasText(nextToken))
        return list
    }

    private fun batchGetOrganizationMembers(userIdList: List<String>): List<Member> {
        // 底座接口限制，一次只能查100个人
        return userIdList.chunked(100).flatMap {
            OrgService.batchGetOrganizationMembers(AuthUtil.getTenant(), it, false, false).result
        }
    }

    private fun batchGetTeams(teamsIdList: List<String>): List<Team> {
        return teamsIdList.map {
            // fixme 改成批量接口，需要底座支持
            TeamService.getTeamInfo(AuthUtil.getTenant(), it)
        }
    }

    private fun batchGetGroups(groupsIdList: List<String>): List<Group> {
        return groupsIdList.chunked(100).flatMap {
            GroupService.listGroups(AuthUtil.getTenant(), it).result
        }
    }

    private fun allPlayersByUser(userId: String): List<ResourcePlayerRequest> {
        // find groups
        val userJoinedGroups =
            GroupService.listJoinedGroups(AuthUtil.getTenant(), userId, null, null, null).result
        val groupIds = userJoinedGroups.map { ResourcePlayerRequest(it.id, SubjectType.Group) }
        // find teams
        val teamIds = OrgService.getMemberByUserId(AuthUtil.getTenant(), userId).teams.map { it.id }.flatMap { teamId ->
            // 查询部门树/层级
            TeamService.getTeamPath(IamContext.getTenant(), teamId).map { it.id } + teamId
        }.toSet()
        val teamPlayers = teamIds.map { ResourcePlayerRequest(it, SubjectType.Team) }
        return listOf(ResourcePlayerRequest(userId, SubjectType.User)) + groupIds + teamPlayers
    }

    private fun findResourcePlayers(protocolType: ProtocolType, resourceName: String): Pair<Set<String>, Set<String>> {
        val resource = iamClient.resourceApi.find(protocolType, resourceName)
        val resourcePlayers = resource.rolePlayerList.map { it.playerList }.reduce { acc, list -> acc + list }
        val groupPlayerIds = resourcePlayers.filter { it.type == SubjectType.Group }.map { it.id }.toSet()
        val teamPlayerIds = resourcePlayers.filter { it.type == SubjectType.Team }.map { it.id }.toSet()
        return Pair(groupPlayerIds, teamPlayerIds)
    }

    private fun checkPermissionByGroupsAndTeams(
        protocolType: ProtocolType,
        resourceName: String,
        action: Action,
    ): Boolean {
        val pair = findResourcePlayers(protocolType, resourceName)

        // check groups
        val groupPlayerIds = pair.first
        if (groupPlayerIds.isNotEmpty()) {
            val userJoinedGroups =
                GroupService.listJoinedGroups(IamContext.getTenant(), IamContext.getOperator(), null, null, null).result
            val groups = userJoinedGroups.map { it.id }.intersect(groupPlayerIds)
            groups.forEach {
                val can = iamClient.resourceApi.can(protocolType, resourceName, action.code, SubjectType.Group, it)
                if (can) return true
            }
        }

        // check teams
        val teamPlayerIds = pair.second
        if (teamPlayerIds.isNotEmpty()) {
            val teamIds = OrgService.getMemberByUserId(IamContext.getTenant(), IamContext.getOperator()).teams.map { it.id }.flatMap { teamId ->
                // 查询部门树/层级
                TeamService.getTeamPath(IamContext.getTenant(), teamId).map { it.id } + teamId
            }.toSet().intersect(teamPlayerIds)
            teamIds.forEach {
                val can = iamClient.resourceApi.can(protocolType, resourceName, action.code, SubjectType.Team, it)
                if (can) return true
            }
        }
        return false
    }


    private fun checkPermissionByGroupsAndTeams(
        protocolType: ProtocolType,
        resourceName: String,
        action: Action,
        canRequest: CanRequest,
        abacProtocolType: ProtocolType,
    ): Boolean {
        val pair = findResourcePlayers(protocolType, resourceName)

        // check groups
        val groupPlayerIds = pair.first
        if (groupPlayerIds.isNotEmpty()) {
            val userJoinedGroups =
                GroupService.listJoinedGroups(IamContext.getTenant(), IamContext.getOperator(), null, null, null).result
            val groups = userJoinedGroups.map { it.id }.intersect(groupPlayerIds)
            groups.forEach {
                val can = iamClient.resourceApi.can(protocolType, resourceName, action.code, canRequest, abacProtocolType, SubjectType.Group, it)
                if (can) return true
            }
        }

        // check teams
        val teamPlayerIds = pair.second
        if (teamPlayerIds.isNotEmpty()) {
            val teamIds = OrgService.getMemberByUserId(IamContext.getTenant(), IamContext.getOperator()).teams.map { it.id }.flatMap { teamId ->
                // 查询部门树/层级
                TeamService.getTeamPath(IamContext.getTenant(), teamId).map { it.id } + teamId
            }.toSet().intersect(teamPlayerIds)
            teamIds.forEach {
                val can = iamClient.resourceApi.can(protocolType, resourceName, action.code, canRequest, abacProtocolType, SubjectType.Team, it)
                if (can) return true
            }
        }
        return false
    }
}