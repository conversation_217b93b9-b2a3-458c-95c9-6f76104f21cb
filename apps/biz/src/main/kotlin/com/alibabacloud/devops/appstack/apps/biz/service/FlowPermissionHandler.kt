package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.constant.FlowPermission
import com.alibabacloud.devops.appstack.libs.model.`do`.app.App
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppType
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageBriefVO
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow

/**
 * <AUTHOR>
 * @create 2023/12/8 11:45 AM
 **/
interface FlowPermissionHandler {

    /**
     * 需要校验的 AppStack 权限点，通过则返回 true
     */
    fun handle(
        app: App,
        stage: ReleaseStageBriefVO
    ): Boolean

    /**
     * 权限点支持的应用类型
     */
    fun suitableAppTypes(): List<AppType>

    /**
     * 权限点支持的研发流程类型
     */
    fun suitableWorkflowTypes(): List<ReleaseWorkflow.TypeEnum>

    /**
     * AppStack 可映射成的 Flow 权限点
     */
    fun suitablePermissions(): List<FlowPermission>

    /**
     * 用于优化性能，待进一步确定规则
     */
    fun order(): Int

}