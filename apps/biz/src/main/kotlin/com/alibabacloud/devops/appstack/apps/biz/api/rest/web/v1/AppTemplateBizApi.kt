package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateBizService
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateService
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.iam.AppRole
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.AddMembersRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateMemberRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.ng.Application
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import io.swagger.v3.oas.annotations.Hidden
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2023-03-02 09:58
 * @version: AppTemplateBizApi, v0.1
 **/
@Tag(name = "AppTemplate", description = "应用模板相关 API")
@RestController
@RequestMapping("/api/v1")
open class AppTemplateBizApi {

    @Autowired
    lateinit var appTemplateBizService: AppTemplateBizService

    @Autowired
    lateinit var appTemplateService: AppTemplateService

    @Operation(summary = "创建应用模板")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "创建应用模板成功"),
            ApiResponse(responseCode = "409", description = "应用模板重名"),
            ApiResponse(responseCode = "422", description = "未指定应用模板名称或者显示名"),
            ApiResponse(responseCode = "500", description = "应用模板创建失败 / 服务端内部异常")
        ]
    )
    @PostMapping("/appTemplates")
    fun createAppTemplate(@RequestBody request: UpsertAppTemplateRequest): Response<AppTemplate> {
        return Response.success(appTemplateBizService.create(request))
    }

    @Operation(summary = "查找应用模板列表")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询应用模板列表成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常")
        ]
    )
    @GetMapping("/appTemplates")
    fun findPaginated(
        @RequestParam("search", required = false, defaultValue = "") search: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
        @RequestParam("type", required = false, defaultValue = "CUSTOMIZE") type: AppTemplate.Type,
    ): Response<Pagination<AppTemplate>> {
        return Response.success(appTemplateBizService.findPaginated(search, current, pageSize, type))
    }

    @Operation(summary = "删除应用模板")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "删除应用模板成功"),
            ApiResponse(responseCode = "404", description = "应用模板未找到"),
            ApiResponse(responseCode = "500", description = "应用模板删除失败 / 服务端内部异常")
        ]
    )
    @DeleteMapping("/appTemplates/{name}")
    fun deleteAppTemplate(@PathVariable name: String): Response<Unit> {
        return Response.success(appTemplateBizService.delete(name))
    }

    @Operation(summary = "复制应用模板")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "创建应用模板成功"),
            ApiResponse(responseCode = "409", description = "应用模板重名"),
            ApiResponse(responseCode = "422", description = "未指定应用模板名称或者显示名"),
            ApiResponse(responseCode = "500", description = "应用模板创建失败 / 服务端内部异常")
        ]
    )
    @PostMapping("/appTemplates/{name}:copy")
    fun copyAppTemplate(
        @PathVariable name: String,
        @RequestBody request: UpsertAppTemplateRequest,
    ): Response<AppTemplate> {
        return Response.success(appTemplateBizService.copy(name, request))
    }

    @Operation(summary = "查询应用模板基本信息")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询应用模板基本信息成功"),
            ApiResponse(responseCode = "404", description = "应用模板未找到"),
            ApiResponse(responseCode = "500", description = "服务端内部异常")
        ]
    )
    @GetMapping("/appTemplates/{name}")
    fun findAppTemplate(@PathVariable name: String): Response<AppTemplate> {
        return Response.success(appTemplateBizService.find(name))
    }

    @Operation(summary = "修改应用模板基本信息")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "修改应用模板成功"),
            ApiResponse(responseCode = "404", description = "应用模板未找到"),
            ApiResponse(responseCode = "409", description = "应用模板重名"),
            ApiResponse(responseCode = "500", description = "应用模板修改失败 / 服务端内部异常")
        ]
    )
    @PutMapping("/appTemplates/{name}")
    fun updateAppTemplate(
        @PathVariable name: String,
        @RequestBody request: UpsertAppTemplateRequest,
    ): Response<AppTemplate> {
        return Response.success(appTemplateBizService.update(name, request))
    }

    @Operation(summary = "查询应用模板关联的应用")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询关联应用成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常")
        ]
    )
    @GetMapping("/appTemplates/{name}:apps")
    fun findPaginatedApps(
        @PathVariable name: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<Application>> {
        return Response.success(appTemplateBizService.findPaginatedApps(name, current, pageSize))
    }

    @PutMapping("/appTemplates/{name}:owner")
    fun transferAppTemplateOwner(
        @PathVariable("name") name: String,
        @RequestBody owner: ResourcePlayerRequest,
    ): Response<Boolean> {
        appTemplateBizService.transferOwner(
            name = name,
            player = owner
        )
        return Response.success(true)
    }

    @GetMapping("/appTemplates/{name}/members")
    fun findAllAppTemplateMember(@PathVariable("name") name: String): Response<List<MemberVO>> {
        val allMemberMap = appTemplateBizService.findAllMember(name)
        val list = allMemberMap.map { MemberVO(it.key, it.value) }.sortedWith { o1, o2 ->
            if (o1.roleList.find { it.name == AppRole.owner.name } != null) -1
            else if (o2.roleList.find { it.name == AppRole.owner.name } != null) 1
            else
                o1.displayName.compareTo(o2.displayName)
        }
        return Response.success(list)
    }

    @Operation(summary = "添加应用模板成员", operationId = "CreateAppTemplateMembers")
    @PostMapping("/appTemplates/{name}/members")
    fun createAppTemplateMembers(
        @PathVariable("name") name: String,
        @RequestBody addMembersRequest: AddMembersRequest,
    ): Response<Boolean> {
        addMembersRequest.roleNames.forEach {
            appTemplateBizService.addRoleMember(
                name = name,
                roleName = it,
                playerList = addMembersRequest.playerList,
            )
        }
        return Response.success(true)
    }

    @Operation(summary = "删除应用模板成员", operationId = "DeleteAppTemplateMember")
    @DeleteMapping("/appTemplates/{name}/members")
    fun deleteAppTemplateMember(
        @PathVariable("name") name: String,
        @RequestParam subjectType: SubjectType,
        @RequestParam subjectId: String,
    ): Response<Boolean> {
        appTemplateBizService.updateMemberRole(
            name = name,
            subjectType = subjectType,
            subjectId = subjectId,
            roleNameList = emptyList()
        )
        return Response.success(true)
    }

    @Operation(summary = "更新应用模板成员", operationId = "UpdateAppTemplateMember")
    @PutMapping("/appTemplates/{name}/members")
    fun updateAppTemplateMember(
        @PathVariable("name") name: String,
        @RequestBody updateMemberRequest: UpdateMemberRequest,
    ): Response<Boolean> {
        appTemplateBizService.updateMemberRole(
            name = name,
            subjectType = updateMemberRequest.player.type,
            subjectId = updateMemberRequest.player.id,
            roleNameList = updateMemberRequest.roleNames
        )
        return Response.success(true)
    }

    @Operation(summary = "查找归属成员的应用模板列表")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询应用模板列表成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常")
        ]
    )
    @GetMapping("/appTemplates:member")
    fun listMemberPaginated(
        @RequestParam("search", required = false, defaultValue = "") search: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<AppTemplate>> {
        return Response.success(appTemplateBizService.listMemberPaginated(search, current, pageSize))
    }

}