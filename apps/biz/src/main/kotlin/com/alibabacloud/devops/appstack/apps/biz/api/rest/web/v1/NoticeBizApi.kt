package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.model.vo.AppStackNotice
import com.alibabacloud.devops.appstack.apps.biz.service.NoticeBizService
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-12-21 15:34
 * @version: NoticeBizApi, v0.1
 **/
@Tag(name = "Notice", description = "通知公告通用API")
@RestController
@RequestMapping("/api/v1/notice")
open class NoticeBizApi {

    @Autowired
    lateinit var noticeBizServiceList: List<NoticeBizService>

    @GetMapping("/findAll")
    fun findAll(
    ): Response<List<AppStackNotice>> {
        val noticeList = noticeBizServiceList.mapNotNull{ it.fetch() }
        return Response.success(noticeList)
    }
}