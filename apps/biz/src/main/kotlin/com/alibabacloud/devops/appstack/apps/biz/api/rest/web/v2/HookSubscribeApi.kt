package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.service.HookSubscribeBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.*
import com.alibabacloud.devops.appstack.libs.model.event.EventResult
import com.alibabacloud.devops.appstack.libs.model.request.PaginationQuery
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.api.annotations.ParameterObject
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 * @create 2024/6/26 11:57 AM
 **/
@Tag(name = "订阅", description = "渠道订阅API")
@RestController
@RequestMapping("/api/v2")
class HookSubscribeApi {

    @Autowired
    lateinit var hookSubscribeBizService: HookSubscribeBizService

    @PostMapping("/hook")
    fun create(
        @RequestBody hookChannelSubscribe: HookChannelSubscribe
    ): HookChannelSubscribe {
        return hookSubscribeBizService.create(hookChannelSubscribe)
    }

    @PostMapping("/hook/{sn}")
    fun update(
        @PathVariable sn: String,
        @RequestBody hookChannelSubscribe: HookChannelSubscribe
    ): HookChannelSubscribe {
        return hookSubscribeBizService.update(sn, hookChannelSubscribe)
    }

    @DeleteMapping("/hook/{sn}")
    fun delete(@PathVariable sn: String): Boolean {
        return hookSubscribeBizService.delete(sn)
    }

    @GetMapping("/hook/{sn}")
    fun get(@PathVariable sn: String): HookChannelSubscribe {
        return hookSubscribeBizService.get(sn)
    }

    @GetMapping("/hook")
    fun findByScopeAndType(
        @RequestParam("scopeType") scopeType: String,
        @RequestParam("scopeId") scopeId: String,
        @RequestParam("channelType") channelType: String
    ): List<HookChannelSubscribe> {
        return hookSubscribeBizService.findByScopeAndType(scopeType, scopeId, channelType)
    }
    @GetMapping("/hook:supportEventTypes")
    fun getSupportEventTypes(
        @RequestParam("channelType") channelType: String,
        @RequestParam("scopeType") scopeType: String
    ): List<HookEventTypeVO> {
        return hookSubscribeBizService.getSupportEventTypes(channelType, scopeType)
    }

    @PostMapping("/hook/{sn}:invoke")
    fun invokeEvent(
        @PathVariable sn: String,
        @RequestBody hookEvent: HookEvent
    ): EventResult {
        return hookSubscribeBizService.invokeEvent(sn, hookEvent)
    }

    @GetMapping("/hook/{sn}/logs")
    fun findInvokeLogsPaginated(
        @PathVariable sn: String,
        @ParameterObject query: PaginationQuery,
        @RequestParam event: String? = null,
        @RequestParam action: String? = null
    ): PageList<HookSendLog> {
        return hookSubscribeBizService.findInvokeLogsPaginated(sn, query, event, action)
    }
}