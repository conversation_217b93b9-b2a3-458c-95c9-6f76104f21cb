package com.alibabacloud.devops.appstack.apps.biz.model.mapper

import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateUsageRelationPO
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import org.apache.ibatis.annotations.Mapper

/**
 * <AUTHOR>
 * @create 2023/10/18 3:09 PM
 **/
@Mapper
interface AppTemplateUsageRelationMapper : BaseMapper<AppTemplateUsageRelationPO> {
}

fun AppTemplateUsageRelationMapper.findPaginatedByAppTemplateName(page: Page<AppTemplateUsageRelationPO>, appTemplateName: String): Page<AppTemplateUsageRelationPO> {
    return selectPage(page, QueryWrapper<AppTemplateUsageRelationPO>().eq("app_template_name", appTemplateName).orderByDesc("gmt_create"))
}

fun AppTemplateUsageRelationMapper.findAllByAppTemplateName(appTemplateName: String): List<AppTemplateUsageRelationPO> {
    return selectList(QueryWrapper<AppTemplateUsageRelationPO>().eq("app_template_name", appTemplateName))
}

fun AppTemplateUsageRelationMapper.findByAppName(appName: String): AppTemplateUsageRelationPO? {
    return selectOne(QueryWrapper<AppTemplateUsageRelationPO>().eq("app_name", appName))
}

fun AppTemplateUsageRelationMapper.findByAppNames(appNames: List<String>): List<AppTemplateUsageRelationPO> {
    if (appNames.isEmpty()) {
        return emptyList()
    }
    return selectList(QueryWrapper<AppTemplateUsageRelationPO>().`in`("app_name", appNames))
}