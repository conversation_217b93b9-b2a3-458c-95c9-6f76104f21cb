package com.alibabacloud.devops.appstack.apps.biz.service.impl.handler.flowPermission

import com.alibabacloud.devops.appstack.apps.biz.service.FlowPermissionHandler
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.model.constant.FlowPermission
import com.alibabacloud.devops.appstack.libs.model.`do`.app.App
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppType
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageBriefVO
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.iam.constant.ProtocolType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @create 2023/12/8 2:44 PM
 **/
@Service
class AppWorkflowManagePermissionHandler : FlowPermissionHandler {

    @Autowired
    lateinit var iamService: IamService

    override fun handle(app: App, stage: ReleaseStageBriefVO): Boolean {
        return iamService.can(ProtocolType.AppStackApp, app.name, Action.APP_WORKFLOW_MANAGE)
    }

    override fun suitableAppTypes() = listOf(AppType.APP)

    override fun suitableWorkflowTypes() = listOf(ReleaseWorkflow.TypeEnum.CR)

    override fun suitablePermissions() = FlowPermission.values().toList()

    override fun order() = 1

}