package com.alibabacloud.devops.appstack.apps.biz.service.impl


import com.alibabacloud.devops.appstack.apps.biz.annotation.ApiMetricCounter
import com.alibabacloud.devops.appstack.apps.biz.config.FlowAuthConfiguration
import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.apps.biz.service.*
import com.alibabacloud.devops.appstack.apps.biz.service.client.api.FlowPipelineApi
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.OnlineGuidanceService
import com.alibabacloud.devops.appstack.libs.change.controller.spring.boot.starter.service.ChangeControllerFacades
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.CommonCode
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.model.ErrorEntry
import com.alibabacloud.devops.appstack.libs.common.util.*
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.constant.SystemVariable
import com.alibabacloud.devops.appstack.libs.model.`do`.OnlyViewAccessableAppV1OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.*
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.Orchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.SyncSourceTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.template.AppTemplateBuildInOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.template.AppTemplateOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.transfer
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.iam.AppRole
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.org.event.Member
import com.alibabacloud.devops.appstack.libs.model.org.event.MemberAudit
import com.alibabacloud.devops.appstack.libs.model.org.event.MemberRole
import com.alibabacloud.devops.appstack.libs.model.org.event.MemberRoleAudit
import com.alibabacloud.devops.appstack.libs.model.request.*
import com.alibabacloud.devops.appstack.libs.model.request.CreateEnvRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppOrchestrationRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.*
import com.alibabacloud.devops.appstack.libs.model.vo.*
import com.alibabacloud.devops.appstack.libs.model.vo.ng.Application
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.exception.IamForbiddenException
import com.alibabacloud.devops.iam.model.ResourcePlayer
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import org.springframework.aop.framework.AopContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.util.*
import kotlin.math.ceil

/**
 * @author: <EMAIL>
 * @date: 2021-12-16 14:00
 * @version: AppBizService, v0.1
 **/
@Service
@Slf4k
open class AppBizServiceImpl : AppBizService {

    @Value("\${service.org.environment}")
    lateinit var environment: String

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var changeControllerFacades: ChangeControllerFacades

    @Autowired
    lateinit var iamService: IamService

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var auditLogService: AuditLogService

    @Autowired
    lateinit var flowV1PipelineApi: FlowPipelineApi

    @Autowired
    lateinit var flowAuthConfig: FlowAuthConfiguration

    @Autowired
    lateinit var onlineGuidanceService: OnlineGuidanceService

    @Autowired
    private lateinit var appTemplateConfigBizService: AppTemplateConfigBizService

    @Autowired
    lateinit var appTemplateService: AppTemplateService

    @Autowired
    private lateinit var envBizService: EnvBizService

    @Autowired
    private lateinit var appOrchestrationBizService: AppOrchestrationBizService

    @Autowired
    private lateinit var changeRequestWorkflowBizService: ChangeRequestWorkflowBizService

    @Autowired
    lateinit var i18nMessageService: I18nMessageService

    @Autowired
    lateinit var permissionService: PermissionService

    @Autowired
    lateinit var orgConfigItemBizService: OrgConfigItemBizService

    @Autowired
    lateinit var appCodeRepoBizService: AppCodeRepoBizService

    @Autowired
    lateinit var appArtifactRepoBizService: AppArtifactRepoBizService

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0)
        ]
    )
    override fun find(name: String): AppBaseInfoVO? {
        val app = coreFacades.appFacade.find(name) ?: return null
        if (app.type != null && app.type == AppType.SYSTEM) {
            throw BizException(ErrorCode.AS_APP_NOT_FOUND)
        }
        val tags = coreFacades.domainTagFacade.listTagsByAppName(name)
        return app.toBaseInfoVO(findFunctionPoints(name), tags)
    }

    override fun findFunctionPoints(name: String): List<FunctionPoint> {
        val findFunctionPoints = coreFacades.appFacade.findFunctionPoints(name) ?: emptyList()
        return findFunctionPoints
    }

    @ApiMetricCounter(metricName = "app_operation", methodTag = "create")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_CREATE),
            Access(action = Action.ORG_APP_MANAGE)
        ]
    )
    override fun create(appReq: AppRequest): App {
        logger.info("biz create app[${appReq.name}]")

        val ownerId = if (appReq.ownerId.isNullOrBlank() || appReq.ownerId.isNullOrEmpty()) {
            logger.info("ownerId is null, use current user instead: userId=${AuthUtil.getUserId()}")
            AuthUtil.getUserId()
        } else {
            appReq.ownerId!!
        }
        // 校验用户存在性
        iamService.checkUserExist(ownerId)

        val app = coreFacades.appFacade.create(appReq)
        iamService.registerResource(ProtocolType.AppStackApp, app.name, AuthUtil.getTenant(), ownerId)

        // 初始化标签
        coreFacades.labelFacade.preset()
        if (!appReq.appTemplateName.isNullOrBlank()) {
            // 带模版初始化
            val appTemplate = appTemplateService.find(appReq.appTemplateName!!)
            // 创建应用与模版的关联关系
            appTemplateService.bindAppTemplateToApp(appTemplate.name, app.name)
            // 基于配置初始化
            val enabledConfig = mutableListOf<AppTemplateConfig.TypeEnum>()
            AppTemplateConfig.TypeEnum.values().forEach {
                try {
                    enabledConfig.add(it)
                    createWithAppTemplateConfig(app, appTemplate.name, it)
                } catch (e: Throwable) {
                    logger.error(
                        "failed to create app[${app.name}] with appTemplateConfig: ${it.name}, msg=${e.message}",
                        e
                    )
                    // 删除应用配置相关
                    enabledConfig.forEach { type -> clearWithAppTemplateConfig(app, appTemplate.name, type) }
                    // 删除应用及附属资源
                    delete(app.name)
                    iamService.unregisterResource(ProtocolType.AppStackApp, app.name, AuthUtil.getTenant(), ownerId)
                    // 删除应用与模版的关联
                    val templateName = appTemplateService.findAppTemplateNameBindedByApp(app.name)
                    templateName?.let { appTemplateService.unbindAppTemplateFromApp(it, app.name) }
                    normalizeError(e, appTemplate.name, app.name)
                }
            }
        }
        if(!appReq.domainTags.isNullOrEmpty()){
            coreFacades.domainTagFacade.bind(appReq.domainTags!!, app.name)
        }
        return app
    }

    fun normalizeError(e: Throwable, appTemplateName: String, appName: String) {
        if (e is BizException && e.httpStatus == 500) {
            // 直接抛出 500 系异常
            throw e
        } else {
            // 非 500 系异常，则完善提示信息
            val bizException = e as? BizException
            var errorMessage = ""
            var errorAdvice: String? = null
            bizException?.let {
                errorMessage = ": " + i18nMessageService.getErrorMessage(it)
                errorAdvice = i18nMessageService.getErrorAdvice(it)
            }
            // 如果非业务异常，也需要获取异常信息
            if (bizException == null) {
                errorMessage = ":" + e.message
            }
            // FIXME jiuya.wb 应用模板创建失败的错误码总是 426，但这个错误码是否便于集成就难说了，用户可能需要手工反复判断各组分。或者我们做幂等
            throw BizException(
                errorEntry = ErrorEntry(
                    code = ErrorCode.AS_APP_CREATE_WITH_TEMPLATE_FAILED,
                    advice = errorAdvice
                ),
                context = mapOf(
                    "appTemplateName" to appTemplateName,
                    "appName" to appName,
                    "msg" to errorMessage,
                )
            )
        }
    }

    fun createWithAppTemplateConfig(app: App, appTemplateName: String, type: AppTemplateConfig.TypeEnum) {
        logger.info("biz create app[${app.name}] with appTemplateConfig: ${type}")
        val config = appTemplateConfigBizService.find(appTemplateName, type)
        if (config != null) {
            val configuration = config.configuration
            when (type) {
                AppTemplateConfig.TypeEnum.Env -> {
                    (configuration as EnvConfiguration).envs.forEach {
                        it.spec?.rolloutStrategy?.forEach { rolloutStrategy ->
                            if (rolloutStrategy.locator != "*") {
                                rolloutStrategy.locator =
                                    rolloutStrategy.locator.replace("{{ .AppStack.appName }}", app.name)
                                        .replace("{{ .AppStack.envName }}", it.name)
                            }
                        }
                        envBizService.create(
                            app.name,
                            CreateEnvRequest(
                                name = it.name,
                                displayName = it.displayName,
                                description = "",
                                deployGroupName = it.deployGroupName,
                                profiles = it.variableGroups?.map { variableGroup ->
                                    if (variableGroup.type == VariableGroup.Type.TEMPLATE) {
                                        VariableGroup(
                                            name = variableGroup.name,
                                            displayName = variableGroup.displayName,
                                            type = VariableGroup.Type.APP
                                        )
                                    } else {
                                        variableGroup
                                    }
                                },
                                labelList = it.labels.toMutableList(),
                                spec = it.spec
                            ).apply {
                                if (!it.resourcePoolName.isNullOrBlank()) {
                                    this.resourcePoolName = it.resourcePoolName!!
                                }
                                if (it.deployType != null) {
                                    this.resourceType = it.deployType!!
                                }
                            }
                        )
                    }
                }

                AppTemplateConfig.TypeEnum.VariableGroup -> {
                    val request = (configuration as VariableGroupConfiguration).profileMap.values.map {
                        ProfileRequest(
                            name = it.name,
                            displayName = it.displayName,
                            vars = it.vars
                        )
                    }
                    coreFacades.variableFacade.createWithProfiles(app.name, request)
                }

                AppTemplateConfig.TypeEnum.Orchestration -> {
                    if ((configuration as OrchestrationConfiguration).orchestrations.isEmpty()) {
                        return
                    }
                    var orchestration = appOrchestrationBizService.create(
                        app.name, AppOrchestrationCreateRequest(
                            storageType = Orchestration.StorageTypeEnum.BUILTIN,
                            format = Orchestration.FormatEnum.MANIFEST,
                            templateType = null,
                            templateId = null,
                            datasource = null
                        )
                    )
                    var appTemplateOrchestration: AppTemplateOrchestration = configuration.orchestrations.first()
                    appTemplateService.initSyncStatus(
                        appTemplateName,
                        AppTemplateConfig.TypeEnum.Orchestration,
                        AppTemplateOrchestrationConfigBizServiceImpl.DEFAULT_APP_TEMPLATE_ORCHESTRATION_INSTANCE_NAME,
                        app.name
                    )

                    orchestration = appOrchestrationBizService.updateAllBuiltin(app.name,
                        UpdateAppOrchestrationRequest(
                            name = app.name,
                            format = configuration.orchestrations.first().format,
                            componentList = configuration.orchestrations.flatMap { (it as AppTemplateBuildInOrchestration).componentList }
                                .toMutableList(),
                            placeholderList = configuration.orchestrations.flatMap { (it as AppTemplateBuildInOrchestration).placeholderList }
                                .toMutableList(),
                            groupNameMap = (configuration.orchestrations.first() as AppTemplateBuildInOrchestration).groupNameMap,
                            label = (configuration.orchestrations.first() as AppTemplateBuildInOrchestration).labelList,
                            syncSourceTemplate = SyncSourceTemplate(
                                appTemplateName = appTemplateName,
                                orchestrationSha = (configuration.orchestrations.first() as AppTemplateBuildInOrchestration).revision!!.sha
                            )
                        ).apply {
                            fromRevisionSha = orchestration.revision!!.sha
                            message = orchestration.revision!!.message
                        }
                    )

                    appTemplateService.updateTemplateSyncStatus(
                        appTemplateName,
                        AppTemplateConfig.TypeEnum.Orchestration,
                        AppTemplateOrchestrationConfigBizServiceImpl.DEFAULT_APP_TEMPLATE_ORCHESTRATION_INSTANCE_NAME,
                        app.name,
                        AppTemplateConfigSyncStatus.TemplateSyncStatus(
                            lastAppliedTemplateRevisionSha = appTemplateOrchestration.revision!!.sha,
                            lastAppliedAppRevisionSha = orchestration.revision!!.sha
                        )
                    )
                }

                AppTemplateConfig.TypeEnum.ReleaseWorkflow -> {
                    (configuration as ReleaseWorkflowConfiguration).appTemplateWorkflowList.forEach { oneAppTemplateWorkflowRequest ->
                        oneAppTemplateWorkflowRequest.message = i18nMessageService.commonResource.getMessage(
                            CommonCode.INIT_RELEASEWORKFLOW_USING_APPTEMPLATE.value,
                            arrayOf(oneAppTemplateWorkflowRequest.appTemplateName),
                            LocaleUtil.getLocale()
                        )
                        changeRequestWorkflowBizService.create(app.name, oneAppTemplateWorkflowRequest.toRequest())
                    }
                }

                AppTemplateConfig.TypeEnum.Source -> {
                    val systemVariablesMap = SystemVariable.build(app.name, "")
                    (configuration as SourceConfiguration).codeRepos.forEach { codeRepo ->
                        // 系统变量替换
                        systemVariablesMap.forEach{
                            val source = "\${${it.key}}"
                            val target = it.value.toString()
                            codeRepo.name = codeRepo.name.replace(source, target)
                            codeRepo.repoUrl = codeRepo.repoUrl.replace(source, target)
                            codeRepo.identifier = codeRepo.identifier.replace(source, target)
                            codeRepo.repoContext.replace(source, target)
                        }
                        // 标识符中将应用的-转化成_
                        codeRepo.identifier = codeRepo.identifier.replace("-", "_")
                        appCodeRepoBizService.create(app.name, CreateAppCodeRepoRequest(
                            name = codeRepo.name,
                            repoUrl = codeRepo.repoUrl,
                            identifier = codeRepo.identifier,
                            repoContext = codeRepo.repoContext,
                            connectionConfig = codeRepo.connectionConfig
                        ))
                    }
                    configuration.artifactRepos.forEach { artifactRepo ->
                        systemVariablesMap.forEach{
                            val source = "\${${it.key}}"
                            val target = it.value.toString()
                            artifactRepo.identifier = artifactRepo.identifier.replace(source, target)
                            artifactRepo.repoContext.replace(source, target)
                        }
                        // 标识符中将应用的-转化成_
                        artifactRepo.identifier = artifactRepo.identifier.replace("-", "_")
                        appArtifactRepoBizService.create(app.name, CreateAppArtifactRepoRequest(
                            identifier = artifactRepo.identifier,
                            repoContext = artifactRepo.repoContext,
                            connectionConfig = artifactRepo.connectionConfig
                        ))
                    }
                }
                else -> {}
            }
        }
    }

    private fun clearWithAppTemplateConfig(app: App, appTemplateName: String, type: AppTemplateConfig.TypeEnum) {
        logger.info("biz clear delete app[${app.name}] with appTemplateConfig: ${type}")
        // 查找应用模版配置可能会出错，出错时说明存在配置，也需要清理
        val find = try {
            appTemplateConfigBizService.find(appTemplateName, type) == null
        } catch (e: Throwable) {
            true
        }
        if (find) {
            try {
                when (type) {
                    AppTemplateConfig.TypeEnum.Env -> {
                        coreFacades.envFacade.deleteAllByAppName(app.name)
                    }

                    AppTemplateConfig.TypeEnum.VariableGroup -> {
                        coreFacades.variableFacade.delete(app.name)
                    }

                    AppTemplateConfig.TypeEnum.Orchestration -> {
                        coreFacades.appOrchestrationFacade.delete(app.name)
                    }

                    AppTemplateConfig.TypeEnum.ReleaseWorkflow -> {
                        workflowControllerFacade.releaseWorkflowFacade.deleteAllByAppName(app.name)
                    }

                    AppTemplateConfig.TypeEnum.Source -> {
                        appCodeRepoBizService.findAll(app.name).forEach { appCodeRepoBizService.delete(app.name, it.sn) }
                        appArtifactRepoBizService.findAll(app.name).forEach { appArtifactRepoBizService.delete(app.name, it.sn) }
                    }
                }
            } catch (e: Throwable) {
                logger.error("biz clear delete app[${app.name}] with appTemplateConfig: ${type} error:", e)
            }
        }
    }

    @ApiMetricCounter(metricName = "app_operation", methodTag = "update")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_BASIC_SET, resourceArgIndex = 0)
        ]
    )
    override fun update(name: String, appReq: UpdateAppRequest) {
        coreFacades.appFacade.update(name, appReq)
        coreFacades.domainTagFacade.updateTagBindings(appReq.domainTags ?: listOf(), name)
    }

    @ApiMetricCounter(metricName = "app_operation", methodTag = "delete")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_DELETE, resourceArgIndex = 0)
        ]
    )
    override fun delete(name: String) {
        logger.info("biz delete app[${name}]")

        var app = coreFacades.appFacade.find(name)

        // 检查是否有关联的系统
        val relatedSystems = listRelatedSystemsPaginated(name, "", 1L, 10L)
        var systemsStr = relatedSystems.records.map { it.name }.joinToString(separator = ",")
        if (relatedSystems.records.size < relatedSystems.total) {
            systemsStr += "..."
        }
        checkBizWithCode(
            relatedSystems.total == 0L, mapOf(
                "systems" to systemsStr
            )
        ) { ErrorCode.AS_APP_DELETE_WITH_SYSTEM_FAILED }

        onlineGuidanceService.delete(name)
        coreFacades.envFacade.deleteAllByAppName(name)
        coreFacades.variableFacade.delete(name)
        coreFacades.appOrchestrationFacade.delete(name)
        changeControllerFacades.changeOrderInputFacade.deleteAll(name)
        changeControllerFacades.changeOrderFacade.deleteAll(name)
        changeControllerFacades.changeableEnvFacade.deleteAll(name)
        workflowControllerFacade.releaseWorkflowFacade.deleteAllByAppName(name)
        workflowControllerFacade.changeRequestFacade.deleteAllByAppName(name)
        workflowControllerFacade.appReleaseFacade.deleteAllByAppName(name)
        // 在删除研发流程后删除代码库，减少删除代码库后的影响
        appCodeRepoBizService.findAll(name).forEach { appCodeRepoBizService.delete(name, it.sn) }
        appArtifactRepoBizService.findAll(name).forEach { appArtifactRepoBizService.delete(name, it.sn) }
        // 删除系统与应用的关联
        coreFacades.appFacade.disrelateAllApps(name)
        coreFacades.appFacade.delete(name)
        // 删除应用与模版的关联
        val templateName = appTemplateService.findAppTemplateNameBindedByApp(name)
        templateName?.let { appTemplateService.unbindAppTemplateFromApp(it, name) }
        iamService.unregisterResource(ProtocolType.AppStackApp, name, AuthUtil.getTenant(), AuthUtil.getUserId())
        unbindAppPipeine(name)
        // 删除应用标签关联
        val tagNames = coreFacades.domainTagFacade.listTagBindingsByAppNames(listOf(name)).map { it.tagName }
        if (tagNames.isNotEmpty()) {
            coreFacades.domainTagFacade.unbind(tagNames, name)
        }
    }

    override fun groupCount(searchAppRequest: SearchAppRequest): AppCountVO {
        val orgConfigItem = orgConfigItemBizService.find(OrgConfigItem.ONLY_VIEW_ACCESSABLE_APP)
        // 如果勾选了【仅支持查看我有权限的应用列表】
        if ((orgConfigItem as? OnlyViewAccessableAppV1OrgConfigItem)?.enable == true) {
            // 如果查询全部但没有查看全部应用权限
            if (searchAppRequest.isMine == false
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_VIEW)
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_MANAGE)) {
                searchAppRequest.isMine = true
            }
        }
        var mine = countMyApp(searchAppRequest.search)
        searchAppRequest.names = null
        if (!searchAppRequest.domainTags.isNullOrEmpty()) {
            var appNamesFilteredByTags: List<String>? = null
            searchAppRequest.domainTags!!.forEach {
                val appNames = coreFacades.domainTagFacade.listAppNamesByTag(it)
                appNamesFilteredByTags = appNamesFilteredByTags?.intersect(appNames.toSet())?.toMutableList() ?: appNames
            }
            searchAppRequest.names = appNamesFilteredByTags
            if (mine > 0) {
                val myAppNames = searchMyAppNames(searchAppRequest.search, 1L, mine)
                mine = myAppNames.filter { appNamesFilteredByTags!!.contains(it) }.size.toLong()
            }
        }
        searchAppRequest.isFavoured = null
        val allPagination = coreFacades.appFacade.searchPaginated(searchAppRequest, 1, 10000)
        searchAppRequest.isFavoured = true
        val favouredPagination = coreFacades.appFacade.searchPaginated(searchAppRequest, 1, 10000)
        val all = allPagination.total
        val favorite = favouredPagination.total
        return AppCountVO(
            all = all,
            mine = mine,
            favorite = favorite
        )
    }

    override fun searchPaginated(
        searchAppRequest: SearchAppRequest,
        current: Long,
        pageSize: Long,
    ): Pagination<AppSearchVO> {
        val orgConfigItem = orgConfigItemBizService.find(OrgConfigItem.ONLY_VIEW_ACCESSABLE_APP)
        // 如果勾选了【仅支持查看我有权限的应用列表】
        if ((orgConfigItem as? OnlyViewAccessableAppV1OrgConfigItem)?.enable == true) {
            // 如果查询全部但没有查看全部应用权限
            if (searchAppRequest.isMine == false
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_VIEW)
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_MANAGE)) {
                throw BizException(ErrorCode.AS_PERMISSION_DENIED)
            }
        }
        val size = if (pageSize < 1) 10L else pageSize
        return if (searchAppRequest.isMine == true) {
            val total = countMyApp(searchAppRequest.search)
            // 查询所有我参与的应用
            val names = if(total > 0) searchMyAppNames(searchAppRequest.search, 1L, total) else emptyList()
            if (names.isEmpty()) {
                Pagination<AppSearchVO>().apply {
                    this.total = 0
                    this.current = current
                    this.pageSize = size
                    this.pages = 0
                    this.records = emptyList()
                }
            } else {
                // 键是应用名称，值是关联标签列表
                val tagMap = getTagBindingMapByAppNames(names)
                // 根据 tag 过滤
                searchAppRequest.names = filterByTags(tagMap, searchAppRequest.domainTags)
                val paginated = coreFacades.appFacade.searchPaginated(searchAppRequest, current, size)
                // names 是iam查询结果，按照授权时间排序；core查询是按应用创建时间排序，这里顺序保持一致
                paginated.records = paginated.records.sortedWith { o1, o2 ->
                    names.indexOf(o1.name) - names.indexOf(o2.name)
                }
                val appOwnerMap = findAppOwner(names)
                val appNameAndTemplateMap = appTemplateService.findAppTemplateBindedByAppList(names)
                val records = paginated.records.map {
                    it.toSearchVO(appOwnerMap[it.name], appNameAndTemplateMap[it.name], tagMap[it.name])
                }
                paginated.transfer(records)
            }
        } else {
            // 权限校验
            if (!iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_VIEW)
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_MANAGE)
            ) {
                throw BizException(ErrorCode.AS_PERMISSION_DENIED)
            }
            // 通过 tags 查询应用并取交集
            if (!searchAppRequest.domainTags.isNullOrEmpty()) {
                var appNamesFilteredByTags: List<String>? = null
                searchAppRequest.domainTags!!.forEach {
                    val appNames = coreFacades.domainTagFacade.listAppNamesByTag(it)
                    appNamesFilteredByTags = appNamesFilteredByTags?.intersect(appNames.toSet())?.toMutableList() ?: appNames
                }
                searchAppRequest.names = appNamesFilteredByTags
            }
            val paginated = coreFacades.appFacade.searchPaginated(searchAppRequest, current, size)
            val names = paginated.records.map { it.name }
            val rolePlayers = findAppOwner(names)
            val appNameAndTemplateMap = appTemplateService.findAppTemplateBindedByAppList(names)
            val tagMap = getTagBindingMapByAppNames(names)
            val records = paginated.records.map {
                it.toSearchVO(rolePlayers[it.name], appNameAndTemplateMap[it.name], tagMap[it.name])
            }
            paginated.transfer(records)
        }
    }

    /**
     * @param appNames 应用名称列表
     * @return 键是应用名称，值是关联 tag 列表
     */
    private fun getTagBindingMapByAppNames(appNames: List<String>): Map<String, List<DomainTag>> {
        if (appNames.isEmpty()) return emptyMap()
        val tagBindings = coreFacades.domainTagFacade.listTagBindingsByAppNames(appNames)
        val tagBindingMap = tagBindings.groupBy { it.refId }
        val domainTags = coreFacades.domainTagFacade.list(TagSearchRequest(), 1, 10000).data.map { it.toModel()}
        // 对于没有关联 tag 的应用，关联 tag 列表为空
        return appNames.associateWith { emptyList<DomainTag>() } +
                tagBindingMap.mapValues { (_, value) -> value.map { tb -> domainTags.first{ it.name == tb.tagName } } }
    }

    private fun filterByTags(domainTagMap: Map<String, List<DomainTag>>, tags: List<String>?): List<String> {
        return if (tags.isNullOrEmpty()) {
            domainTagMap.keys.toList()
        } else {
            // 过滤掉不包含 tags 中所有标签的应用
            domainTagMap.filter { (name, bindingTags) -> bindingTags.map { it.name }.containsAll(tags) }
                .keys.toList()
        }

    }

    override fun list(query: ApplicationQuery): PageList<AppVO> {
        if (!query.checkPassed()) {
            throw BizException(ErrorCode.AS_PAGINATION_PARAM_INVALID)
        }

        val orgConfigItem = orgConfigItemBizService.find(OrgConfigItem.ONLY_VIEW_ACCESSABLE_APP)
        // 如果勾选了【仅支持查看我有权限的应用列表】
        if ((orgConfigItem as? OnlyViewAccessableAppV1OrgConfigItem)?.enable == true) {
            // 如果查询全部但没有查看全部应用权限
            if (query.isMine == false
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_VIEW)
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_MANAGE)
            ) {
                throw BizException(ErrorCode.AS_PERMISSION_DENIED)
            }
        }

        return if (query.isMine) {
            val names = searchAllMyAppNames()
            if (names.isEmpty()) {
                PageList<AppVO>().apply {
                    this.total = 0
                }
            } else {
                coreFacades.appFacade.list(
                    ApplicationQueryRequest(
                        pagination = query.pagination,
                        perPage = query.perPage,
                        orderBy = query.orderBy,
                        sort = query.sort,
                        nextToken = query.nextToken,
                        names = names
                    )
                )
            }
        } else {
            return coreFacades.appFacade.list(
                ApplicationQueryRequest(
                    pagination = query.pagination,
                    perPage = query.perPage,
                    orderBy = query.orderBy,
                    sort = query.sort,
                    nextToken = query.nextToken
                )
            )
        }
    }

    override fun findAllMember(appName: String): Map<ResourcePlayer, List<Role>> {
        val playerRolesMap = iamService.findPlayerRoles(ProtocolType.AppStackApp, appName)
        playerRolesMap.values.forEach { roleList ->
            roleList.sortBy { AppRole.valueOf(it.name).ordinal }
        }
        return playerRolesMap
    }

    override fun listMembers(appName: String, current: Long, pageSize: Long): Pagination<MemberVO> {
        val allMemberMap = findAllMember(appName)
        val list = allMemberMap.map { MemberVO(it.key, it.value) }.sortedWith { o1, o2 ->
            if (o1.roleList.find { it.name == AppRole.owner.name } != null) -1
            else if (o2.roleList.find { it.name == AppRole.owner.name } != null) 1
            else
                o1.displayName.compareTo(o2.displayName)
        }
        return Pagination(
            total = list.size.toLong(),
            current = 1L,
            records = list
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_MEMBER_SET, resourceArgIndex = 0)
        ]
    )
    override fun addRoleMember(appName: String, roleName: String, playerList: List<ResourcePlayerRequest>) {
        if (playerList.firstOrNull { it.type !in listOf(SubjectType.User, SubjectType.Group, SubjectType.Team) } != null) {
            throw BizException(ErrorCode.AS_SUBJECT_TYPE_NOT_SUPPORT)
        }
        val role = permissionService.findAppRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        checkBizWithCode(role.name != AppRole.owner.name, null) {
            ErrorCode.AS_ROLE_OWNER_ONLY_CAN_BE_MODIFIED_BY_TRANSFER
        }
        if (!iamService.checkPlayersExist(playerList)) {
            throw BizException(ErrorCode.AS_BASE_USER_NOT_FOUND)
        }
        iamService.addRoleMember(
            protocolType = ProtocolType.AppStackApp,
            resourceName = appName,
            roleName = role.name,
            playerList = playerList
        )
        val audit = MemberAudit(
            target = AuditTarget(id = appName, name = appName),
            member = Member(roleName = role.name, userIds = playerList.map { it.id })
        )
        auditLogService.log(OrgEventType.APP_MEMBER_ADD, audit)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_MEMBER_SET, resourceArgIndex = 0)
        ]
    )
    override fun updateRoleMember(appName: String, roleName: String, playerList: List<ResourcePlayerRequest>) {
        if (playerList.firstOrNull { it.type !in listOf(SubjectType.User, SubjectType.Group, SubjectType.Team) } != null) {
            throw BizException(ErrorCode.AS_SUBJECT_TYPE_NOT_SUPPORT)
        }
        val role = permissionService.findAppRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        checkBizWithCode(role.name != AppRole.owner.name, null) {
            ErrorCode.AS_ROLE_OWNER_ONLY_CAN_BE_MODIFIED_BY_TRANSFER
        }
        if (!iamService.checkPlayersExist(playerList)) {
            throw BizException(ErrorCode.AS_BASE_USER_NOT_FOUND)
        }
        iamService.updateRole(
            protocolType = ProtocolType.AppStackApp,
            resourceName = appName,
            roleName = role.name,
            playerList = playerList
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_MEMBER_SET, resourceArgIndex = 0)
        ]
    )
    override fun updateMemberRole(
        appName: String,
        subjectId: String,
        subjectType: SubjectType,
        roleNameList: List<String>,
    ) {
        if (subjectType !in listOf(SubjectType.User, SubjectType.Group, SubjectType.Team)) {
            throw BizException(ErrorCode.AS_SUBJECT_TYPE_NOT_SUPPORT)
        }
        val allRoleNames = permissionService.findAllAppRoles().map { it.name }
        // 如果有不存在的应用角色
        val unknownRole = roleNameList.firstOrNull { !allRoleNames.contains(it) }
        if (unknownRole != null) {
            throw BizException(ErrorCode.AS_ROLE_NOT_FOUND)
        }
        // 如果有不存在的用户
        if (!iamService.checkPlayersExist(listOf(ResourcePlayerRequest(subjectId, subjectType)))) {
            throw BizException(ErrorCode.AS_BASE_USER_NOT_FOUND)
        }
        val roleNameList = roleNameList.filter { it != AppRole.owner.name }
        val previousRoles = iamService.findPlayerRoles(
            protocolType = ProtocolType.AppStackApp,
            resourceName = appName,
        ).mapKeys { it.key.id }[subjectId] ?: emptyList()
        iamService.updatePlayer(
            protocolType = ProtocolType.AppStackApp,
            resourceName = appName,
            subjectType = subjectType,
            subjectId = subjectId,
            roleNameList = roleNameList
        )
        val audit = MemberRoleAudit(
            target = AuditTarget(id = appName, name = appName),
            member = MemberRole(
                roleNames = roleNameList,
                previousRoleNames = previousRoles.map { it.name },
                userId = subjectId
            )
        )
        val auditType = if (roleNameList.isEmpty()) OrgEventType.APP_MEMBER_DELETE else OrgEventType.APP_MEMBER_MODIFY
        auditLogService.log(auditType, audit)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_OWNER_TRANSFER, resourceArgIndex = 0)
        ]
    )
    override fun transferOwner(appName: String, player: ResourcePlayerRequest) {
        // 校验用户是否存在
        iamService.checkUserExist(player.id)

        findAppOwner(listOf(appName))[appName]?.let { owner ->
            iamService.updatePlayer(
                protocolType = ProtocolType.AppStackApp,
                resourceName = appName,
                subjectType = SubjectType.User,
                subjectId = owner,
                roleNameList = listOf(AppRole.developer.name)
            )
        }
        iamService.updatePlayer(
            protocolType = ProtocolType.AppStackApp,
            resourceName = appName,
            subjectType = player.type,
            subjectId = player.id,
            roleNameList = listOf(AppRole.owner.name)
        )
    }

    override fun favour(name: String) {
        coreFacades.appFacade.favour(name)
    }

    override fun disfavour(name: String) {
        coreFacades.appFacade.disfavour(name)
    }

    private fun countMyApp(search: String? = ""): Long {
        return iamService.countResource(
            protocolType = ProtocolType.AppStackApp,
            subjectType = SubjectType.User,
            subjectId = AuthUtil.getUserId(),
            search = search
        ).toLong()
    }

    private fun searchMyAppNames(search: String? = "", page: Long, size: Long): List<String> {
        return iamService.searchResourceName(
            protocolType = ProtocolType.AppStackApp,
            subjectType = SubjectType.User,
            subjectId = AuthUtil.getUserId(),
            search = search,
            page = page.toInt(),
            size = size.toInt(),
        )
    }

    private fun searchAllMyAppNames(search: String? = ""): List<String> {
        val nameList = mutableListOf<String>()
        var page = 1
        val pageSize = 150
        while (true){
            val tempList = iamService.searchResourceName(
                protocolType = ProtocolType.AppStackApp,
                subjectType = SubjectType.User,
                subjectId = AuthUtil.getUserId(),
                search = search,
                page = page,
                size = pageSize,
            )
            page++
            nameList.addAll(tempList)
            if(tempList.size < pageSize){
                break
            }
        }
        return nameList
    }

    private fun findAppOwner(names: List<String>): Map<String, String?> {
        if (names.isEmpty()) return emptyMap()
        return iamService.findRolePlayers(ProtocolType.AppStackApp, names, AppRole.owner.name).mapValues { it.value[0] }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0)
        ]
    )
    override fun findTemplate(name: String): AppTemplate? {
        val app = find(name)
        checkExists(app) { ErrorCode.AS_APP_NOT_FOUND }
        val templateName = appTemplateService.findAppTemplateNameBindedByApp(name) ?: return null
        return appTemplateService.find(templateName)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_BASIC_SET, resourceArgIndex = 0)
        ]
    )
    override fun bindTemplate(appName: String, templateName: String) {
        val app = find(appName)
        checkExists(app) { ErrorCode.AS_APP_NOT_FOUND }
        val alreadyBindTemplateName = appTemplateService.findAppTemplateNameBindedByApp(appName)
        checkBizWithEntry(alreadyBindTemplateName == null) { ErrorEntry(code = ErrorCode.AS_APP_TEMPLATE_ALREADY_BIND) }
        appTemplateService.find(templateName)
        appTemplateService.bindAppTemplateToApp(templateName, appName)
    }


    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_BASIC_SET, resourceArgIndex = 0)
        ]
    )
    override fun unbindTemplate(appName: String) {
        val app = find(appName)
        checkExists(app) { ErrorCode.AS_APP_NOT_FOUND }
        val alreadyBindTemplateName = appTemplateService.findAppTemplateNameBindedByApp(appName)
        checkExists(alreadyBindTemplateName) { ErrorCode.AS_APP_TEMPLATE_NOT_FOUND }
        appTemplateService.unbindAppTemplateFromApp(alreadyBindTemplateName, appName)

    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_CREATE),
        ]
    )
    override fun listAdminedAppsPaginated(search: String, current: Long, pageSize: Long): Pagination<Application> {
        // TODO: 公有云场景下 resourceName
        val size = if (pageSize < 1) 10L else pageSize
        if (iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_MANAGE)) {
            // 具有【管理应用】权限点，则返回所有应用
            val apps = searchPaginated(SearchAppRequest(search = search), current, size)
            val records = apps.records.map {
                Application(
                    name = it.name,
                    description = it.description,
                    creatorId = it.creatorId ?: "",
                    gmtCreate = it.gmtCreate ?: Date(),
                )
            }
            return apps.transfer(records)
        } else {
            // 返回作为【拥有者】的应用
            // 此处分页内结果可能少于 pageSize，但总页数是正确的，而且一般是大 pageSize 全量取，暂时忽略此问题
            val apps = searchPaginated(SearchAppRequest(search = search, isMine = true), current, size)
            val ownerId = AuthUtil.getUserId()
            val records = apps.records.filter { ownerId == it.ownerId }.map {
                Application(
                    name = it.name,
                    description = it.description,
                    creatorId = it.creatorId ?: "",
                    gmtCreate = it.gmtCreate ?: Date(),
                )
            }
            return apps.transfer(records)
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0)
        ]
    )
    override fun listRelatedSystemsPaginated(
        name: String,
        search: String,
        current: Long,
        pageSize: Long,
    ): Pagination<Application> {
        val size = if (pageSize < 1) 10L else pageSize
        val apps = coreFacades.appFacade.listRelatedSystemsPaginated(name, search, current, size)
        val records = apps.records.map { it.toStandardVo(null) }
        return apps.transfer(records)
    }

    override fun batchAddMembers(
        appNames: List<String>,
        addMembersRequest: AddMembersRequest,
    ): List<MemberOperateResult> {
        val dynamicProxy = AopContext.currentProxy() as AppBizServiceImpl
        return appNames.flatMap { appName ->
            addMembersRequest.roleNames.mapNotNull { roleName ->
                val pair = try {
                    // owner不做修改，直接略过
                    val owners = iamService.findRolePlayers(
                        protocolType = ProtocolType.AppStackApp,
                        resourceNameList = listOf(appName), roleName = AppRole.owner.name
                    )[appName] ?: emptyList()
                    val playerList = addMembersRequest.playerList.filter { player -> !owners.contains(player.id) }
                    dynamicProxy.addRoleMember(appName, roleName, playerList)
                    Pair(true, null)
                } catch (e: IamForbiddenException) {
                    Pair(
                        false,
                        i18nMessageService.getErrorMessage(BizException(errorCode = ErrorCode.AS_APP_MEMBER_MANAGER_PERMISSION_DENIED))
                    )
                } catch (e: BizException) {
                    Pair(false, i18nMessageService.getErrorMessage(e))
                } catch (e: Exception) {
                    Pair(false, e.message)
                }
                if (pair.first) {
                    null
                } else {
                    MemberOperateResult(
                        resourceName = appName,
                        playerList = addMembersRequest.playerList,
                        operation = MemberOperate.add,
                        result = pair.first,
                        errorMsg = pair.second,
                    )
                }
            }
        }
    }

    override fun batchDeleteMembers(
        appNames: List<String>,
        players: List<ResourcePlayerRequest>,
    ): List<MemberOperateResult> {
        val dynamicProxy = AopContext.currentProxy() as AppBizServiceImpl
        return appNames.mapNotNull { appName ->
            val pair = try {
                dynamicProxy.deleteMember(appName, players)
                Pair(true, null)
            } catch (e: IamForbiddenException) {
                Pair(
                    false,
                    i18nMessageService.getErrorMessage(BizException(errorCode = ErrorCode.AS_APP_MEMBER_MANAGER_PERMISSION_DENIED))
                )
            } catch (e: BizException) {
                Pair(false, i18nMessageService.getErrorMessage(e))
            } catch (e: Exception) {
                Pair(false, e.message)
            }
            if (pair.first) {
                null
            } else {
                MemberOperateResult(
                    resourceName = appName,
                    playerList = players,
                    operation = MemberOperate.delete,
                    result = pair.first,
                    errorMsg = pair.second,
                )
            }
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_MEMBER_SET, resourceArgIndex = 0)
        ]
    )
    override fun deleteMember(appName: String, playerList: List<ResourcePlayerRequest>) {

        val owners = iamService.findRolePlayers(
            protocolType = ProtocolType.AppStackApp,
            resourceNameList = listOf(appName), roleName = AppRole.owner.name
        )[appName] ?: emptyList()

        if (playerList.any { owners.contains(it.id) }) {
            throw BizException(errorCode = ErrorCode.AS_ROLE_OWNER_ONLY_CAN_BE_MODIFIED_BY_TRANSFER)
        }

        playerList.forEach { player ->
            iamService.updatePlayer(
                protocolType = ProtocolType.AppStackApp,
                resourceName = appName,
                subjectType = player.type,
                subjectId = player.id,
                roleNameList = emptyList()
            )
        }
    }

    private fun getHeaders(): Map<String, String> {
        return TokenUtil.generateAuthHeaders(flowAuthConfig.appId, flowAuthConfig.appSecret)
    }

    private fun unbindAppPipeine(appName: String) {
        try {
            val pipelineList = flowV1PipelineApi.queryByRegion(headers = getHeaders(), refObjectId = appName)
            pipelineList.data?.dataList?.forEach {
                val pipelineId = it.pipelineId
                flowV1PipelineApi.removePipelineBinding(
                    headers = getHeaders(),
                    refObjectId = appName,
                    pipelineId = pipelineId
                )
            }
        } catch (e: Exception) {
            logger.error("remove app's pipeline error", e)
        }
    }

}