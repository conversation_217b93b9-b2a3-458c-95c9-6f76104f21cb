package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateBizService
import com.alibabacloud.devops.appstack.apps.biz.service.GlobalVarBizService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.globalvar.GlobalVar
import com.alibabacloud.devops.appstack.libs.model.`do`.globalvar.GlobalVarRevisionContent
import com.alibabacloud.devops.appstack.libs.model.`do`.globalvar.GlobalVarUsageReference
import com.alibabacloud.devops.appstack.libs.model.`do`.transfer
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.iam.VarRole
import com.alibabacloud.devops.appstack.libs.model.request.CreateGlobalVarRequest
import com.alibabacloud.devops.appstack.libs.model.request.SearchGlobalVarRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateGlobalVarContentRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateGlobalVarRequest
import com.alibabacloud.devops.appstack.libs.model.response.GlobalVarVO
import com.alibabacloud.devops.appstack.libs.model.response.toVO
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.ResourcePlayer
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
open class  GlobalVarBizServiceImpl: GlobalVarBizService {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var iamService: IamService

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var appTemplateBizService: AppTemplateBizService

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_VIEW),
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_VIEW, resourceArgIndex = 0),
            Access(action = Action.VAR_EDIT, resourceArgIndex = 0),
            Access(action = Action.VAR_USE, resourceArgIndex = 0),
        ]
    )
    override fun find(name: String): GlobalVarVO {
        val globalVar = coreFacades.globalVarFacade.find(name) ?: throw BizException(ErrorCode.AS_GLOBAL_VAR_NOT_FOUND)
        val listUsageReference = coreFacades.globalVarFacade.findUsageReferences(listOf(name))
        val ownerId = findVarOwner(listOf(name))[name] ?: ""
        return toGlobalVarVO(globalVar, ownerId, listUsageReference, true)
    }

    private fun toGlobalVarVO(
        globalVar: GlobalVar,
        ownerId: String,
        usageReferences: List<GlobalVarUsageReference>,
        fulfillDetailInfo: Boolean = false
    ): GlobalVarVO {
        // 为填充更多信息做准备
        val appTemplateNameSet = mutableSetOf<String>()

        // 模型转化
        val voList = usageReferences.map {
            when (it.usageRefType) {
                GlobalVarUsageReference.UsageRefType.AppEnv -> {
                    GlobalVarVO.GlobalVarUsageReferenceVO(
                        globalVar.name,
                        it.usageRefType,
                        (it.usageRefObject as GlobalVarUsageReference.AppEnvUsageRefObject).toVO(),
                    )
                }
                GlobalVarUsageReference.UsageRefType.AppTemplateEnv -> {
                    val appTemplateEnvUsageRefObject =
                        it.usageRefObject as GlobalVarUsageReference.AppTemplateEnvUsageRefObject
                    appTemplateNameSet.add(appTemplateEnvUsageRefObject.appTemplateName)
                    GlobalVarVO.GlobalVarUsageReferenceVO(
                        globalVar.name,
                        it.usageRefType,
                        (it.usageRefObject as GlobalVarUsageReference.AppTemplateEnvUsageRefObject).toVO(),
                    )
                }
                GlobalVarUsageReference.UsageRefType.AppTemplateWorkflowStage -> {
                    val appTemplateWorkflowStageUsageRefObject =
                        it.usageRefObject as GlobalVarUsageReference.AppTemplateWorkflowStageUsageRefObject
                    appTemplateNameSet.add(appTemplateWorkflowStageUsageRefObject.appTemplateName)
                    GlobalVarVO.GlobalVarUsageReferenceVO(
                        globalVar.name,
                        it.usageRefType,
                        appTemplateWorkflowStageUsageRefObject.toVO(),
                    )
                }
                GlobalVarUsageReference.UsageRefType.AppWorkflowStage -> {
                    GlobalVarVO.GlobalVarUsageReferenceVO(
                        globalVar.name,
                        it.usageRefType,
                        (it.usageRefObject as GlobalVarUsageReference.AppWorkflowStageUsageRefObject).toVO()
                    )
                }
            }
        }

        if (fulfillDetailInfo) {
            val appTemplateDisplayNameMap =
                appTemplateBizService.searchByNames(appTemplateNameSet.toList()).mapValues { (_, v) -> v.displayName }
            voList.forEach {
                if (it.usageRefType == GlobalVarUsageReference.UsageRefType.AppTemplateEnv) {
                    val o = (it.usageRefObject as GlobalVarVO.GlobalVarUsageReferenceVO.AppTemplateEnvUsageRefObjectVO)
                    o.appTemplateDisplayName = appTemplateDisplayNameMap[o.appTemplateName]
                } else if (it.usageRefType == GlobalVarUsageReference.UsageRefType.AppTemplateWorkflowStage) {
                    val o =
                        (it.usageRefObject as GlobalVarVO.GlobalVarUsageReferenceVO.AppTemplateWorkflowStageUsageRefObjectVO)
                    o.appTemplateDisplayName = appTemplateDisplayNameMap[o.appTemplateName]
                }
            }
        }

        return GlobalVarVO(
            name = globalVar.name,
            displayName = globalVar.displayName!!,
            creatorId = globalVar.creator!!,
            gmtCreate = globalVar.gmtCreate!!,
            modifierId = globalVar.modifier!!,
            gmtModified = globalVar.gmtModified!!,
            ownerId = ownerId,
            usageReferences = voList
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_VIEW),
            Access(action = Action.ORG_VAR_MANAGE),
        ]
    )
    override fun listPaginated(current: Long, pageSize: Long, req: SearchGlobalVarRequest): Pagination<GlobalVarVO> {
        val globalVarList = coreFacades.globalVarFacade.findPaginated(current, pageSize, req)
        val voList = convert4ListVO(globalVarList.records)
        return globalVarList.transfer(voList)

    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_CREATE),
            Access(action = Action.ORG_VAR_MANAGE),
        ]
    )
    override fun create(req: CreateGlobalVarRequest): GlobalVarVO {
        val globalVar = coreFacades.globalVarFacade.create(req)
        val ownerId = req.ownerId ?: AuthUtil.getUserId()
        iamService.registerResource(
            protocolType = ProtocolType.AppStackVar,
            resourceName = globalVar.name,
            orgId = AuthUtil.getTenant(),
            ownerId = ownerId
        )
        return toGlobalVarVO(globalVar, ownerId, emptyList())
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_EDIT, resourceArgIndex = 0)
        ]
    )
    override fun update(name: String, req: UpdateGlobalVarRequest): GlobalVarVO {
        coreFacades.globalVarFacade.update(name, req)
        return find(name)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_DELETE, resourceArgIndex = 0)
        ]
    )
    override fun delete(name: String): Boolean {
        val listUsageReference = coreFacades.globalVarFacade.findUsageReferences(listOf(name))
        if (listUsageReference.isNotEmpty()) {
            throw BizException(ErrorCode.AS_USAGE_REFERENCES_EXISTS)
        }
        coreFacades.globalVarFacade.delete(name)
        return true
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_VIEW),
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_VIEW, resourceArgIndex = 0),
            Access(action = Action.VAR_EDIT, resourceArgIndex = 0),
            Access(action = Action.VAR_USE, resourceArgIndex = 0),
        ]
    )
    override fun findContent(name: String, revisionSha: String?): GlobalVarRevisionContent {
        return coreFacades.globalVarFacade.findContent(name, revisionSha)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_VIEW),
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_VIEW, resourceArgIndex = 0),
            Access(action = Action.VAR_EDIT, resourceArgIndex = 0),
            Access(action = Action.VAR_USE, resourceArgIndex = 0),
        ]
    )
    override fun findGlobalVarContentByTag(name: String, tag: String): GlobalVarRevisionContent {
        return coreFacades.globalVarFacade.findTag(name, tag)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_EDIT, resourceArgIndex = 0),
        ]
    )
    override fun updateContent(name: String, req: UpdateGlobalVarContentRequest): GlobalVarRevisionContent {
        return coreFacades.globalVarFacade.updateContent(name, req)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_VIEW),
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_VIEW, resourceArgIndex = 0),
            Access(action = Action.VAR_EDIT, resourceArgIndex = 0),
            Access(action = Action.VAR_USE, resourceArgIndex = 0),
        ]
    )
    override fun listContentRevisionPaginated(
        name: String,
        current: Long,
        pageSize: Long,
    ): Pagination<Revision> {
        return coreFacades.globalVarFacade.listContentRevisionPaginated(name, current, pageSize)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_VIEW),
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_VIEW, resourceArgIndex = 0),
            Access(action = Action.VAR_EDIT, resourceArgIndex = 0),
            Access(action = Action.VAR_USE, resourceArgIndex = 0),
        ]
    )
    override fun compare(
        name: String,
        beforeRevisionSha: String,
        afterRevisionSha: String,
    ): Pair<GlobalVarRevisionContent?, GlobalVarRevisionContent?> {
        return coreFacades.globalVarFacade.compare(name, beforeRevisionSha, afterRevisionSha)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_VIEW),
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_VIEW, resourceArgIndex = 0),
            Access(action = Action.VAR_EDIT, resourceArgIndex = 0),
            Access(action = Action.VAR_USE, resourceArgIndex = 0),
            Access(action = Action.VAR_MEMBER_SET, resourceArgIndex = 0)
        ]
    )
    override fun findAllMember(name: String): Map<ResourcePlayer, List<Role>> {
        return iamService.findPlayerRoles(
            protocolType = ProtocolType.AppStackVar,
            resourceName = name
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_MEMBER_SET, resourceArgIndex = 0)
        ]
    )
    override fun addRoleMember(name: String, role: Role, playerList: List<ResourcePlayerRequest>) {
        iamService.addRoleMember(
            protocolType = ProtocolType.AppStackVar,
            resourceName = name,
            roleName = role.name,
            playerList = playerList,
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_MEMBER_SET, resourceArgIndex = 0)
        ]
    )
    override fun updateRoleMember(name: String, role: Role, playerList: List<ResourcePlayerRequest>) {
        doUpdateRoleMember(name, role, playerList)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_ADMIN_TRANSFER, resourceArgIndex = 0)
        ]
    )
    override fun transferOwner(name: String, player: ResourcePlayerRequest) {
        val role = iamService.findRole(ProtocolType.AppStackVar, VarRole.owner.name)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        // 原拥有者降级为管理员
        updateOwnerToAdmin(name)
        doUpdateRoleMember(name, role, listOf(player))
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_MEMBER_SET, resourceArgIndex = 0)
        ]
    )
    override fun updateMemberRole(
        name: String,
        subjectId: String,
        subjectType: SubjectType,
        roleNameList: List<String>,
    ) {
        iamService.updatePlayer(
            protocolType = ProtocolType.AppStackVar,
            resourceName = name,
            subjectId = subjectId,
            subjectType = subjectType,
            roleNameList = roleNameList,
        )
    }

    override fun listPaginatedCanUse(current: Long, pageSize: Long, req: SearchGlobalVarRequest): Pagination<GlobalVarVO> {
        val can = iamService.can(protocolType = ProtocolType.AppStack, "any", Action.ORG_VAR_MANAGE)
        if (can) {
            return listPaginated(current, pageSize, req)
        } else {
            val count = iamService.countResourceWithPolicy(
                protocolType = ProtocolType.AppStackVar,
                subjectType = SubjectType.User,
                subjectId = AuthUtil.getUserId(),
                policyName = Action.VAR_USE.code,
                search = req.search
            )
            val list = iamService.searchResourceWithPolicy(
                protocolType = ProtocolType.AppStackVar,
                subjectType = SubjectType.User,
                subjectId = AuthUtil.getUserId(),
                policyName = Action.VAR_USE.code,
                search = req.search,
                page = current.toInt(),
                size = pageSize.toInt(),
            )
            val varList = coreFacades.globalVarFacade.findList(list)
            return Pagination(
                total = count.toLong(),
                current = current,
                pageSize = pageSize,
                records = convert4ListVO(varList),
            )
        }
    }

    private fun convert4ListVO(list: List<GlobalVar>): List<GlobalVarVO> {
        val listUsageReference = coreFacades.globalVarFacade.findUsageReferences(list.map { it.name })
        val varOwnerMap = findVarOwner(list.map { it.name })
        return list.map { globalVar ->
            val ownerId = varOwnerMap[globalVar.name] ?: ""
            toGlobalVarVO(globalVar, ownerId, listUsageReference.filter { it.globalVarName == globalVar.name })
        }
    }

    private fun findVarOwner(names: List<String>): Map<String, String?> {
        if (names.isEmpty()) return emptyMap()
        return iamService.findRolePlayers(ProtocolType.AppStackVar, names, VarRole.owner.name).mapValues { it.value[0] }
    }

    private fun updateOwnerToAdmin(name: String) {
        val rolePlayers = iamService.findRolePlayers(
            protocolType = ProtocolType.AppStackVar,
            resourceNameList = listOf(name),
            roleName = VarRole.owner.name
        )
        rolePlayers[name]?.forEach { player ->
            updateMemberRole(
                name = name,
                subjectId = player,
                subjectType = SubjectType.User,
                roleNameList = listOf(VarRole.admin.name)
            )
        }
    }

    private fun doUpdateRoleMember(name: String, role: Role, playerList: List<ResourcePlayerRequest>) {
        iamService.updateRole(
            protocolType = ProtocolType.AppStackVar,
            resourceName = name,
            roleName = role.name,
            playerList = playerList,
        )
    }

}