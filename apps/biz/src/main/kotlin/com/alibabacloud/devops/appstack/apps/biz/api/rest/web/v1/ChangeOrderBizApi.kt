package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.*
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateChangeOrderRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.SearchChangeOrderRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderListRecord
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderRecord
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderVO
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderVersionRecord
import com.alibabacloud.devops.appstack.libs.model.vo.JobRecord
import com.alibabacloud.devops.appstack.libs.model.vo.StageEnvOccupy
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-03-21 16:26
 * @version: ChangeOrderBizApi, v0.1
 **/
@Tag(name = "ChangeOrder", description = "工单相关 API")
@RestController
@RequestMapping("/api/v1")
open class ChangeOrderBizApi {

    @Autowired
    lateinit var changeOrderBizService: ChangeOrderBizService

    @Operation(summary = "预渲染工单")
    @PostMapping("/changeOrders:dryRun")
    fun dryRunChangeOrder(
        @RequestBody createChangeOrderRequest: CreateChangeOrderRequest,
    ): Response<ChangeOrder> {
        return Response.success(changeOrderBizService.dryRun(createChangeOrderRequest))
    }

    @Operation(summary = "创建工单")
    @PostMapping("/changeOrders")
    fun createChangeOrder(
        @RequestBody createChangeOrderRequest: CreateChangeOrderRequest,
    ): Response<ChangeOrderVO> {
        if (createChangeOrderRequest.sourceType.isNullOrBlank()) {
            createChangeOrderRequest.sourceType = ChangeOrder.SourceType.CUSTOMIZE.name
            createChangeOrderRequest.sourceSn = AuthUtil.getUserId()
        }
        return Response.success(changeOrderBizService.create(createChangeOrderRequest))
    }

    @Operation(summary = "检查工单")
    @PostMapping("/changeOrders:check")
    fun checkChangeOrder(
        @RequestBody createChangeOrderRequest: CreateChangeOrderRequest,
    ): Response<List<InvalidChangeItem>> {
        if (createChangeOrderRequest.sourceType.isNullOrBlank()) {
            createChangeOrderRequest.sourceType = ChangeOrder.SourceType.CUSTOMIZE.name
            createChangeOrderRequest.sourceSn = AuthUtil.getUserId()
        }
        return Response.success(changeOrderBizService.check(createChangeOrderRequest))
    }



    @Operation(summary = "查看工单")
    @GetMapping("/changeOrders/{sn}")
    fun findChangeOrder(@PathVariable sn: String): Response<ChangeOrderVO> {
        return Response.success(changeOrderBizService.find(sn))
    }

    @Operation(summary = "操作工单")
    @PutMapping("/changeOrders/{sn}")
    fun changeAction(
        @PathVariable sn: String,
        @RequestBody changeAction: ChangeAction,
    ): Response<ChangeOrderVO> {
        return Response.success(changeOrderBizService.change(sn, changeAction))
    }

    @Operation(summary = "查看工单")
    @GetMapping("/changeOrders")
    fun findPaginated(
        @RequestParam appName: String,
        @RequestParam(defaultValue = "", required = false) envNames: List<String>,
        @RequestParam(defaultValue = "", required = false) jobTypes: List<Job.Type>,
        @RequestParam(defaultValue = "", required = false) jobStates: List<Job.State>,
        @RequestParam(defaultValue = "1", required = false) current: Long,
        @RequestParam(defaultValue = "10", required = false) pageSize: Long,
    ): Response<Pagination<ChangeOrderListRecord>> {
        return Response.success(
            changeOrderBizService.findPaginated(
                appName = appName,
                envNames = envNames,
                jobTypes = jobTypes,
                jobStates = jobStates,
                current = current,
                pageSize = pageSize
            )
        )
    }

    @PostMapping("/changeOrders:search")
    fun searchChangeOrderPaginated(
        @RequestBody searchChangeOrderRequest: SearchChangeOrderRequest,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<ChangeOrderListRecord>> {
        val paginatedChangeOrders = changeOrderBizService.searchPaginated(searchChangeOrderRequest, current, pageSize)
        return Response.success(paginatedChangeOrders)
    }

    @Operation(summary = "查看版本列表")
    @GetMapping("/changeOrders/versions")
    fun findPaginatedVersion(
        @RequestParam appName: String,
        @RequestParam(required = false) envNames: List<String>? = null,
        @RequestParam(required = false) creators: List<String>? = null,
        @RequestParam(defaultValue = "1", required = false) current: Long,
        @RequestParam(defaultValue = "10", required = false) pageSize: Long,
    ): Response<Pagination<ChangeOrderVersionRecord>> {
        return Response.success(
            changeOrderBizService.findPaginatedVersion(
                appName = appName,
                envNames = envNames ?: emptyList(),
                creators = creators ?: emptyList(),
                current = current,
                pageSize = pageSize
            )
        )
    }

    @Operation(summary = "根据版本号查工单")
    @GetMapping("/changeOrders/versions/{version}")
    fun findByVersion(
        @RequestParam appName: String,
        @PathVariable version: String,
    ): Response<ChangeOrderVersionRecord> {
        val changeOrderRecord = changeOrderBizService.findRecordByVersion(appName, version)
        checkExists(changeOrderRecord) { ErrorCode.AS_CHANGE_ORDER_NOT_EXISTED }
        return Response.success(changeOrderRecord)
    }

    @Operation(summary = "查看工单作业日志")
    @GetMapping("/jobLogs")
    fun findPaginatedJobLog(
        @RequestParam sns: List<String>,
        @RequestParam(defaultValue = "1", required = false) current: Long,
        @RequestParam(defaultValue = "10", required = false) pageSize: Long,
    ): Response<Pagination<JobLog>> {
        return Response.success(changeOrderBizService.findPaginatedJobLog(sns, current, pageSize))
    }

    @Operation(summary = "查询指定工单")
    @GetMapping("/changeOrders/sns")
    fun findBySns(
        @RequestParam appName: String,
        @RequestParam sns: List<String>
    ): Response<List<ChangeOrderRecord>> {
        return Response.success(changeOrderBizService.findBySns(appName, sns))
    }


    @Operation(summary = "查看工单作业日志")
    @GetMapping("/jobs/{sn}:diff")
    fun findJobDiff(
        @PathVariable sn: String,
    ): Response<Pair<JobRecord, JobRecord>> {
        return Response.success(changeOrderBizService.findJobDiff(sn))
    }

    @Operation(summary = "查询阶段相关环境占用详情")
    @GetMapping("/changeOrders/findEnvOccupyList")
    fun findEnvsOccupy(
        @RequestParam appName: String,
        @RequestParam envNames: String,
    ): Response<List<StageEnvOccupy>> {
        return Response.success(
            changeOrderBizService.findEnvOccupyList(appName = appName, envNames = envNames)
        )
    }

    @Operation(summary = "查询部署任务执行日志，其中通常包含下游部署引擎的调度细节信息")
    @GetMapping("/changeOrders/{changeOrderSn}/jobs/{jobSn}/stages/{stageSn}/tasks/{taskSn}/operationLog")
    fun findTaskOperationLog(
        @PathVariable("changeOrderSn") changeOrderSn: String,
        @PathVariable("jobSn") jobSn: String,
        @PathVariable("stageSn") stageSn: String,
        @PathVariable("taskSn") taskSn: String
    ): Response<String> {
        return Response.success(
            changeOrderBizService.findTaskExecutionLog(
                changeOrderSn = changeOrderSn,
                jobSn = jobSn,
                stageSn = stageSn,
                taskSn = taskSn
            )
        )
    }
}