package com.alibabacloud.devops.appstack.apps.biz.model

/**
 * @author: <EMAIL>
 * @date: 2022-08-25 22:02
 * @version: OnelineGuidance, v0.1
 **/
data class OnlineGuidance(
    val name: String,
    val appName: String? = null,
    var stepList: List<Step> = emptyList(),
)

data class Step(
    val name: String,
    var state: State
)

enum class State {
    FINISH, PROCESSING, INIT;
}

data class StepProgress(
    val needUpdate: Boolean,
    val step: Step
)