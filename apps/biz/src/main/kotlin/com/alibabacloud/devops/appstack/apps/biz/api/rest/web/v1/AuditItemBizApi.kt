package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.AuditItemBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.model.`do`.audit.AuditItem
import com.alibabacloud.devops.appstack.libs.model.request.audit.splc.AuditSubmitRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-11-29 10:46
 * @version: AuditBizApi, v0.1
 **/
@Tag(name = "AuditItemBizApi", description = "审核项接口")
@RestController
@RequestMapping("/api/v1")
@Slf4k
open class AuditItemBizApi {

    @Autowired
    lateinit var auditItemBizService: AuditItemBizService

    @PostMapping("/auditItem")
    fun submit(@RequestBody req: AuditSubmitRequest): Response<AuditItem> {
        return Response.success(auditItemBizService.submit(req))
    }

    @GetMapping("/auditItem/{refType}/{refSn}/{type}")
    fun find(
        @PathVariable type: AuditItem.Type,
        @PathVariable refType: AuditItem.RefType,
        @PathVariable refSn: String,
    ): Response<AuditItem> {
        return Response.success(auditItemBizService.find(type, refType, refSn))
    }

    @GetMapping("/auditItem/{refType}/{refSn}")
    fun findAll(@PathVariable refType: AuditItem.RefType, @PathVariable refSn: String): Response<List<AuditItem>> {
        return Response.success(auditItemBizService.findAll(refType, refSn))
    }

    @GetMapping("/auditItem/{refType}")
    fun findByRefs(
        @PathVariable refType: AuditItem.RefType,
        @RequestParam refSns: List<String>,
    ): Response<Map<String, List<AuditItem>>> {
        return Response.success(auditItemBizService.findByRefs(refType, refSns))
    }
}