package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import com.alibabacloud.devops.appstack.apps.biz.service.EnvBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import com.alibabacloud.devops.appstack.libs.model.request.CreateEnvRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateEnvRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.EnvQuery
import com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateEnvComputationResourceRequest
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import com.alibabacloud.devops.appstack.libs.model.vo.ng.Environment
import com.alibabacloud.devops.appstack.libs.model.vo.ng.toStandardVo
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.api.annotations.ParameterObject
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2023-09-01 16:26
 * @version: EnvApi, v0.1
 **/
@Tag(name = "环境", description = "环境领域 API")
@RestController
@RequestMapping("/api/v2")
@ConditionalOnProperty(name = ["devops.iam.environment"], havingValue = "vpc", matchIfMissing = false)
open class EnvApi {

    @Autowired
    lateinit var envBizService: EnvBizService

    @VpcApi
    @Operation(summary = "创建环境", operationId = "CreateEnvironments")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "创建成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/apps/{appName}/envs")
    fun createEnvironment(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @RequestBody request: com.alibabacloud.devops.appstack.libs.model.request.ng.CreateEnvRequest,
    ): Environment {
        val env = envBizService.create(appName, request.toCreateEnvRequest())
        return env.toStandardVo()
    }

    @VpcApi
    @Operation(summary = "分页查找环境详情列表", operationId = "ListEnvironments")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "分页查找完成（包括未查找到对象的情况）"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/envs")
    fun listEnvironments(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @ParameterObject query: EnvQuery,
    ): PageList<Environment> {
        val rawList = envBizService.list(appName, query)
        return PageList(
            nextToken = rawList.nextToken,
            data = rawList.data.map { it.toStandardVo() }
        )
    }

    @VpcApi
    @Operation(summary = "查找环境详情", operationId = "GetEnvironment")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已按名称查找到环境详情"),
            ApiResponse(
                responseCode = "404",
                description = "未查找到环境详情",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/envs/{envName}")
    fun findEnvironment(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @Schema(description = "环境名", example = "dev", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("envName") envName: String,
    ): Environment {
        val rawEnv = envBizService.find(appName = appName, envName = envName)
            ?: throw BizException(errorCode = ErrorCode.AS_ENV_NOT_FOUND)
        return rawEnv.toStandardVo()
    }

    @VpcApi
    @Operation(summary = "为环境关联计算资源", operationId = "UpdateEnvComputationResource")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已为环境关联计算资源"),
            ApiResponse(
                responseCode = "404",
                description = "未查找到环境详情",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "409",
                description = "环境上已经关联了计算资源",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/apps/{appName}/envs/{envName}:updateComputationResource")
    fun updateEnvComputationResource(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @Schema(description = "环境名", example = "dev", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("envName") envName: String,
        @RequestBody requestEntity: UpdateEnvComputationResourceRequest,
    ): Environment {
        val rawEnv = envBizService.find(appName = appName, envName = envName)
            ?: throw BizException(errorCode = ErrorCode.AS_ENV_NOT_FOUND)
        return envBizService.update(rawEnv, requestEntity.toLegacyEnvUpdateRequest()).toStandardVo()
    }

    @VpcApi
    @Operation(summary = "更新环境", operationId = "UpdateEnv")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "更新环境成功"),
            ApiResponse(
                responseCode = "404",
                description = "未查找到环境详情",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PutMapping("/apps/{appName}/envs/{envName}")
    fun updateEnv(
        @PathVariable("appName") appName: String,
        @PathVariable("envName") envName: String,
        @RequestBody req: com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateEnvRequest,
    ): Environment {
        var env = envBizService.find(appName, envName)
        checkExists(env) { ErrorCode.AS_ENV_NOT_FOUND }
        env = envBizService.update(env, req.toUpdateEnvRequest())
        return env.toStandardVo()
    }

    @Operation(summary = "删除环境", operationId = "DeleteEnv")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "删除环境成功"),
            ApiResponse(
                responseCode = "404",
                description = "未查找到环境详情",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @DeleteMapping("/apps/{appName}/envs/{envName}")
    fun deleteEnv(
        @PathVariable("appName") appName: String,
        @PathVariable("envName") envName: String,
    ): Environment {
        var env = envBizService.find(appName, envName)
        checkExists(env) { ErrorCode.AS_ENV_NOT_FOUND }
        env = envBizService.delete(env, false)
        return env.toStandardVo()
    }

}

fun UpdateEnvComputationResourceRequest.toLegacyEnvUpdateRequest(): UpdateEnvRequest {
    return UpdateEnvRequest(
        deployGroupName = this.deployGroupName,
        resourceType = ResourceTypeEnum.valueOf(this.resourceType)
    )
}

fun com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateEnvRequest.toUpdateEnvRequest(): UpdateEnvRequest {
    return UpdateEnvRequest(
        displayName = this.displayName,
        description = this.description,
        orchestrationName = this.orchestrationName,
        profiles = this.profiles,
        deployGroupName = this.deployGroupName,
        status = this.status,
        resourceType = this.resourceType,
        labelList = this.labelList,
        spec = Env.Spec(
            replicasManagement = replicasManagement,
            rolloutStrategy = rolloutStrategies,
        )
    )
}

fun com.alibabacloud.devops.appstack.libs.model.request.ng.CreateEnvRequest.toCreateEnvRequest() = CreateEnvRequest(
    name = name,
    displayName = displayName,
    description = description,
    resourcePoolName = resourcePoolName,
    deployGroupName = deployGroupName,
    profiles = profiles,
    resourceType = resourceType,
    labelList = labelList,
    spec = Env.Spec(
        replicasManagement = replicasManagement,
        rolloutStrategy = rolloutStrategies,
    )
)