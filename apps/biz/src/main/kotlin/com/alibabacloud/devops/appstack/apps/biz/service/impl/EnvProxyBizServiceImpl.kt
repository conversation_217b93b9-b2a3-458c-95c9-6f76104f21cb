package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.EnvProxyBizAdapterFactory
import com.alibabacloud.devops.appstack.apps.biz.service.EnvProxyBizService
import com.alibabacloud.devops.appstack.apps.biz.service.ResourceListVO
import com.alibabacloud.devops.appstack.libs.change.controller.spring.boot.starter.service.ChangeControllerFacades
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.model.ErrorEntry
import com.alibabacloud.devops.appstack.libs.common.util.AESCryptUtil
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.LocaleUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkBiz
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.constant.K8sResourceLabelConstants
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeableEnv
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.Task
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.TaskContext
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeTaskContext
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.toAdeTask
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.*
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.ResourceClaimRequest
import com.alibabacloud.devops.appstack.libs.model.request.resourcemanager.FlowHostInfo
import com.alibabacloud.devops.appstack.resource.manager.spring.boot.starter.ResourceManagerFacades
import com.fasterxml.jackson.module.kotlin.convertValue
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.fabric8.kubernetes.api.model.HasMetadata
import io.fabric8.kubernetes.api.model.Pod
import org.springframework.beans.factory.annotation.Autowired

import org.springframework.stereotype.Service
import io.fabric8.kubernetes.api.model.networking.v1.Ingress as V1Ingress
import io.fabric8.kubernetes.api.model.networking.v1beta1.Ingress as V1Beta1Ingress

/**
 * @author: <EMAIL>
 * @date: 2022-04-12 16:25
 * @version: EnvProxyBizServiceImpl, v0.1
 **/
@Service
@Slf4k
open class EnvProxyBizServiceImpl(
    @Autowired val resourceManagerFacades: ResourceManagerFacades,
    @Autowired val changeControllerFacades: ChangeControllerFacades,
    @Autowired val envProxyBizAdapterFactory: EnvProxyBizAdapterFactory,
    @Autowired val i18nMessageService: I18nMessageService,
    @Autowired val coreFacades: CoreFacades
) : EnvProxyBizService {


    override fun findAllPods(
        appName: String,
        envName: String,
        namespace: String,
        deploymentName: String,
    ): List<Pod> {
        return resourceManagerFacades.envProxyFacade.findAllPodByDeployment(appName, envName, namespace, deploymentName)
    }

    override fun findResourceList(
        appName: String,
        envName: String,
        resourceKind: String,
    ): ResourceListVO? {
        return if (resourceKind == TaskContext.Kind.Application.name) {
            val pair = findHostResource(appName, envName)
            ResourceListVO(
                typeCount = mapOf(TaskContext.Kind.Application.name to 1),
                resourceKind = resourceKind,
                resourceList = pair.first,
                errorMsg = pair.second,
            )
        } else {
            val changeableEnv = changeControllerFacades.changeableEnvFacade.find(appName, envName) ?: return null
            val adaptor = envProxyBizAdapterFactory.get(
                engineType = changeableEnv.engineType,
                kind = resourceKind
            )
            val changeOrder = changeControllerFacades.changeOrderFacade.lastDelivery(appName, envName) ?: return null
            val typeCount = adaptor.countGroupByKind(changeOrder = changeOrder)
            val pair = findK8sResource(appName, envName, resourceKind, changeOrder.getAllTask())
            val resourceList = adaptor.convertHasMetadataVOList(
                changeOrder = changeOrder,
                kind = resourceKind,
                envName = envName,
                resourceList = pair.first
            )
            ResourceListVO(
                typeCount = typeCount,
                resourceKind = resourceKind,
                resourceList = resourceList,
                errorMsg = pair.second
            )
        }
    }

    override fun countResource(appName: String, envName: String): Map<String, Int> {
        val changeableEnv = changeControllerFacades.changeableEnvFacade.find(appName, envName) ?: return emptyMap()
        return when (changeableEnv.resourceType) {
            ResourceTypeEnum.HOST -> mapOf(TaskContext.Kind.Application.name to 1)
            ResourceTypeEnum.KUBERNETES -> {
                val adaptor = envProxyBizAdapterFactory.get(engineType = changeableEnv.engineType, kind = "")
                val changeOrder =
                    changeControllerFacades.changeOrderFacade.lastDelivery(appName, envName) ?: return emptyMap()
                adaptor.countGroupByKind(changeOrder = changeOrder)
            }
            else -> emptyMap()
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ENV_EXEC_POD),
        ]
    )
    override fun generatePodExecToken(
        env: Env,
        namespace: String,
        podName: String,
        containerName: String
    ): PodExecToken {
        // 1. 校验pod是否归属当前环境
        // 使用工单+ADE部署的资源，可以通过label进行一次校验；未来做部署工作流，需要再关注一下如何改进 TODO
        // 由于 statefulset 生成的pod没有固定的3元组label，只能逐级向顶层资源发起校验
        val hasMetadata: HasMetadata? = resourceManagerFacades.envProxyFacade.findKubernetesResource(
            appName = env.appName,
            envName = env.name,
            group = HasMetadata.getGroup(Pod::class.java),
            version = HasMetadata.getVersion(Pod::class.java),
            kind = HasMetadata.getKind(Pod::class.java),
            name = podName,
            namespace = namespace,
        )
        checkBiz(findMatchedLabelsRecursive(hasMetadata, env.appName, env.name)) {
            ErrorEntry(
                code = ErrorCode.AS_K8S_RESOURCE_NOT_FOUND
            )
        }

        // 2. 生成token
        val podExecRefactor = PodExecRefactor(
            env.appName, env.name, namespace, podName, containerName
        )
        val token = AESCryptUtil.encrypt(
            jacksonObjectMapper().writeValueAsString(podExecRefactor)
        )
        return PodExecToken(token = token)
    }

    override fun findDeploymentRevisionInfo(
        appName: String,
        envName: String,
        namespace: String,
        name: String,
        revision: String,
        taskSn: String?
    ): WorkloadBrief? {
        var task: Task? = null
        try {
            if (taskSn != null) {
                task = changeControllerFacades.taskFacade.find(taskSn)
                if (task != null && task.state.isFinish()) {
                    // 任务存在且完结，取快照
                    val diagnosisSnapshotView = changeControllerFacades.taskFacade.fetchDiagnosisSnapshot(taskSn)
                    if (diagnosisSnapshotView?.workloadBrief != null) {
                        (diagnosisSnapshotView.workloadBrief as WorkloadBrief).snapshot = true
                        diagnosisSnapshotView.workloadBrief!!.children.forEach { rs ->
                            (rs as WorkloadBrief).snapshot = true
                            rs.children.filterIsInstance<PodBrief>().forEach { pod ->
                                pod.snapshot = true
                            }
                        }
                        return diagnosisSnapshotView.workloadBrief
                    }
                }
            }
        } catch (e: Throwable) {
            logger.error("[EnvProxyBizServiceImpl] Failed to find workload brief snapshot: " +
                "app=$appName, env=$envName, ns=$namespace, name=$name, revision=$revision, task=${taskSn ?: "n/a"}, " +
                "message=${e.message ?: "null"}", e)
        }
        return resourceManagerFacades.envProxyFacade.findDeploymentRevisionInfo(
            appName = appName,
            envName = envName,
            namespace = namespace,
            name = name,
            revision = revision,
            initialRevisions = if (task != null && task.engineType == ChangeableEnv.EngineType.Ade2 && task.resourceType == ResourceTypeEnum.KUBERNETES) {
                try {
                    task.toAdeTask().context.initialRevisions ?: emptyList()
                } catch (e: Throwable) {
                    emptyList()
                }
            } else {
                emptyList()
            }
        )
    }

    private fun findMatchedLabelsRecursive(hasMetadata: HasMetadata?, appName: String, envName: String): Boolean {
        if (hasMetadata == null) {
            return false
        } else if (matchAppEnvLabels(hasMetadata.metadata.labels, appName, envName)) {
            return true
        } else if (hasMetadata.metadata.ownerReferences.isNotEmpty()) {
            hasMetadata.metadata.ownerReferences.forEach { owner ->
                val vg = owner.apiVersion.split("/").reversed()
                val kind = owner.kind
                val name = owner.name
                val version = vg[0]
                val group = vg.getOrElse(1) { "core" }

                val parentHasMetadata = resourceManagerFacades.envProxyFacade.findKubernetesResource(
                    appName = appName,
                    envName = envName,
                    group = group,
                    version = version,
                    kind = kind,
                    name = name,
                    namespace = hasMetadata.metadata.namespace,
                )
                if (findMatchedLabelsRecursive(parentHasMetadata, appName, envName)) {
                    return true
                }
            }
            return false
        } else {
            return false
        }
    }

    private fun matchAppEnvLabels(labels: Map<String, String>?, appName: String, envName: String) =
        labels?.get(K8sResourceLabelConstants.APP_NAME) == appName
                && labels[K8sResourceLabelConstants.ENV_NAME] == envName
                && labels[K8sResourceLabelConstants.ORG_ID] == AuthUtil.getTenant()


    private fun findHostResource(appName: String, envName: String): Pair<List<FlowHostInfo>, String?> {
        var list: List<FlowHostInfo>? = null
        var errorMsg: String? = null
        try {
            val env = coreFacades.envFacade.find(appName, envName)
            if (env != null && !env.deployGroupName.isNullOrBlank() && !env.resourcePoolName.isNullOrBlank()) {
                val deployGroup =
                    resourceManagerFacades.resourceDeployGroupFacade.find(env.resourcePoolName!!, env.deployGroupName!!)
                list =
                    jacksonObjectMapper().convertValue<List<FlowHostInfo>>(deployGroup.claimList.first().specMap[ResourceClaimRequest.KEY_FLOW_HOST_LIST]!!)
            }
        } catch (e: BizException) {
            errorMsg = i18nMessageService.errorAdviceResource.getMessage(
                e.errorEntry.code.value,
                null,
                LocaleUtil.getLocale(),
            )
        }
        return Pair(first = list ?: emptyList(), second = errorMsg)
    }

    private fun findK8sResource(
        appName: String,
        envName: String,
        resourceKind: String,
        allTask: List<Task>,
    ): Pair<List<HasMetadata>, String?> {
        var list: List<HasMetadata>? = null
        var errorMsg: String? = null
        try {
            list = when (resourceKind) {
                TaskContext.Kind.Deployment.name ->
                    try {
                        resourceManagerFacades.envProxyFacade.findDeploymentList(appName, envName)?.items
                    } catch (e: Throwable) {
                        val namespaces = extractTaskLimitedNamespaces(allTask, resourceKind)
                        namespaces.map {
                            resourceManagerFacades.envProxyFacade.findDeploymentList(appName, envName, namespace = it)?.items ?: emptyList()
                        }.flatten()
                    }

                TaskContext.Kind.ConfigMap.name ->
                    try {
                        resourceManagerFacades.envProxyFacade.findConfigMapList(appName, envName)?.items
                    } catch (e: Throwable) {
                        val namespaces = extractTaskLimitedNamespaces(allTask, resourceKind)
                        namespaces.map {
                            resourceManagerFacades.envProxyFacade.findConfigMapList(appName, envName, namespace = it)?.items ?: emptyList()
                        }.flatten()
                    }

                TaskContext.Kind.Secret.name ->
                    try {
                        resourceManagerFacades.envProxyFacade.findSecretList(appName, envName)?.items
                    } catch (e: Throwable) {
                        val namespaces = extractTaskLimitedNamespaces(allTask, resourceKind)
                        namespaces.map {
                            resourceManagerFacades.envProxyFacade.findSecretList(appName, envName, namespace = it)?.items ?: emptyList()
                        }.flatten()
                    }

                TaskContext.Kind.Service.name ->
                    try {
                        resourceManagerFacades.envProxyFacade.findServiceList(appName, envName)?.items
                    } catch (e: Throwable) {
                        val namespaces = extractTaskLimitedNamespaces(allTask, resourceKind)
                        namespaces.map {
                            resourceManagerFacades.envProxyFacade.findServiceList(appName, envName, namespace = it)?.items ?: emptyList()
                        }.flatten()
                    }

                TaskContext.Kind.Ingress.name -> mergeIngressList(appName, envName, allTask)

                TaskContext.Kind.PersistentVolumeClaim.name ->
                    try {
                        resourceManagerFacades.envProxyFacade.findPvcList(appName, envName)?.items
                    } catch (e: Throwable) {
                        val namespaces = extractTaskLimitedNamespaces(allTask, resourceKind)
                        namespaces.map {
                            resourceManagerFacades.envProxyFacade.findPvcList(appName, envName, namespace = it)?.items ?: emptyList()
                        }.flatten()
                    }

                else -> {
                    // FIXME 替代上面的类型
                    allTask.map {
                        jacksonObjectMapper().readValue<AdeTaskContext>(it.contextContent)
                    }.filter {
                        it.kind == resourceKind
                    }.mapNotNull {
                        resourceManagerFacades.envProxyFacade.findKubernetesResource(
                            appName = appName,
                            envName = envName,
                            group = it.group,
                            version = it.version,
                            kind = it.kind,
                            name = it.name,
                            namespace = it.namespace,
                        )
                    }
                }
            }
        } catch (e: BizException) {
            errorMsg = i18nMessageService.errorAdviceResource.getMessage(
                e.errorEntry.code.value,
                null,
                LocaleUtil.getLocale(),
            )
        }
        return Pair(first = list ?: emptyList(), second = errorMsg)
    }

    private fun extractTaskLimitedNamespaces(tasks: List<Task>, targetResourceKind: String): Set<String> {
        return tasks.map {
            jacksonObjectMapper().readValue<AdeTaskContext>(it.contextContent)
        }.filter {
            it.kind == targetResourceKind
        }.map {
            it.namespace
        }.toSet()
    }

    private fun mergeIngressList(
        appName: String,
        envName: String,
        tasks: List<Task>
    ): MutableList<HasMetadata> {
        val ingressPair = try {
            resourceManagerFacades.envProxyFacade.findIngressList(appName, envName)
        } catch (e: Throwable) {
            val namespaces = extractTaskLimitedNamespaces(tasks, TaskContext.Kind.Ingress.name)
            val targets = Pair<MutableList<V1Ingress>, MutableList<V1Beta1Ingress>>(mutableListOf(), mutableListOf())
            namespaces.forEach {
                val ingresses = resourceManagerFacades.envProxyFacade.findIngressList(appName, envName, namespace = it)
                if (ingresses != null) {
                    if (ingresses.first.isNotEmpty()) {
                        targets.first.addAll(ingresses.first)
                    }
                    if (ingresses.second.isNotEmpty()) {
                        targets.second.addAll(ingresses.second)
                    }
                }
            }
            targets
        }
        // 合并查询结果
        val resultList = mutableListOf<HasMetadata>()
        // 直接并入v1版本
        if (ingressPair != null && ingressPair.first.isNotEmpty()) {
            resultList.addAll(ingressPair.first)
        }
        // 按name+ns去重归并v1beta1版本
        if (ingressPair != null && ingressPair.second.isNotEmpty()) {
            val existingIngresses = try {
                resultList.map { ing ->
                    val typedIng = ing as V1Ingress
                    val name = typedIng.metadata.name
                    val namespace = typedIng.metadata.namespace ?: "default"
                    "$name@$namespace"
                }.toSet()
            } catch (e: Throwable) {
                setOf()
            }
            val diffIngresses = try {
                ingressPair.second.filter { ing ->
                    val name = ing.metadata.name
                    val namespace = ing.metadata.namespace ?: "default"
                    !existingIngresses.contains("$name@$namespace")
                }
            } catch (e: Throwable) {
                listOf()
            }
            if (!diffIngresses.isEmpty()) {
                resultList.addAll(diffIngresses)
            }
        }
        return resultList
    }
}