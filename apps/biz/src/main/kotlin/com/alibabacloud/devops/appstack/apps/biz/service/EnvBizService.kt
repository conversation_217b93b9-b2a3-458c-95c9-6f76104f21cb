package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1.EnvStrategyForm
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.RevisionVariableGroup
import com.alibabacloud.devops.appstack.libs.model.event.ActionLog
import com.alibabacloud.devops.appstack.libs.model.request.CreateEnvRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateEnvRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.EnvQuery
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeableEnvRecordVO
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeableEnvVO

/**
 * <AUTHOR>
 * @date 2021-12-31
 */
interface EnvBizService {
    fun create(appName: String, createEnvRequest: CreateEnvRequest): Env
    fun update(env: Env, updateEnvRequest: UpdateEnvRequest): Env
    fun delete(env: Env, deleteDeployGroup: Boolean): Env
    fun lock(env: Env, isLock: Boolean): Env
    fun find(appName: String, envName: String): Env?
    fun findAll(appName: String): List<Env>
    fun list(appName: String, query: EnvQuery): PageList<Env>
    fun findChangeableEnv(appName: String, envName: String): ChangeableEnvVO?
    fun findAllChangeableEnvs(appName: String): List<ChangeableEnvRecordVO>
    fun findPaginatedActionLog(appName: String, envName: String, pageSize: Long, current: Long): Pagination<ActionLog>

    fun listEnvRevisionVariableGroups(appName: String, envName: String): List<RevisionVariableGroup>
    fun findEnvStrategyForm(appName: String, envName: String): EnvStrategyForm?
}