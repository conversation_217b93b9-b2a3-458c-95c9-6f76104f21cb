package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.ChangeRequestSet
import com.alibabacloud.devops.appstack.libs.model.request.cr.TransferChangeSetOwnerRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.UpdateChangeSetParticipatorsRequest
import com.alibabacloud.devops.appstack.libs.model.request.crset.QueryChangeRequestSet
import com.alibabacloud.devops.appstack.libs.model.request.crset.UpsertChangeRequestSetRequest
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeRequestSetOutlineVO

/**
 * @author: <EMAIL>
 * @date: 2022-08-22 20:19
 * @version: ChangeRequestSetBizService, v0.1
 **/
interface ChangeRequestSetBizService {

    fun upsert(upsertChangeRequestSetRequest: UpsertChangeRequestSetRequest): ChangeRequestSet?

    fun find(sn: String): ChangeRequestSet?

    fun findPaginated(
        request: QueryChangeRequestSet,
        current: Long,
        pageSize: Long,
        orderBy: String,
        sort: String
    ): Pagination<ChangeRequestSet>

    fun findDetail(sn: String): ChangeRequestSetOutlineVO?

    fun transferOwner(sn: String, request: TransferChangeSetOwnerRequest): ChangeRequestSet?
    fun updateParticipators(sn: String, request: UpdateChangeSetParticipatorsRequest): ChangeRequestSet?

    fun close(sn: String): ChangeRequestSet?
}