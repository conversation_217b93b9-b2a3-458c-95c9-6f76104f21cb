package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.request.AppTemplateQuery
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateRequest
import com.alibabacloud.devops.appstack.libs.model.vo.ng.Application
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.ResourcePlayer
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest

/**
 * @author: <EMAIL>
 * @date: 2023-03-02 10:05
 * @version: AppTemplateBizService, v0.1
 **/
interface AppTemplateBizService {

    fun create(request: UpsertAppTemplateRequest): AppTemplate

    fun findPaginated(search: String, current: Long, pageSize: Long, type: AppTemplate.Type = AppTemplate.Type.CUSTOMIZE): Pagination<AppTemplate>

    fun list(query: AppTemplateQuery): PageList<AppTemplate>

    fun delete(name: String)

    fun copy(name: String, request: UpsertAppTemplateRequest): AppTemplate

    fun findPaginatedApps(name: String, current: Long, pageSize: Long): Pagination<Application>

    fun update(name: String, request: UpsertAppTemplateRequest): AppTemplate

    fun find(name: String): AppTemplate

    fun preset()

    fun searchByNames(names: List<String>): Map<String, AppTemplate>
    fun transferOwner(name: String, player: ResourcePlayerRequest)

    fun findAllMember(name: String): Map<ResourcePlayer, List<Role>>

    fun addRoleMember(name: String, roleName: String, playerList: List<ResourcePlayerRequest>)

    fun updateMemberRole(name: String, subjectId: String, subjectType: SubjectType, roleNameList: List<String>)

    fun listMemberPaginated(search: String, current: Long, pageSize: Long): Pagination<AppTemplate>
}