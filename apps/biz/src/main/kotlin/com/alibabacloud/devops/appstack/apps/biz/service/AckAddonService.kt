package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.request.resource.AckAddonInstallRequest

interface AckAddonService {

    /**
     * 查询集群当前组件状态（通过版本号来看）
     */
    fun describeClusterAddonsUpgradeStatus(clusterId: String): Map<String, String>

    /**
     * 为集群安装组件
     */
    fun installClusterAddons(ackAddonInstallRequest: AckAddonInstallRequest)

    /**
     * 获取集群的Grafana大盘的列表
     */
    fun listDashboards(clusterId: String, regionId: String): Map<String, String>
}