package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateOrchestrationConfigBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.Label
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.InvalidChangeItem
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.OrchestrationSyncRevision
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.k8s.LocatorInfo
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.template.AppTemplateOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateCheckRequest
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateSyncRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.response.orchestration.OrchestrationTemplateSyncStatusResponse
import com.alibabacloud.devops.appstack.libs.model.vo.AppOrchestrationDiffVO
import com.alibabacloud.devops.appstack.libs.model.vo.OrchestrationDiffVO
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @date 2023-10-25
 */
@Tag(name = "AppTemplateOrchestrationConfig", description = "应用模板编排配置相关 API")
@RestController
@RequestMapping("/api/v1/appTemplates/{name}/configs/Orchestration")
open class AppTemplateOrchestrationConfigBizApi {

    @Autowired
    lateinit var appTemplateOrchestrationConfigBizService: AppTemplateOrchestrationConfigBizService

    @Operation(summary = "应用模板配置-编排配置-校验")
    @PostMapping("/check")
    fun checkOrchestration(
        @PathVariable("name") name: String,
        @RequestBody checkRequest: OrchestrationTemplateCheckRequest,
    ): Response<List<InvalidChangeItem>> {
        return Response.success(appTemplateOrchestrationConfigBizService.check(name, checkRequest))
    }

    @Operation(summary = "应用模板配置-编排配置-版本查询")
    @GetMapping("/revisions:withLabel")
    fun findPaginatedRevisionAndLabel(
        @PathVariable("name") name: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<OrchestrationSyncRevision>> {
        return Response.success(
            appTemplateOrchestrationConfigBizService.findPaginatedRevisionAndLabel(
                name,
                current,
                pageSize
            )
        )
    }

    @Operation(summary = "应用模板配置-编排配置-查询编排")
    @GetMapping("/{sn}")
    fun findOrchestrationTemplateBySha(
        @PathVariable("name") name: String,
        @PathVariable("sn") sn: String,
        @RequestParam("sha") sha: String,
    ): Response<AppTemplateOrchestration> {
        return Response.success(
            appTemplateOrchestrationConfigBizService.findOrchestrationBySha(
                name,
                sn,
                sha,
            )
        )
    }

    @Operation(summary = "应用模板配置-编排配置-版本比较")
    @GetMapping("/{sn}:compare")
    fun compareAppOrchestration(
        @PathVariable("name") name: String,
        @PathVariable("sn") sn: String,
        @RequestParam("beforeRevisionSha") beforeRevisionSha: String,
        @RequestParam("afterRevisionSha") afterRevisionSha: String,
    ): Response<Triple<OrchestrationDiffVO, OrchestrationDiffVO, List<DiffItem<String>>>> {
        return Response.success(
            appTemplateOrchestrationConfigBizService.compare(name, sn, beforeRevisionSha, afterRevisionSha)
        )
    }

    @Operation(summary = "应用模板配置-编排配置-绑定标签")
    @PostMapping("/label:bind")
    fun bindLabels(
        @PathVariable("name") name: String,
        @RequestParam("sha") sha: String,
        @RequestBody labels: List<Label>,
    ): Response<Unit> {
        return Response.success(
            appTemplateOrchestrationConfigBizService.bindLabels(name, sha, labels),
        )
    }

    @Operation(summary = "发起同步")
    @PostMapping("/sync")
    fun syncToApps(
        @PathVariable("name") name: String,
        @RequestBody request: OrchestrationTemplateSyncRequest
    ): Response<Boolean>{
        appTemplateOrchestrationConfigBizService.sync2Apps(name, request)
        return Response.success(true)
    }

    @Operation(summary = "查询同步状态")
    @GetMapping("/{sn}/syncStatus")
    fun syncStatus(
        @PathVariable("name") name: String,
        @PathVariable("sn") sn: String,
    ): Response<OrchestrationTemplateSyncStatusResponse> {
        return Response.success(
            appTemplateOrchestrationConfigBizService.syncStatus(name, sn)
        )
    }

    @Operation(summary = "模板与应用编排的比较")
    @GetMapping("/{sn}/compareToAppOrchestration")
    fun compareToAppOrchestration(
        @PathVariable("name") name: String,
        @PathVariable("sn") sn: String,
        @RequestParam("appName") appName: String,
    ): Response<Triple<AppOrchestrationDiffVO, OrchestrationDiffVO, List<DiffItem<String>>>> {
        return Response.success(
            appTemplateOrchestrationConfigBizService.compareToAppOrchestration(name, sn, appName)
        )
    }

    @Operation(summary = "渲染获取模板中的工作负载信息列表")
    @PostMapping("/{sn}/getLocatorList")
    fun getLocatorList(
        @PathVariable("name") name: String,
        @PathVariable("sn") sn: String,
        @RequestParam("envName") envName: String,
        @RequestBody profileName: List<Map<String,String>>,
    ): Response<List<LocatorInfo>> {
        return Response.success(
            appTemplateOrchestrationConfigBizService.getLocatorList(name = name, sn = sn, envName = envName, profileName)
        )
    }

}