package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateConfigRequest

/**
 * <AUTHOR> <EMAIL>
 * @version : AppTemplateConfigBizService, v0.1
 * @date : 2023-10-19 20:08
 **/
interface AppTemplateConfigBizService {

    fun upsert(
        appTemplateName: String,
        type: AppTemplateConfig.TypeEnum,
        request: UpsertAppTemplateConfigRequest,
    ): AppTemplateConfig

    fun find(appTemplateName: String, type: AppTemplateConfig.TypeEnum): AppTemplateConfig?

    fun delete(appTemplateName: String, type: AppTemplateConfig.TypeEnum, appTemplateConfigSn: String)

}