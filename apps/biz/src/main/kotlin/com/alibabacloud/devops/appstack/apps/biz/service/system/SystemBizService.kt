package com.alibabacloud.devops.appstack.apps.biz.service.system

import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.request.ng.*
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppCodeRepo
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import com.alibabacloud.devops.appstack.libs.model.response.ng.AppWithSourcesVO
import com.alibabacloud.devops.appstack.libs.model.response.ng.SystemCountVO
import com.alibabacloud.devops.appstack.libs.model.response.ng.SystemWithPersonalFavouritesVO
import com.alibabacloud.devops.appstack.libs.model.vo.ng.System
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest

/**
 * @author: <EMAIL>
 * @date: 2023-11-03 17:30
 * @version: SystemBizService, v0.1
 **/
interface SystemBizService {

    fun find(systemName: String): SystemWithPersonalFavouritesVO
    fun search(request: SearchSystemFilter, current: Long, pageSize: Long): Pagination<SystemWithPersonalFavouritesVO>
    fun list(current: Long, pageSize: Long): Pagination<SystemWithPersonalFavouritesVO>

    // 不加载个人统计的查找，供 openapi 使用
    fun listRaw(current: Long, pageSize: Long): Pagination<System>

    fun getGroupCount(request: SearchSystemFilter): SystemCountVO

    fun create(request: CreateSystemRequest): System
    fun update(systemName: String, request: UpdateSystemRequest): System
    fun delete(systemName: String): Boolean

    fun listMembers(systemName: String, current: Long, pageSize: Long): Pagination<MemberVO>

    fun addRoleMember(systemName: String, roleName: String, playerList: List<ResourcePlayerRequest>): Boolean
    fun updateRoleMember(systemName: String, roleName: String, playerList: List<ResourcePlayerRequest>): Boolean

    fun updateMemberRole(systemName: String, subjectType: SubjectType, subjectId: String, roleNameList: List<String>): Boolean

    fun transferOwner(systemName: String, owner: ResourcePlayerRequest): Boolean

    fun favourSystem(systemName: String): Boolean
    fun disfavourSystem(systemName: String): Boolean

    fun attachAppsToSystem(systemName: String, targetApps: Set<String>): Boolean
    fun detachAppsFromSystem(systemName: String, appNames: Set<String>): Boolean

    fun listAttachedApps(systemName: String, current: Long, pageSize: Long): Pagination<AppWithSourcesVO>
    fun findAllCodeRepos(systemName: String): List<AppCodeRepo>
    fun findAllOrchestrations(systemName: String): List<AppOrchestration>
}