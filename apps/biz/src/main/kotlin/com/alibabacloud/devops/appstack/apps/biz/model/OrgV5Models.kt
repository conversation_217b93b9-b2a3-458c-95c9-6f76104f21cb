package com.alibabacloud.devops.appstack.apps.biz.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonRawValue
import com.fasterxml.jackson.databind.annotation.JsonDeserialize


/**
 * <AUTHOR> <EMAIL>
 * @version : OrgV5EventRequest, v0.1
 * @date : 2021-12-03 11:56
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
data class OrgV5EventRequest(
    val action: String? = null,
    val resource: String? = null,
    @JsonProperty("resource_id")
    val resourceId: String? = null,
    @JsonProperty("organization_id")
    val organizationId: String? = null,
    @JsonRawValue
    @JsonDeserialize(using = RawJsonDeserializer::class)
    val data: String,
    val operator: Operator,
)

enum class OrgV5EventType(val value: String) {
    ORG_CREATE("yunxiao.base.organization.v1.created"),
    ORG_REMOVE("yunxiao.base.organization.v1.deleted"),
    ORG_MEMBER_CREATE("yunxiao.base.member.v1.created"),
    ORG_MEMBER_UPDATE("yunxiao.base.member.v1.updated"),
    ORG_MEMBER_REMOVE("yunxiao.base.member.v1.deleted"),
    ORG_ROLESHIP_CREATED("yunxiao.base.roleship.v1.created"),
    ORG_ROLESHIP_DELETED("yunxiao.base.roleship.v1.deleted"),
    ;

    override fun toString(): String {
        return value
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class Operator(
    @JsonProperty("user_id")
    var userId: String,
    @JsonProperty("user_name")
    val userName: String? = null,
    @JsonProperty("organization_id")
    val organizationId: String,
    @JsonProperty("is_admin")
    val isAdmin: Boolean = false,
    @JsonProperty("member_id")
    val memberId: String? = null,
    val ip: String? = null,
)