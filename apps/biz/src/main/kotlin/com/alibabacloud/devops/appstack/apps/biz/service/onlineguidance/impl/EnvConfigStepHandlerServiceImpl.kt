package com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.impl

import com.alibabacloud.devops.appstack.apps.biz.model.State
import com.alibabacloud.devops.appstack.apps.biz.model.Step
import com.alibabacloud.devops.appstack.apps.biz.model.StepProgress
import com.alibabacloud.devops.appstack.apps.biz.service.EnvBizService
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.StepHandlerService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR> liyebin.lyb
 * @date : 2022/8/26 2:45 PM
 */
@Service
class EnvConfigStepHandlerServiceImpl: StepHandlerService {

    @Autowired
    lateinit var envBizService: EnvBizService

    override fun handle(appName: String, step: Step): StepProgress {
        val state = if(envBizService.findAll(appName).any { it.deployGroupName != null && it.resourcePoolName != null }){
            State.FINISH
        }else{
            step.state
        }
        return StepProgress(
            needUpdate = step.state != state,
            step = Step(
                name = step.name,
                state = state
            )
        )
    }
}