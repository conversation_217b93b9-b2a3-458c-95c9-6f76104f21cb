package com.alibabacloud.devops.appstack.apps.biz.service.impl.handler.flowPermission

import com.alibabacloud.devops.appstack.apps.biz.service.FlowPermissionHandler
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.model.constant.FlowPermission
import com.alibabacloud.devops.appstack.libs.model.`do`.app.App
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppType
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageBriefVO
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.iam.constant.ProtocolType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @create 2023/12/8 2:31 PM
 **/
@Service
class OrgSystemViewPermissionHandler : FlowPermissionHandler {

    @Autowired
    lateinit var iamService: IamService

    override fun handle(app: App, stage: ReleaseStageBriefVO): Boolean {
        return iamService.can(ProtocolType.AppStack, "any", Action.ORG_SYSTEM_VIEW)
    }

    override fun suitableAppTypes() = listOf(AppType.SYSTEM)

    override fun suitableWorkflowTypes() = ReleaseWorkflow.TypeEnum.values().toList()

    override fun suitablePermissions() = listOf(FlowPermission.PERMISSION_PIPELINE_VIEW)

    override fun order() = 2
}