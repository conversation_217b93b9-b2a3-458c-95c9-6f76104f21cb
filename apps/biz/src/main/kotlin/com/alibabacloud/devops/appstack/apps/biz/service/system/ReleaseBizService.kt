package com.alibabacloud.devops.appstack.apps.biz.service.system

import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.ChangeRequest
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.WorkflowMetadata
import com.alibabacloud.devops.appstack.libs.model.`do`.release.AppRelease
import com.alibabacloud.devops.appstack.libs.model.`do`.release.AppReleaseInst
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.CreateAppReleaseRequest
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.SearchAppReleasableRequest
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.SearchAppReleaseRequest
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.SearchOrgAppReleaseRequest
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.UpdateAppReleaseRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.MetadataSearch
import com.alibabacloud.devops.appstack.libs.model.vo.AppReleaseRecordWithOwner
import com.alibabacloud.devops.appstack.libs.model.vo.ReleaseExecutionVO
import com.alibabacloud.devops.iam.model.ResourcePlayer
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest

/**
 * @author: <EMAIL>
 * @date: 2023-03-02 18:34
 * @version: AppReleaseBizService, v0.1
 **/
interface ReleaseBizService {
    fun create(systemName: String, createAppReleaseRequest: CreateAppReleaseRequest): AppRelease
    fun delete(systemName: String, sn: String): Boolean
    fun update(systemName: String, sn: String, updateAppReleaseRequest: UpdateAppReleaseRequest): AppRelease
    fun find(systemName: String, sn: String): AppRelease?
    fun searchReleasablePaginated(systemName: String, current: Long, pageSize: Long, request: SearchAppReleasableRequest): Pagination<AppReleaseRecordWithOwner>
    fun searchPaginatedUnderApp(
        systemName: String,
        current: Long,
        pageSize: Long,
        request: SearchAppReleaseRequest
    ): Pagination<AppReleaseRecordWithOwner>

    fun searchPaginated(
        searchOrgAppReleaseRequest: SearchOrgAppReleaseRequest,
        current: Long,
        pageSize: Long,
    ): Pagination<AppReleaseRecordWithOwner>

    fun close(systemName: String, sn: String): Boolean
    fun release(systemName: String, sn: String, instSn: String): Boolean
    fun findInstance(systemName: String, releaseSn: String, instanceSn: String): AppReleaseInst?

    fun findInstanceLatest(systemName: String, releaseSn: String): AppReleaseInst?

    fun compareAppReleaseInst(
        systemName: String,
        releaseSn: String,
        from: String,
        to: String?,
    ): AppReleaseInst.ComparedResult

    fun findItemVersion(
        systemName: String,
        releaseSn: String,
        instanceSn: String?,
        itemSn: String,
    ): AppReleaseInst.Version

    fun findInstancePaginated(systemName: String, releaseSn: String, current: Long, pageSize: Long): Pagination<AppReleaseInst>
    fun findMetadata(systemName: String, releaseSn: String, workflowSn: String): List<WorkflowMetadata>
    fun findInstanceByTrigger(systemName: String, trigger: AppReleaseInst.Trigger): AppReleaseInst?

    fun findAllMember(systemName: String, releaseSn: String): Map<ResourcePlayer, List<Role>>

    fun listMembers(systemName: String, releaseSn: String, current: Long, pageSize: Long): Pagination<MemberVO>

    fun updateMemberRole(systemName: String, releaseSn: String, userId: String, roleNameList: List<String>)

    fun updateRoleMember(systemName: String, releaseSn: String, roleName: String, playerList: List<ResourcePlayerRequest>)

    fun transferOwner(systemName: String, releaseSn: String, userId: String)

    fun listAttachedChangeRequests(appName: String, releaseSn: String, current: Long, pageSize: Long): Pagination<ChangeRequest>

    fun attachChangeRequests(appName: String, releaseSn: String, changeRequestSnList: List<String>): Boolean

    fun detachChangeRequests(appName: String, releaseSn: String, changeRequestSnList: List<String>): Boolean

    fun listReleaseExecutions(appName: String, releaseSn: String, query : MetadataSearch): Pagination<ReleaseExecutionVO>
}