package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.AckAddonService
import com.alibabacloud.devops.appstack.apps.biz.service.client.api.ResourceProxyApi
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkBizWithCode
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.model.request.resource.AckAddonInstallRequest
import com.alibabacloud.devops.appstack.libs.org.spring.boot.starter.service.OrgFacades
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import javax.annotation.PostConstruct

@Service
@Slf4k
open class AckAddonServiceImpl : AckAddonService {

    @Autowired
    lateinit var resourceProxyApi: ResourceProxyApi

    @Autowired
    lateinit var orgFacades: OrgFacades

    private lateinit var dashboardsMandarinMap: Map<String, String>

    @PostConstruct
    fun initSchema() {
        dashboardsMandarinMap = jacksonObjectMapper().readValue(
            javaClass.getResource("/arms/tab-mandarin-map.json").readText()
        )
    }

    override fun describeClusterAddonsUpgradeStatus(
        clusterId: String,
    ): Map<String, String> {
        val describeClusterAddonsUpgradeStatus =
            resourceProxyApi.describeClusterAddonsUpgradeStatus(AuthUtil.getHeaders(), getParentAliyunPkId(), clusterId)
        checkBizWithCode(describeClusterAddonsUpgradeStatus.success && describeClusterAddonsUpgradeStatus.data != null) {
            ErrorCode.AS_ACK_ADDON_DESCRIBE_ADDONS_FAILED
        }
        return describeClusterAddonsUpgradeStatus.data!!
    }

    private fun getParentAliyunPkId(): String {
        val aliyunPkId = getAliyunPkId()
        checkExists(aliyunPkId) {
            ErrorCode.AS_ACK_ADDON_ALIYUNPKID_NOT_FOUND
        }
        logger.info("getAliyunPkId aliyunPkId:$aliyunPkId")

        val parentAliyunPkId = getParentAliyunPkId(aliyunPkId)
        checkExists(parentAliyunPkId) {
            ErrorCode.AS_ACK_ADDON_ALIYUNPKID_NOT_FOUND
        }
        logger.info("getParentAliyunPkId parentAliyunPkId:$parentAliyunPkId")

        return parentAliyunPkId
    }

    override fun installClusterAddons(ackAddonInstallRequest: AckAddonInstallRequest) {
        resourceProxyApi.installClusterAddons(AuthUtil.getHeaders(), ackAddonInstallRequest.apply {
            aliyunPK = getParentAliyunPkId()
        })
    }

    override fun listDashboards(clusterId: String, regionId: String): Map<String, String> {
        val listDashboards =
            resourceProxyApi.listDashboards(AuthUtil.getHeaders(), getParentAliyunPkId(), clusterId, regionId)
        checkBizWithCode(listDashboards.success && listDashboards.data != null) {
            ErrorCode.AS_ACK_ADDON_DESCRIBE_ADDONS_FAILED
        }
        return listDashboards.data!!.mapKeys { dashboardsMandarinMap.getOrDefault(it.key, it.key) }
    }

    private fun getAliyunPkId(): String? {
        val aliens = orgFacades.userFacade.getAliens(userId = AuthUtil.getUserId())
        logger.info("getAliyunPkId aliens:{}", jacksonObjectMapper().writeValueAsString(aliens))
        return aliens?.get(0)?.openId
    }

    private fun getParentAliyunPkId(pk: String?): String? {
        val user = orgFacades.userFacade.getUserByAliyunPk(pk = pk)
        logger.info("getUserByAliyunPk aliens:{}", jacksonObjectMapper().writeValueAsString(user))
        if (user?.parentPk == null) {
            return null
        } else if (user.parentPk!!.length < 16) {
            return user.pk
        }
        return user.parentPk
    }
}