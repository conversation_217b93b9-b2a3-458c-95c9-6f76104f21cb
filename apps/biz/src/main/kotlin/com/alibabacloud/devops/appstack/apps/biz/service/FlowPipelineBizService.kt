package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.ExecuteReleaseStagePipelineRequest


/**
 * <AUTHOR>
 * @create 2023/11/9 8:18 PM
 **/
interface FlowPipelineBizService {

    fun executeStagePipeline(request: ExecuteReleaseStagePipelineRequest): Map<String, Any>

    fun getRunInfo(pipelineId: String, pipelineRunId: String): Map<String, Any>

    fun getAppMembers(roleNameList: List<String>, pipelineId: String): List<String>

}