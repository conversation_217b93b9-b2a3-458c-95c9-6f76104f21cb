package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import com.alibabacloud.devops.appstack.apps.biz.service.DeployGroupBizService
import com.alibabacloud.devops.appstack.apps.biz.service.ResourceBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.request.ng.ResourceInstanceQuery
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import com.alibabacloud.devops.appstack.libs.model.vo.ng.DeployGroup
import com.alibabacloud.devops.appstack.libs.model.vo.ng.ResourceInstance
import com.alibabacloud.devops.appstack.libs.model.vo.ng.toStandardVo
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.api.annotations.ParameterObject
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2023-09-01 16:28
 * @version: ResourceApi, v0.1
 **/
@Tag(name = "部署资源", description = "部署资源相关 API")
@RestController
@RequestMapping("/api/v2")
open class ResourceApi {

    @Autowired
    private lateinit var resourceBizService: ResourceBizService

    @Autowired
    lateinit var deployGroupBizService: DeployGroupBizService

    @VpcApi
    @Operation(summary = "查找资源实例", operationId = "GetResourceInstance")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已按名称查找到资源实例"),
            ApiResponse(responseCode = "404", description = "未查找到资源实例", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @GetMapping("/pools/{poolName}/instances/{instanceName}")
    fun findResourceInstance(
        @PathVariable("poolName") poolName: String,
        @PathVariable("instanceName") instanceName: String,
    ): ResourceInstance {
        val rawRecord = resourceBizService.findInstance(poolName = poolName, instanceName = instanceName)
            ?: throw BizException(ErrorCode.AS_RES_INST_NOT_FOUND)
        return rawRecord.toStandardVo()
    }

    @VpcApi
    @Operation(summary = "查找资源实例列表", operationId = "ListResourceInstances")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "分页查找完成（包括未查找到对象的情况）"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @GetMapping("/pools/{poolName}/instances")
    fun listResourceInstances(
        @PathVariable("poolName") poolName: String,
        @ParameterObject query: ResourceInstanceQuery
    ): PageList<ResourceInstance> {
        val rawPage = resourceBizService.listInstances(poolName, query)
        return PageList(
            nextToken = rawPage.nextToken,
            data = rawPage.data.map { it.toStandardVo() }
        )
    }

    @VpcApi
    @Operation(summary = "查找部署组详情", operationId = "GetDeployGroup")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已按名称查找到部署组"),
            ApiResponse(responseCode = "404", description = "未查找到部署组", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @GetMapping("/pools/{poolName}/deployGroups/{deployGroupName}")
    fun getDeployGroup(
        @PathVariable("poolName") poolName: String,
        @PathVariable("deployGroupName") deployGroupName: String
    ): DeployGroup {
        val rawDeployGroup = deployGroupBizService.find(poolName = poolName, groupName = deployGroupName)
        return rawDeployGroup.toVO(emptyList()).toStandardVo()
    }

}