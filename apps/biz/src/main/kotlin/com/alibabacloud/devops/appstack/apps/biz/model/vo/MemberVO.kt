package com.alibabacloud.devops.appstack.apps.biz.model.vo

import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.ResourcePlayer
import com.alibabacloud.devops.iam.model.Role
import io.swagger.v3.oas.annotations.media.Schema

/**
 * <AUTHOR> <EMAIL>
 * @version : AppMemberVO, v0.1
 * @date : 2022-01-07 11:02
 **/
data class MemberVO(
    @field:Schema(
        description = "成员 ID（如用户 ID），含义需要根据 type 字段确定",
        example = "bd9e3c6d-624f-4580-af7d-c5e26f1ed0f0",
        type = "string"
    )
    var id: String,
    @field:Schema(
        description = "成员类型，如用户（User）、企业（Org）等，用于辅助判断 id 字段的类型",
        example = "User",
        type = "string",
        allowableValues = ["Member", "User", "Team", "Group", "Org"]
    )
    var type: SubjectType,
    @field:Schema(
        description = "成员展示名",
        example = "示例成员名",
        type = "string"
    )
    var displayName: String,
    @field:Schema(
        description = "成员描述",
        example = "示例描述",
        type = "string"
    )
    var description: String,
    @field:Schema(
        description = "成员头像 URL",
        type = "string"
    )
    var avatar: String,
    @field:Schema(
        description = "成员所具有的角色列表",
        type = "array"
    )
    var roleList: List<RoleVO>,
)

data class RoleVO(
    @field:Schema(
        description = "角色的唯一标识名",
        example = "owner",
        type = "string"
    )
    var name: String,
    @field:Schema(
        description = "角色的展示名",
        example = "拥有者",
        type = "string"
    )
    val displayName: String,
)

fun MemberVO(resourcePlayer: ResourcePlayer, roleList: List<Role>): MemberVO {
    val roles = roleList.map { RoleVO(name = it.name, displayName = it.displayName) }
    return MemberVO(
        id = resourcePlayer.id,
        type = resourcePlayer.type,
        displayName = resourcePlayer.displayName,
        description = resourcePlayer.description ?: "",
        avatar = resourcePlayer.avatar ?: "",
        roleList = roles,
    )
}