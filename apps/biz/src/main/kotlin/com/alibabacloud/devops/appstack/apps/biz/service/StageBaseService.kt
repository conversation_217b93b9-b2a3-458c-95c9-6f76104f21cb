package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ExecutePipelineResult
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStage
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageBriefVO
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageInstance
import com.alibabacloud.devops.appstack.libs.model.request.PaginationQuery
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.*
import com.alibabacloud.devops.appstack.libs.model.vo.ReleaseIntegratedMetadata

/**
 * <AUTHOR>
 * @date 2022-06-28
 */
interface StageBaseService {
    fun create(appName: String, releaseWorkflowSn: String, request: CreateReleaseStageBizRequest): ReleaseStage
    fun findAll(appName: String, releaseWorkflowSn: String): List<ReleaseStage>
    fun find(appName: String, releaseWorkflowSn: String, releaseStageSn: String): ReleaseStage
    fun findBrief(appName: String, releaseWorkflowSn: String, releaseStageSn: String): ReleaseStageBriefVO
    fun update(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        request: UpdateReleaseStageRequest
    ): ReleaseStage

    fun delete(appName: String, releaseWorkflowSn: String, releaseStageSn: String, commitMessage: String? = ""): ReleaseStage
    fun updatePipeline(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        request: UpdateReleaseStagePipelineRequest
    ): ReleaseStage

    fun executePipeline(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        request: ExecuteReleaseStagePipelineRequest
    ): ExecutePipelineResult

    fun findReleaseStageCrMetadata(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        number: Long
    ): ReleaseIntegratedMetadata?

    fun findPreviousMetadata(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String
    ): ReleaseIntegratedMetadata?

    fun findStageEnvList(releaseStageSn: String): List<String>
    fun findVariableGroups(appName: String, releaseWorkflowSn: String, releaseStageSn: String): List<VariableGroup>
    fun listReleaseStageInstance(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        paginationQuery: PaginationQuery
    ): PageList<ReleaseStageInstance>

}