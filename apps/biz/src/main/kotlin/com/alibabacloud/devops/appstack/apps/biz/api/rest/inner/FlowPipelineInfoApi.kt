package com.alibabacloud.devops.appstack.apps.biz.api.rest.inner

import com.alibabacloud.devops.appstack.apps.biz.service.FlowPipelineBizService
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Hidden
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR> <EMAIL>
 * @version : FlowPipelineApi, v0.1
 * @date : 2024-06-18 16:17
 **/
@Hidden
@Tag(name = "flowPipelineApi", description = "流水线相关 Inner API")
@RestController
@RequestMapping("/innerapi/flow/pipeline")
class FlowPipelineInfoApi {

    @Autowired
    lateinit var flowPipelineBizService: FlowPipelineBizService

    @Operation(summary = "获取流水线关联的应用成员")
    @GetMapping("/members")
    fun getAppMembers(
        @RequestParam("roleNameList") roleNameList: List<String>,
        @RequestParam("pipelineId") pipelineId: String,
    ): Response<List<String>> {
        return Response.success(flowPipelineBizService.getAppMembers(roleNameList, pipelineId))
    }
}