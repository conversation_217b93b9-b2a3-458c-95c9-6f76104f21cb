package com.alibabacloud.devops.appstack.apps.biz.api.rest.hook.splc

import com.alibabacloud.devops.appstack.apps.biz.service.AuditItemBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.model.`do`.audit.AuditItem
import com.alibabacloud.devops.appstack.libs.model.request.audit.splc.AuditCallbackRequest
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.util.MultiValueMap
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-11-28 15:48
 * @version: SplcCallbackApi, v0.1
 **/
@Tag(name = "SplcCallbackApi", description = "splc 回调 API")
@RestController
@RequestMapping("/callbackapi/splc")
@Slf4k
class SplcCallbackApi {

    @Autowired
    lateinit var auditItemBizService: AuditItemBizService

    @PostMapping("/code/review/callback")
    fun callback(
        @RequestParam("orgId") orgId: String,
        @RequestParam("userId") userId: String,
        @RequestParam("crSn") crSn: String,
        @RequestBody splcCallbackRequest: MultiValueMap<String, String>,
    ) {
        logger.info(
            "SplcCallbackApi callback orgId:$orgId userId:$userId crSn:$crSn req:${
                jacksonObjectMapper().writeValueAsString(
                    splcCallbackRequest
                )
            }"
        )
        AuthThreadContext.setTenant(orgId)
        AuthThreadContext.setUserId(userId)
        val callBackParam =
            jacksonObjectMapper().readValue(
                (splcCallbackRequest["callBackParam"] as List<String>)[0],
                CallBackParam::class.java
            )
        auditItemBizService.callback(
            AuditCallbackRequest(
                AuditItem.Type.CODE_SPLC,
                AuditItem.RefType.CR,
                crSn,
                callBackParam.data
            )
        )
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class CallBackParam(
    val data: CallBackParamData?,
    val success: Boolean?,
) {
    @JsonIgnoreProperties(ignoreUnknown = true)
    data class CallBackParamData(
        val flowId: String?,
        val reviewUrl: String?,
        val reviewResult: String?,
    )
}