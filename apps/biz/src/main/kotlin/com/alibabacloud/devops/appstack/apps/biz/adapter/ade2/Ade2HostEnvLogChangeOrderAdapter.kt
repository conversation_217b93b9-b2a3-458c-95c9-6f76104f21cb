package com.alibabacloud.devops.appstack.apps.biz.adapter.ade2

import com.alibabacloud.devops.appstack.apps.biz.adapter.EnvLogChangeOrderAdapter
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.Task
import com.alibabacloud.devops.appstack.libs.model.event.OpLogCode
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2022-08-20 17:50
 * @version: Ade2HostEnvLogChangeOrderAdapter, v0.1
 **/
@Service
class Ade2HostEnvLogChangeOrderAdapter : EnvLogChangeOrderAdapter {

    override fun buildScaleLog(task: Task): List<OpLogCode> {
        // TODO implementation
        return listOf()
    }

}