package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.apps.biz.model.ext.ChangeOrderInputPatchValueInfoDTO
import com.alibabacloud.devops.appstack.apps.biz.model.vo.DeliveryDiffInfo
import com.alibabacloud.devops.appstack.apps.biz.model.vo.DeployDiffInfo
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderForm
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderInput
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderInputForm
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateChangeOrderInputRequest
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderCheckVO

/**
 * <AUTHOR>
 * @date 2022-02-22
 */
interface ChangeOrderInputBizService {
    fun find(sn: String): ChangeOrderInput?
    fun findAllForms(appName: String, envNameList: List<String>, type: ChangeOrder.Type, sha: String ?= null): List<ChangeOrderInputForm>
    fun create(createChangeOrderInputRequest: CreateChangeOrderInputRequest): ChangeOrderForm
    fun findDeliveries(sn: String, envName: String, locator: String): String
    fun diffDelivery(sn: String, appName: String, envName: String, locator: String): DeliveryDiffInfo
    fun check(appName: String, envNames: List<String>, type: ChangeOrder.Type): ChangeOrderCheckVO
    fun diffDeployInput(appName: String, envName: String): List<DeployDiffInfo>

    fun getPatchValues(env: Env, type: ChangeOrder.Type, customPatchValues: Map<String, String>, sha: String? = null): ChangeOrderInputPatchValueInfoDTO
}