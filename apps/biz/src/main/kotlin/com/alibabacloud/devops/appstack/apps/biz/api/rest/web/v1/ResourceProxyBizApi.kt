package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.ResourceProxyBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.AckClusterInfo
import com.alibabacloud.devops.appstack.libs.model.request.resourcemanager.AccessKeyRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.KubernetesHealthyVO
import com.alibabacloud.devops.appstack.libs.model.vo.KubernetesObjectInfoVO
import com.alibabacloud.devops.appstack.libs.model.vo.PodInfoVO
import io.fabric8.kubernetes.api.model.EventList
import io.fabric8.kubernetes.api.model.NamespaceList
import io.fabric8.kubernetes.api.model.NodeList
import io.fabric8.kubernetes.api.model.Pod
import io.fabric8.kubernetes.api.model.PodList
import io.fabric8.kubernetes.api.model.apps.Deployment
import io.fabric8.kubernetes.api.model.metrics.v1beta1.NodeMetricsList
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-04-12 15:38
 * @version: ResourceProxyBizApi, v0.1
 **/
@Tag(name = "ResourceProxy", description = "集群资源代理查询 API")
@RestController
@RequestMapping("/api/v1")
open class ResourceProxyBizApi {
    @Autowired
    lateinit var resourceProxyBizService: ResourceProxyBizService

    @PostMapping("/ack/clusters/{clusterId}")
    fun findAckCluster(
        @PathVariable clusterId: String,
        @RequestBody accessKeyRequest: AccessKeyRequest
    ): Response<AckClusterInfo> {
        return Response.success(resourceProxyBizService.findAckCluster(clusterId, accessKeyRequest))
    }

    @GetMapping("/{resourcePath}/namespaces")
    fun findAllNamespace(@PathVariable resourcePath: String): Response<NamespaceList> {
        return Response.success(resourceProxyBizService.findAllNamespace(resourcePath))
    }

    @GetMapping("/{resourcePath}/nodes")
    fun findAllNodes(
        @PathVariable resourcePath: String,
        @RequestParam("limit", required = false, defaultValue = "10") limit: Int,
        @RequestParam("continueVal", required = false) continueVal: String?
    ): Response<NodeList> {
        return Response.success(resourceProxyBizService.findAllNodes(resourcePath, continueVal, limit))
    }


    @GetMapping("/{resourcePath}/nodes:top")
    fun findAllNodeMetrics(@PathVariable resourcePath: String): Response<NodeMetricsList> {
        return Response.success(resourceProxyBizService.findAllNodeMetrics(resourcePath))
    }

    @GetMapping("/{resourcePath}/{namespace}/pods")
    fun findAllPod(
        @PathVariable resourcePath: String,
        @PathVariable namespace: String,
        @RequestParam("limit", required = false, defaultValue = "10") limit: Int,
        @RequestParam("continueVal", required = false) continueVal: String?
    ): Response<PodList> {
        return Response.success(resourceProxyBizService.findAllPod(resourcePath, namespace, limit, continueVal))
    }

    @GetMapping("/{resourcePath}/{namespace}/pods/{name}")
    fun findPod(
        @PathVariable resourcePath: String,
        @PathVariable namespace: String,
        @PathVariable name: String
    ): Response<Pod> {
        return Response.success(resourceProxyBizService.findPod(resourcePath, namespace, name))
    }

    @GetMapping("/{resourcePath}/{namespace}/pods/{name}:event")
    fun findAllPodEvent(
        @PathVariable resourcePath: String,
        @PathVariable namespace: String,
        @PathVariable name: String
    ): Response<EventList> {
        return Response.success(resourceProxyBizService.findAllPodEvent(resourcePath, namespace, name))
    }

    @GetMapping("/{resourcePath}/{namespace}/pods/{name}/info")
    fun findPodInfo(
        @PathVariable resourcePath: String,
        @PathVariable namespace: String,
        @PathVariable name: String,
        @RequestParam("taskSn", required = false) taskSn: String?,
    ): Response<PodInfoVO> {
        return Response.success(resourceProxyBizService.findPodInfo(resourcePath, namespace, name, taskSn))
    }

    @GetMapping("/{resourcePath}/{namespace}/{kind}/{name}/info")
    fun describeKubernetesObjectInfo(
        @PathVariable resourcePath: String,
        @PathVariable namespace: String,
        @PathVariable kind: String,
        @PathVariable name: String,
        @RequestParam("taskSn", required = false) taskSn: String? = null
    ): Response<KubernetesObjectInfoVO> {
        return Response.success(
            resourceProxyBizService.describeKubernetesObjectInfo(
                resourcePath = resourcePath,
                namespace = namespace,
                kind = kind,
                name = name,
                taskSn = taskSn
            )
        )
    }

    @GetMapping("/deployments/{resourcePath}")
    fun findDeployment(@PathVariable resourcePath: String): Response<Deployment> {
        return Response.success(resourceProxyBizService.find(resourcePath))
    }

    @GetMapping("/{resourcePath}/{namespace}/pods/{name}/containers/{container}:logs")
    fun fetchPodContainerLog(
        @PathVariable resourcePath: String,
        @PathVariable namespace: String,
        @PathVariable name: String,
        @PathVariable container: String,
        @RequestParam(required = false, defaultValue = "1000") tailingLines: Int
    ): Response<String> {
        return Response.success(
            resourceProxyBizService.fetchPodContainerLog(
                resourcePath,
                namespace,
                name,
                container,
                tailingLines
            )
        )
    }

    @Deprecated(message = "前端替换后删除")
    @GetMapping("/{resourcePath}/health")
    fun probeKubernetesHealth(@PathVariable resourcePath: String): Response<Boolean> {
        return Response.success(resourceProxyBizService.probeKubernetesHealth(resourcePath).healthy)
    }

    @GetMapping("/{resourcePath}/healthy")
    fun probeKubernetesHealthy(@PathVariable resourcePath: String): Response<KubernetesHealthyVO> {
        return Response.success(resourceProxyBizService.probeKubernetesHealth(resourcePath))
    }
}