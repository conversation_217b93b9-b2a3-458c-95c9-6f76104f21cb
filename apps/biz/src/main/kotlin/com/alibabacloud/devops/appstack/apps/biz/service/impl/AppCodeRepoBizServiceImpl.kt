package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.annotation.AllCrShouldClosed
import com.alibabacloud.devops.appstack.apps.biz.annotation.ApiMetricCounter
import com.alibabacloud.devops.appstack.apps.biz.service.AppCodeRepoBizService
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.constant.LockConfig
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.filter.TraceFilter
import com.alibabacloud.devops.appstack.libs.common.util.PageUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppCodeRepo
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ObjectType
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ServiceConnectionBindDTO
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AppCodeAudit
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.org.event.CodeRepo
import com.alibabacloud.devops.appstack.libs.model.request.CreateAppCodeRepoRequest
import com.alibabacloud.devops.appstack.libs.model.request.PaginationQuery
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppCodeRepoRequest
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.alibabacloud.devops.appstack.resource.manager.spring.boot.starter.ResourceManagerFacades
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2022-06-23 15:16
 * @version: AppCodeRepoBizServiceImpl, v0.1
 **/
@Service
open class AppCodeRepoBizServiceImpl : AppCodeRepoBizService {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var resourceManagerFacades: ResourceManagerFacades

    @Autowired
    lateinit var auditLogService: AuditLogService

    @ApiMetricCounter(metricName = "appCodeRepo_operation", methodTag = "create")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_BASIC_SET, resourceArgIndex = 0),
        ]
    )
    override fun create(appName: String, createAppCodeRepoRequest: CreateAppCodeRepoRequest): AppCodeRepo {
        validate(appName, createAppCodeRepoRequest.identifier)
        val appCodeRepo = coreFacades.appCodeRepoFacade.create(appName, createAppCodeRepoRequest)
        bindServiceConnection(appCodeRepo)
        afterAppCodeRepoChanged(appName)
        val audit = AppCodeAudit(
            target = AuditTarget(id = appName, name = appName),
            codeRepo = CodeRepo(type = appCodeRepo.repoContext.repoType, name = appCodeRepo.name)
        )
        auditLogService.log(OrgEventType.APP_CODE_REPO_BIND, audit)
        return appCodeRepo
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_BASIC_SET, resourceArgIndex = 0),
        ]
    )
    @ApiMetricCounter(metricName = "appCodeRepo_operation", methodTag = "delete")
    @AllCrShouldClosed
    override fun delete(appName: String, sn: String) {
        val previous = find(appName, sn)
        checkExists(previous) {
            ErrorCode.AS_APP_CODE_REPO_NOT_FOUND
        }
        coreFacades.appCodeRepoFacade.delete(appName, sn)
        unbindServiceConnection(previous)
        afterAppCodeRepoChanged(appName)
        val audit = AppCodeAudit(
            target = AuditTarget(id = appName, name = appName),
            codeRepo = CodeRepo(type = previous.repoContext.repoType, name = previous.name)
        )
        auditLogService.log(OrgEventType.APP_CODE_REPO_UNBIND, audit)
    }

    @ApiMetricCounter(metricName = "appCodeRepo_operation", methodTag = "update")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_BASIC_SET, resourceArgIndex = 0),
        ]
    )
    @AllCrShouldClosed
    override fun update(
        appName: String,
        sn: String,
        updateAppCodeRepoRequest: UpdateAppCodeRepoRequest
    ): AppCodeRepo {
        val previous = find(appName, sn)
        checkExists(previous) {
            ErrorCode.AS_APP_CODE_REPO_NOT_FOUND
        }
        /*
        // 应用代码源冲突检测
        val codeRepoIdentifiers = findAllWithAppSource(appName).map { it.identifier }
        checkBizWithCode(!codeRepoIdentifiers.contains(updateAppCodeRepoRequest.identifier), mapOf(
            "map" to ""
        )) { ErrorCode.AS_APP_CODE_REPO_IDENTIFIER_DUPLICATED }
         */
        val after = coreFacades.appCodeRepoFacade.update(appName, sn, updateAppCodeRepoRequest)
        unbindServiceConnection(previous)
        bindServiceConnection(after)
        afterAppCodeRepoChanged(appName)
        val audit = AppCodeAudit(
            target = AuditTarget(id = appName, name = appName),
            previousCodeRepo = CodeRepo(type = previous.repoContext.repoType, name = previous.name),
            codeRepo = CodeRepo(type = after.repoContext.repoType, name = after.name)
        )
        auditLogService.log(OrgEventType.APP_CODE_REPO_MODIFY, audit)
        return after
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun findAll(appName: String): List<AppCodeRepo> {
        return coreFacades.appCodeRepoFacade.findAll(appName)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun list(appName: String, query: PaginationQuery): PageList<AppCodeRepo> {
        if (!query.checkPassed()) {
            throw BizException(ErrorCode.AS_PAGINATION_PARAM_INVALID)
        }
        return coreFacades.appCodeRepoFacadeV2.list(appName, query)
    }

    override fun find(appName: String, sn: String): AppCodeRepo {
        val appCodeRepo = coreFacades.appCodeRepoFacade.find(appName, sn)
        checkExists(appCodeRepo) {
            ErrorCode.AS_APP_CODE_REPO_NOT_FOUND
        }
        return appCodeRepo
    }

    private fun unbindServiceConnection(appCodeRepo: AppCodeRepo) {
        if (appCodeRepo.connectionConfig.connectionId.isEmpty()) {
            logger.info("AppCodeRepo[${appCodeRepo.sn}] has no connectionId, skip unbind")
            return
        }
        val result = resourceManagerFacades.serviceConnectionFacade.unbind(
            ServiceConnectionBindDTO(
                connectionConfigType = appCodeRepo.connectionConfig.connectionType,
                connectionId = appCodeRepo.connectionConfig.connectionId,
                objectType = ObjectType.appStackAppCodeRepo,
                objectId = getAppCodeRepoServiceConnectionObjectId(appCodeRepo),
            )
        )
        logger.info("Unbind AppCodeRepo[${appCodeRepo.sn}] ServiceConnection[${appCodeRepo.connectionConfig}]: $result")
    }

    private fun bindServiceConnection(appCodeRepo: AppCodeRepo) {
        if (appCodeRepo.connectionConfig.connectionId.isEmpty()) {
            logger.info("AppCodeRepo[${appCodeRepo.sn}] has no connectionId, skip bind")
            return
        }
        val result = resourceManagerFacades.serviceConnectionFacade.bind(
            ServiceConnectionBindDTO(
                connectionConfigType = appCodeRepo.connectionConfig.connectionType,
                connectionId = appCodeRepo.connectionConfig.connectionId,
                objectType = ObjectType.appStackAppCodeRepo,
                objectId = getAppCodeRepoServiceConnectionObjectId(appCodeRepo),
            )
        )
        logger.info("Bind AppCodeRepo[${appCodeRepo.sn}] ServiceConnection[${appCodeRepo.connectionConfig}]: $result")
    }

    private fun getAppCodeRepoServiceConnectionObjectId(appCodeRepo: AppCodeRepo) =
        "${appCodeRepo.appName}::${appCodeRepo.sn}"

    private fun afterAppCodeRepoChanged(appName: String) {
        logger.info("App[$appName] AppCodeRepo changed")
        val ctx = AuthThreadContext.export()
        GlobalScope.launch {
            // prepare context
            AuthThreadContext.replaceAll(ctx)
            MDC.put(TraceFilter.TRACE_CONTEXT_KEY, AuthThreadContext.getTraceId())
            // 当应用代码库更新后，刷新研发阶段流水线
            // TODO 当前只刷新变更的流水线，是否刷新发布的流水线后续产品再对
            try {
                workflowControllerFacade.releaseWorkflowFacade.findAll(appName)
                    .forEach { releaseWorkflow ->
                        val stageCount = releaseWorkflow.releaseStages.size
                        val releaseKey = workflowControllerFacade.releaseWorkflowFacade.tryLock(releaseWorkflow.sn!!,
                            LockConfig.DEFAULT_TRY_LOCK_TIME, stageCount * LockConfig.DEFAULT_REFRESH_PIPELINE_EXPIRATION_TIME)
                        if (releaseKey != null) {
                            releaseWorkflow.releaseStages.forEach { releaseStage ->
                                releaseStage.pipeline?.let {
                                    logger.info("Refresh releaseStage[${releaseStage.sn}]'s pipeline start")
                                    try {
                                        workflowControllerFacade.releaseStageFacade.refreshPipeline(releaseStage.sn!!)
                                    } catch (e: Throwable) {
                                        // 更新失败不影响其他流水线的更新
                                        logger.error("Refresh releaseStage[${releaseStage.sn}]'s pipeline error", e)
                                    }
                                    logger.info("Refresh releaseStage[${releaseStage.sn}]'s pipeline finish")
                                }
                            }
                            workflowControllerFacade.releaseWorkflowFacade.unLock(releaseWorkflow.sn!!, releaseKey)
                            workflowControllerFacade.releaseWorkflowFacade.upsertReleaseWorkflowRevision(releaseWorkflow.sn!!, "更新流水线源")
                        } else {
                            logger.error("Refresh releaseWorkflow[${releaseWorkflow.sn}]'s pipeline failed: failed to get lock")
                        }
                    }
            } catch (e: Exception) {
                logger.error("App[$appName] AppCodeRepo changed exception", e)
            }
        }
    }

    fun validate(appName: String, newIdentifier: String){
        val codeRepoIds = coreFacades.appCodeRepoFacade.findAll(appName).map { it.identifier }
        if(codeRepoIds.isNotEmpty()){
            throw BizException(errorCode = ErrorCode.AS_APP_CODE_REPO_NUMBER_LIMIT)
        }

        val artifactRepoIds = coreFacades.appArtifactRepoFacade.findAll(appName).map { it.identifier }
        if(codeRepoIds.contains(newIdentifier) || artifactRepoIds.contains(newIdentifier)){
            throw BizException(errorCode = ErrorCode.AS_APP_CODE_REPO_IDENTIFIER_DUPLICATED)
        }
    }

}