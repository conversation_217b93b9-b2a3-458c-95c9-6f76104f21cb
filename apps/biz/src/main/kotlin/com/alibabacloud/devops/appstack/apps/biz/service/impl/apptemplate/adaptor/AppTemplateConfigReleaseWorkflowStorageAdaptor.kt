package com.alibabacloud.devops.appstack.apps.biz.service.impl.apptemplate.adaptor

import com.alibabacloud.devops.appstack.apps.biz.model.ext.toModel
import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateConfigPO
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateConfigStorageAdaptor
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.ReleaseWorkflowConfiguration
import com.alibabacloud.devops.appstack.libs.model.`do`.globalvar.GlobalVarUsageReference
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.AbstractConfigurationUpsertRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.ReleaseWorkflowConfigurationUpsertRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateConfigRequest
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR> <EMAIL>
 * @version : AppTemplateConfigReleaseWorkflowTemplateStorageAdaptor, v0.1
 * @date : 2023-10-18 20:48
 **/
@Service
@Slf4k
open class AppTemplateConfigReleaseWorkflowStorageAdaptor :
    AppTemplateConfigStorageAdaptor<ReleaseWorkflowConfiguration>() {

    @Autowired
    private lateinit var coreFacades: CoreFacades

    @Autowired
    private lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var auditLogService: AuditLogService

    override fun insert(appTemplate: AppTemplate, request: UpsertAppTemplateConfigRequest): AppTemplateConfig {
        val configurationRequest = request.configuration as ReleaseWorkflowConfigurationUpsertRequest
        val appTemplateWorkflowList =
            workflowControllerFacade.appTemplateWorkflowFacade.findByAppTemplate(appTemplate.name)
        if (configurationRequest.appTemplateWorkflowRequests.any { oneRequest -> appTemplateWorkflowList.any { template -> oneRequest.sn != template.content.sn && template.content.name == oneRequest.name } }) {
            throw BizException(ErrorCode.AS_RELEASE_WORKFLOW_NAME_DUPLICATED)
        }
        // 更新全局变量组引用，并移除无效变量组
        updateVariableGroupUsageReference(appTemplate.name, configurationRequest)
        val templates = configurationRequest.appTemplateWorkflowRequests
        templates.forEach { oneTemplate ->
            workflowControllerFacade.appTemplateWorkflowFacade.upsertByAppTemplate(
                appTemplate.name, oneTemplate
            )
        }

        return super.insert(appTemplate, request)
    }

    override fun update(
        appTemplate: AppTemplate,
        previous: AppTemplateConfig,
        request: UpsertAppTemplateConfigRequest,
    ): AppTemplateConfig {
        request.configuration?.let {
            val appTemplateName = previous.appTemplateName
            val configurationRequest = request.configuration as ReleaseWorkflowConfigurationUpsertRequest
            val appTemplateWorkflowList =
                workflowControllerFacade.appTemplateWorkflowFacade.findByAppTemplate(appTemplateName)
            if (configurationRequest.appTemplateWorkflowRequests.any { oneRequest -> appTemplateWorkflowList.any { template -> oneRequest.sn != template.content.sn && template.content.name == oneRequest.name } }) {
                throw BizException(ErrorCode.AS_RELEASE_WORKFLOW_NAME_DUPLICATED)
            }

            // 更新全局变量组引用，并移除无效变量组
            updateVariableGroupUsageReference(appTemplateName, configurationRequest)
            val templates = configurationRequest.appTemplateWorkflowRequests
            logger.info("update release workflow configuration:${jacksonObjectMapper().writeValueAsString(templates)}")
            templates.forEach { oneTemplate ->
                workflowControllerFacade.appTemplateWorkflowFacade.upsertByAppTemplate(
                    appTemplateName, oneTemplate
                )
            }
        }
        return super.update(appTemplate, previous, request)
    }

    override fun convertToModel(appTemplateConfigPO: AppTemplateConfigPO): AppTemplateConfig {
        val dto = JacksonUtil.jacksonObjectMapper()
            .readValue<ReleaseWorkflowTemplateConfigDTO>(appTemplateConfigPO.configuration!!)
        val configuration = when (dto) {
            is ReleaseWorkflowTemplateConfigV1DTO -> {
                var appTemplateWorkflowRevisionList =
                    workflowControllerFacade.appTemplateWorkflowFacade.findByAppTemplate(
                        appTemplateConfigPO.appTemplateName!!
                    )

                if (appTemplateWorkflowRevisionList.isEmpty()) {
                    appTemplateWorkflowRevisionList =
                        workflowControllerFacade.appTemplateWorkflowFacade.migrateOldData(appTemplateConfigPO.appTemplateName!!)
                }

                ReleaseWorkflowConfiguration(
                    appTemplateWorkflowList = appTemplateWorkflowRevisionList.map { oneRevision ->
                        oneRevision.content.toVO(oneRevision.revision)
                    },
                )
            }

            else -> TODO()
        }
        return appTemplateConfigPO.toModel(configuration)
    }

    override fun convertConfiguration(appTemplateName: String, configuration: AbstractConfigurationUpsertRequest): Any {
        return ReleaseWorkflowTemplateConfigV1DTO()
    }

    override fun configType(): AppTemplateConfig.TypeEnum = AppTemplateConfig.TypeEnum.ReleaseWorkflow

    override fun logAudit(appTemplate: AppTemplate) {
        auditLogService.commonLog(OrgEventType.APP_TEMPLATE_MODIFY_RELEASE_WORKFLOW_AUDIT, appTemplate.name, appTemplate.displayName)
    }

    private fun updateVariableGroupUsageReference(
        appTemplateName: String,
        configurationRequest: ReleaseWorkflowConfigurationUpsertRequest,
    ) {
        val workflowTemplates =
            workflowControllerFacade.appTemplateWorkflowFacade.findByAppTemplate(appTemplateName)
        val originVariableGroups = mutableMapOf<Pair<String, String>, List<VariableGroup>>()
        val finalVariableGroups = mutableMapOf<Pair<String, String>, List<VariableGroup>>()
        workflowTemplates.forEach { oneTemplate ->
            oneTemplate.content.releaseStageTemplate.forEach { stageTemplate ->
                originVariableGroups[Pair(oneTemplate.content.name, stageTemplate.name)] =
                    stageTemplate.variableGroups
            }
        }

        configurationRequest.appTemplateWorkflowRequests.forEach { oneReq ->
            oneReq.releaseStageTemplate.forEach {
                // 移除无效变量组
                val toRemove = mutableListOf<VariableGroup>()
                it.variableGroups.filter { it.type == VariableGroup.Type.GLOBAL }.forEach {
                    val globalVar = coreFacades.globalVarFacade.find(it.name)
                    if (globalVar == null) {
                        toRemove.add(it)
                    }
                }
                it.variableGroups.removeAll(toRemove)
                finalVariableGroups[Pair(it.name, it.name)] = it.variableGroups
            }
        }

        // 原有现没有的部分，需要删除引用
        // TODO: 改成支持批量
        val toDeleteVariableGroups = mutableListOf<Triple<String, String, VariableGroup>>()
        originVariableGroups.forEach { (k, v) ->
            if (!finalVariableGroups.contains(k)) {
                toDeleteVariableGroups.addAll(v.filter { it.type == VariableGroup.Type.GLOBAL }
                    .map { Triple(k.first, k.second, it) })
            } else {
                val final = finalVariableGroups[k]
                toDeleteVariableGroups.addAll(v.filter {
                    it.type == VariableGroup.Type.GLOBAL && !final!!.contains(
                        it
                    )
                }
                    .map { Triple(k.first, k.second, it) })
            }
        }
        toDeleteVariableGroups.forEach {
            coreFacades.globalVarFacade.deleteUsageReference(
                GlobalVarUsageReference(
                    globalVarName = it.third.name,
                    GlobalVarUsageReference.UsageRefType.AppTemplateWorkflowStage,
                    GlobalVarUsageReference.AppTemplateWorkflowStageUsageRefObject(
                        appTemplateName = appTemplateName, it.first, it.second
                    )
                )
            )
        }
        // 原没有现有的部分，需要增加引用
        // TODO: 改成支持批量
        val toUpsertVariableGroups = mutableListOf<Triple<String, String, VariableGroup>>()
        finalVariableGroups.forEach { (k, v) ->
            if (!originVariableGroups.contains(k)) {
                toUpsertVariableGroups.addAll(v.filter { it.type == VariableGroup.Type.GLOBAL }
                    .map { Triple(k.first, k.second, it) })
            } else {
                val origin = originVariableGroups[k]
                toUpsertVariableGroups.addAll(v.filter {
                    it.type == VariableGroup.Type.GLOBAL && !origin!!.contains(
                        it
                    )
                }
                    .map { Triple(k.first, k.second, it) })
            }
        }
        toUpsertVariableGroups.forEach {
            coreFacades.globalVarFacade.addUsageReference(
                GlobalVarUsageReference(
                    globalVarName = it.third.name,
                    GlobalVarUsageReference.UsageRefType.AppTemplateWorkflowStage,
                    GlobalVarUsageReference.AppTemplateWorkflowStageUsageRefObject(
                        appTemplateName = appTemplateName, it.first, it.second
                    )
                )
            )
        }
    }

    fun findPaginatedRevision(
        appTemplateName: String,
        name: String,
        current: Long,
        pageSize: Long,
    ): Pagination<Revision> {
        return workflowControllerFacade.appTemplateWorkflowFacade.findPaginatedRevision(
            appTemplateName,
            name,
            current,
            pageSize
        )
    }

    @JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "version",
        visible = true
    )
    @JsonSubTypes(
        JsonSubTypes.Type(value = ReleaseWorkflowTemplateConfigV1DTO::class, name = "v1"),
    )
    interface ReleaseWorkflowTemplateConfigDTO {
        val version: String
    }

    data class ReleaseWorkflowTemplateConfigV1DTO(
        override val version: String = "v1",
    ) : ReleaseWorkflowTemplateConfigDTO
}