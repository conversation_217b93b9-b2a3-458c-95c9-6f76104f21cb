package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.annotation.ApiMetricCounter
import com.alibabacloud.devops.appstack.apps.biz.annotation.PremiumVersionCheck
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestBizService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.apps.biz.service.OrgConfigItemBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkBizWithCode
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.IamClient
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.ChangeRequestMustBindWorkItemV1OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.OnlyViewAccessableAppV1OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.OrderEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.SortEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.ChangeRequest
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.MetadataItemContent
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.WorkflowMetadata
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.ActionType
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.ResourceType
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageInstance
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.iam.CrRole
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.org.event.CrAudit
import com.alibabacloud.devops.appstack.libs.model.request.cr.CreateChangeRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.QueryAppChangeRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.QueryChangeRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.TransferChangeOwnerRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.UpdateChangeRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.toQueryChangeRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.ChangeRequestSearch
import com.alibabacloud.devops.appstack.libs.model.request.ng.MetadataSearch
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeRequestDetailVO
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeRequestExecutionVO
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.util.StringUtils

@Service
open class ChangeRequestBizServiceImpl : ChangeRequestBizService {

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var iamService: IamService

    @Autowired
    lateinit var iamClient: IamClient

    @Autowired
    lateinit var auditLogService: AuditLogService

    @Autowired
    lateinit var orgConfigItemBizService: OrgConfigItemBizService

    @ApiMetricCounter(metricName = "appChangeRequest_operation", methodTag = "create")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_CR_CREATE),
            Access(action = Action.APP_CR_MANAGE)
        ]
    )
    @PremiumVersionCheck
    override fun create(appName: String, request: CreateChangeRequest): ChangeRequest {
        request.appName = appName
        val orgConfigItem = orgConfigItemBizService.find(OrgConfigItem.CHANGE_REQUEST_MUST_BIND_WORKITEM)
        // 勾选「变更必须关联工作项」
        if ((orgConfigItem as? ChangeRequestMustBindWorkItemV1OrgConfigItem)?.enable == true) {
            if (request.workItems.isNullOrEmpty()) {
                throw BizException(ErrorCode.AS_CR_MUST_BIND_WORKITEM)
            }
        }
        val changeRequest = workflowControllerFacade.changeRequestFacade.create(request)
        iamService.registerResource(
            ProtocolType.AppstackCr,
            changeRequest.sn!!,
            AuthUtil.getTenant(),
            changeRequest.ownerId
        )
        return changeRequest
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW),
            Access(action = Action.APP_CR_MANAGE),
            Access(action = Action.CR_VIEW, resourceArgIndex = 1),
            Access(action = Action.CR_EDIT, resourceArgIndex = 1),
            Access(action = Action.CR_CLOSE, resourceArgIndex = 1),
        ]
    )
    override fun find(appName: String, sn: String): ChangeRequestDetailVO {
        val changeRequestVO = workflowControllerFacade.changeRequestFacade.findDetail(sn)
        checkBizWithCode(changeRequestVO?.changeRequest?.appName == appName) { ErrorCode.AS_CR_NOT_EXIST }
        // 以 iam 记录的 owner 为准
        val owner = iamClient.resourceApi.find(
            ProtocolType.AppstackCr,
            sn
        ).rolePlayerList.firstOrNull { roleWithPlayer -> roleWithPlayer.name == CrRole.admin.name }?.playerList?.firstOrNull { it.type == SubjectType.User }?.id
            ?: ""
        changeRequestVO!!.changeRequest.ownerId = owner
        val allWorkflows = workflowControllerFacade.releaseWorkflowFacade.findAll(appName, ReleaseWorkflow.TypeEnum.CR)
        val latestActive =
            workflowControllerFacade.releaseWorkflowFacade.findLatestActiveWorkflow(allWorkflows.flatMap { oneWorkflow -> oneWorkflow.releaseStages.mapNotNull { oneStage -> oneStage.sn } })
        return changeRequestVO.apply {
            workflowList = allWorkflows.sortedBy {
                if (null != latestActive && it.sn == latestActive.sn) 0 else 1
            }
        }
    }

    @ApiMetricCounter(metricName = "appChangeRequest_operation", methodTag = "close")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_CR_MANAGE, resourceArgIndex = 0),
            Access(action = Action.CR_CLOSE, resourceArgIndex = 1)
        ]
    )
    @PremiumVersionCheck
    override fun close(appName: String, sn: String) {
        val changeRequestVO = workflowControllerFacade.changeRequestFacade.findDetail(sn)
        checkBizWithCode(changeRequestVO?.changeRequest?.appName == appName) { ErrorCode.AS_CR_NOT_EXIST }
        workflowControllerFacade.changeRequestFacade.close(sn)
    }

    @ApiMetricCounter(metricName = "appChangeRequest_operation", methodTag = "finish")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_CR_MANAGE, resourceArgIndex = 0),
            Access(action = Action.CR_CLOSE, resourceArgIndex = 1)
        ]
    )
    @PremiumVersionCheck
    override fun finish(appName: String, sn: String) {
        val changeRequestVO = workflowControllerFacade.changeRequestFacade.findDetail(sn)
        checkBizWithCode(changeRequestVO?.changeRequest?.appName == appName) { ErrorCode.AS_CR_NOT_EXIST }
        workflowControllerFacade.changeRequestFacade.release(sn)
    }

    @ApiMetricCounter(metricName = "appChangeRequest_operation", methodTag = "update")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_CR_MANAGE, resourceArgIndex = 0),
            Access(action = Action.CR_EDIT, resourceArgIndex = 1)
        ]
    )
    @PremiumVersionCheck
    override fun update(appName: String, sn: String, request: UpdateChangeRequest): ChangeRequest? {
        val changeRequest = workflowControllerFacade.changeRequestFacade.find(sn)
        checkBizWithCode(changeRequest.appName == appName) { ErrorCode.AS_CR_NOT_EXIST }
        if (StringUtils.hasText(request.name)) {
            changeRequest.name = request.name!!
        }
        request.autoDeleteBranchWhenEnd?.let {
            changeRequest.extInfo.autoDeleteBranchWhenEnd = it
        }
        return workflowControllerFacade.changeRequestFacade.update(changeRequest)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_CR_MANAGE, resourceArgIndex = 0),
            Access(action = Action.CR_ADMIN_TRANSFER, resourceArgIndex = 1)
        ]
    )
    @PremiumVersionCheck
    override fun transferOwner(
        appName: String,
        sn: String,
        request: TransferChangeOwnerRequest,
    ): ChangeRequest? {
        val changeRequest = workflowControllerFacade.changeRequestFacade.find(sn)
        checkBizWithCode(changeRequest.appName == appName) { ErrorCode.AS_CR_NOT_EXIST }

        iamService.updatePlayer(ProtocolType.AppstackCr, sn, changeRequest.ownerId, SubjectType.User, listOf())
        iamService.updatePlayer(ProtocolType.AppstackCr, sn, request.owner, SubjectType.User, listOf(CrRole.admin.name))
        changeRequest.ownerId = request.owner
        return workflowControllerFacade.changeRequestFacade.update(changeRequest)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW),
            Access(action = Action.APP_CR_MANAGE),
            Access(action = Action.CR_VIEW, resourceArgIndex = 2),
            Access(action = Action.CR_EDIT, resourceArgIndex = 2),
            Access(action = Action.CR_CLOSE, resourceArgIndex = 2),
        ]
    )
    override fun findReleaseMetadata(
        appName: String,
        releaseWorkflowSn: String,
        crSn: String,
    ): List<WorkflowMetadata> {
        val releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.find(releaseWorkflowSn)
        checkBizWithCode(releaseWorkflow.appName == appName) { ErrorCode.AS_RELEASE_WORKFLOW_NOT_FOUND }
        return workflowControllerFacade.changeRequestFacade.findReleaseMetadata(releaseWorkflowSn, crSn)
    }

    override fun findByOrgPaginated(
        request: QueryChangeRequest,
        current: Long,
        pageSize: Long,
        order: OrderEnum,
        sort: SortEnum,
    ): Pagination<ChangeRequest> {
        val orgConfigItem = orgConfigItemBizService.find(OrgConfigItem.ONLY_VIEW_ACCESSABLE_APP)
        // 如果勾选了【仅支持查看我有权限的应用列表】
        if ((orgConfigItem as? OnlyViewAccessableAppV1OrgConfigItem)?.enable == true) {
            // 如果查询全部但没有查看全部应用权限
            if (request.owner.isEmpty()
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_VIEW)
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_MANAGE)
            ) {
                throw BizException(ErrorCode.AS_PERMISSION_DENIED)
            }
        }
        return workflowControllerFacade.changeRequestFacade.findPaginated(request, current, pageSize, order, sort)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW),
            Access(action = Action.APP_CR_MANAGE),
        ]
    )
    override fun findByAppPaginated(
        appName: String,
        request: QueryAppChangeRequest,
        current: Long,
        pageSize: Long,
        order: OrderEnum,
        sort: SortEnum,
    ): Pagination<ChangeRequest> {
        return workflowControllerFacade.changeRequestFacade.findPaginated(
            request = request.toQueryChangeRequest(
                listOf(
                    appName
                )
            ),
            current, pageSize, order, sort
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW),
            Access(action = Action.APP_CR_MANAGE),
        ]
    )
    override fun findBySns(appName: String, sns: String): List<ChangeRequestDetailVO> {
        return sns.split(",").filter { it.isNotEmpty() }.mapNotNull {
            workflowControllerFacade.changeRequestFacade.findDetail(it)
        }
    }

    override fun list(changeRequestSearch: ChangeRequestSearch): PageList<ChangeRequest> {
        return workflowControllerFacade.changeRequestFacade.list(changeRequestSearch)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW),
            Access(action = Action.APP_CR_MANAGE),
            Access(action = Action.CR_VIEW, resourceArgIndex = 1),
            Access(action = Action.CR_EDIT, resourceArgIndex = 1),
            Access(action = Action.CR_CLOSE, resourceArgIndex = 1),
        ]
    )
    override fun listChangeRequestMetadata(
        appName: String,
        sn: String,
        query: MetadataSearch,
    ): Pagination<ChangeRequestExecutionVO> {
        if (query.orderBy.uppercase() != "ID" && query.orderBy.uppercase() != "GMTCREATE") {
            throw BizException(ErrorCode.AS_PAGINATION_PARAM_INVALID)
        }
        if (query.sort.uppercase() != "ASC" && query.sort.uppercase() != "DESC") {
            throw BizException(ErrorCode.AS_PAGINATION_PARAM_INVALID)
        }
        val releaseStage = workflowControllerFacade.releaseStageFacade.find(query.releaseStageSn)
        checkBizWithCode(releaseStage.appName == appName) { ErrorCode.AS_RELEASE_STAGE_NOT_FOUND }
        val listChangeRequestMetadata =
            workflowControllerFacade.changeRequestFacade.listChangeRequestMetadata(appName, sn, query)

        val records = listChangeRequestMetadata.records.mapNotNull {
            val state = it.getState()
            if (state == MetadataItemContent.MetadataState.NOT_FOUND) {
                null
            } else {
                ChangeRequestExecutionVO(
                    commit = ChangeRequestExecutionVO.StageMetadataCommitVO(id = it.revision!!),
                    state = when (state) {
                        MetadataItemContent.MetadataState.PASS -> {
                            ReleaseStageInstance.State.SUCCESS
                        }

                        MetadataItemContent.MetadataState.FAIL -> {
                            ReleaseStageInstance.State.FAILED
                        }

                        MetadataItemContent.MetadataState.CANCELED -> {
                            ReleaseStageInstance.State.CANCELED
                        }

                        MetadataItemContent.MetadataState.RUNNING -> {
                            ReleaseStageInstance.State.RUNNING
                        }

                        else -> {
                            ReleaseStageInstance.State.UNKNOWN
                        }
                    },
                    runNumber = it.contextSnSecond!!
                )
            }
        }
        return Pagination(
            records = records,
            total = listChangeRequestMetadata.total,
            current = listChangeRequestMetadata.current,
            pageSize = listChangeRequestMetadata.pageSize,
            pages = listChangeRequestMetadata.pages
        )
    }

}