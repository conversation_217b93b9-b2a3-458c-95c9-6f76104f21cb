package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.Label
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.InvalidChangeItem
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.Orchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.OrchestrationLabeledRevision
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.k8s.LocatorInfo
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.request.AppOrchestrationCheckRequest
import com.alibabacloud.devops.appstack.libs.model.request.AppOrchestrationCreateRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppOrchestrationRequest
import com.alibabacloud.devops.appstack.libs.model.vo.AppOrchestrationDiffVO
import javax.servlet.http.HttpServletResponse

/**
 * @author: <EMAIL>
 * @date: 2021-12-17 13:33
 * @version: OrchestrationService, v0.1
 **/
interface AppOrchestrationBizService {

    fun find(appName: String, sn: String, tagName: String?, sha: String?): AppOrchestration?

    fun findAll(appName: String): List<AppOrchestration>

    fun create(appName: String, appOrchestrationCreateRequest: AppOrchestrationCreateRequest): AppOrchestration

    fun update(
        appName: String,
        sn: String,
        updateAppOrchestrationRequest: UpdateAppOrchestrationRequest
    ): AppOrchestration

    fun updateAllBuiltin(
        appName: String,
        updateAppOrchestrationRequest: UpdateAppOrchestrationRequest
    ): AppOrchestration

    fun delete(appName: String, sn: String)

    fun findPaginatedRevision(
        appName: String,
        sn: String,
        current: Long,
        pageSize: Long
    ): Pagination<Revision>

    fun findPaginatedRevisionAndLabel(
        appName: String,
        sn: String,
        envName: String?,
        current: Long,
        pageSize: Long
    ): Pagination<OrchestrationLabeledRevision>

    fun findLatestForEnv(appName: String, envName: String): AppOrchestration

    fun compare(
        appName: String, sn: String, beforeRevisionSha: String, afterRevisionSha: String,
    ): Triple<AppOrchestrationDiffVO, AppOrchestrationDiffVO, List<DiffItem<String>>>

    fun exportAppOrchestration(
        appName: String,
        suitableResourceType: ResourceTypeEnum,
        format: Orchestration.FormatEnum,
        sha: String,
        renderType: Orchestration.RenderTypeEnum,
        profileName: String,
        response: HttpServletResponse,
    )

    fun check(
        appName: String,
        appOrchestrationCheckRequest: AppOrchestrationCheckRequest
    ): List<InvalidChangeItem>

    fun bindLabels(appName: String, sn: String, sha: String, labels: List<Label>)

    fun getLocatorList(appName: String, envName: String): List<LocatorInfo>
}