package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.HookChannelSubscribe
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.HookEvent
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.HookEventTypeVO
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.HookSendLog
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.HookSubscribeScopeType
import com.alibabacloud.devops.appstack.libs.model.event.EventResult
import com.alibabacloud.devops.appstack.libs.model.request.PaginationQuery

/**
 * <AUTHOR>
 * @create 2024/6/26 11:41 AM
 * hook相关服务
 **/
interface HookSubscribeBizService {

    /**
     * 根据sn查询渠道订阅
     */
    fun get(sn: String): HookChannelSubscribe

    /**
     * 创建渠道订阅
     */
    fun create(hookChannelSubscribe: HookChannelSubscribe): HookChannelSubscribe

    /**
     * 更新渠道订阅
     */
    fun update(sn: String, hookChannelSubscribe: HookChannelSubscribe): HookChannelSubscribe

    /**
     * 删除渠道订阅
     */
    fun delete(sn: String): Boolean

    /**
     * 根据订阅范围和订阅类型查询渠道订阅
     */
    fun findByScopeAndType(scopeType: String, scopeId: String, channelType: String): List<HookChannelSubscribe>

    /**
     * 从指定的订阅渠道发送手动触发的事件
     */
    fun invokeEvent(sn: String, event: HookEvent): EventResult

    /**
     * 分页查询订阅渠道下的事件调用日志
     * 支持指定事件类型和操作类型
     */
    fun findInvokeLogsPaginated(sn: String, query: PaginationQuery, event: String? = null, action: String? = null): PageList<HookSendLog>

    /**
     * 获取订阅渠道支持的事件类型
     */
    fun getSupportEventTypes(channelType: String, scopeType: String): List<HookEventTypeVO>
}