package com.alibabacloud.devops.appstack.apps.biz.model.ext

import com.alibabacloud.devops.appstack.apps.biz.model.AgentTask
import com.alibabacloud.devops.appstack.apps.biz.model.po.AgentTaskPO

fun AgentTaskPO.toModel(): AgentTask {
    val model = AgentTask()
    model.taskNo = taskNo
    model.jobId = jobId
    model.refObj = refObj
    model.refObjName = refObjName
    model.status = status
    return model
}