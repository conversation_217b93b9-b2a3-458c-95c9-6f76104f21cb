package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.cr.StageRuleConfig
import com.alibabacloud.devops.appstack.libs.model.request.temp.StageRuleConfigRequest

/**
 * <AUTHOR>
 * @date 2023-11-08
 */
interface ChangeRequestStageRuleConfigBizService {


    fun find(appName: String, releaseWorkflowSn: String, releaseStageSn: String): StageRuleConfig?

    fun upsert(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        stageRuleConfigRequest: StageRuleConfigRequest
    ): StageRuleConfig?
}