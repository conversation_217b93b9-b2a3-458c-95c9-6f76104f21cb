package com.alibabacloud.devops.appstack.apps.biz.model.mapper

import com.alibabacloud.devops.appstack.apps.biz.model.po.OnlineGuidancePO
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.core.mapper.BaseMapper
import org.apache.ibatis.annotations.Mapper

@Mapper
interface OnlineGuidanceMapper : BaseMapper<OnlineGuidancePO>

fun OnlineGuidanceMapper.find(appName: String): OnlineGuidancePO? =
     selectOne(
        QueryWrapper<OnlineGuidancePO?>()
            .eq("app_name", appName)
    )


fun OnlineGuidanceMapper.update(onlineGuidancePO: OnlineGuidancePO): Int =
    update(onlineGuidancePO, QueryWrapper<OnlineGuidancePO>()
        .eq("name", onlineGuidancePO.name)
        .eq("app_name", onlineGuidancePO.appName)
)
