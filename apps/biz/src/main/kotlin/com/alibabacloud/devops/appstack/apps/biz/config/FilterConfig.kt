package com.alibabacloud.devops.appstack.apps.biz.config

import com.alibabacloud.devops.appstack.libs.common.filter.AuthFilter
import com.alibabacloud.devops.appstack.libs.common.filter.CorsFilter
import com.alibabacloud.devops.appstack.libs.common.filter.TraceFilter
import io.micrometer.core.instrument.MeterRegistry
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.web.servlet.FilterRegistrationBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

/**
 * <AUTHOR>
 * @version : FilterConfiguration.java, v 0.1 2021年08月10日 01:11 lockelee Exp $
 */
@Configuration
open class FilterConfig {

    @Autowired
    private lateinit var meterRegistry: MeterRegistry

    @Bean
    open fun authFilter(): FilterRegistrationBean<*> {
        val filterRegistrationBean: FilterRegistrationBean<AuthFilter> = FilterRegistrationBean<AuthFilter>()
        val authFilter = AuthFilter()
        filterRegistrationBean.order = 10
        filterRegistrationBean.filter = authFilter
        filterRegistrationBean.urlPatterns = listOf("/api/*", "/inner/api/*", "/ws/*", "/innerapi/*", "/openapi/*", "/forward/*")
        return filterRegistrationBean
    }

    @Bean
    open fun metricsFilter(): FilterRegistrationBean<*>{
        val filterRegistrationBean: FilterRegistrationBean<WebMetricsFilter> = FilterRegistrationBean<WebMetricsFilter>()
        val webMetricsFilter = WebMetricsFilter(meterRegistry)
        filterRegistrationBean.order = 8
        filterRegistrationBean.filter = webMetricsFilter
        filterRegistrationBean.urlPatterns = listOf("/api/*", "/inner/api/*", "/innerapi/*", "/openapi/*", "/forward/*")
        return filterRegistrationBean
    }

    @Bean
    open fun traceFilter(): FilterRegistrationBean<*> {
        val filterRegistrationBean: FilterRegistrationBean<TraceFilter> = FilterRegistrationBean<TraceFilter>()
        val traceFilter = TraceFilter()
        filterRegistrationBean.order = 5
        filterRegistrationBean.filter = traceFilter
        filterRegistrationBean.urlPatterns = listOf("/api/*", "/inner/api/*", "/ws/*", "/innerapi/*", "/openapi/*", "/forward/*")
        return filterRegistrationBean
    }

    @Bean
    open fun corsFilter(): FilterRegistrationBean<*>? {
        val bean: FilterRegistrationBean<CorsFilter> = FilterRegistrationBean<CorsFilter>(CorsFilter())
        bean.setName("corsFilter")
        bean.order = 3
        bean.addUrlPatterns("/api/v1/flow/config/*")
        return bean
    }
}