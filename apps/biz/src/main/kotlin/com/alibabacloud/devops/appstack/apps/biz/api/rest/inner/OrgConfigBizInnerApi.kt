package com.alibabacloud.devops.appstack.apps.biz.api.rest.inner

import com.alibabacloud.devops.appstack.apps.biz.service.OrgConfigItemBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @date 2023-12-23
 */
@Tag(name = "OrgConfigBizInnerApi", description = "企业级配置 API")
@RestController
@RequestMapping("/inner/api/orgConfigs")
class OrgConfigBizInnerApi {

    @Autowired
    lateinit var orgConfigBizService: OrgConfigItemBizService

    @Operation(summary = "查询企业配置")
    @GetMapping
    fun findAll(): Response<List<OrgConfigItem>> {
        return Response.success(orgConfigBizService.findAll())
    }
}