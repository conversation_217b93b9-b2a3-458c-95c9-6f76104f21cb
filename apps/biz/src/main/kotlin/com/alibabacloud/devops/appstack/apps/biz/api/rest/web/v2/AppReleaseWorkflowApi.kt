package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestWorkflowBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import com.aliyun.amp.plugin.annotation.AmpApi
import com.aliyun.amp.plugin.annotation.AmpParam
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 * @create 2023/12/26 3:37 PM
 **/
@Tag(name = "研发流程", description = "研发流程相关API")
@RestController
@RequestMapping("/api/v2")
class AppReleaseWorkflowApi {

    @Autowired
    lateinit var changeRequestWorkflowBizService: ChangeRequestWorkflowBizService

    @AmpApi(
        name = "ListAllReleaseWorkflows",
        summary = "查找应用下所有的研发流程",
        method = "get",
        path = "/appstack/apps/{appName}/releaseWorkflows",
        operationType = "read"
    )
    @VpcApi
    @Operation(summary = "查找应用下所有的研发流程", operationId = "ListAllReleaseWorkflows")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查找应用下所有的研发流程成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @GetMapping("/apps/{appName}/releaseWorkflows")
    fun listAllAppReleaseWorkflows(
        @AmpParam(description = "应用名")
        @PathVariable appName: String
    ): List<ReleaseWorkflow> {
        return changeRequestWorkflowBizService.findAll(appName)
    }

}