package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.config.FlowAuthConfiguration
import com.alibabacloud.devops.appstack.apps.biz.model.DeployFlow
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.DeployFlowMapper
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.deleteByObjectTypeAndObjectId
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.find
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.findList
import com.alibabacloud.devops.appstack.apps.biz.model.toModel
import com.alibabacloud.devops.appstack.apps.biz.model.toPO
import com.alibabacloud.devops.appstack.apps.biz.service.DeployFlowService
import com.alibabacloud.devops.appstack.apps.biz.service.client.api.FlowPipelineApi
import com.alibabacloud.devops.appstack.libs.common.util.TokenUtil
import com.alibabacloud.devops.appstack.libs.model.vo.PipelineSource
import com.alibabacloud.devops.appstack.libs.model.vo.WorkflowSource
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.util.StringUtils

/**
 * @author: <EMAIL>
 * @date: 2022-03-07 19:26
 * @version: DeployFlowServiceImpl, v0.1
 **/
@Service
class DeployFlowServiceImpl : DeployFlowService {

    @Autowired
    lateinit var deployFlowMapper: DeployFlowMapper

    @Autowired
    lateinit var flowAuthConfig: FlowAuthConfiguration

    @Autowired
    lateinit var flowPipelineApi: FlowPipelineApi

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    override fun create(deployFlow: DeployFlow): DeployFlow {
        val split = deployFlow.objectId.split("+")
        val workflowStageList = workflowControllerFacade.releaseStageFacade.findListByPipelineIds(listOf(split[0]))
        val buildId = split[1].toLong()
        deployFlow.source = if (workflowStageList.isEmpty()) {
            val pipelineId = split[0].toLong()
            val headers = TokenUtil.generateAuthHeaders(flowAuthConfig.appId, flowAuthConfig.appSecret)
            try {
                val pipelineInfo = flowPipelineApi.get(headers = headers, id = pipelineId).data
                PipelineSource(
                    pipelineId = pipelineId,
                    pipelineName = pipelineInfo?.pipelineVo?.get("name")?.asText() ?: "",
                    buildId = buildId,
                )
            } catch (e: Exception) {
                null
            }
        } else {
            val workflowStage = workflowStageList.first()
            WorkflowSource(
                workflowSn = workflowStage.releaseWorkflowSn,
                workflowName = workflowStage.releaseWorkflowName,
                stageSn = workflowStage.releaseStageSn,
                stageName = workflowStage.releaseStageName,
                buildId = buildId,
            )
        }
        deployFlowMapper.insert(deployFlow.toPO())
        val deployFlowPO = deployFlowMapper.find(deployFlow.objectType.name, deployFlow.objectId)
        return deployFlowPO!!.toModel()
    }

    override fun find(
        objectType: DeployFlow.ObjectType,
        objectId: String,
        appName: String?,
        envName: String?
    ): DeployFlow? {
        val deployFlowPO = if (StringUtils.hasText(appName) && StringUtils.hasText(envName))
            deployFlowMapper.find(objectType.name, objectId, appName!!, envName!!)
        else
            deployFlowMapper.find(objectType.name, objectId)
        return deployFlowPO?.toModel()
    }

    override fun listByChangeOrderSns(changeOrderSnList: List<String>): List<DeployFlow> {
        return deployFlowMapper.findList(changeOrderSnList).map { it.toModel() }
    }

    override fun deleteByObjectTypeAndObjectId(objectType: DeployFlow.ObjectType, objectId: String): Int {
        return deployFlowMapper.deleteByObjectTypeAndObjectId(objectType.name, objectId)
    }
}