package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.apps.biz.service.PermissionChangedHandlerService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.model.iam.AppRole
import com.alibabacloud.devops.appstack.libs.model.iam.ResRole
import com.alibabacloud.devops.appstack.libs.org.spring.boot.starter.service.OrgFacades
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Slf4k
@Service
class PermissionChangedHandlerServiceImpl : PermissionChangedHandlerService {

    @Autowired
    lateinit var iamService: IamService

    @Autowired
    lateinit var orgFacades: OrgFacades

    companion object {
        val PROTOCOL_TYPE_TYPE_MAP =
            mapOf(ProtocolType.AppStackApp to AppRole.admin, ProtocolType.AppStackRes to ResRole.admin)
    }

    override fun removeResourceAuth(userId: String) {
        var orgOwner: String? = null
        PROTOCOL_TYPE_TYPE_MAP.forEach { (oneProtocolType, ownerName) ->
            var i = 1
            val pageSize = 10
            do {
                logger.info("removeResourceAuth searchResourceName protocolType:${oneProtocolType.name} userId:$userId page:${i}")
                // 相关的资源名称
                val resourceNameList = iamService.searchResourceName(
                    protocolType = oneProtocolType,
                    subjectType = SubjectType.User,
                    subjectId = userId,
                    page = i,
                    size = pageSize,
                )
                logger.info(
                    "removeResourceAuth resourceNameList:${
                        jacksonObjectMapper().writeValueAsString(
                            resourceNameList
                        )
                    }"
                )
                // 找到这些资源中的owner
                val findRolePlayers = if (resourceNameList.isEmpty()) emptyMap() else iamService.findRolePlayers(
                    protocolType = oneProtocolType,
                    resourceNameList = resourceNameList,
                    roleName = ownerName.name
                )
                logger.info(
                    "removeResourceAuth findRolePlayers:${
                        jacksonObjectMapper().writeValueAsString(
                            findRolePlayers
                        )
                    }"
                )
                // 计算出owner为离职用户的资源
                val needTransferNames =
                    findRolePlayers.mapNotNull { (name, userList) -> if (userList.size == 1 && userList.first() == userId) name else null }
                logger.info(
                    "removeResourceAuth needTransferNames:${
                        jacksonObjectMapper().writeValueAsString(
                            needTransferNames
                        )
                    }"
                )
                // 如果有需要转交的，找到企业owner
                if (needTransferNames.isNotEmpty() && null == orgOwner) {
                    orgOwner = orgFacades.organizationFacade.getOwnerId(AuthUtil.getTenant())
                }
                if (null != orgOwner && userId != orgOwner) {
                logger.info("removeResourceAuth orgOwner:$orgOwner")
                    needTransferNames.forEach { oneTransferName ->
                        val findPlayerRoles = iamService.findPlayerRoles(
                            protocolType = oneProtocolType,
                            resourceName = oneTransferName
                        )
                        logger.info(
                            "removeResourceAuth findPlayerRoles:${
                                jacksonObjectMapper().writeValueAsString(
                                    findPlayerRoles
                                )
                            }"
                        )
                        // 找到企业owner原来在这个资源的权限
                        val oldRoles =
                            findPlayerRoles
                                .firstNotNullOf { (k, v) -> if (k.type == SubjectType.User && k.id == orgOwner) v.map { oneV -> oneV.name } else emptyList() }
                                .toMutableList()
                        logger.info(
                            "removeResourceAuth oneTransferName:$oneTransferName oldRoles:${
                                jacksonObjectMapper().writeValueAsString(
                                    oldRoles
                                )
                            }"
                        )
                        // 拼上owner权限
                        if (!oldRoles.contains(ownerName.name)) {
                            oldRoles.add(ownerName.name)
                        }
                        logger.info("removeResourceAuth oldRoles:${jacksonObjectMapper().writeValueAsString(oldRoles)}")
                        // 给企业owner更新权限
                        iamService.updatePlayer(
                            protocolType = oneProtocolType,
                            resourceName = oneTransferName,
                            subjectId = orgOwner!!,
                            subjectType = SubjectType.User,
                            roleNameList = oldRoles
                        )
                        logger.info(
                            "removeResourceAuth orgOwner oneProtocolType:$oneProtocolType oneTransferName:$oneTransferName oldRoles:${
                                jacksonObjectMapper().writeValueAsString(
                                    oldRoles
                                )
                            }"
                        )
                    }
                }
                // 给离职用户清空权限
                resourceNameList.forEach { oneResourceName ->
                    logger.info("removeResourceAuth oneProtocolType:$oneProtocolType oneTransferName:$oneResourceName")
                    iamService.updatePlayer(
                        protocolType = oneProtocolType,
                        resourceName = oneResourceName,
                        subjectId = userId,
                        subjectType = SubjectType.User,
                        roleNameList = emptyList()
                    )
                }

                i++
            } while (resourceNameList.size >= pageSize)
        }
    }
}