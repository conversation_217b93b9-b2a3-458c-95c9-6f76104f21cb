package com.alibabacloud.devops.appstack.apps.biz.service.client.api

import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient
import com.alibabacloud.devops.appstack.libs.common.model.AliyunLoginUserInfo
import retrofit2.http.*

/**
 * <AUTHOR>
 * @create 2023/12/20 9:52 PM
 **/
@RetrofitClient(baseUrl = "http://api-devops.aliyun-inc.com/tbs")
interface AuthApi {
    @GET("user/v1/aliens")
    fun getLoginUserByAliyunId(
        @HeaderMap headers: Map<String, String>,
        @Query("refer") refer: String,
        @Query("openId") openId: String? = null,
        @Query("_userId") _userId: String? = null,
    ): AliyunLoginUserInfo
}