package com.alibabacloud.devops.appstack.apps.biz.mq.unicast

import com.alibabacloud.devops.appstack.apps.biz.service.AppOrchestrationBizService
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateService
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.impl.AppTemplateOrchestrationConfigBizServiceImpl.Companion.DEFAULT_APP_TEMPLATE_ORCHESTRATION_INSTANCE_NAME
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.filter.TraceFilter
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.config.IamContext
import com.alibabacloud.devops.appstack.libs.lock.spring.boot.starter.DistributedLockService
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfigSyncStatus
import com.alibabacloud.devops.appstack.libs.model.`do`.apptemplate.AppTemplateSyncProgress
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.Orchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.SyncSourceTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppBuiltInOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.template.AppTemplateBuildInOrchestration
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AppTemplateOrchestrationSyncAudit
import com.alibabacloud.devops.appstack.libs.model.org.event.AppTemplateReleaseWorkflowSyncAudit
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.request.AppOrchestrationCreateRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppOrchestrationRequest
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.BroadcastSender
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.UnicastListener
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.*
import com.alibabacloud.devops.iam.exception.IamForbiddenException
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.apache.commons.lang3.StringUtils
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2024-02-17
 */
@Slf4k
@Service
class AppTemplateOrchestrationSyncListener : UnicastListener {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var appOrchestrationBizService: AppOrchestrationBizService

    @Autowired
    lateinit var appTemplateService: AppTemplateService

    @Autowired
    lateinit var i18nMessageService: I18nMessageService

    @Autowired
    lateinit var distributedLockService: DistributedLockService

    @Autowired
    lateinit var broadcastSender: BroadcastSender

    @Value("\${devops.appstack.unicast.groupId}")
    lateinit var groupId: String

    @Value("\${devops.appstack.unicast.topic}")
    lateinit var topic: String

    @Autowired
    lateinit var auditLogService: AuditLogService

    override fun expectedGroupId(): String {
        return groupId
    }

    override fun expectedTopic(): String {
        return topic
    }

    override fun expectedTag(): UnicastTagEnum {
        return UnicastTagEnum.APPTEMPLATE_ORCHESTRATION_SYNC
    }

    override fun handle(body: ByteArray) {
        logger.info("Receive AppTemplateOrchestrationSync content: ${String(body)}")
        val content = jacksonObjectMapper().readValue(body, AppTemplateOrchestrationSyncBody::class.java)
        AuthThreadContext.setTenant(content.tenant)
        AuthThreadContext.setUserId(content.operator)
        AuthThreadContext.setTraceId(content.traceId)
        AuthThreadContext.setUserIP(content.ip)
        AuthThreadContext.setLocale(content.locale)
        MDC.put(TraceFilter.TRACE_CONTEXT_KEY, content.traceId)
        IamContext.setTenant(content.tenant)
        IamContext.setOperator(content.operator)
        // 1. 锁: 根据处理任务的多少，动态调整锁的时长
        var expirationMillSeconds = content.appNames.size * 1000L
        if (expirationMillSeconds < 60 * 1000) {
            expirationMillSeconds = 60 * 1000
        }
        val releaseKey = distributedLockService.tryLockWithExpiration(
            method = "AppTemplateOrchestrationSyncListener",
            param = content.transactionId,
            expirationMillSeconds = expirationMillSeconds
        )
        if (StringUtils.isBlank(releaseKey)) {
            logger.info("Handle message for AppTemplateOrchestrationSync skipped, lock fail")
            throw Exception("Lock fail, not handle")
        }
        val appTemplate = appTemplateService.find(content.appTemplateName)

        // 2. 遍历同步（先检查、再同步）
        val templateOrchestrations =
            coreFacades.appTemplateOrchestrationFacade.findAll(content.appTemplateName, content.orchestrationSha).first
        content.appNames.forEachIndexed { index, appName ->
            try {
                val errorMessage =
                    syncAppOrchestration(content.appTemplateName, templateOrchestrations, appName, content.commitMsg)
                broadcastSender.send(
                    BroadcastTagEnum.APP_TEMPLATE_SYNC_PROGRESS,
                    jacksonObjectMapper().writeValueAsBytes(
                        AppTemplateSyncProgressBody(
                            transactionId = content.transactionId,
                            progress = AppTemplateSyncProgress(
                                totalCount = content.appNames.size,
                                currentIndex = index+1,
                                appSyncResult = AppTemplateSyncProgress.AppSyncResult(
                                    appName = appName,
                                    success = StringUtils.isBlank(errorMessage),
                                    errorMessage = errorMessage
                                )
                            )
                        )
                    )
                )
                auditLogService.log(
                        OrgEventType.APP_TEMPLATE_SYNC_ORCHESTRATION_AUDIT, AppTemplateOrchestrationSyncAudit(
                        target = AuditTarget(id = content.appTemplateName, name = appTemplate.displayName),
                        appName = appName,
                        success = StringUtils.isBlank(errorMessage)
                    )
                )
            } catch (e: Exception) {
                logger.error(
                    "Loop sync appOrchestration fail for appName[$appName], templateName[${content.appTemplateName}]",
                    e
                )
            }
        }
        // 3. 释放锁
        distributedLockService.releaseLock(releaseKey!!)
    }

    /**
     * @return 如果是null，说明同步成功/无需同步；如果不是null，则是异常信息
     */
    private fun syncAppOrchestration(
        templateName: String,
        templateOrchestrations: List<AppTemplateBuildInOrchestration>,
        appName: String,
        commitMsg: String
    ): String? {
        val configSyncStatus = appTemplateService.findConfigSyncStatus(
            templateName,
            AppTemplateConfig.TypeEnum.Orchestration,
            DEFAULT_APP_TEMPLATE_ORCHESTRATION_INSTANCE_NAME,
            appName
        )
        // 如果缺失 AppTemplateConfigSyncStatus ，则跳过同步
        // AppTemplateConfigSyncStatus 删除的时机是应用被删除了，如果缺失意味着异步处理过程中应用被删除了
        if (configSyncStatus == null) {
            logger.error("AppOrchestration sync skip, missing AppTemplateConfigSyncStatus. AppName[$appName], templatename[$templateName]")
            return null
        }
        val templateSyncStatus = AppTemplateConfigSyncStatus.TemplateSyncStatus()
        templateSyncStatus.lastAppliedTemplateRevisionSha =
            configSyncStatus.templateSyncStatus?.lastAppliedTemplateRevisionSha
        templateSyncStatus.lastAppliedAppRevisionSha = configSyncStatus.templateSyncStatus?.lastAppliedAppRevisionSha
        try {
            val appBuiltinOrchesrations = appOrchestrationBizService.findAll(appName)
                .filter { it.storageType == Orchestration.StorageTypeEnum.BUILTIN }
            var appOrchestrationRevision = appBuiltinOrchesrations.firstOrNull()?.revision
            // 如果应用与待同步的编排模板保持一致，则跳过同步
            val templateSynchronizedRevisionEqual =
                templateOrchestrations.first().revision!!.sha == configSyncStatus.templateSyncStatus?.lastAppliedTemplateRevisionSha
            val appSynchronizedRevisionEqual =
                appOrchestrationRevision?.sha == configSyncStatus.templateSyncStatus?.lastAppliedAppRevisionSha
            val labelEqual =
                templateOrchestrations.first().labelList == (appBuiltinOrchesrations.firstOrNull() as AppBuiltInOrchestration?)?.labelList
            if (templateSynchronizedRevisionEqual && appSynchronizedRevisionEqual && labelEqual) {
                logger.info(
                    "AppOrchestration sync skip, already synchronized. " +
                            "AppName[$appName], AppLastAppliedAppRevisionSha[${configSyncStatus.templateSyncStatus?.lastAppliedAppRevisionSha}], AppRevisionSha[${appOrchestrationRevision?.sha}], " +
                            "templatename[$templateName], templateLastAppliedTemplateRevisionSha[${configSyncStatus.templateSyncStatus?.lastAppliedTemplateRevisionSha}], templateRevisionSha[${templateOrchestrations.first().revision!!.sha}]" +
                            "AppLabels[${(appBuiltinOrchesrations.firstOrNull() as AppBuiltInOrchestration?)?.labelList}], templateLabels[${templateOrchestrations.first().labelList}]"
                )
                return null
            }

            // 应用如果还没有编排，先初始化
            if (appBuiltinOrchesrations.isEmpty()) {
                val createdOrc = appOrchestrationBizService.create(
                    appName, AppOrchestrationCreateRequest(
                        storageType = Orchestration.StorageTypeEnum.BUILTIN,
                        format = Orchestration.FormatEnum.MANIFEST,
                        templateType = null,
                        templateId = null,
                        datasource = null
                    )
                )
                appOrchestrationRevision = createdOrc.revision
            }
            val updatedOrchestration = appOrchestrationBizService.updateAllBuiltin(appName,
                UpdateAppOrchestrationRequest(
                    name = appName,
                    format = templateOrchestrations.first().format,
                    componentList = templateOrchestrations.flatMap { it.componentList }.toMutableList(),
                    placeholderList = templateOrchestrations.flatMap { it.placeholderList }.toMutableList(),
                    groupNameMap = templateOrchestrations.first().groupNameMap,
                    label = templateOrchestrations.first().labelList,
                    syncSourceTemplate = SyncSourceTemplate(
                        appTemplateName = templateName,
                        orchestrationSha = templateOrchestrations.first().revision!!.sha
                    )
                ).apply {
                    fromRevisionSha = appOrchestrationRevision!!.sha
                    message = commitMsg
                }
            )
            templateSyncStatus.lastAppliedTemplateRevisionSha = templateOrchestrations.first().revision!!.sha
            templateSyncStatus.lastAppliedAppRevisionSha = updatedOrchestration.revision!!.sha
        } catch (e: Exception) {
            logger.error("AppOrchestration sync failed. AppName[$appName], templatename[$templateName]", e)
            templateSyncStatus.lastSyncErrorMessage = if (e is BizException) {
                i18nMessageService.getErrorMessage(e)
            } else if (e is IamForbiddenException) {
                i18nMessageService.getErrorMessage(BizException(errorCode = ErrorCode.AS_PERMISSION_DENIED))
            } else {
                i18nMessageService.getErrorMessage(BizException(errorCode = ErrorCode.AS_UNKNOWN))
            }
        } finally {
            appTemplateService.updateTemplateSyncStatus(
                templateName,
                AppTemplateConfig.TypeEnum.Orchestration,
                DEFAULT_APP_TEMPLATE_ORCHESTRATION_INSTANCE_NAME,
                appName,
                templateSyncStatus
            )
        }
        return templateSyncStatus.lastSyncErrorMessage
    }


}