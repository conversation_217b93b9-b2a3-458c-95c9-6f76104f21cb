package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.apps.biz.model.vo.ReleaseStageInstanceIntegratedMetadataVO
import com.alibabacloud.devops.appstack.apps.biz.model.vo.ReleaseStageInstanceVO
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ExecutePipelineResult
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStage
import com.alibabacloud.devops.appstack.libs.model.request.PaginationQuery
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.*
import com.alibabacloud.devops.appstack.libs.model.vo.ReleaseIntegratedMetadata
import com.alibabacloud.devops.appstack.libs.model.vo.ng.PaginationVO

/**
 * <AUTHOR>
 * @date 2023-11-08
 */
interface ChangeRequestStageBizService {
    fun create(appName: String, releaseWorkflowSn: String, request: CreateReleaseStageBizRequest): ReleaseStage
    fun findAll(appName: String, releaseWorkflowSn: String): List<ReleaseStage>
    fun find(appName: String, releaseWorkflowSn: String, releaseStageSn: String): ReleaseStage
    fun update(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        request: UpdateReleaseStageRequest
    ): ReleaseStage

    fun delete(appName: String, releaseWorkflowSn: String, releaseStageSn: String)
    fun updatePipeline(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        request: UpdateReleaseStagePipelineRequest
    ): ReleaseStage

    fun executePipeline(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        request: ExecuteReleaseStagePipelineRequest
    ): ExecutePipelineResult

    fun findReleaseStageCrMetadata(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        request: QueryReleaseStageCrMetadataRequest
    ): ReleaseIntegratedMetadata?

    fun findPreviousMetadata(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String
    ): ReleaseIntegratedMetadata?

    fun findStageEnvList(releaseStageSn: String): List<String>
    fun findVariableGroups(appName: String, releaseWorkflowSn: String, releaseStageSn: String): List<VariableGroup>

    fun listReleaseStageInstances(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        paginationQuery: PaginationQuery
    ): PageList<ReleaseStageInstanceVO>

    fun listReleaseStageInstanceIntegratedMetadata(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        number: Long
    ): List<ReleaseStageInstanceIntegratedMetadataVO>
    fun getPipelineParams(appName: String, releaseWorkflowSn: String, releaseStageSn: String): MutableMap<String, Any>
}