package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.Variable
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.request.ProfileRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateVariableGroupDryRunRequest
import com.alibabacloud.devops.appstack.libs.model.vo.VariableStorageVO

/**
 * <AUTHOR>
 * @date 2023-12-19
 */
interface AppVariableGroupsBizService {

    fun createProfile(appName: String, profileRequest: ProfileRequest): VariableStorageVO

    fun updateProfile(appName: String, profileRequest: ProfileRequest): VariableStorageVO

    fun findAllProfiles(appName: String): VariableStorageVO?

    fun deleteProfile(appName: String, profileName: String)

    fun findProfilesRevisions(appName: String, current: Long, pageSize: Long): Pagination<Revision>

    fun findProfilesByRevisionSha(appName: String, revisionSha: String): VariableStorageVO

    fun findProfilesByTag(appName: String, tagName: String): VariableStorageVO

    fun compare(appName: String, beforeRevisionSha: String, afterRevisionSha: String): Triple<VariableStorageVO, VariableStorageVO, List<DiffItem<String>>>

    fun updateDryrun(appName: String, profileName: String, request: UpdateVariableGroupDryRunRequest): List<Variable>
}