package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.config.WebSocketConfig
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestWebSocketService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils
import org.springframework.web.socket.CloseStatus
import org.springframework.web.socket.TextMessage
import org.springframework.web.socket.WebSocketSession
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentSkipListSet

/**
 * <AUTHOR> liyebin.lyb
 * @date : 2022/8/8 2:27 PM
 */

@Slf4k
class ChangeRequestWebSocketServiceImpl: ChangeRequestWebSocketService() {

    companion object {
        val changeRequestClientMap: ConcurrentHashMap<String, ConcurrentSkipListSet<String>> =
            ConcurrentHashMap()
        val sessionMap: ConcurrentHashMap<String, WebSocketSession> = ConcurrentHashMap()
    }

    override fun notify(releaseStageSn: String, content: String) {
        val sessionIdSet = changeRequestClientMap.get(releaseStageSn)
        if (CollectionUtils.isNotEmpty(sessionIdSet)) {
            sessionIdSet!!.forEach {
                try {
                    sessionMap.get(it)?.sendMessage(TextMessage(content))
                    logger.info("Notify MateData Changed for releaseStage[$releaseStageSn] sessionId[$it] content[$content]")
                } catch (exception: Exception) {
                    val session = sessionMap.remove(it)
                    logger.info("Session[${session?.id}] has been closed, Remove it")
                    changeRequestClientMap.get(WebSocketConfig.RELEASE_STAGE_SN_KEY)?.remove(it)
                    try {
                        session?.close()
                    } catch (exception: Exception) {

                    }
                }
            }
        }
    }

    override fun afterConnectionEstablished(session: WebSocketSession) {
        val releaseStageSn = getReleaseStageSn(session)
        releaseStageSn?.let {
            sessionMap.put(session.id, session)
            changeRequestClientMap.putIfAbsent(it, ConcurrentSkipListSet(setOf(session.id)))?.let {
                it.add(session.id)
            }
            logger.info("WebSocket Client Connected for releaseStage[$releaseStageSn] Session[${session.id}]")
        }
    }

    override fun afterConnectionClosed(session: WebSocketSession, status: CloseStatus) {
        val releaseStageSn = getReleaseStageSn(session)
        sessionMap.remove(session.id)
        releaseStageSn?.let {
            changeRequestClientMap.get(it)?.remove(session.id)
            logger.info("WebSocket Client DisConnected for releaseStage[$releaseStageSn] Session[${session.id}] StatusCode[${status.code}]")
        }
    }

    override fun handleTextMessage(session: WebSocketSession, message: TextMessage) {

    }

    private fun getReleaseStageSn(session: WebSocketSession): String? {
        val releaseStageSn = session.attributes.get(WebSocketConfig.RELEASE_STAGE_SN_KEY)
        releaseStageSn?.let {
            if (it is String) {
                return it
            }
        }
        return null
    }

}