package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestStageBizService
import com.alibabacloud.devops.appstack.apps.biz.service.FlowPipelineBizService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkBizWithCode
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.app.App
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppType
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.Pipeline
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStage
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.ExecuteReleaseStagePipelineRequest
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.lang.RuntimeException

/**
 * <AUTHOR>
 * @create 2023/11/9 8:19 PM
 **/
@Service
@Slf4k
class FlowPipelineBizServiceImpl: FlowPipelineBizService {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var changeRequestStageBizService: ChangeRequestStageBizService

    @Autowired
    lateinit var auditLogService: AuditLogService

    @Autowired
    lateinit var iamService: IamService

    companion object {
        val APPSTACK_RUN_STAGE_STEP = "AppStackAppReleaseWorkflowRun"
        val APPSTACK_RUN_STAGE_COMPONENT = "private/AppStackAppReleaseWorkflowRun"
    }

    override fun executeStagePipeline(request: ExecuteReleaseStagePipelineRequest): Map<String, Any> {
        val appName = request.appName
        checkExists(appName) { ErrorCode.AS_APP_NAME_NULL }
        val releaseWorkflowName = request.releaseWorkflowName
        checkExists(releaseWorkflowName) { ErrorCode.AS_RELEASE_WORKFLOW_NAME_NULL }
        val releaseStageName = request.releaseStageName
        checkExists(releaseStageName) { ErrorCode.AS_RELEASE_STAGE_NAME_NULL }
        val app = coreFacades.appFacade.find(appName)
        checkExists(app, appName) { ErrorCode.AS_APP_NOT_FOUND }
        // 不允许运行系统的流程
        checkBizWithCode(app.type == AppType.APP) { ErrorCode.AS_APP_NOT_FOUND }
        val releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findByName(appName, releaseWorkflowName, ReleaseWorkflow.TypeEnum.CR)
        checkExists(releaseWorkflow, releaseWorkflowName) { ErrorCode.AS_RELEASE_WORKFLOW_NOT_FOUND }
        val releaseStage = releaseWorkflow.releaseStages.firstOrNull() { it.name == releaseStageName }
        checkExists(releaseStage, releaseStageName) { ErrorCode.AS_RELEASE_STAGE_NOT_FOUND }
        return _executeStagePipeline(app, releaseWorkflow, releaseStage, request)
    }

    private fun _executeStagePipeline(app: App, releaseWorkflow: ReleaseWorkflow, releaseStage: ReleaseStage,
                                      request: ExecuteReleaseStagePipelineRequest): Map<String, Any> {
        // 流水线非空校验
        checkExists(releaseStage.pipeline) { ErrorCode.AS_RELEASE_STAGE_PIPELINE_NOT_FOUND }
        // 循环依赖校验：判断流水线中是否存在【运行应用流程阶段】节点且存在 A->B->A/B的情况
        // 禁止调用源流水线和当前阶段流水线
        val forbiddenPipelineIds = listOf(request.pipelineId, releaseStage.pipeline!!.engineSn)
        when (releaseStage.pipeline!!.engineType) {
            Pipeline.Type.FlowV1 -> {
                val locators = listOf("pipeline", "stages", "jobs", "params", "steps")
                val nodes = workflowControllerFacade.releaseStageFacade.findNodes(releaseStage.sn!!, locators)
                if (nodes.any {
                        it.get("stepType") != null
                                && it.get("stepType").asText().equals(APPSTACK_RUN_STAGE_STEP)
                                && forbiddenPipelineIds.contains(
                                    getPipelineIdByStage(
                                        it.get("APP_NAME").asText(),
                                        it.get("RELEASE_WORKFLOW_NAME").asText(),
                                        it.get("RELEASE_STAGE_NAME").asText()
                                    )
                                ) }) {
                    throw BizException(ErrorCode.AS_RELEASE_STAGE_PIPELINE_CIRCULAR_ERROR)
                }
            }
            Pipeline.Type.FlowV2 -> {
                val locators = listOf("stages", "*", "jobs", "*")
                val nodes = workflowControllerFacade.releaseStageFacade.findNodes(releaseStage.sn!!, locators)
                if (nodes.any {
                        it.get("component") != null
                                && it.get("component").asText().equals(APPSTACK_RUN_STAGE_COMPONENT)
                                && it.get("with") != null
                                && forbiddenPipelineIds.contains(
                                    getPipelineIdByStage(
                                        it.get("with").get("application").asText(),
                                        it.get("with").get("workflow").asText(),
                                        it.get("with").get("stage").asText()
                                    )
                                ) }) {
                    throw BizException(ErrorCode.AS_RELEASE_STAGE_PIPELINE_CIRCULAR_ERROR)
                }
            }
            else -> throw BizException(ErrorCode.AS_RELEASE_STAGE_PIPELINE_ADAPTOR_NOT_FOUND)
        }
        // 运行研发流程，避免触发发布
        request.appReleaseSn = null
        val executeResult = changeRequestStageBizService.executePipeline(app.name, releaseWorkflow.sn!!, releaseStage.sn!!, request)
        val result = mutableMapOf<String, Any>()
        // 填充应用研发流程信息
        result["object"] = executeResult.`object`
        result["pipelineId"] = executeResult.pipelineId
        result["pipelineRunId"] = executeResult.pipelineRunId
        result["releaseWorkflowSn"] = releaseWorkflow.sn!!
        result["releaseStageSn"] = releaseStage.sn!!
        return result
    }

    // TODO: 权限
    override fun getRunInfo(pipelineId: String, pipelineRunId: String): Map<String, Any> {
        // TODO：先用研发阶段实例状态信息返回，后续完善为从 Flow 流水线获取实时信息
        val stage = workflowControllerFacade.releaseStageFacade.findBriefByProcessEngineSn(pipelineId) ?: return mapOf()
        val stageInstace = workflowControllerFacade.releaseStageFacade.findInstanceByPipeline(stage.sn!!, pipelineId, pipelineRunId) ?: return mapOf()
        val pipelineRun: MutableMap<String, Any> = mutableMapOf()
        pipelineRun["status"] = stageInstace.state.name
        return mapOf(
            "pipelineRun" to pipelineRun
        )
    }

    override fun getAppMembers(roleNameList: List<String>, pipelineId: String): List<String> {
        val appName = workflowControllerFacade.releaseStageFacade.findBriefByProcessEngineSn(pipelineId)?.appName ?: return emptyList()
        val list = iamService.findRolePlayers(ProtocolType.AppStackApp, listOf(appName), roleNameList)[appName] ?: return emptyList()
        val players = list.flatMap { it.playerList }
        val userIds = players.flatMap { player ->
            when (player.type) {
                SubjectType.User -> listOf(player.id)
                SubjectType.Group -> iamService.listUserIdsByGroup(orgId = AuthUtil.getTenant(), groupId = player.id)
                SubjectType.Team -> iamService.listUserIdsByTeam(orgId = AuthUtil.getTenant(), teamId = player.id)
                else -> throw RuntimeException("not support")
            }
        }
        return userIds.distinct()
    }

    /**
     * 获取研发阶段对应的流水线id
     * 如果流水线不存在则返回nul
     */
    private fun getPipelineIdByStage(appName: String?, workflowName: String?, stageName: String?): String? {
        try {
            if (appName.isNullOrBlank() || workflowName.isNullOrBlank() || stageName.isNullOrBlank()) {
                return null
            }
            val app = coreFacades.appFacade.find(appName) ?: return null
            val releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findByName(appName, workflowName, ReleaseWorkflow.TypeEnum.CR)
            val releaseStage = releaseWorkflow.releaseStages.firstOrNull() { it.name == stageName } ?: return null
            return releaseStage.pipeline?.engineSn
        } catch (e: Throwable) {
            logger.error("failed to getPipelineIdByStage: appName: $appName workflowName:$workflowName stageName:$stageName")
            return null
        }
    }
}