package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.CommonIngress
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.PodExecToken
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.WorkloadBrief
import io.fabric8.kubernetes.api.model.ConfigMapList
import io.fabric8.kubernetes.api.model.HasMetadata
import io.fabric8.kubernetes.api.model.PersistentVolumeClaimList
import io.fabric8.kubernetes.api.model.Pod
import io.fabric8.kubernetes.api.model.SecretList
import io.fabric8.kubernetes.api.model.ServiceList
import io.fabric8.kubernetes.api.model.apps.DeploymentList

/**
 * @author: <EMAIL>
 * @date: 2022-04-11 14:39
 * @version: EnvProxyBizService, v0.1
 **/
interface EnvProxyBizService {

    fun findAllPods(appName: String, envName: String, namespace: String, deploymentName: String): List<Pod>

    fun findResourceList(
        appName: String,
        envName: String,
        resourceKind: String
    ): ResourceListVO?

    fun countResource(
        appName: String,
        envName: String,
    ): Map<String, Int>

    fun generatePodExecToken(
        env: Env,
        namespace: String,
        podName: String,
        containerName: String
    ): PodExecToken

    fun findDeploymentRevisionInfo(
        appName: String,
        envName: String,
        namespace: String,
        name: String,
        revision: String,
        taskSn: String? = null
    ): WorkloadBrief?
}

data class ResourceListVO(
    var typeCount: Map<String, Int>,
    var resourceKind: String,
    var resourceList: List<Any>? = emptyList(),
    var errorMsg: String?
)

data class HasMetadataVO(
    val data: HasMetadata,
    val state: HasMetadataVOState,
    val deliveries: String,
    val relatedResource: List<HasMetadata>? = null,
    val displayName: String
) {
    enum class HasMetadataVOState {
        /**
         * 匹配的
         */
        MATCHED,

        /**
         * 编排有但是资源没有的
         */
        LOST,

        /**
         * 资源有但是编排没有的
         */
        EXTRA
    }
}

/*
enum class ResourceKind {
    Service, Ingress, ConfigMap, Deployment, Secret, PersistentVolumeClaim, Application
}*/
