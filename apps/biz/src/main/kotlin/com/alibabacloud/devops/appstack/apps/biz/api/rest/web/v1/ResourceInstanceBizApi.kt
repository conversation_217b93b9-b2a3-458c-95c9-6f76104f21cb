package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.ade2.sdk.ext.defaultClient
import com.alibabacloud.devops.appstack.ade2.sdk.remote.AdeClientFactory
import com.alibabacloud.devops.appstack.ade2.sdk.request.v2.concrete.ControlPlaneCreateTenantNamespaceOrgBasedV1Request
import com.alibabacloud.devops.appstack.ade2.sdk.request.v2.concrete.DataPlaneHandleCreateRequest
import com.alibabacloud.devops.appstack.ade2.sdk.request.v2.concrete.DataPlaneHandleDeleteRequest
import com.alibabacloud.devops.appstack.ade2.sdk.request.v2.concrete.DataPlaneHandleUpdateRequest
import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.DeployGroupBizService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.apps.biz.service.ResourceBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceInstance
import com.alibabacloud.devops.appstack.libs.model.iam.ResRole
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.org.event.HostAudit
import com.alibabacloud.devops.appstack.libs.model.request.resource.ResourceInstanceRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.DeployGroupVO
import com.alibabacloud.devops.appstack.libs.model.vo.ResourceInstanceRecord
import com.alibabacloud.devops.appstack.libs.model.vo.ResourceInstanceVO
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2021-12-15 16:55
 * @version: ResourceBizApi, v0.1
 **/
@Tag(name = "ResourceInstance", description = "资源管理相关 API")
@RestController
@RequestMapping("/api/v1")
open class ResourceInstanceBizApi {

    // 这里是构造器调用，一次即可
    private val ade2Client = AdeClientFactory.defaultClient()

    @Autowired
    private lateinit var coreFacades: CoreFacades

    @Autowired
    private lateinit var resourceBizService: ResourceBizService

    @Autowired
    lateinit var iamService: IamService

    @Autowired
    lateinit var deployGroupBizService: DeployGroupBizService

    @Autowired
    lateinit var auditLogService: AuditLogService

    companion object {
        val UPSTREAM_NAME = "appstack-change-controller"

        val CLUSTER_HOST_TYPE_SET =
            setOf(ResourceInstance.InstanceConfigType.ECS, ResourceInstance.InstanceConfigType.FLOW_SELF_HOST)
    }

    @GetMapping("/pools/{poolName}/instances")
    fun findAllInstance(
        @PathVariable("poolName") poolName: String,
        @RequestParam("type", required = false) type: String?,
    ): Response<List<ResourceInstanceRecord>> {
        return Response.success(resourceBizService.findAllInstance(poolName, type))
    }

    @GetMapping("/pools/{poolName}/instances/{instanceName}")
    fun findResourceInstance(
        @PathVariable("poolName") poolName: String,
        @PathVariable("instanceName") instanceName: String,
    ): Response<ResourceInstanceVO> {
        return Response.success(resourceBizService.findInstance(poolName, instanceName))
    }

    @PostMapping("/pools/{poolName}/instances:import")
    fun importInstance(
        @PathVariable("poolName") poolName: String,
        @RequestBody resourceInstanceRequest: ResourceInstanceRequest,
    ): Response<ResourceInstance> {
        val resourceInstance = resourceBizService.importInstance(poolName, resourceInstanceRequest)
        // ADE2的灰度初始化，在资源导入后进行，如果有异常，删除资源实例
        try {
            if (resourceInstanceRequest.type == ResourceInstance.InstanceConfigType.ACK ||
                resourceInstanceRequest.type == ResourceInstance.InstanceConfigType.SELF_KUBERNETES
            ) {
                // 注册控制面租户
                val controlPlaneCreateResult =
                    ade2Client.invoke(ControlPlaneCreateTenantNamespaceOrgBasedV1Request(orgId = AuthUtil.getTenant()))
                if (!controlPlaneCreateResult.successful) {
                    logger.error("ControlPlaneCreateTenantNamespace fail: ${controlPlaneCreateResult.errorCode}")
                    throw BizException(errorCode = ErrorCode.AS_UNKNOWN)
                }
                val dataPlaneHandleSpec =
                    fetchDataPlaneHandleSpec(resourceInstanceRequest.type, resourceInstanceRequest.contextMap)
                // 注册数据面句柄
                val dataPlaneCreateResult = ade2Client.invoke(
                    DataPlaneHandleCreateRequest(
                        orgId = AuthUtil.getTenant(),
                        userId = AuthUtil.getUserId(),
                        upstream = UPSTREAM_NAME,
                        externalId = resourceInstanceRequest.name,
                        specJson = dataPlaneHandleSpec
                    )
                )
                if (!dataPlaneCreateResult.successful) {
                    logger.error("DataPlaneHandleCreate fail: ${dataPlaneCreateResult.errorCode}")
                    throw BizException(errorCode = ErrorCode.AS_UNKNOWN)
                }
            }
        } catch (e: Exception) {
            logger.error(e.message, e)
            resourceBizService.deleteResourceInstance(poolName, resourceInstanceRequest.name)
        }
        return Response.success(resourceInstance)
    }

    @PutMapping("/pools/{poolName}/instances/{name}")
    fun updateResourceInstance(
        @PathVariable poolName: String,
        @PathVariable name: String,
        @RequestBody resourceInstance: ResourceInstance,
    ): Response<ResourceInstance> {
        val previousInstance = resourceBizService.findInstance(poolName, name)
        val resourceInstanceUpdated = resourceBizService.updateResourceInstance(poolName, name, resourceInstance)
        if (resourceInstance.type == ResourceInstance.InstanceConfigType.ACK ||
            resourceInstance.type == ResourceInstance.InstanceConfigType.SELF_KUBERNETES
        ) {
            val dataPlaneHandleSpec = fetchDataPlaneHandleSpec(resourceInstance.type, resourceInstance.contextMap)
            // 更新数据面配置
            ade2Client.invoke(
                DataPlaneHandleUpdateRequest(
                    orgId = AuthUtil.getTenant(),
                    userId = AuthUtil.getUserId(),
                    upstream = UPSTREAM_NAME,
                    externalId = resourceInstance.name,
                    specJson = dataPlaneHandleSpec
                )
            )
        }
        if (CLUSTER_HOST_TYPE_SET.contains(resourceInstanceUpdated.type)) {
            val previousHostSn = previousInstance!!.resourceItemList.map { it.sn!! }.toSet()
            val hostSn = resourceInstanceUpdated.resourceItemList.map { it.sn!! }.toSet()
            val addSet = hostSn - previousHostSn.toSet()
            if (addSet.isNotEmpty()) {
                auditLogService.log(
                    OrgEventType.CLUSTER_HOST_ADD, HostAudit(
                        target = AuditTarget(
                            id = resourceInstanceUpdated.instanceId,
                            name = resourceInstanceUpdated.name
                        ),
                        ids = addSet.toList()
                    )
                )
            }
            val deleteSet = previousHostSn - hostSn
            if (deleteSet.isNotEmpty()) {
                auditLogService.log(
                    OrgEventType.CLUSTER_HOST_DELETE, HostAudit(
                        target = AuditTarget(
                            id = resourceInstanceUpdated.instanceId,
                            name = resourceInstanceUpdated.name
                        ),
                        ids = deleteSet.toList()
                    )
                )
            }
        }
        return Response.success(resourceInstanceUpdated)
    }

    @DeleteMapping("/pools/{poolName}/instances/{instanceName}")
    fun deleteResourceInstance(
        @PathVariable("poolName") poolName: String,
        @PathVariable("instanceName") instanceName: String,
    ): Response<Unit> {
        resourceBizService.deleteResourceInstance(poolName, instanceName)
        return Response.success()
    }

    @GetMapping("/pools/{poolName}/instances/everImported")
    fun everImported(
        @PathVariable(value = "poolName") poolName: String,
        @RequestParam(required = false, value = "type") type: String?,
    ): Response<Boolean> {
        return Response.success(coreFacades.resourceFacade.everImported(poolName, type))
    }

    @GetMapping("/pools/{poolName}/instances/{instanceName}/members")
    fun findAllResourceInstanceMember(
        @PathVariable("poolName") poolName: String,
        @PathVariable("instanceName") instanceName: String,
    ): Response<List<MemberVO>> {
        val allMemberMap = resourceBizService.findAllMember(poolName, instanceName)
        val list = allMemberMap.map { MemberVO(it.key, it.value) }.sortedWith { o1, o2 ->
            if (o1.roleList.find { it.name == ResRole.owner.name } != null) -1
            else if (o2.roleList.find { it.name == ResRole.owner.name } != null) 1
            else
                o1.displayName.compareTo(o2.displayName)
        }
        return Response.success(list)
    }


    @PutMapping("/pools/{poolName}/instances/{instanceName}/types/{type}/members/{member}")
    fun updateResourceInstanceMemberRole(
        @PathVariable("poolName") poolName: String,
        @PathVariable("instanceName") instanceName: String,
        @PathVariable("type") subjectType: SubjectType,
        @PathVariable("member") subjectId: String,
        @RequestBody roleNameList: List<String>,
    ): Response<Boolean> {
        resourceBizService.updateMemberRole(
            poolName = poolName,
            instanceName = instanceName,
            subjectType = subjectType,
            subjectId = subjectId,
            roleNameList = roleNameList,
        )
        return Response.success(true)
    }


    @PostMapping("/pools/{poolName}/instances/{instanceName}/roles/{roleName}")
    fun addResourceInstanceMember(
        @PathVariable("poolName") poolName: String,
        @PathVariable("instanceName") instanceName: String,
        @PathVariable("roleName") roleName: String,
        @RequestBody playerList: List<ResourcePlayerRequest>,
    ): Response<Boolean> {
        val role = iamService.findRole(ProtocolType.AppStackRes, roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        resourceBizService.addRoleMember(
            poolName = poolName,
            instanceName = instanceName,
            role = role,
            playerList = playerList,
        )
        return Response.success(true)
    }

    @PutMapping("/pools/{poolName}/instances/{instanceName}/roles/{roleName}")
    fun updateRoleMember(
        @PathVariable("poolName") poolName: String,
        @PathVariable("instanceName") instanceName: String,
        @PathVariable("roleName") roleName: String,
        @RequestBody playerList: List<ResourcePlayerRequest>,
    ): Response<Boolean> {
        val role = iamService.findRole(ProtocolType.AppStackRes, roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        resourceBizService.updateRoleMember(
            poolName = poolName,
            instanceName = instanceName,
            role = role,
            playerList = playerList,
        )
        return Response.success(true)
    }

    @PutMapping("/pools/{poolName}/instances/{instanceName}:owner")
    fun transferOwner(
        @PathVariable("poolName") poolName: String,
        @PathVariable("instanceName") instanceName: String,
        @RequestBody playerList: List<ResourcePlayerRequest>,
    ): Response<Boolean> {
        resourceBizService.transferOwner(
            poolName = poolName,
            instanceName = instanceName,
            playerList = playerList,
        )
        return Response.success(true)
    }

    @GetMapping("/pools/{poolName}/instances:use")
    fun findAllInstanceCanUse(
        @PathVariable("poolName") poolName: String,
        @RequestParam("type", required = false) type: String? = null,
    ): Response<List<ResourceInstanceVO>> {
        return Response.success(resourceBizService.findAllInstanceCanUse(poolName, type))
    }

    @Operation(summary = "实例下关联的部署组")
    @GetMapping("/pools/{poolName}:groupsByInstance")
    fun findAllDeployGroupByInstance(
        @PathVariable("poolName") poolName: String,
        @RequestParam instanceName: String
    ): Response<List<DeployGroupVO>> {
        return Response.success(deployGroupBizService.findAllByInstance(poolName, instanceName))
    }

    private fun fetchDataPlaneHandleSpec(
        type: ResourceInstance.InstanceConfigType,
        contextMap: MutableMap<String, String>
    ): String {
        return when (type) {
            ResourceInstance.InstanceConfigType.SELF_KUBERNETES -> {
                val map: Map<String, String> = contextMap
                val specMap = mapOf<String, Any>(
                    "type" to "persistent",
                    "kubeconfig" to map["content"]!!
                )
                jacksonObjectMapper().writeValueAsString(specMap)
            }
            else -> {
                val specMap = mapOf<String, Any>(
                    "type" to "mirana-proxy",
                    "age" to 0,
                    "expiration" to 0,
                    "deadline" to 0,
                    "aliyunPk" to 0,
                    "clusterId" to "any",
                    "kubeconfig" to ""
                )
                jacksonObjectMapper().writeValueAsString(specMap)
            }
        }
    }
}