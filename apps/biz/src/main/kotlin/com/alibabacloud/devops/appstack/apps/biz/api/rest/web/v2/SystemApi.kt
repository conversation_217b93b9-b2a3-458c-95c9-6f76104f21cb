package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.apps.biz.service.system.SystemBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.request.ng.*
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import com.alibabacloud.devops.appstack.libs.model.response.ng.AppWithSourcesVO
import com.alibabacloud.devops.appstack.libs.model.vo.ng.System
import com.alibabacloud.devops.iam.constant.SubjectType
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * @author: <EMAIL>
 * @date: 2023-10-21 11:36
 * @version: SystemApi, v0.1
 **/
@Tag(name = "系统", description = "系统领域 OpenAPI")
@RestController
@RequestMapping("/api/v2")
open class SystemApi {

    @Autowired
    lateinit var systemBizService: SystemBizService

    @VpcApi
    @Operation(summary = "分页查找系统", operationId = "ListSystems")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "分页查找完成（包括未查找到对象的情况）"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @GetMapping("/systems")
    fun listSystems(
        @Schema(description = "当前页号（从 1 开始，默认取 1）", example = "1", type = "integer", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @Schema(description = "分页记录数（默认 10 条）", example = "10", type = "integer", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Pagination<System> {
         return systemBizService.listRaw(current = current, pageSize = pageSize)
    }

    @VpcApi
    @Operation(summary = "创建系统", operationId = "CreateSystem")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "创建系统成功"),
            ApiResponse(responseCode = "409", description = "系统重名", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]),
            ApiResponse(responseCode = "422", description = "未指定系统名", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PostMapping("/systems")
    fun createSystem(
        @RequestBody request: CreateSystemRequest
    ): System {
        return systemBizService.create(request)
    }

    @VpcApi
    @Operation(summary = "更新系统信息", operationId = "UpdateSystem")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "更新成功"),
            ApiResponse(responseCode = "404", description = "未找到指定的系统", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PutMapping("/systems/{systemName}")
    fun updateSystem(
        @Schema(description = "系统名", example = "my-demo-system", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("systemName") systemName: String,
        @RequestBody req: UpdateSystemRequest
    ): System {
        return systemBizService.update(systemName, req)
    }

    @VpcApi
    @Operation(summary = "将应用关联到系统", operationId = "AttachApps")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "关联成功"),
            ApiResponse(responseCode = "404", description = "系统或待关联的应用未找到", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]),
            ApiResponse(responseCode = "409", description = "重复关联应用", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PostMapping("/systems/{systemName}/apps:attach")
    fun attachApps(
        @Schema(description = "系统名", example = "my-demo-system", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("systemName") systemName: String,
        @Schema(description = "待关联的应用名列表", type = "array", requiredMode = Schema.RequiredMode.REQUIRED, example = "[\n\t\"my-app\"\n]")
        @RequestBody targetAppNames: Set<String>
    ): Boolean {
        return systemBizService.attachAppsToSystem(systemName, targetAppNames)
    }

    @VpcApi
    @Operation(summary = "解除应用与系统的关联", operationId = "DetachApps")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "解除关联成功"),
            ApiResponse(responseCode = "404", description = "一个或多个应用未找到，又或是未关联到指定的系统", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PostMapping("/systems/{systemName}/apps:detach")
    fun detachApps(
        @Schema(description = "系统名", example = "my-demo-system", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("systemName") systemName: String,
        @Schema(description = "待解除关联的应用名列表", type = "array", requiredMode = Schema.RequiredMode.REQUIRED, example = "[\n\t\"my-app\"\n]")
        @RequestBody targetAppNames: Set<String>
    ): Boolean {
        return systemBizService.detachAppsFromSystem(systemName, targetAppNames)
    }

    @VpcApi
    @Operation(summary = "查找系统所关联的应用列表", operationId = "ListAttachedApps")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "分页查找完成（包括未查找到对象的情况）"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @GetMapping("/systems/{systemName}/apps")
    fun listAttachedApps(
        @Schema(description = "系统名", example = "my-demo-system", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("systemName") systemName: String,
        @Schema(description = "当前页号（从 1 开始，默认取 1）", example = "1", type = "integer", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @Schema(description = "分页记录数（默认 10 条）", example = "10", type = "integer", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Pagination<AppWithSourcesVO> {
        return systemBizService.listAttachedApps(systemName, current = current, pageSize = pageSize)
    }

    @VpcApi
    @Operation(summary = "查找系统成员列表", operationId = "ListSystemMembers")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "分页查找完成（包括未查找到对象的情况）"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @GetMapping("/systems/{systemName}/members")
    fun listSystemMembers(
        @Schema(description = "系统名", example = "my-demo-system", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("systemName") systemName: String,
        @Schema(description = "当前页号（从 1 开始，默认取 1）", example = "1", type = "integer", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @Schema(description = "分页记录数（默认 10 条）", example = "10", type = "integer", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Pagination<MemberVO> {
        return systemBizService.listMembers(systemName = systemName, current = current, pageSize = pageSize)
    }

    @VpcApi
    @Operation(summary = "添加系统成员", operationId = "CreateSystemMembers")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "添加系统成员成功"),
            ApiResponse(responseCode = "403", description = "没权限设置拥有者"),
            ApiResponse(responseCode = "404", description = "角色未找到"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PostMapping("/systems/{systemName}/members")
    fun createSystemMembers(
        @Schema(description = "系统名", example = "my-demo-system", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("systemName") systemName: String,
        @RequestBody addMembersRequest: AddMembersRequest
    ): Boolean {
        var flag = true
        addMembersRequest.roleNames.forEach {
            if (!systemBizService.addRoleMember(systemName, it, addMembersRequest.playerList)) {
                flag = false
            }
        }
        return flag
    }

    @VpcApi
    @Operation(summary = "删除系统成员", operationId = "DeleteSystemMember")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "删除系统成员成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @DeleteMapping("/systems/{systemName}/members")
    fun deleteSystemMember(
        @Schema(description = "系统", example = "my-demo-system", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("systemName") systemName: String,
        @Schema(description = "成员类型", example = "User", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @RequestParam subjectType: SubjectType,
        @Schema(description = "成员id", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @RequestParam subjectId: String,
    ): Boolean {
        systemBizService.updateMemberRole(
            systemName = systemName,
            subjectType = subjectType,
            subjectId = subjectId,
            roleNameList = emptyList()
        )
        return true
    }

    @VpcApi
    @Operation(summary = "更新系统成员角色", operationId = "UpdateSystemMember")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "更新系统成员角色成功"),
            ApiResponse(responseCode = "404", description = "系统角色未找到"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PutMapping("/systems/{systemName}/members")
    fun updateSystemMember(
        @Schema(description = "系统名", example = "my-demo-system", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("systemName") systemName: String,
        @RequestBody updateMemberRequest: UpdateMemberRequest
    ): Boolean {
        systemBizService.updateMemberRole(
            systemName = systemName,
            subjectType = updateMemberRequest.player.type,
            subjectId = updateMemberRequest.player.id,
            roleNameList = updateMemberRequest.roleNames,
        )
        return true
    }

}