package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import com.alibabacloud.devops.appstack.apps.biz.service.AppOrchestrationBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.Orchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.template.OrchestrationTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Branch
import com.alibabacloud.devops.appstack.libs.model.request.AppOrchestrationCreateRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.CreateAppOrchestrationRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateAppOrchestrationRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateAppOrchestrationRequestSpec
import com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateBuiltinAppOrchestrationRequestSpec
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * @author: <EMAIL>
 * @date: 2023-09-13 22:27
 * @version: AppOrchestrationApi, v0.1
 **/
@Tag(name = "应用编排", description = "应用编排领域 OpenAPI")
@RestController
@RequestMapping("/api/v2/apps/{appName}")
open class AppOrchestrationApi {

    @Autowired
    lateinit var appOrchestrationBizService: AppOrchestrationBizService

    @VpcApi
    @Operation(summary = "查找匹配给定环境的最新一版编排实例", operationId = "GetLatestOrchestration")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "成功查找到编排实例"),
            ApiResponse(responseCode = "404", description = "编排或其关联对象（环境、部署资源信息等）未找到，可参考错误码辨别原因", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]),
            ApiResponse(responseCode = "409", description = "部署组中存在多种计算资源，无法辨别部署架构并匹配与之相对应的编排", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @GetMapping("/envs/{envName}/orchestration:latestAvailable")
    fun findLatest(
        @Schema(description = "应用名", example = "my-web-service", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("appName") appName: String,
        @Schema(description = "环境名", example = "dev", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("envName") envName: String,
    ): AppOrchestration {
        return appOrchestrationBizService.findLatestForEnv(appName = appName, envName = envName)
    }

    @VpcApi
    @Operation(summary = "查找应用编排详情", operationId = "GetAppOrchestration")
    @GetMapping("/orchestrations/{sn}")
    fun findAppOrchestration(
        @Schema(description = "应用名", example = "my-web-service", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("appName") appName: String,
        @Schema(description = "应用编排唯一序列号，未指定 tag 和 sha 时将查找最新版本", example = "my-web-service@KUBERNETES", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("sn") sn: String,
        @Schema(description = "编排 tag", example = "demotag", type = "string", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
        @RequestParam("tagName") tagName: String? = null,
        @Schema(description = "编排 commit sha", example = "d23ccbd926c31e3ca0b153f22c4548b93b1426e", type = "string", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
        @RequestParam("sha") sha: String? = null,
    ): AppOrchestration {

        return appOrchestrationBizService.find(
            appName = appName,
            sn = sn,
            sha = sha,
            tagName = tagName
        ) ?: throw BizException(ErrorCode.AS_APP_ORC_NOT_FOUND)
    }

    @VpcApi
    @Operation(summary = "查找应用编排列表", operationId = "ListAppOrchestration")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "分页查找完成（包括未查找到对象的情况）"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @GetMapping("/orchestrations")
    fun listAppOrchestrations(
        @Schema(description = "应用名", example = "my-web-service", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("appName") appName: String
    ): Pagination<AppOrchestration> {

        val list = appOrchestrationBizService.findAll(appName)
        return Pagination(
            total = list.size.toLong(),
            current = 1L,
            pageSize = list.size.toLong(),
            pages = 1L,
            records = list
        )
    }

    @VpcApi
    @Operation(summary = "创建应用编排", operationId = "CreateAppOrchestration")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "应用编排创建成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PostMapping("/orchestrations")
    fun createAppOrchestration(
        @Schema(description = "应用名", example = "my-web-service", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("appName") appName: String,
        @RequestBody request: CreateAppOrchestrationRequest
    ): AppOrchestration {

        return appOrchestrationBizService.create(
            appName = appName,
            AppOrchestrationCreateRequest(
                storageType = Orchestration.StorageTypeEnum.BUILTIN,
                format = Orchestration.FormatEnum.MANIFEST,
                templateType = if (request.templateType != null) {
                    OrchestrationTemplate.TemplateScopeEnum.valueOf(request.templateType!!)
                } else {
                    null
                },
                templateId = request.templateId,
                datasource = null
            )
        )
    }

    @VpcApi
    @Operation(summary = "更新应用编排", operationId = "UpdateAppOrchestration")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "应用编排更新成功"),
            ApiResponse(responseCode = "404", description = "未查找到指定的应用编排"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PutMapping("/orchestrations/{sn}")
    fun updateAppOrchestration(
        @Schema(description = "应用名", example = "my-web-service", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("appName") appName: String,
        @Schema(description = "应用编排唯一序列号", example = "my-web-service@KUBERNETES", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("sn") sn: String,
        @RequestBody request: UpdateAppOrchestrationRequest
    ): AppOrchestration {

        val internalRequest = com.alibabacloud.devops.appstack.libs.model.request.UpdateAppOrchestrationRequest(
            name = request.name,
            format = Orchestration.FormatEnum.MANIFEST,
            description = request.description,
            componentList = if (request.spec != null && request.spec!!.type == UpdateAppOrchestrationRequestSpec.TYPE_BUILTIN) {
                (request.spec as UpdateBuiltinAppOrchestrationRequestSpec).componentList ?: mutableListOf()
            } else {
                mutableListOf()
            },
            placeholderList = if (request.spec != null && request.spec!!.type == UpdateAppOrchestrationRequestSpec.TYPE_BUILTIN) {
                (request.spec as UpdateBuiltinAppOrchestrationRequestSpec).placeholderList ?: mutableListOf()
            } else {
                mutableListOf()
            },
            groupNameMap = if (request.spec != null && request.spec!!.type == UpdateAppOrchestrationRequestSpec.TYPE_BUILTIN) {
                (request.spec as UpdateBuiltinAppOrchestrationRequestSpec).groupNameMap ?: mapOf()
            } else {
                mapOf()
            },
            label = if (request.spec != null && request.spec!!.type == UpdateAppOrchestrationRequestSpec.TYPE_BUILTIN) {
                (request.spec as UpdateBuiltinAppOrchestrationRequestSpec).labels ?: listOf()
            } else {
                listOf()
            }
        )
        internalRequest.fromRevisionSha = request.fromRevisionSha
        internalRequest.branchName = request.branchName ?: Branch.TRUNK
        internalRequest.message = request.commitMessage
        return appOrchestrationBizService.update(
            appName = appName,
            sn = sn,
            internalRequest
        )
    }

    @VpcApi
    @Operation(summary = "删除应用编排", operationId = "DeleteAppOrchestration")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "应用编排删除成功"),
            ApiResponse(responseCode = "404", description = "未查找到指定的应用编排"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @DeleteMapping("/orchestrations/{sn}")
    fun deleteAppOrchestration(
        @Schema(description = "应用名", example = "my-web-service", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("appName") appName: String,
        @Schema(description = "应用编排唯一序列号", example = "my-web-service@KUBERNETES", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("sn") sn: String,
    ): Boolean {
        appOrchestrationBizService.delete(appName = appName, sn = sn)
        return true
    }

}