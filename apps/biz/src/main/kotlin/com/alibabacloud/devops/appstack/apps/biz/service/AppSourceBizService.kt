package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.request.ng.CreateAppSourceRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateAppSourceRequest
import com.alibabacloud.devops.appstack.libs.model.vo.ng.AppSource

/**
 * @author: <EMAIL>
 * @date: 2023-10-12 10:25
 * @version: AppSourceBizService, v0.1
 **/
interface AppSourceBizService {
    fun create(appName: String, requestEntity: CreateAppSourceRequest): AppSource
    fun update(appName: String, requestEntity: UpdateAppSourceRequest): AppSource
    fun delete(appName: String, sn: String): Boolean
}