package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.DeployGroupBizService
import com.alibabacloud.devops.appstack.apps.biz.service.ResourceBizService

import com.alibabacloud.devops.appstack.libs.model.`do`.resource.DeployGroup
import com.alibabacloud.devops.appstack.libs.model.request.UpsertDeployGroupRequest
import com.alibabacloud.devops.appstack.libs.model.vo.DeployGroupVO
import com.alibabacloud.devops.appstack.resource.manager.spring.boot.starter.ResourceManagerFacades
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2022-08-02 11:08
 * @version: DeployGroupBizServiceImpl, v0.1
 **/
@Service
class DeployGroupBizServiceImpl : DeployGroupBizService {

    @Autowired
    lateinit var resourceManagerFacades: ResourceManagerFacades

    @Autowired
    lateinit var resourceBizService: ResourceBizService

    override fun findAllByInstance(poolName: String, instanceName: String): List<DeployGroupVO> {
        val resourceInstanceVO  = resourceBizService.findInstance(poolName,instanceName)
        val deployGroupToEnvMap = resourceInstanceVO?.relateAppToEnvMap?.flatMap {
            it.value
        }?.groupBy {  it.deployGroupName!! }
        val deployGroups = resourceManagerFacades.resourceDeployGroupFacade.findAllByInstance(poolName, instanceName)
        return deployGroups.map { group -> group.toVO(deployGroupToEnvMap?.get(group.name), group.claimList.map { it.toVO(resourceInstanceVO) }) }
    }

    override fun findAllByType(poolName: String, type: String): List<DeployGroup> {
        val instanceNameSet = resourceBizService.findAllInstanceCanUse(poolName, type).map { it.name }.toSet()
        return instanceNameSet.flatMap { oneInstanceName ->
            resourceManagerFacades.resourceDeployGroupFacade.findAllByInstance(poolName, oneInstanceName)
        }
    }

    override fun find(poolName: String, groupName: String): DeployGroup {
        return resourceManagerFacades.resourceDeployGroupFacade.find(poolName, groupName)
    }

    override fun create(poolName: String, upsertDeployGroupRequest: UpsertDeployGroupRequest): DeployGroup {
        return resourceManagerFacades.resourceDeployGroupFacade.create(poolName, upsertDeployGroupRequest)
    }

    override fun update(
        poolName: String,
        groupName: String,
        upsertDeployGroupRequest: UpsertDeployGroupRequest
    ): DeployGroup {
        return resourceManagerFacades.resourceDeployGroupFacade.update(poolName, groupName, upsertDeployGroupRequest)
    }

    override fun delete(poolName: String, groupName: String) {
        resourceManagerFacades.resourceDeployGroupFacade.delete(poolName, groupName)
    }

}