package com.alibabacloud.devops.appstack.apps.biz.service.client.api

import com.alibabacloud.devops.appstack.apps.biz.model.FlowRole
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ServiceConnection
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ServiceConnectionRegion
import com.alibabacloud.devops.appstack.libs.model.response.FlowApiResponse
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient
import retrofit2.http.GET
import retrofit2.http.HeaderMap
import retrofit2.http.POST
import retrofit2.http.Query

@RetrofitClient(baseUrl = "\${service.flow-service-connection.url}", callTimeoutMs = 20 * 1000)
interface FlowServiceConnectionApi {

    @GET("/inner/api/v2/role/permissions")
    fun queryRolePermission(
        @HeaderMap headers: Map<String, String>,
        @Query("id") roleId: String,
    ): FlowApiResponse<FlowRole>

    @POST("/inner/api/v2/role/permissions")
    fun updateRolePermission(
        @HeaderMap headers: Map<String, String>,
        @Query("id") roleId: String,
        @Query("name") roleName: String,
        @Query("permissions") permissions: String,
    ): FlowApiResponse<Boolean>

    @GET("/inner/api/v2/service/connection/listUserServiceConnections")
    fun find(
        @HeaderMap headers: Map<String, String>,
        @Query("scType") scType: String,
        @Query("userId") userId: String
    ): FlowApiResponse<List<ServiceConnection>>

    @GET("/inner/api/v2/aliyun/region/list")
    fun findRegionsByServiceConnectionId(
        @HeaderMap headers: Map<String, String>,
        @Query("scId") scId: Long
    ): FlowApiResponse<List<ServiceConnectionRegion>>

    @GET("/inner/api/v2/service/connection/list")
    fun findByIds(
        @HeaderMap headers: Map<String, String>,
        @Query("ids") ids: String,
    ): FlowApiResponse<List<ServiceConnection>>
}