package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.JobBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.SearchAppJobRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.SearchEnvJobRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.JobRecord
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2023-11-30 17:15
 * @version: JobBizApi, v0.1
 **/
@Tag(name = "Job", description = "环境部署单相关 API")
@RestController
@RequestMapping("/api/v1")
open class JobBizApi {

    @Autowired
    lateinit var jobBizService: JobBizService

    @PostMapping("/jobs")
    fun searchEnvJobPaginated(
        @RequestBody searchEnvJobRequest: SearchEnvJobRequest,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<JobRecord>> {
        val paginatedChangeOrders = jobBizService.searchEnvJobPaginated(searchEnvJobRequest, current, pageSize)
        return Response.success(paginatedChangeOrders)
    }

    @PostMapping("/jobs:search")
    fun searchAppJobPaginated(
        @RequestBody searchAppJobRequest: SearchAppJobRequest,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<JobRecord>> {
        val paginatedChangeOrders = jobBizService.searchAppJobPaginated(searchAppJobRequest, current, pageSize)
        return Response.success(paginatedChangeOrders)
    }
}