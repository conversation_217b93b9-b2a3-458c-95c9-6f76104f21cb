package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.apps.biz.service.system.ReleaseBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.ChangeRequest
import com.alibabacloud.devops.appstack.libs.model.`do`.release.AppRelease
import com.alibabacloud.devops.appstack.libs.model.`do`.release.AppReleaseInst
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.CreateAppReleaseRequest
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.UpdateAppReleaseRequest
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.UpsertAppReleaseItemRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.*
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import com.alibabacloud.devops.appstack.libs.model.vo.ReleaseExecutionVO
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.api.annotations.ParameterObject
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * @author: <EMAIL>
 * @date: 2023-10-17 17:32
 * @version: AppReleaseApi, v0.1
 **/
@Tag(name = "发布", description = "发布相关API")
@RestController
@RequestMapping("/api/v2")
class ReleaseApi {

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var releaseBizService: ReleaseBizService

    @VpcApi
    // TODO appRelease -> release
    @Operation(summary = "查找发布详情", operationId = "GetRelease")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已按 sn 查找到发布详情"),
            ApiResponse(
                responseCode = "404",
                description = "未查找到发布详情",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/systems/{systemName}/releases/{sn}")
    fun getRelease(
        @Schema(
            description = "系统名",
            example = "my-demo-system",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("systemName") systemName: String,
        @Schema(description = "发布 sn", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("sn") sn: String
    ): ReleaseVO {
        return workflowControllerFacade.appReleaseFacade.find(appName = systemName, sn = sn)?.toNormalizedVO()
            ?: throw BizException(ErrorCode.AS_APP_RELEASE_NOT_FOUND)
    }

    @VpcApi
    @Operation(summary = "查找发布关联的变更列表", operationId = "ListAttachedChangeRequests")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查找发布关联的变更列表成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/systems/{systemName}/releases/{releaseSn}/changeRequests")
    fun listAttachedChangeRequests(
        @PathVariable systemName: String,
        @PathVariable releaseSn: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Pagination<ChangeRequest> {
        return releaseBizService.listAttachedChangeRequests(
            appName = systemName,
            releaseSn = releaseSn,
            current = current,
            pageSize = pageSize
        )
    }

    @Operation(summary = "搜索发布", operationId = "SearchReleases")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "分页搜索完成（包括未匹配到对象的情况）"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/releases:search")
    fun searchRelease(
        @ParameterObject query: AppReleaseSearch
    ): PageList<ReleaseRecord> {
        val rawResult = workflowControllerFacade.appReleaseFacade.searchPaginatedV2(query)
        return PageList(
            nextToken = rawResult.nextToken,
            data = rawResult.data.map { it.toNormalizedVO() }
        )
    }

    @VpcApi
    @Operation(summary = "创建发布", operationId = "CreateRelease")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "创建发布成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/systems/{systemName}/releases")
    fun createRelease(
        @PathVariable systemName: String,
        @RequestBody request: CreateReleaseRequest
    ): ReleaseVO {
        return releaseBizService.create(
            systemName = systemName,
            CreateAppReleaseRequest(
                name = request.name,
                version = request.version,
                baseAppReleaseSn = request.baseAppReleaseSn,
                description = request.description,
                scheduledReleaseTime = request.scheduledReleaseTime,
                workItemIds = request.workItemIds,
                ownerId = request.ownerId,
                members = request.members,
                relations = request.relations,
                attachedChangeRequestSnList = request.attachedChangeRequestSnList,
                releaseItems = request.releaseItems.map { it.toUpsertAppReleaseItemRequest() }
            )
        ).toNormalizedVO()
    }

    @Operation(summary = "更新发布", operationId = "UpdateRelease")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "更新发布成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PutMapping("/systems/{systemName}/releases/{sn}")
    fun updateRelease(
        @PathVariable systemName: String,
        @PathVariable sn: String,
        @RequestBody request: UpdateReleaseRequest
    ): ReleaseVO {
        return releaseBizService.update(systemName, sn,
            UpdateAppReleaseRequest(
                version = request.version,
                name = request.name,
                description = request.description,
                note = request.note,
                scheduledReleaseTime = request.scheduledReleaseTime,
                workItemIds = request.workItemIds,
                relations = request.relations,
                releaseItems = request.releaseItems.map { it.toUpsertAppReleaseItemRequest() }
            )
        ).toNormalizedVO()
    }

    @VpcApi
    @Operation(summary = "查找发布成员列表", operationId = "ListReleaseMembers")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查找发布成员列表成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/systems/{systemName}/releases/{sn}/members")
    fun listReleaseMembers(
        @PathVariable systemName: String,
        @PathVariable sn: String
    ): Pagination<MemberVO> {
        return releaseBizService.listMembers(systemName, sn, 1L, 100L)
    }

    @VpcApi
    @Operation(summary = "查找发布产物列表", operationId = "ListReleaseProducts")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查找发布产物列表成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/systems/{systemName}/releases/{sn}/products")
    fun listReleaseProducts(
        @PathVariable systemName: String,
        @PathVariable sn: String
    ): List<AppReleaseInst.Product> {
        val res = releaseBizService.findInstanceLatest(systemName, sn)
        return res?.products ?: emptyList()
    }

    @Operation(summary = "废弃发布", operationId = "CloseRelease")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "废弃发布成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PutMapping("/systems/{systemName}/releases/{sn}:close")
    fun closeRelease(
        @PathVariable systemName: String,
        @PathVariable sn: String,
    ): Response<Boolean> {
        return Response.success(releaseBizService.close(systemName, sn))
    }


    @Operation(summary = "查询发布执行记录", operationId = "ListReleaseExecutions")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询发布执行记录成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/systems/{systemName}/releases/{sn}/executions")
    fun listReleaseExecutions(
        @PathVariable systemName: String,
        @PathVariable sn: String,
        @ParameterObject query: MetadataSearch,
    ): Pagination<ReleaseExecutionVO> {
        return releaseBizService.listReleaseExecutions(systemName, sn, query)
    }

}

fun UpsertReleaseItemRequest.toUpsertAppReleaseItemRequest() = UpsertAppReleaseItemRequest(
    type = UpsertReleaseItemRequest.rawTypeToEnum(this.type),
    spec = when (this.type) {
        UpsertReleaseItemRequest.TYPE_APP -> {
            val typedRequest =
                this as com.alibabacloud.devops.appstack.libs.model.request.ng.UpsertAppReleaseItemRequest
            AppRelease.AppReleaseItem.AppSpec(
                appName = typedRequest.appName,
                codeRepos = typedRequest.codeRepos?.map { appCodeRepoReferenceRequest ->
                    AppRelease.AppReleaseItem.AppSpec.CodeRepo(
                        context = AppRelease.AppReleaseItem.CodeContext(
                            fromRevision = appCodeRepoReferenceRequest.fromRevision,
                            createBranch = appCodeRepoReferenceRequest.createBranch
                        ),
                        spec = AppRelease.AppReleaseItem.AppCodeRepoSpec(
                            appCodeRepoSn = appCodeRepoReferenceRequest.appCodeRepoSn,
                            branchName = appCodeRepoReferenceRequest.branchName,
                            autoDeleteBranchWhenEnd = appCodeRepoReferenceRequest.autoRemoveBranchAfterReleaseEnd
                        )
                    )
                } ?: emptyList(),
                artifactRepos = typedRequest.artifactRepos?.map { appArtifactRepoReferenceRequest ->
                    val versionType = AppRelease.AppReleaseItem.AppSpec.AppArtifactRepoSpec.Type.valueOf(
                        AppArtifactLocator.locatingStyleToInnerCorresponding(appArtifactRepoReferenceRequest.locator.locatingStyle)
                    )
                    AppRelease.AppReleaseItem.AppSpec.AppArtifactRepoSpec(
                        appArtifactRepoSn = appArtifactRepoReferenceRequest.artifactRepoSn,
                        versionType = versionType,
                        specificVersion = if (versionType == AppRelease.AppReleaseItem.AppSpec.AppArtifactRepoSpec.Type.SPECIFIC) {
                            (appArtifactRepoReferenceRequest.locator as VersionSpecifiedAppArtifactLocator).version
                        } else {
                            null
                        },
                        filterSpec = if (versionType == AppRelease.AppReleaseItem.AppSpec.AppArtifactRepoSpec.Type.FILTER) {
                            (appArtifactRepoReferenceRequest.locator as VersionFilteringAppArtifactLocator).filterKeyValues
                        } else {
                            null
                        }
                    )
                } ?: emptyList()
            )
        }
        UpsertReleaseItemRequest.TYPE_GENERIC -> {
            val typedRequest = this as UpsertGenericReleaseItemRequest
            AppRelease.AppReleaseItem.AppGenericSpec(
                name = typedRequest.name,
                value = typedRequest.value,
                description = typedRequest.description ?: ""
            )
        }
        else -> throw RuntimeException("Unknown type: ${this.type}")
    }
)