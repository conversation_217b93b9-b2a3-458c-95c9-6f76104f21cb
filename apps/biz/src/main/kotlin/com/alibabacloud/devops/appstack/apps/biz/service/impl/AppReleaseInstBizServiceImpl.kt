package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.AppReleaseInstBizService
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.UpdateAppReleaseInstRequest
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2023-03-10 14:22
 * @version: AppReleaseInstBizServiceImpl, v0.1
 **/
@Service
class AppReleaseInstBizServiceImpl : AppReleaseInstBizService {

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    override fun update(appName: String, appReleaseSn: String, appReleaseInstSn: String, request: UpdateAppReleaseInstRequest) {
        workflowControllerFacade.appReleaseInstFacade.update(
            appName = appName,
            appReleaseInstSn = appReleaseInstSn,
            updateAppReleaseInstRequest = request
        )
    }

}