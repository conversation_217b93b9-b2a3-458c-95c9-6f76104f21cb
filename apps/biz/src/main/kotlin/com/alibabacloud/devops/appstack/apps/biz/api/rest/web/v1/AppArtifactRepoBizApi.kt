package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.AppArtifactRepoBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppArtifactRepo
import com.alibabacloud.devops.appstack.libs.model.request.CreateAppArtifactRepoRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppArtifactRepoRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 * @date 2023-11-02
 */
@Tag(name = "AppArtifactRepo", description = "应用制品仓库 API")
@RestController
@RequestMapping("/api/v1")
open class AppArtifactRepoBizApi {

    @Autowired
    lateinit var appArtifactRepoBizService: AppArtifactRepoBizService

    @Operation(summary = "创建应用制品仓库")
    @PostMapping("/apps/{appName}/artifactRepos")
    fun createAppArtifactRepo(
        @PathVariable appName: String,
        @RequestBody request: CreateAppArtifactRepoRequest
    ): Response<AppArtifactRepo> {
        return Response.success(appArtifactRepoBizService.create(appName, request))
    }


    @Operation(summary = "删除应用制品仓库")
    @DeleteMapping("/apps/{appName}/artifactRepos/{sn}")
    fun deleteAppArtifactRepo(
        @PathVariable appName: String,
        @PathVariable sn: String,
    ): Response<AppArtifactRepo> {
        appArtifactRepoBizService.delete(appName, sn)
        return Response.success()
    }

    @Operation(summary = "修改应用制品仓库")
    @PutMapping("/apps/{appName}/artifactRepos/{sn}")
    fun updateAppArtifactRepo(
        @PathVariable appName: String,
        @PathVariable sn: String,
        @RequestBody request: UpdateAppArtifactRepoRequest
    ): Response<AppArtifactRepo> {
        return Response.success(appArtifactRepoBizService.update(appName, sn, request))
    }

    @Operation(summary = "查找应用下的应用制品仓库")
    @GetMapping("/apps/{appName}/artifactRepos")
    fun findAllAppArtifactRepo(@PathVariable appName: String): Response<List<AppArtifactRepo>> {
        return Response.success(appArtifactRepoBizService.findAll(appName))
    }

    @Operation(summary = "查找单个应用制品仓库")
    @GetMapping("/apps/{appName}/artifactRepos/{sn}")
    fun findAppArtifactRepo(@PathVariable appName: String, @PathVariable sn: String): Response<AppArtifactRepo> {
        return Response.success(appArtifactRepoBizService.find(appName, sn))
    }
}