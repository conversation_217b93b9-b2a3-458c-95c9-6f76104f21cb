package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import com.alibabacloud.devops.appstack.apps.biz.model.vo.ChangeRequestVO
import com.alibabacloud.devops.appstack.apps.biz.model.vo.toChangeRequestVO
import com.alibabacloud.devops.appstack.apps.biz.service.AuditItemBizService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestBizService
import com.alibabacloud.devops.appstack.apps.biz.service.system.ReleaseBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.audit.AuditItem
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.ChangeRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.CreateChangeRequestRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.ChangeRequestPopSearch
import com.alibabacloud.devops.appstack.libs.model.request.ng.ChangeRequestSearch
import com.alibabacloud.devops.appstack.libs.model.request.ng.MetadataSearch
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeRequestExecutionVO
import com.aliyun.amp.plugin.annotation.AmpApi
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.api.annotations.ParameterObject
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @create 2023/12/25 7:37 PM
 **/
@Tag(name = "变更", description = "变更相关API")
@RestController
@RequestMapping("/api/v2")
class ChangeRequestApi {

    @Autowired
    lateinit var auditItemBizService: AuditItemBizService

    @Autowired
    lateinit var changeRequestBizService: ChangeRequestBizService

    @VpcApi
    @Operation(summary = "查找变更关联的审批项列表", operationId = "GetChangeRequestAuditItems")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查找变更关联的审批项列表"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/changeRequests/{sn}/auditItems")
    fun getChangeRequestAuditItems(
        @PathVariable appName: String,
        @PathVariable sn: String,
        @RequestParam("refType", required = false, defaultValue = "CR") refType: AuditItem.RefType,
    ): List<AuditItem> {
        return auditItemBizService.findAll(refType, sn)
    }

    @VpcApi
    @Operation(summary = "关闭变更", operationId = "CancelChangeRequest")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "关闭变更成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/apps/{appName}/changeRequests/{sn}:cancel")
    fun cancelChangeRequest(
        @PathVariable appName: String,
        @PathVariable sn: String,
    ): Boolean {
        changeRequestBizService.close(appName, sn)
        return true
    }

    @VpcApi
    @Operation(summary = "完成变更", operationId = "CloseChangeRequest")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "完成变更成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/apps/{appName}/changeRequests/{sn}:finish")
    fun closeChangeRequest(
        @PathVariable appName: String,
        @PathVariable sn: String,
    ): Boolean {
        changeRequestBizService.finish(appName, sn)
        return true
    }

    @AmpApi(
        name = "CreateChangeRequest",
        summary = "创建变更",
        method = "post",
        path = "/appstack/apps/{appName}/changeRequests",
        operationType = "write"
    )
    @VpcApi
    @Operation(summary = "创建变更", operationId = "CreateChangeRequest")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "创建变更成功"),
            ApiResponse(
                responseCode = "500",
                description = "变更创建失败 / 服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/apps/{appName}/changeRequests")
    fun createChangeRequest(
        @PathVariable appName: String,
        @RequestBody req: CreateChangeRequestRequest,
    ): ChangeRequestVO {
        return changeRequestBizService.create(appName, req.toCreateChangeRequest(appName)).toChangeRequestVO()
    }

    @AmpApi(
        name = "ListChangeRequests",
        summary = "查询变更列表",
        method = "get",
        path = "/appstack/apps/{appName}/changeRequests",
        operationType = "write"
    )
    @VpcApi
    @Operation(summary = "查询变更列表", operationId = "ListChangeRequests")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询变更列表成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/changeRequests")
    fun listChangeRequests(
        @PathVariable appName: String,
        @ParameterObject query: ChangeRequestPopSearch,
    ): PageList<ChangeRequestVO> {
        val list = changeRequestBizService.list(query.toChangeRequestSearch(appName))
        return list.toPageList(list.data.map { it.toChangeRequestVO() })
    }

    @AmpApi(
        name = "ListChangeRequestWorkflowExecutions",
        summary = "查询变更研发流程运行记录",
        method = "get",
        path = "/appstack/apps/{appName}/changeRequests/{sn}/executions",
        operationType = "write"
    )
    @VpcApi
    @Operation(summary = "查询变更研发流程运行记录", operationId = "ListWorkflowExecutions")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询变更研发流程运行记录成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/changeRequests/{sn}/executions")
    fun listWorkflowExecutions(
        @PathVariable appName: String,
        @PathVariable sn: String,
        @ParameterObject query: MetadataSearch,
    ): Pagination<ChangeRequestExecutionVO> {
        return changeRequestBizService.listChangeRequestMetadata(appName, sn, query)
    }
}