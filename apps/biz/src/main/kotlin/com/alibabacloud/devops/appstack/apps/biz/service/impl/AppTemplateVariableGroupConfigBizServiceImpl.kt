package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateVariableGroupConfigBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.UuidUtils
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.VariableGroupConfiguration
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.VariableTemplateBatchUpdateRequest
import com.alibabacloud.devops.appstack.libs.model.vo.AppTemplateVariableGroupDiffDTO
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.UnicastSender
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.AppTemplateVariableGroupBatchUpdateBody
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.UnicastTagEnum
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2023-10-26
 */
@Service
open class AppTemplateVariableGroupConfigBizServiceImpl : AppTemplateVariableGroupConfigBizService {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var unicastSender: UnicastSender

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.ORG_APP_TEMPLATE_VIEW),
            Access(action = Action.APP_TEMPLATE_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_EDIT, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_USE, resourceArgIndex = 0),
        ]
    )
    override fun findByRevison(appTemplateName: String, sha: String): VariableGroupConfiguration {
        val findRevison = coreFacades.variableTemplateFacade.findByRevisionSha(appTemplateName, sha)
        return VariableGroupConfiguration(
            profileMap = findRevison.profileMap,
            revision = findRevison.revision,
            branchInfo = findRevison.branchInfo
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.ORG_APP_TEMPLATE_VIEW),
            Access(action = Action.APP_TEMPLATE_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_EDIT, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_USE, resourceArgIndex = 0),
        ]
    )
    override fun listRevisionPaginated(appTemplateName: String, current: Long, pageSize: Long): Pagination<Revision> {
        return coreFacades.variableTemplateFacade.findPaginatedRevision(appTemplateName, current, pageSize)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.ORG_APP_TEMPLATE_VIEW),
            Access(action = Action.APP_TEMPLATE_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_EDIT, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_USE, resourceArgIndex = 0),
        ]
    )
    override fun compareRevision(
        appTemplateName: String,
        beforeRevisionSha: String,
        afterRevisionSha: String
    ): AppTemplateVariableGroupDiffDTO {
        val (beforeRevision, afterRevision, diffItems) = coreFacades.variableTemplateFacade.compare(
            appTemplateName,
            beforeRevisionSha,
            afterRevisionSha
        )
        return AppTemplateVariableGroupDiffDTO(
            before = VariableGroupConfiguration(
                profileMap = beforeRevision.profileMap,
                revision = beforeRevision.revision,
                branchInfo = beforeRevision.branchInfo
            ),
            after = VariableGroupConfiguration(
                profileMap = afterRevision.profileMap,
                revision = afterRevision.revision,
                branchInfo = afterRevision.branchInfo
            ),
            diffItems = diffItems
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.APP_TEMPLATE_EDIT, resourceArgIndex = 0),
        ]
    )
    override fun batchUpdate(appTemplateName: String, request: VariableTemplateBatchUpdateRequest) {
        if (request.appNames.isEmpty()) {
            throw BizException(ErrorCode.AS_APP_NOT_FOUND)
        }
        unicastSender.send(
            UnicastTagEnum.APPTEMPLATE_VARIABLE_BATCH_UPDATE,
            jacksonObjectMapper().writeValueAsBytes(
                AppTemplateVariableGroupBatchUpdateBody(
                    appTemplateName = appTemplateName,
                    profile = request.profile,
                    mode = request.mode,
                    appNames = request.appNames,
                    commitMsg = request.commitMsg,
                    transactionId = request.transactionId ?: UuidUtils.getUuid()
                )
            )
        )
    }
}