package com.alibabacloud.devops.appstack.apps.biz.service.system.impl

import com.alibabacloud.devops.appstack.apps.biz.annotation.ApiMetricCounter
import com.alibabacloud.devops.appstack.apps.biz.annotation.IsSystem
import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.system.ReleaseBizService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.apps.biz.service.OrgConfigItemBizService
import com.alibabacloud.devops.appstack.apps.biz.service.system.SystemBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.*
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.ChangeRequest
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.WorkflowMetadata
import com.alibabacloud.devops.appstack.libs.model.`do`.release.AppRelease
import com.alibabacloud.devops.appstack.libs.model.`do`.release.AppReleaseInst
import com.alibabacloud.devops.appstack.libs.model.`do`.release.AppReleaseRecord
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.iam.ReleaseRole
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.org.event.ReleaseAudit
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.*
import com.alibabacloud.devops.appstack.libs.model.request.ng.MetadataSearch
import com.alibabacloud.devops.appstack.libs.model.response.ng.AppWithSourcesVO
import com.alibabacloud.devops.appstack.libs.model.vo.AppReleaseRecordWithOwner
import com.alibabacloud.devops.appstack.libs.model.vo.ReleaseExecutionVO
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.exception.IamForbiddenException
import com.alibabacloud.devops.iam.model.ResourcePlayer
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import org.springframework.aop.framework.AopContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.regex.Pattern
import kotlin.math.ceil

/**
 * @author: <EMAIL>
 * @date: 2023-03-02 18:36
 * @version: AppReleaseBizServiceImpl, v0.1
 **/
@Service
open class ReleaseBizServiceImpl : ReleaseBizService {

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var systemBizService: SystemBizService

    @Autowired
    lateinit var iamService: IamService

    @Autowired
    lateinit var orgConfigItemBizService: OrgConfigItemBizService

    @Autowired
    lateinit var auditLogService: AuditLogService

    @ApiMetricCounter(metricName = "release_operation", methodTag = "create")
    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_CREATE, resourceArgIndex = 0),
        ]
    )
    @IsSystem
    override fun create(systemName: String, createAppReleaseRequest: CreateAppReleaseRequest): AppRelease {
        validateReleaseVersionUsingOrgConfig(createAppReleaseRequest.version)
        fulfillSpec4UpsertReleaseItemRequest(systemName, createAppReleaseRequest.releaseItems)
        val appRelease = workflowControllerFacade.appReleaseFacade.create(systemName, createAppReleaseRequest)
        iamService.registerResource(
            ProtocolType.AppStackRelease,
            resourceName = appRelease.iamResourceName(),
            AuthUtil.getTenant(),
            createAppReleaseRequest.ownerId
        )
        if (!createAppReleaseRequest.members.isNullOrEmpty()) {
            iamService.updateRole(
                ProtocolType.AppStackRelease,
                resourceName = appRelease.iamResourceName(),
                roleName = ReleaseRole.user.name,
                playerList = createAppReleaseRequest.members!!.map {
                    ResourcePlayerRequest(it, SubjectType.User)
                }
            )
        }
        try {
            if (!createAppReleaseRequest.attachedChangeRequestSnList.isNullOrEmpty()) {
                attachChangeRequests(
                    appName = systemName,
                    releaseSn = appRelease.sn,
                    changeRequestSnList = createAppReleaseRequest.attachedChangeRequestSnList!!
                )
            }
        } catch (e: Throwable) {
            delete(systemName, appRelease.sn)
            throw e
        }
        auditLogService.log(OrgEventType.APP_RELEASE_CREATE_HOOK,
            ReleaseAudit(
                target = AuditTarget(id = appRelease.sn, name = appRelease.name),
                systemName = systemName,
            )
        )
        auditLogService.log(OrgEventType.APP_RELEASE_CREATE_AUDIT,
            ReleaseAudit(
                target = AuditTarget(id = appRelease.sn, name = appRelease.name),
                systemName = systemName,
            )
        )
        return appRelease
    }

    private fun validateReleaseVersionUsingOrgConfig(version: String){
        val orgConfigItem = orgConfigItemBizService.find(OrgConfigItem.RELEASE_VERSION_REGEX) as ReleaseVersionRegexV1OrgConfigItem
        if(orgConfigItem.enable && orgConfigItem.releaseVersionRegex.isNotBlank()){
            val pattern = Pattern.compile(orgConfigItem.releaseVersionRegex)
            if (!pattern.matcher(version).find()) {
                throw BizException(ErrorCode.AS_APP_RELEASE_REVISION_ORG_CONFIG_VALIDATE_FAILED)
            }
        }
    }

    private fun fulfillSpec4UpsertReleaseItemRequest(
        systemName: String,
        releaseItemRequestList: List<UpsertAppReleaseItemRequest>
    ) {
        // 查询出所有sources
        val appWithSourceList = mutableListOf<AppWithSourcesVO>()
        var page = 1L
        var totalPage: Long
        do {
            val pageResult = systemBizService.listAttachedApps(systemName, page, 50L)
            appWithSourceList.addAll(pageResult.records)
            totalPage = pageResult.total
            page++
        } while (page <= totalPage)

        // 填充应用发布的context
        releaseItemRequestList.forEach { upsertRequest ->
            if(upsertRequest.sn.isNullOrBlank()){
                // 只有新加的发布项做 spec 填充，已有的不更新
                val spec = upsertRequest.spec
                if (spec is AppRelease.AppReleaseItem.AppSpec) {
                    val appWithSource = appWithSourceList.firstOrNull { spec.appName == it.name }
                    checkExists(appWithSource) { ErrorCode.AS_APP_NOT_FOUND }
                    val appCodeRepos = appWithSource.codeRepos
                    val appArtifactRepos = appWithSource.artifactRepos
                    spec.codeRepos.forEach { codeRepoInRequest ->
                        val appCodeRepo = appCodeRepos.firstOrNull { codeRepoInRequest.spec!!.appCodeRepoSn == it.sn }
                        checkExists(appCodeRepo) { ErrorCode.AS_APP_CODE_REPO_NOT_FOUND }
                        codeRepoInRequest.spec?.apply {
                            this.identifier = appCodeRepo.identifier
                            this.repoContext = appCodeRepo.repoContext
                            this.connectionConfig = appCodeRepo.connectionConfig
                        }
                    }
                    spec.artifactRepos.forEach { artifactRepoInRequest ->
                        val artifactRepo = appArtifactRepos.firstOrNull { artifactRepoInRequest.appArtifactRepoSn == it.sn }
                        checkExists(artifactRepo) { ErrorCode.AS_APP_ARTIFACT_REPO_NOT_FOUND }
                        artifactRepoInRequest.apply {
                            this.identifier = artifactRepo.identifier
                            this.artifactContext = artifactRepo.repoContext
                            this.connectionConfig = artifactRepo.connectionConfig
                        }
                    }
                }
            }
        }
    }

    @ApiMetricCounter(metricName = "release_operation", methodTag = "delete")
    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_EDIT, resourceArgIndex = 0, resourceArgSecondIndex = 1)
        ]
    )
    @IsSystem
    override fun delete(systemName: String, sn: String): Boolean {
        val success = workflowControllerFacade.appReleaseFacade.delete(systemName, sn)
        if (success) {
            iamService.unregisterResource(
                ProtocolType.AppStackRelease,
                resourceName = AppRelease.iamResourceName(appName = systemName, sn = sn),
                orgId = AuthUtil.getTenant(),
                operatorId = AuthUtil.getUserId(),
            )
        }
        return success
    }

    @ApiMetricCounter(metricName = "release_operation", methodTag = "update")
    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_EDIT, resourceArgIndex = 0, resourceArgSecondIndex = 1)
        ]
    )
    @IsSystem
    override fun update(systemName: String, sn: String, updateAppReleaseRequest: UpdateAppReleaseRequest): AppRelease {
        updateAppReleaseRequest.releaseItems?.let {
            fulfillSpec4UpsertReleaseItemRequest(systemName, it)
        }
        val release = workflowControllerFacade.appReleaseFacade.update(appName = systemName, sn = sn, updateAppReleaseRequest)
        auditLogService.log(OrgEventType.APP_RELEASE_UPDATE_HOOK,
            ReleaseAudit(
                target = AuditTarget(id = sn, name = release.name),
                systemName = systemName,
            )
        )
        auditLogService.log(OrgEventType.APP_RELEASE_UPDATE_AUDIT,
            ReleaseAudit(
                target = AuditTarget(id = sn, name = release.name),
                systemName = systemName,
            )
        )
        return release
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_VIEW, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_EDIT, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_CLOSE, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    @IsSystem
    override fun find(systemName: String, sn: String): AppRelease? {
        val release = workflowControllerFacade.appReleaseFacade.find(systemName, sn)
            ?: return null
        try {
            release.ownerId = findAppReleaseOwner(release)
        } catch (e: Throwable) {
            logger.error("Failed to find release owner, skipped: message=${e.message ?: "null"}", e)
        }
        return release
    }

    /**
     * 查找有发布权限的，方法内有鉴权，无需Can annotation
     */
    @IsSystem
    override fun searchReleasablePaginated(
        systemName: String,
        current: Long,
        pageSize: Long,
        request: SearchAppReleasableRequest,
    ): Pagination<AppReleaseRecordWithOwner> {
        val recordPagination = try {
            // 查找应用下的全部发布，需要企业级的「管理应用」或者 应用级的「管理发布」权限
            (AopContext.currentProxy() as ReleaseBizServiceImpl).searchPaginatedUsingAdminPermission(
                systemName = systemName,
                current = current,
                pageSize = pageSize,
                request = SearchAppReleaseRequest(
                    keyword = request.keyword,
                    states = request.states,
                )
            )
        } catch (e: IamForbiddenException) {
            // 无上述权限时，查找我参与的发布
            searchMine(
                appName = systemName,
                keyword = request.keyword,
                states = request.states,
                current = current,
                pageSize = pageSize
            )
        }
        return recordPagination.transfer(
            fillOwner(recordPagination.records)
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
        ]
    )
    @IsSystem
    open fun searchPaginatedUsingAdminPermission(
        systemName: String,
        current: Long,
        pageSize: Long,
        request: SearchAppReleaseRequest,
    ): Pagination<AppReleaseRecord> {
        return workflowControllerFacade.appReleaseFacade.searchPaginated(
            request.toInnerSearchAppReleaseRequest(systemName),
            current,
            pageSize
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
        ]
    )
    @IsSystem
    override fun searchPaginatedUnderApp(
        systemName: String,
        current: Long,
        pageSize: Long,
        request: SearchAppReleaseRequest
    ): Pagination<AppReleaseRecordWithOwner> {
        val recordPagination = if (request.isMine == true) {
            searchMine(
                appName = systemName,
                keyword = request.keyword,
                states = request.states,
                current = current,
                pageSize = pageSize
            )
        } else {
            workflowControllerFacade.appReleaseFacade.searchPaginated(
                request.toInnerSearchAppReleaseRequest(systemName),
                current,
                pageSize
            )
        }
        return recordPagination.transfer(
            fillOwner(recordPagination.records)
        )
    }

    override fun searchPaginated(
        searchOrgAppReleaseRequest: SearchOrgAppReleaseRequest,
        current: Long,
        pageSize: Long,
    ): Pagination<AppReleaseRecordWithOwner> {
        val orgConfigItem = orgConfigItemBizService.find(OrgConfigItem.ONLY_VIEW_ACCESSABLE_SYSTEM)
        // 如果勾选了【仅支持查看我有权限的系统列表】
        if ((orgConfigItem as? OnlyViewAccessableSystemV1OrgConfigItem)?.enable == true) {
            // 如果查询全部但没有查看全部系统权限
            if (searchOrgAppReleaseRequest.isMine == false
                && searchOrgAppReleaseRequest.systemName.isEmpty()
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_SYSTEM_VIEW)
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_SYSTEM_MANAGE)) {
                throw BizException(ErrorCode.AS_PERMISSION_DENIED)
            }
            // 如果查询指定系统但没有查看指定系统权限
            if (searchOrgAppReleaseRequest.systemName.isNotEmpty()
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_SYSTEM_VIEW)
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_SYSTEM_MANAGE)
                && searchOrgAppReleaseRequest.systemName.firstOrNull {
                    !iamService.can(ProtocolType.AppStackSystem, it, Action.SYSTEM_VIEW)
                            && !iamService.can(ProtocolType.AppStackSystem, it, Action.SYSTEM_RELEASE_MANAGE)
                } != null) {
                throw BizException(ErrorCode.AS_PERMISSION_DENIED)
            }
        }
        val recordPagination = if (searchOrgAppReleaseRequest.isMine == true) {
            searchMine(
                appNames = searchOrgAppReleaseRequest.systemName,
                keyword = searchOrgAppReleaseRequest.keyword,
                states = searchOrgAppReleaseRequest.states,
                current = current,
                pageSize = pageSize
            )
        } else {
            workflowControllerFacade.appReleaseFacade.searchPaginated(
                searchOrgAppReleaseRequest.toInnerSearchAppReleaseRequest(),
                current,
                pageSize
            )
        }
        return recordPagination.transfer(
            fillOwner(recordPagination.records)
        )
    }

    @ApiMetricCounter(metricName = "release_operation", methodTag = "close")
    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_CLOSE, resourceArgIndex = 0, resourceArgSecondIndex = 1)
        ]
    )
    @IsSystem
    override fun close(systemName: String, sn: String): Boolean {
        val releae = workflowControllerFacade.appReleaseFacade.find(systemName, sn)
        val result = workflowControllerFacade.appReleaseFacade.close(systemName, sn)
        if(result){
            auditLogService.log(OrgEventType.APP_RELEASE_CLOSE_HOOK,
                ReleaseAudit(
                    target = AuditTarget(id = sn, name = releae!!.name),
                    systemName = systemName,
                )
            )
            auditLogService.log(OrgEventType.APP_RELEASE_CLOSE_AUDIT,
                ReleaseAudit(
                    target = AuditTarget(id = sn, name = releae.name),
                    systemName = systemName,
                )
            )
        }
        return result
    }

    @ApiMetricCounter(metricName = "release_operation", methodTag = "release")
    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_CLOSE, resourceArgIndex = 0, resourceArgSecondIndex = 1)
        ]
    )
    @IsSystem
    override fun release(systemName: String, sn: String, instSn: String): Boolean {
        val releae = workflowControllerFacade.appReleaseFacade.find(systemName, sn)
        val result = workflowControllerFacade.appReleaseFacade.release(systemName, sn, instSn)
        if(result){
            auditLogService.log(OrgEventType.APP_RELEASE_FINISH_HOOK,
                ReleaseAudit(
                    target = AuditTarget(id = sn, name = releae!!.name),
                    systemName = systemName,
                )
            )
            auditLogService.log(OrgEventType.APP_RELEASE_FINISH_AUDIT,
                ReleaseAudit(
                    target = AuditTarget(id = sn, name = releae.name),
                    systemName = systemName,
                )
            )
        }
        return result
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_VIEW, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_EDIT, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_CLOSE, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    @IsSystem
    override fun findInstance(systemName: String, releaseSn: String, instanceSn: String): AppReleaseInst? {
        return workflowControllerFacade.appReleaseInstFacade.find(appName = systemName, appReleaseInstSn = instanceSn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_VIEW, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_EDIT, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_CLOSE, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    @IsSystem
    override fun findInstanceLatest(systemName: String, releaseSn: String): AppReleaseInst? {
        return workflowControllerFacade.appReleaseInstFacade.findLatest(appName = systemName, appReleaseSn = releaseSn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_VIEW, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_EDIT, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_CLOSE, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    @IsSystem
    override fun compareAppReleaseInst(
        systemName: String,
        releaseSn: String,
        from: String,
        to: String?,
    ): AppReleaseInst.ComparedResult {
        return workflowControllerFacade.appReleaseFacade.compareAppReleaseInst(
            appName = systemName,
            appReleaseSn = releaseSn,
            from = from,
            to = to
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_VIEW, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_EDIT, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_CLOSE, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    @IsSystem
    override fun findItemVersion(
        systemName: String,
        releaseSn: String,
        instanceSn: String?,
        itemSn: String,
    ): AppReleaseInst.Version {
        return workflowControllerFacade.appReleaseFacade.findItemVersion(
            appName = systemName,
            appReleaseSn = releaseSn,
            instanceSn = instanceSn,
            itemSn = itemSn,
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_VIEW, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_EDIT, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_CLOSE, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    @IsSystem
    override fun findInstancePaginated(
        systemName: String,
        releaseSn: String,
        current: Long,
        pageSize: Long,
    ): Pagination<AppReleaseInst> {
        return workflowControllerFacade.appReleaseInstFacade.findPaginated(
            appName = systemName,
            appReleaseSn = releaseSn,
            current = current,
            pageSize = pageSize,
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_VIEW, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_EDIT, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_CLOSE, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    @IsSystem
    override fun findMetadata(
        systemName: String,
        releaseSn: String,
        workflowSn: String,
    ): List<WorkflowMetadata> {
        return workflowControllerFacade.appReleaseFacade.findMetadata(
            appName = systemName,
            sn = releaseSn,
            workflowSn = workflowSn,
        )
    }

    override fun findInstanceByTrigger(systemName: String, trigger: AppReleaseInst.Trigger): AppReleaseInst? {
        return workflowControllerFacade.appReleaseInstFacade.findByTrigger(
            appName = systemName,
            trigger = trigger,
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_VIEW, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_EDIT, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_CLOSE, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    @IsSystem
    override fun findAllMember(systemName: String, releaseSn: String): Map<ResourcePlayer, List<Role>> {
        val playerRolesMap = iamService.findPlayerRoles(
            protocolType = ProtocolType.AppStackRelease,
            resourceName = AppRelease.iamResourceName(systemName, releaseSn)
        )
        playerRolesMap.values.forEach { roleList ->
            roleList.sortBy { ReleaseRole.valueOf(it.name).ordinal }
        }
        return playerRolesMap
    }

    override fun listMembers(systemName: String, releaseSn: String, current: Long, pageSize: Long): Pagination<MemberVO> {
        // 暂时保持原先的一次性查找不变
        val allMemberMap = findAllMember(systemName, releaseSn)
        val list = allMemberMap.map { MemberVO(it.key, it.value) }.sortedWith { o1, o2 ->
            if (o1.roleList.find { it.name == ReleaseRole.admin.name } != null) -1
            else if (o2.roleList.find { it.name == ReleaseRole.admin.name } != null) 1
            else o1.displayName.compareTo(o2.displayName)
        }
        return Pagination(
            total = list.size.toLong(),
            current = 1L,
            records = list
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_MEMBER_SET, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    @IsSystem
    override fun updateRoleMember(
        systemName: String,
        releaseSn: String,
        roleName: String,
        playerList: List<ResourcePlayerRequest>,
    ) {
        if (roleName == ReleaseRole.admin.name) return
        iamService.updateRole(
            protocolType = ProtocolType.AppStackRelease,
            resourceName = AppRelease.iamResourceName(systemName, releaseSn),
            roleName = roleName,
            playerList = playerList
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_MEMBER_SET, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    @IsSystem
    override fun updateMemberRole(systemName: String, releaseSn: String, userId: String, roleNameList: List<String>) {
        iamService.updatePlayer(
            protocolType = ProtocolType.AppStackRelease,
            resourceName = AppRelease.iamResourceName(systemName, releaseSn),
            subjectType = SubjectType.User,
            subjectId = userId,
            roleNameList = roleNameList.filter { it != ReleaseRole.admin.name }
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_ADMIN_TRANSFER, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    @IsSystem
    override fun transferOwner(systemName: String, releaseSn: String, userId: String) {
        iamService.updateRole(
            protocolType = ProtocolType.AppStackRelease,
            resourceName = AppRelease.iamResourceName(systemName, releaseSn),
            roleName = ReleaseRole.admin.name,
            playerList = listOf(ResourcePlayerRequest().apply { this.id = userId; this.type = SubjectType.User })
        )
    }

    override fun listAttachedChangeRequests(
        appName: String,
        releaseSn: String,
        current: Long,
        pageSize: Long
    ): Pagination<ChangeRequest> {
        systemBizService.find(systemName = appName)
        return workflowControllerFacade.appReleaseFacade.listAttachedChangeRequests(
            appName = appName,
            appReleaseSn = releaseSn,
            current = current,
            pageSize = pageSize
        )
    }

    override fun attachChangeRequests(
        appName: String,
        releaseSn: String,
        changeRequestSnList: List<String>
    ): Boolean {
        systemBizService.find(systemName = appName)
        return workflowControllerFacade.appReleaseFacade.attachChangeRequests(
            appName = appName,
            appReleaseSn = releaseSn,
            changeRequestSnList = changeRequestSnList
        )
    }

    override fun detachChangeRequests(
        appName: String,
        releaseSn: String,
        changeRequestSnList: List<String>
    ): Boolean {
        systemBizService.find(systemName = appName)
        return workflowControllerFacade.appReleaseFacade.detachChangeRequests(
            appName = appName,
            appReleaseSn = releaseSn,
            changeRequestSnList = changeRequestSnList
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_MANAGE, resourceArgIndex = 0),
            Access(action = Action.RELEASE_VIEW, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_EDIT, resourceArgIndex = 0, resourceArgSecondIndex = 1),
            Access(action = Action.RELEASE_CLOSE, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    @IsSystem
    override fun listReleaseExecutions(
        appName: String,
        releaseSn: String,
        query: MetadataSearch
    ): Pagination<ReleaseExecutionVO> {
        if (query.orderBy.uppercase() != "ID" && query.orderBy.uppercase() != "GMTCREATE") {
            throw BizException(ErrorCode.AS_PAGINATION_PARAM_INVALID)
        }
        if (query.sort.uppercase() != "ASC" && query.sort.uppercase() != "DESC") {
            throw BizException(ErrorCode.AS_PAGINATION_PARAM_INVALID)
        }
        return workflowControllerFacade.appReleaseFacade.listReleaseExecutions(appName, releaseSn, query)
    }

    private fun searchMine(
        appName: String?, keyword: String?, states: List<AppRelease.State>, current: Long, pageSize: Long,
    ): Pagination<AppReleaseRecord> {
        // 我参与的总数
        val total = iamService.countResource(
            protocolType = ProtocolType.AppStackRelease,
            subjectType = SubjectType.User,
            subjectId = AuthUtil.getUserId(),
            search = appName ?: ""
        ).toLong()

        if (total == 0L) {
            return Pagination<AppReleaseRecord>().apply {
                this.total = 0
                this.current = current
                this.pageSize = pageSize
                this.pages = 0
                this.records = emptyList()
            }
        }

        return if (keyword.isNullOrBlank() && states.isEmpty()) {
            // 只有应用名过滤
            val appReleaseSnList = findMyAppReleaseSn(appName = appName, page = current, size = pageSize)
            val records = if (appReleaseSnList.isEmpty()) {
                emptyList()
            } else {
                workflowControllerFacade.appReleaseFacade.findBySns(appReleaseSnList).sortedByDescending {
                    it.gmtCreate
                }
            }
            Pagination<AppReleaseRecord>().apply {
                this.total = total
                this.current = current
                this.pageSize = pageSize
                this.pages = ceil(total.toDouble() / pageSize).toLong()
                this.records = records
            }
        } else {
            // 我参与的全部appRelease sn 作为筛选条件
            val innerRequest = InnerSearchAppReleaseRequest(
                keyword = keyword,
                appNames = appName?.let { listOf(it) } ?: emptyList(),
                states = states,
                limitSnList = findMyAppReleaseSn(appName, page = 0, size = total)
            )
            workflowControllerFacade.appReleaseFacade.searchPaginated(innerRequest, current, pageSize)
        }
    }

    private fun searchMine(
        appNames: List<String>, keyword: String?, states: List<AppRelease.State>, current: Long, pageSize: Long,
    ): Pagination<AppReleaseRecord> {
        // 我参与的总数
        val total = iamService.countResource(
            protocolType = ProtocolType.AppStackRelease,
            subjectType = SubjectType.User,
            subjectId = AuthUtil.getUserId(),
            search = ""
        ).toLong()

        if (total == 0L) {
            return Pagination<AppReleaseRecord>().apply {
                this.total = 0
                this.current = current
                this.pageSize = pageSize
                this.pages = 0
                this.records = emptyList()
            }
        }

        // 我参与的全部appRelease sn 作为筛选条件
        val innerRequest = InnerSearchAppReleaseRequest(
            keyword = keyword,
            appNames = appNames,
            states = states,
            limitSnList = findMyAppReleaseSn("", page = 0, size = total)
        )
        return workflowControllerFacade.appReleaseFacade.searchPaginated(innerRequest, current, pageSize)
    }

    private fun findMyAppReleaseSn(appName: String?, page: Long, size: Long): List<String> {
        return iamService.searchResourceName(
            protocolType = ProtocolType.AppStackRelease,
            subjectType = SubjectType.User,
            subjectId = AuthUtil.getUserId(),
            search = appName ?: "",
            page = page.toInt(),
            size = size.toInt(),
        ).map {
            AppRelease.decode(it).second
        }
    }

    private fun findAppReleaseOwner(appRelease: AppRelease): String? {
        val resourceName = AppRelease.iamResourceName(appName = appRelease.appName, sn = appRelease.sn)
        return findAppReleaseOwner(listOf(resourceName)).values.firstOrNull()
    }

    private fun findAppReleaseOwner(resourceNames: List<String>): Map<String, String?> {
        if (resourceNames.isEmpty()) return emptyMap()
        return iamService.findRolePlayers(ProtocolType.AppStackRelease, resourceNames, ReleaseRole.admin.name)
            .mapValues { it.value[0] }
    }

    private fun fillOwner(appReleaseRecordList: List<AppReleaseRecord>): List<AppReleaseRecordWithOwner> {
        val resourceNameList = appReleaseRecordList.map { AppRelease.iamResourceName(it.appName, it.sn) }
        val ownerMap = findAppReleaseOwner(resourceNameList).mapKeys {
            AppRelease.decode(it.key).second
        }
        return appReleaseRecordList.map {
            AppReleaseRecordWithOwner(
                sn = it.sn,
                name = it.name,
                appName = it.appName,
                version = it.version,
                baseAppRelease = it.baseAppRelease,
                baseAppReleaseInstSn = it.baseAppReleaseInstSn,
                state = it.state,
                gmtCreate = it.gmtCreate,
                gmtModified = it.gmtModified,
                creatorId = it.creatorId,
                modifierId = it.modifierId,
                releaseItems = it.releaseItems,
                scheduledReleaseTime = it.scheduledReleaseTime,
                actualReleaseTime = it.actualReleaseTime,
                description = it.description,
                owner = ownerMap[it.sn]
            )
        }
    }
}