package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.SearchAppJobRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.SearchEnvJobRequest
import com.alibabacloud.devops.appstack.libs.model.vo.JobRecord

/**
 * @author: <EMAIL>
 * @date: 2023-11-30 17:17
 * @version: JobBizService, v0.1
 **/
interface JobBizService {

    fun searchEnvJobPaginated(
        searchEnvJobRequest: SearchEnvJobRequest,
        current: Long = 1L,
        pageSize: Long = 10L,
    ): Pagination<JobRecord>

    fun searchAppJobPaginated(
        searchAppJobRequest: SearchAppJobRequest,
        current: Long = 1L,
        pageSize: Long = 10L,
    ): Pagination<JobRecord>
}