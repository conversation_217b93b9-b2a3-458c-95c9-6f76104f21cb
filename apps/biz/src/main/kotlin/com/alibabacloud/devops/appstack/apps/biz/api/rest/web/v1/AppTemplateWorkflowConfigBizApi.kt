package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.model.vo.AppTemplateWorkflowRevisionVO
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateConfigBizService
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateWorkflowConfigBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig.TypeEnum.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.sync.AppTemplateSyncRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.response.apptemplate.sync.AppTemplateSyncResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2024-03-12 16:35
 * @version: AppTemplateWorkflowConfigBizApi, v0.1
 **/
@Tag(name = "AppTemplateWorkflowConfig", description = "应用模板研发流程配置相关 API")
@RestController
@RequestMapping("/api/v1/appTemplates/{appTemplateName}/configs/ReleaseWorkflow")
open class AppTemplateWorkflowConfigBizApi {

    @Autowired
    lateinit var appTemplateConfigBizService: AppTemplateConfigBizService

    @Autowired
    lateinit var appTemplateWorkflowConfigBizService: AppTemplateWorkflowConfigBizService

    @Operation(summary = "应用模板配置-研发流程配置-删除")
    @DeleteMapping
    fun deleteAppTemplateWorkflowConfig(
        @PathVariable("appTemplateName") appTemplateName: String,
        @RequestParam("name") name: String,
    ): Response<Unit> {
        val appTemplateConfig = appTemplateConfigBizService.find(appTemplateName, ReleaseWorkflow)
        return Response.success(
            appTemplateWorkflowConfigBizService.deleteByName(appTemplateName, name, appTemplateConfig?.sn)
        )
    }

    @Operation(summary = "应用模板配置-研发流程配置-版本查询")
    @GetMapping("/revisions")
    fun findAppTemplateWorkflowConfigPaginatedRevision(
        @PathVariable("appTemplateName") appTemplateName: String,
        @RequestParam("name") name: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<AppTemplateWorkflowRevisionVO>> {
        return Response.success(
            appTemplateWorkflowConfigBizService.findPaginatedRevision(
                appTemplateName,
                name,
                current,
                pageSize
            )
        )
    }

    @Operation(summary = "应用模板配置-研发流程配置-版本比较")
    @GetMapping("/compare")
    fun compareAppTemplateWorkflowConfigRevision(
        @PathVariable("appTemplateName") appTemplateName: String,
        @RequestParam("name") name: String,
        @RequestParam("beforeRevisionSha") beforeRevisionSha: String,
        @RequestParam("afterRevisionSha") afterRevisionSha: String,
    ): Response<Triple<String, String, List<DiffItem<String>>>> {
        return Response.success(
            appTemplateWorkflowConfigBizService.compare(appTemplateName, name, beforeRevisionSha, afterRevisionSha)
        )
    }

    @Operation(summary = "发起同步")
    @PostMapping("/sync")
    fun syncAppTemplateWorkflowConfigToApps(
        @PathVariable("appTemplateName") appTemplateName: String,
        @RequestParam("name") name: String,
        @RequestBody request: AppTemplateSyncRequest,
    ): Response<Boolean> {
        appTemplateWorkflowConfigBizService.sync2Apps(appTemplateName, request)
        return Response.success(true)
    }

    @Operation(summary = "查询同步状态")
    @GetMapping("/syncStatus")
    fun syncAppTemplateWorkflowConfigToAppStatus(
        @PathVariable("appTemplateName") appTemplateName: String,
        @RequestParam("name") name: String,
    ): Response<AppTemplateSyncResponse> {
        return Response.success(
            appTemplateWorkflowConfigBizService.syncStatus(appTemplateName, name)
        )
    }

    @Operation(summary = "应用模板配置-研发流程配置-和应用比较")
    @GetMapping("/compare:withApp")
    fun compareAppTemplateWorkflowConfigWithApp(
        @PathVariable("appTemplateName") appTemplateName: String,
        @RequestParam("name") name: String,
        @RequestParam("appName") appName: String,
    ): Response<Triple<String, String, List<DiffItem<String>>>> {
        return Response.success(
            appTemplateWorkflowConfigBizService.compareWithApp(appTemplateName, name, appName)
        )
    }
}