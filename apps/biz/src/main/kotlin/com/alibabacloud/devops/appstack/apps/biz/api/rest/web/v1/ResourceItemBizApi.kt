package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.ResourceItemBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.ResourceItemRecord
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2023-04-17 14:12
 * @version: ResourceItemBizApi, v0.1
 **/
@Tag(name = "ResourceItem", description = "资源Item相关 API")
@RestController
@RequestMapping("/api/v1")
open class ResourceItemBizApi {

    @Autowired
    lateinit var resourceItemBizService: ResourceItemBizService

    @Operation(summary = "查看资源Item列表")
    @GetMapping("/pools/{poolName}/instances/{instanceName}/items")
    fun findPaginated(
        @PathVariable poolName: String,
        @PathVariable instanceName: String,
        @RequestParam(required = false) search: String? =null,
        @RequestParam(defaultValue = "1", required = false) current: Long,
        @RequestParam(defaultValue = "10", required = false) pageSize: Long
    ): Response<Pagination<ResourceItemRecord>> {
        return Response.success(
            resourceItemBizService.findPaginated(
                poolName = poolName,
                instanceName = instanceName,
                search = search,
                current = current,
                pageSize = pageSize
            )
        )
    }
}