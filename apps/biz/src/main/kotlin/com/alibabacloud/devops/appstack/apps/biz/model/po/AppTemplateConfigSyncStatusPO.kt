package com.alibabacloud.devops.appstack.apps.biz.model.po

import com.alibabacloud.devops.appstack.libs.common.annotation.Comment
import com.alibabacloud.devops.appstack.libs.common.model.BasePO
import com.baomidou.mybatisplus.annotation.TableName
import javax.persistence.Column
import javax.persistence.Index
import javax.persistence.Table

/**
 * <AUTHOR>
 * @date 2024-02-15
 */
@TableName("app_template_config_sync_status")
@Comment("AppTemplateConfigSyncStatus")
@Table(
    indexes = [
        Index(unique = false, columnList = "org_id(64),is_deleted(32),app_template_name(64),app_template_config_type(16),app_template_config_instance_name(32),app_name(64)"),
        Index(unique = false, columnList = "org_id(64),is_deleted(32),app_name(64),app_template_name(64)"),
    ]
)
class AppTemplateConfigSyncStatusPO: BasePO() {

    @Comment("应用模版名")
    @Column(columnDefinition = "varchar(255)", nullable = false)
    lateinit var appTemplateName: String

    @Comment("应用模板配置类型")
    @Column(columnDefinition = "varchar(64)", nullable = false)
    lateinit var appTemplateConfigType: String

    @Comment("应用模板具体某一套配置中的唯一业务键")
    @Column(columnDefinition = "varchar(255)", nullable = false)
    lateinit var appTemplateConfigInstanceName: String

    @Comment("应用名")
    @Column(columnDefinition = "varchar(255)", nullable = false)
    lateinit var appName: String

    @Comment("最后一次写入实例修改状态的全局单调增序列号")
    @Column(columnDefinition = "bigint", nullable = true)
    var lastInstanceUpdateSeqNo: Long? = null

    @Comment("模板同步结果富状态")
    @Column(columnDefinition = "text", nullable = true)
    var templateSyncStatus: String? = null

    @Comment("被模板影响的具体实例修改状态")
    @Column(columnDefinition = "text", nullable = true)
    var instanceUpdateStatus: String? = null

}