package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppArtifactRepo
import com.alibabacloud.devops.appstack.libs.model.request.CreateAppArtifactRepoRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppArtifactRepoRequest

/**
 * <AUTHOR>
 * @date 2023-11-02
 */
interface AppArtifactRepoBizService {

    fun create(appName: String, request: CreateAppArtifactRepoRequest): AppArtifactRepo
    fun delete(appName: String, sn: String)
    fun update(appName: String, sn: String, request: UpdateAppArtifactRepoRequest): AppArtifactRepo
    fun findAll(appName: String): List<AppArtifactRepo>
    fun find(appName: String, sn: String): AppArtifactRepo

}