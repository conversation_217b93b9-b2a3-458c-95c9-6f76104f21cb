package com.alibabacloud.devops.appstack.apps.biz.model.po

import com.alibabacloud.devops.appstack.libs.common.annotation.Comment
import com.alibabacloud.devops.appstack.libs.common.model.BasePO
import com.baomidou.mybatisplus.annotation.TableName
import lombok.Data
import javax.persistence.Index
import javax.persistence.Table

@TableName("agents")
@Comment("Agent")
@Table(
    indexes = [
        Index(columnList = "name(100), is_deleted(16), org_id(24)", unique = true),
    ]
)
@Data
open class AgentPO : BasePO() {
    @Comment("名称")
    var name: String? = null

    @Comment("签名")
    var sign: String? = null

    @Comment("类型")
    var type: String? = null

    @Comment("版本")
    var version: String? = null

    @Comment("安装脚本")
    var installScript: String? = null

    @Comment("卸载脚本")
    var uninstallScript: String? = null
}