package com.alibabacloud.devops.appstack.apps.biz.service.client.api

import com.alibabacloud.devops.appstack.libs.model.request.KubeConfigVO
import com.alibabacloud.devops.appstack.libs.model.request.UpsertAliyunAuthAccountRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpsertKubeConfigRequest
import com.alibabacloud.devops.appstack.libs.model.response.ResponseLegacy
import com.alibabacloud.devops.appstack.libs.model.vo.AliyunAuthAccountVO
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path


/**
 * @author: <EMAIL>
 * @date: 2022-01-27 11:13
 * @version: EnterpriseSettingsApi, v0.1
 **/
@RetrofitClient(baseUrl = "http://appstack-enterprise-settings", errorDecoder = EnterpriseSettingsDecoder::class)
interface EnterpriseSettingsApi {

    @GET("/api/AliyunAuthAccounts")
    fun findAllAliyunAuthAccounts(
        @Header("X-Tenant-Id") tenantId: String,
        @Header("X-Operator-Id") userId: String,
        @Header("X-Tenant-Type") tenantType: String = "long",
    ): ResponseLegacy<List<AliyunAuthAccountVO>>

    @POST("/api/AliyunAuthAccounts")
    fun createAliyunAuthAccount(
        @Header("X-Tenant-Id") tenantId: String,
        @Header("X-Operator-Id") userId: String,
        @Header("X-Tenant-Type") tenantType: String = "long",
        @Body upsertAliyunAuthAccountRequest: UpsertAliyunAuthAccountRequest
    ): AliyunAuthAccountVO

    @PUT("/api/AliyunAuthAccounts")
    fun upsertAliyunAuthAccount(
        @Header("X-Tenant-Id") tenantId: String,
        @Header("X-Operator-Id") userId: String,
        @Header("X-Tenant-Type") tenantType: String = "long",
        @Body upsertAliyunAuthAccountRequest: UpsertAliyunAuthAccountRequest
    ): AliyunAuthAccountVO

    @DELETE("/api/AliyunAuthAccounts/{id}")
    fun deleteAliyunAuthAccount(
        @Header("X-Tenant-Id") tenantId: String,
        @Header("X-Operator-Id") userId: String,
        @Header("X-Tenant-Type") tenantType: String = "long",
        @Path("id") id: Long
    ): ResponseLegacy<Unit>

    @GET("/api/KubeConfigs")
    fun findAllKubeConfig(
        @Header("X-Tenant-Id") tenantId: String,
        @Header("X-Operator-Id") userId: String,
        @Header("X-Tenant-Type") tenantType: String = "long",
    ): ResponseLegacy<List<KubeConfigVO>>

    @POST("/api/KubeConfigs")
    fun createKubeConfig(
        @Header("X-Tenant-Id") tenantId: String,
        @Header("X-Operator-Id") userId: String,
        @Header("X-Tenant-Type") tenantType: String = "long",
        @Body upsertKubeConfigRequest: UpsertKubeConfigRequest,
    ): KubeConfigVO

    @PUT("/api/KubeConfigs/{id}")
    fun updateKubeConfig(
        @Header("X-Tenant-Id") tenantId: String,
        @Header("X-Operator-Id") userId: String,
        @Header("X-Tenant-Type") tenantType: String = "long",
        @Path("id") id: Long,
        @Body upsertKubeConfigRequest: UpsertKubeConfigRequest,
    ): KubeConfigVO

    @DELETE("/api/KubeConfigs/{id}")
    fun deleteKubeConfig(
        @Header("X-Tenant-Id") tenantId: String,
        @Header("X-Operator-Id") userId: String,
        @Header("X-Tenant-Type") tenantType: String = "long",
        @Path("id") id: Long,
    ): Void
}