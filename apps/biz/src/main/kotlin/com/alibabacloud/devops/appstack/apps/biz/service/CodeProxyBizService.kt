package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.app.ConnectionConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.RepoContext
import com.alibabacloud.devops.appstack.libs.model.response.cr.GitBranchInfo
import com.alibabacloud.devops.appstack.libs.model.response.cr.GitProject

/**
 * <AUTHOR>
 * @date 2022-11-26
 */
interface CodeProxyBizService {

    fun searchBranches(
        repoContext: RepoContext,
        connectionConfig: ConnectionConfig,
        search: String
    ): List<GitBranchInfo>

    fun findBranch(
        repoContext: RepoContext,
        connectionConfig: ConnectionConfig,
        branchName: String
    ): GitBranchInfo

    fun searchProjects(
        repoContext: RepoContext,
        connectionConfig: ConnectionConfig,
        search: String
    ): List<GitProject>
}