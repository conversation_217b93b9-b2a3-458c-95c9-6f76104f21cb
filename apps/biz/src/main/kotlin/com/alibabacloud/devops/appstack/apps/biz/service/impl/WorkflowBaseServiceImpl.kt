package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.annotation.ApiMetricCounter
import com.alibabacloud.devops.appstack.apps.biz.service.StageBaseService
import com.alibabacloud.devops.appstack.apps.biz.service.WorkflowBaseService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.constant.LockConfig
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.filter.TraceFilter
import com.alibabacloud.devops.appstack.libs.common.model.ErrorEntry
import com.alibabacloud.devops.appstack.libs.common.util.checkBiz
import com.alibabacloud.devops.appstack.libs.common.util.checkBizWithCode
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.config.IamContext
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppType
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.ActionType
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.ResourceType
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.*
import com.alibabacloud.devops.appstack.libs.model.event.EventResult
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.*
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.ga.AppTemplateWorkflowRequest
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.CountDownLatch

/**
 * 基础的流程Service，只实现通用的、可复用的业务逻辑，特异性实现放在上层
 * <AUTHOR>
 * @date 2022-06-26
 */
@Service
open class WorkflowBaseServiceImpl : WorkflowBaseService {


    private lateinit var workflowControllerFacade: WorkflowControllerFacade

    private lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var i18nMessageService: I18nMessageService

    @Autowired
    fun setCoreFacades(coreFacades: CoreFacades) {
        this.coreFacades = coreFacades
    }

    @Autowired
    fun setWorkflowControllerFacade(workflowControllerFacade: WorkflowControllerFacade) {
        this.workflowControllerFacade = workflowControllerFacade
    }

    @Autowired
    lateinit var stageBaseService: StageBaseService

    @ApiMetricCounter(metricName = "appReleaseWorkflow_operation", methodTag = "create")
    override fun create(appName: String, request: CreateReleaseWorkflowBizRequest): ReleaseWorkflow {
        val releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.create(
            CreateReleaseWorkflowRequest(
                appName = appName,
                name = request.name,
                type = request.type!!,
                note = request.note,
            )
        )

        try {
            if (request.template != null) {
                // 优先使用模版模型
                upsertWithTemplate(appName, releaseWorkflow, request.template?.toModel(), null)
            } else {
                request.templateSn?.let { templateSn ->
                    // 进行模板导入
                    val template = workflowControllerFacade.releaseWorkflowTemplateFacade.find(templateSn)
                    upsertWithTemplate(appName, releaseWorkflow, template, null)
                }
            }
        } catch (e: Throwable) {
            // 模版初始化失败后，删除研发流程
            delete(appName, releaseWorkflow.sn!!)
            throw e
        }
        return find(releaseWorkflow.appName!!, releaseWorkflow.sn!!)
    }

    private fun upsertWithTemplate(
        appName: String,
        releaseWorkflow: ReleaseWorkflow,
        template: ReleaseWorkflowTemplate?,
        commitMessage: String? = "",
    ) {
        // 记录研发阶段的处理结果
        val templateStageResultMap: MutableMap<String, EventResult> = mutableMapOf()
        var releaseKey: String? = null
        // 如果 commitMessage 不为null，意味着接下来是中间操作，不产生版本和锁，则在外部上锁
        if (commitMessage == null) {
            val stageCount = template?.releaseStageTemplate?.size ?: 1
            releaseKey = workflowControllerFacade.releaseWorkflowFacade.tryLock(releaseWorkflow.sn!!,
                LockConfig.DEFAULT_TRY_LOCK_TIME, stageCount * LockConfig.DEFAULT_TEMPLATE_RELEASE_STAGE_EXPIRATION_TIME)
            checkBiz(releaseKey != null) {
                ErrorEntry(ErrorCode.AS_RELEASE_WORKFLOW_OPERATION_LOCK_FAIL)
            }
        }
        try {
            checkExists(template) { ErrorCode.AS_RELEASE_WORKFLOW_TEMPLATE_NOT_FOUND }
            val templateInstance = template.toInstance(releaseWorkflow)

            // 1. 初始化 ReleaseStage，并收集 stageTemplateSn -> stageSn 映射
            templateInstance.releaseStageTemplateInstances.forEach { releaseStageTemplateInstance ->
                // 获取已有的同名的研发阶段
                var releaseStage = releaseWorkflow.releaseStages.firstOrNull { it.name == releaseStageTemplateInstance.releaseStage.name }
                try {
                    // 变量组的处理逻辑，创建/更新时相同，抽离出来
                    val variableGroups = releaseStageTemplateInstance.releaseStage.variableGroups.map {
                        when (it.type) {
                            VariableGroup.Type.TEMPLATE -> {
                                // 判断应用变量组是否存在
                                val profile = coreFacades.variableFacade.findProfileVO(appName, it.name)
                                checkExists(profile, it.name) { ErrorCode.AS_VAR_PROFILE_NOT_FOUND }
                                VariableGroup(
                                    type = VariableGroup.Type.APP,
                                    name = it.name,
                                    displayName = it.displayName
                                )
                            }

                            else -> it
                        }
                    }.toMutableList()
                    if (releaseStage != null) {
                        // 研发阶段已存在，更新研发阶段
                        workflowControllerFacade.releaseStageFacade.update(releaseStage.sn!!,
                            UpdateReleaseStageRequest(
                                name = releaseStageTemplateInstance.releaseStage.name,
                                labelList = releaseStageTemplateInstance.releaseStage.labels,
                                // 需要将变量组中的模板变量组转换成应用变量组
                                variableGroups = variableGroups,
                                commitMessage = commitMessage,
                                order = releaseStageTemplateInstance.releaseStage.order
                            )
                        )
                        // 更新阶段流水线信息，填充 pipelineId 和 pipelineConfigId
                        releaseStageTemplateInstance.updateReleaseStagePipelineRequest?.let {
                            val pipeline = it.toPipeline()
                            val pipelineId = releaseStage?.pipeline?.engineSn?.toLongOrNull()
                            val pipelineConfigId = releaseStage?.pipeline?.getPipelineConfigId()
                            if (pipelineId != null && pipelineConfigId != null) {
                                pipeline.fillIdAndConfigId(pipelineId, pipelineConfigId)
                                it.data = pipeline.toRequest().data
                            }
                        }
                    } else {
                        // 研发阶段不存在，创建研发阶段
                        releaseStage = workflowControllerFacade.releaseStageFacade.create(
                            CreateReleaseStageRequest(
                                name = releaseStageTemplateInstance.releaseStage.name,
                                releaseWorkflowSn = releaseWorkflow.sn!!,
                                labelList = releaseStageTemplateInstance.releaseStage.labels,
                                order = releaseStageTemplateInstance.releaseStage.order,
                                appName = appName,
                                // 需要将变量组中的模板变量组转换成应用变量组
                                variableGroups = variableGroups,
                                commitMessage = commitMessage
                            )
                        )
                    }
                    // 创建/更新阶段成功，记录结果
                    templateStageResultMap[releaseStageTemplateInstance.releaseStage.sn!!] = EventResult(
                        success = true,
                        data = ReleaseStage(
                            sn = releaseStage.sn!!,
                            name = releaseStage.name
                        )
                    )
                } catch (e: Throwable) {
                    // 创建/更新阶段失败，记录结果
                    templateStageResultMap[releaseStageTemplateInstance.releaseStage.sn!!] = EventResult(
                        success = false,
                        message = e.message,
                        data = ReleaseStage(
                            sn = releaseStage?.sn,
                            name = releaseStage?.name ?: "unknown"
                        )
                    )
                }
            }

            // 2. 更新所有研发阶段的规则配置
            val updateRuleCountDownLatch = CountDownLatch(templateInstance.releaseStageTemplateInstances.size)
            val ctx = AuthThreadContext.export()
            val iamCtx = IamContext.export()
            templateInstance.releaseStageTemplateInstances.forEach { releaseStageTemplateInstance ->
                Thread(
                    UpdateRuleWorker(
                        templateStageResultMap,
                        releaseStageTemplateInstance,
                        commitMessage,
                        releaseWorkflow,
                        updateRuleCountDownLatch,
                        ctx,
                        iamCtx
                    )
                ).start()
            }
            updateRuleCountDownLatch.await()

            // 3. 并发：更新流水线信息
            val countDownLatch = CountDownLatch(templateInstance.releaseStageTemplateInstances.size)
            val initResults = CopyOnWriteArrayList<InitReleaseStagePipelineResult>()
            templateInstance.releaseStageTemplateInstances.forEach { releaseStageTemplateInstance ->
                val stageSn = (templateStageResultMap[releaseStageTemplateInstance.releaseStage.sn!!]!!.data as ReleaseStage?)?.sn
                // 如果前置阶段存在且操作成功且需要更新流水线
                if (stageSn != null && templateStageResultMap[releaseStageTemplateInstance.releaseStage.sn!!]?.success == true
                    && releaseStageTemplateInstance.updateReleaseStagePipelineRequest != null) {
                    // 模板的流水线强制设置 doValidate 为false
                    var request = releaseStageTemplateInstance.updateReleaseStagePipelineRequest!!
                    val pipeline = request.toPipeline()
                    pipeline.setDoValidate(false)
                    request = pipeline.toRequest()
                    request.commitMessage = commitMessage
                    Thread(
                        InitReleaseStagePipelineWorker(
                            releaseStageTemplateInstance.releaseStage.sn!!,
                            stageSn,
                            request,
                            countDownLatch,
                            initResults,
                            ctx,
                            iamCtx
                        )
                    ).start()
                } else {
                    countDownLatch.countDown()
                }
            }
            countDownLatch.await()
            initResults.filter { !it.success }.forEach {
                templateStageResultMap[it.originStageSn]?.success = false
                templateStageResultMap[it.originStageSn]?.message = it.errorMessage
            }
            // 检查研发阶段更新结果
            checkEventResult(templateStageResultMap.values.toMutableList())
        } catch (e: Throwable) {
            // 模版初始化失败后，删除过程中创建的研发阶段
            logger.error(
                "upsertWithTemplate failed, appName: $appName, workflowSn: ${releaseWorkflow.sn}, commitMessage: $commitMessage",
                e
            )
            // 仅创建失败的时候需要删除研发阶段，更新失败不删除，仅返回错误信息
            if (releaseWorkflow.releaseStages.isEmpty()) {
                templateStageResultMap.values.mapNotNull { (it.data as ReleaseStage?)?.sn }.
                forEach { workflowControllerFacade.releaseStageFacade.delete(it, null) }
            }
            throw e
        } finally {
            releaseKey?.let {
                workflowControllerFacade.releaseWorkflowFacade.unLock(releaseWorkflow.sn!!, it)
            }
        }
    }

    private fun checkEventResult(eventResults: List<EventResult>) {
        val failedList = eventResults.filter { !it.success }
        if (failedList.isNotEmpty()) {
            val failedStageNames = failedList.map { (it.data as ReleaseStage?)?.name ?: "unknown" }.joinToString(",")
            throw BizException(
                errorEntry = ErrorEntry(
                    code = ErrorCode.AS_RELEASE_WORKFLOW_UPSERT_WITH_TEMPLATE_FAIELD,
                    message = "阶段${failedStageNames}更新失败",
                    advice = failedList.map {
                        val name = (it.data as ReleaseStage?)?.name ?: "unknown"
                        // TODO： i18n
                        // 错误信息截断
                        "阶段${name}更新失败: ${it.message?.take( ErrorCode.ERROR_MESSAGE_LIMIT) ?: "未知错误"}"
                    }.joinToString("\n")
                )
            )
        }
    }

    override fun findAll(appName: String, type: ReleaseWorkflow.TypeEnum?): List<ReleaseWorkflow> {
        return workflowControllerFacade.releaseWorkflowFacade.findAll(appName, type)
    }

    override fun findAllBrief(appName: String, type: ReleaseWorkflow.TypeEnum?): List<ReleaseWorkflowBriefVO> {
        return workflowControllerFacade.releaseWorkflowFacade.findAllBrief(appName, type)
    }


    override fun find(appName: String, sn: String): ReleaseWorkflow {
        val releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.find(sn)
        checkBizWithCode(appName == releaseWorkflow.appName) { ErrorCode.AS_RELEASE_WORKFLOW_NOT_FOUND }
        return releaseWorkflow
    }


    override fun findBrief(appName: String, sn: String): ReleaseWorkflowBriefVO {
        val releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findBrief(sn)
        checkBizWithCode(appName == releaseWorkflow.appName) { ErrorCode.AS_RELEASE_WORKFLOW_NOT_FOUND }
        return releaseWorkflow
    }

    override fun findWithRevision(appName: String, sn: String): ReleaseWorkflowRevision? {
        return workflowControllerFacade.releaseWorkflowFacade.findWithRevision(sn)
    }

    override fun findYamlBySha(appName: String, sn: String, sha: String): String {
        return workflowControllerFacade.releaseWorkflowFacade.findYamlBySha(appName, sn, sha)
    }

    override fun listRevisionPaginated(
        systemName: String,
        sn: String,
        current: Long,
        pageSize: Long,
    ): Pagination<Revision> {
        return workflowControllerFacade.releaseWorkflowFacade.listRevisionPaginated(sn, current, pageSize)
    }

    override fun compare(
        appName: String,
        sn: String,
        beforeRevisionSha: String,
        afterRevisionSha: String,
    ): Triple<String, String, List<DiffItem<String>>> {
        return workflowControllerFacade.releaseWorkflowFacade.compare(sn, beforeRevisionSha, afterRevisionSha)
    }

    @ApiMetricCounter(metricName = "appReleaseWorkflow_operation", methodTag = "update")
    override fun update(
        appName: String,
        sn: String,
        updateReleaseWorkflowRequest: UpdateReleaseWorkflowRequest,
    ): ReleaseWorkflow {
        find(appName, sn)
        return workflowControllerFacade.releaseWorkflowFacade.update(sn, updateReleaseWorkflowRequest)
    }

    @ApiMetricCounter(metricName = "appReleaseWorkflow_operation", methodTag = "delete")
    override fun delete(appName: String, sn: String): ReleaseWorkflow {
        val releaseWorkflow = find(appName, sn)
        workflowControllerFacade.releaseWorkflowFacade.delete(sn)
        return releaseWorkflow
    }

    @ApiMetricCounter(metricName = "appReleaseWorkflow_operation", methodTag = "sort")
    override fun sort(appName: String, request: SortReleaseWorkflowRequest): List<ReleaseWorkflow> {
        return workflowControllerFacade.releaseWorkflowFacade.sort(request)
    }

    override fun create(appName: String, request: AppTemplateWorkflowRequest): ReleaseWorkflowRevision {
        val app = coreFacades.appFacade.find(appName)
        checkExists(app, appName) { ErrorCode.AS_APP_NOT_FOUND }
        val type = if (app.type == AppType.SYSTEM) ReleaseWorkflow.TypeEnum.APP_RELEASE else ReleaseWorkflow.TypeEnum.CR
        val releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.create(
            CreateReleaseWorkflowRequest(
                appName = appName,
                name = request.name,
                type = type,
                commitMessage = null
            )
        )


        val template = ReleaseWorkflowTemplate(
            templateSn = request.getOrGenerateSn(),
            scope = ReleaseWorkflowTemplate.ScopeEnum.APPTEMPLATE,
            name = request.name,
            releaseStageTemplate = request.releaseStageTemplate,
            workflowType = type
        )
        try {
            upsertWithTemplate(appName, releaseWorkflow, template, null)
        } catch (e: Throwable) {
            // 模版初始化失败后，删除研发流程
            delete(appName, releaseWorkflow.sn!!)
            throw e
        }
        workflowControllerFacade.releaseWorkflowFacade.upsertReleaseWorkflowRevision(
            releaseWorkflow.sn!!,
            request.message ?: ""
        )
        return findWithRevision(releaseWorkflow.appName!!, releaseWorkflow.sn!!)!!
    }

    override fun update(
        appName: String,
        sn: String,
        request: AppTemplateWorkflowRequest,
    ): ReleaseWorkflowRevision {
        val releaseWorkflow = find(appName, sn)
        val template = ReleaseWorkflowTemplate(
            templateSn = request.getOrGenerateSn(),
            scope = ReleaseWorkflowTemplate.ScopeEnum.APPTEMPLATE,
            name = request.name,
            releaseStageTemplate = request.releaseStageTemplate,
            workflowType = releaseWorkflow.type!!
        )
        var throwable: Throwable? = null
        var errorMessage: String? = null
        var errorAdvice: String? = null
        // 更新流水线信息失败，获取失败信息，不影响后续操作
        try {
            upsertWithTemplate(appName, releaseWorkflow, template, null)
        } catch (e: Throwable) {
            throwable = e
            if ((throwable as? BizException)?.errorEntry?.code == ErrorCode.AS_RELEASE_WORKFLOW_UPSERT_WITH_TEMPLATE_FAIELD) {
                errorAdvice = throwable.errorEntry.advice
                errorMessage = throwable.errorEntry.message
            } else
                errorMessage = e.message?.take(ErrorCode.ERROR_MESSAGE_LIMIT)
        }
        // 删除旧的不存在阶段
        val stageCount = releaseWorkflow.releaseStages.size
        val releaseKey = workflowControllerFacade.releaseWorkflowFacade.tryLock(releaseWorkflow.sn!!,
            LockConfig.DEFAULT_TRY_LOCK_TIME, stageCount * LockConfig.DEFAULT_DELETE_RELEASE_STAGE_EXPIRATION_TIME)
        checkBiz(releaseKey != null) {
            ErrorEntry(ErrorCode.AS_RELEASE_WORKFLOW_OPERATION_LOCK_FAIL)
        }
        val newStageNames = template.releaseStageTemplate.map { it.name }
        releaseWorkflow.releaseStages.filter { !newStageNames.contains(it.name) }.forEach {
            try {
                stageBaseService.delete(appName, sn, it.sn!!, null)
            } catch (e: Throwable) {
                if (throwable == null) {
                    throwable = BizException(ErrorCode.AS_RELEASE_WORKFLOW_UPSERT_WITH_TEMPLATE_FAIELD)
                }
                errorMessage = (errorMessage ?: "") + ",阶段${it.name}删除失败"
                errorAdvice = (errorAdvice ?: "") + "\n阶段${it.name}删除失败: ${e.message?.take(ErrorCode.ERROR_MESSAGE_LIMIT)}"
            }
        }
        workflowControllerFacade.releaseWorkflowFacade.unLock(releaseWorkflow.sn!!, releaseKey!!)
        // 更新失败时，创建版本信息，包含失败信息
        var message = request.message ?: ""
        if (throwable != null) {
            // TODO: i18n
            message += "失败：${errorMessage ?: ""}"
        }
        workflowControllerFacade.releaseWorkflowFacade.upsertReleaseWorkflowRevision(sn, message)

        throwable?.let {
            if (it is BizException && it.errorEntry.code == ErrorCode.AS_RELEASE_WORKFLOW_UPSERT_WITH_TEMPLATE_FAIELD) {
                it.errorEntry.advice = errorAdvice
                it.errorEntry.message = errorMessage
            }
            throw it
        }
        return findWithRevision(appName, sn)!!
    }

    inner class InitReleaseStagePipelineWorker(
        val originStageSn: String,
        val stageSn: String,
        val request: UpdateReleaseStagePipelineRequest,
        val countDownLatch: CountDownLatch,
        val initResults: CopyOnWriteArrayList<InitReleaseStagePipelineResult>,
        val ctx: MutableMap<String, Any>,
        val iamCtx: MutableMap<String, String>,
    ) : Runnable {

        override fun run() {
            AuthThreadContext.replaceAll(ctx)
            IamContext.replaceAll(iamCtx)
            MDC.put(TraceFilter.TRACE_CONTEXT_KEY, AuthThreadContext.getTraceId())
            try {
                val releaseStage = workflowControllerFacade.releaseStageFacade.updatePipeline(stageSn, request)
            } catch (e: Exception) {
                logger.error(e.message, e)
                var message = e.message
                if (e is BizException) {
                    message = i18nMessageService.getErrorMessage(e)
                }
                initResults.add(
                    InitReleaseStagePipelineResult(originStageSn = originStageSn, success = false, errorMessage = message)
                )
            } finally {
                countDownLatch.countDown()
            }
        }
    }

    inner class InitReleaseStagePipelineResult(
        val originStageSn: String,
        val success: Boolean,
        val errorMessage: String? = null,
    )

    inner class UpdateRuleWorker(
        val templateStageResultMap: MutableMap<String, EventResult>,
        val releaseStageTemplateInstance: ReleaseStageTemplateInstance,
        val commitMessage: String?,
        val releaseWorkflow: ReleaseWorkflow,


        val countDownLatch: CountDownLatch,
        val ctx: MutableMap<String, Any>,
        val iamCtx: MutableMap<String, String>,
    ) : Runnable {

        override fun run() {
            AuthThreadContext.replaceAll(ctx)
            IamContext.replaceAll(iamCtx)
            MDC.put(TraceFilter.TRACE_CONTEXT_KEY, AuthThreadContext.getTraceId())
            try {
                val stageSn =
                    (templateStageResultMap[releaseStageTemplateInstance.releaseStage.sn!!]!!.data as ReleaseStage?)?.sn

                // 如果前置阶段存在且操作成功
                if (stageSn != null && templateStageResultMap[releaseStageTemplateInstance.releaseStage.sn!!]?.success == true) {
                    releaseStageTemplateInstance.stageRuleConfigRequest.validateStageSnList =
                        releaseStageTemplateInstance.stageRuleConfigRequest.validateStageSnList.mapNotNull {
                            (templateStageResultMap[it]?.data as? ReleaseStage)?.sn
                        }

                    releaseStageTemplateInstance.stageRuleConfigRequest.commitMessage = commitMessage

                    workflowControllerFacade.stageRuleConfigFacade.upsert(
                        releaseWorkflow.sn!!,
                        stageSn,
                        releaseStageTemplateInstance.stageRuleConfigRequest
                    )
                }

            } catch (e: Throwable) {
                // 更新失败，记录结果
                templateStageResultMap[releaseStageTemplateInstance.releaseStage.sn!!]?.success = false
                templateStageResultMap[releaseStageTemplateInstance.releaseStage.sn!!]?.message = e.message
            } finally {
                countDownLatch.countDown()
            }
        }
    }
}