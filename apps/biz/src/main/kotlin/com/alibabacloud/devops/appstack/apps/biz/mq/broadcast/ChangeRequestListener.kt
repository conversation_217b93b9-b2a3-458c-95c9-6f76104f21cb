package com.alibabacloud.devops.appstack.apps.biz.mq.broadcast

import com.alibaba.fastjson.JSON
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestWebSocketService
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.BroadcastListener
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.BroadcastTagEnum
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR> liyebin.lyb
 * @date : 2022/8/8 3:46 PM
 */

@Slf4k
@Service
class ChangeRequestListener: BroadcastListener {

    @Autowired
    lateinit var changeRequestWebSocketService: ChangeRequestWebSocketService

    override fun expectedTag(): BroadcastTagEnum {
        return BroadcastTagEnum.CR_MATE_DATA_CHANGED
    }

    override fun handle(body: ByteArray) {
        val content = String(body)
        logger.info("Received broadcast content: ${String(body)}")
        val contentMap = JSON.parseObject(String(body), HashMap::class.java)
        logger.info("releaseStageSn: ${contentMap.get("releaseStageSn")}")
        contentMap.get("releaseStageSn")?.let {
            changeRequestWebSocketService.notify(contentMap.get("releaseStageSn").toString(), content)
            return
        }
    }
}