package com.alibabacloud.devops.appstack.apps.biz.service.impl.handler.appsource

import com.alibabacloud.devops.appstack.apps.biz.service.AppSourceBizService
import com.alibabacloud.devops.appstack.libs.model.request.ng.CreateAppSourceRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateAppSourceRequest
import com.alibabacloud.devops.appstack.libs.model.vo.ng.AppSource

/**
 * @author: <EMAIL>
 * @date: 2023-10-12 10:52
 * @version: AppSourceBizServiceHandler, v0.1
 **/
interface AppSourceBizServiceHandler {
    val type: String

    fun create(appName: String, requestEntity: CreateAppSourceRequest): AppSource
    fun update(appName: String, requestEntity: UpdateAppSourceRequest): AppSource
    fun delete(appName: String, sn: String): Boolean
}