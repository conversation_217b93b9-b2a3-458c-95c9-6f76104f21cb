package com.alibabacloud.devops.appstack.apps.biz.api.rest.inner

import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.request.SearchAppRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Hidden
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-05-09 07:12
 * @version: OrgBizApi, v0.1
 **/
@Hidden
@Tag(name = "<PERSON>Org", description = "灰度企业相关 API")
@RestController
@RequestMapping("/inner/api/orgs")
open class GrayOrgBizApi {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Operation(summary = "企业未导入应用")
    @GetMapping("/emptyApp")
    fun emptyApp(): Response<Boolean> {
        val appVOList = coreFacades.appFacade.searchPaginated(SearchAppRequest(), 1, 1).records
        return Response.success(appVOList.isEmpty())
    }
}