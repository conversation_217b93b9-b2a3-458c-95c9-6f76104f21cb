package com.alibabacloud.devops.appstack.apps.biz.model.po

import com.alibabacloud.devops.appstack.libs.common.annotation.Comment
import com.alibabacloud.devops.appstack.libs.common.model.BasePO
import com.baomidou.mybatisplus.annotation.TableName
import lombok.Data
import javax.persistence.Column
import javax.persistence.Index
import javax.persistence.Table

/**
 * @author: <EMAIL>
 * @date: 2022-08-25 21:47
 * @version: OnelineGuidancePO, v0.1
 **/

@TableName("oneline_guidances")
@Comment("OnelineGuidances")
@Table(
    indexes = [
        Index(columnList = "name(100), app_name(100), is_deleted(16), org_id(24)", unique = true),
    ]
)
@Data
 class OnlineGuidancePO : BasePO() {

    @Comment("应用名")
    var appName: String? = null

    @Comment("名称")
    var name: String? = null

    @Comment("步骤引导内容")
    @Column(columnDefinition = "text")
    var context: String? = null
}