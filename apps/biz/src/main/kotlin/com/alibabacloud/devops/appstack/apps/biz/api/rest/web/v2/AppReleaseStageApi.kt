package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import com.alibabacloud.devops.appstack.apps.biz.model.vo.ReleaseStageInstanceIntegratedMetadataVO
import com.alibabacloud.devops.appstack.apps.biz.model.vo.ReleaseStageInstanceVO
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestStageBizService
import com.alibabacloud.devops.appstack.apps.biz.service.FlowForwardBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ExecutePipelineResult
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStage
import com.alibabacloud.devops.appstack.libs.model.request.PaginationQuery
import com.alibabacloud.devops.appstack.libs.model.request.ng.ExecuteChangeRequestStagePipelineRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.ExecuteReleaseStagePipelineRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.UpdateReleaseStageRequest
import com.alibabacloud.devops.appstack.libs.model.response.*
import com.aliyun.amp.plugin.annotation.AmpApi
import com.aliyun.amp.plugin.annotation.AmpParam
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.treeToValue
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.api.annotations.ParameterObject
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 * <AUTHOR>
 * @create 2023/12/25 9:17 PM
 **/
@Tag(name = "研发阶段", description = "研发阶段相关API")
@RestController
@RequestMapping("/api/v2")
class AppReleaseStageApi {

    @Autowired
    lateinit var changeRequestStageBizService: ChangeRequestStageBizService

    @Autowired
    lateinit var flowForwardBizService: FlowForwardBizService

    @VpcApi
    @Operation(summary = "更新研发阶段", operationId = "UpdateReleaseStage")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "更新研发阶段成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PutMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}")
    fun updateReleaseStage(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @RequestBody request: UpdateReleaseStageRequest
    ): ReleaseStage {
        return changeRequestStageBizService.update(appName, releaseWorkflowSn, releaseStageSn, request)
    }

    @VpcApi
    @AmpApi(
        name = "ExecuteChangeRequestReleaseStage",
        summary = "执行研发阶段流水线",
        path = "/appstack/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}:execute",
        method = "post",
        operationType = "write"
    )
    @Operation(summary = "执行研发阶段流水线", operationId = "ExecuteChangeRequestReleaseStage")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "执行研发阶段成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}:execute")
    fun executeChangeRequestStagePipeline(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @RequestBody request: ExecuteChangeRequestStagePipelineRequest
    ): ExecutePipelineResult {
        return changeRequestStageBizService.executePipeline(
            appName = appName,
            releaseWorkflowSn = releaseWorkflowSn,
            releaseStageSn = releaseStageSn,
            request = ExecuteReleaseStagePipelineRequest(
                params = request.params.toMutableMap()
            )
        )
    }

    @AmpApi(
        name = "CancelExecutionReleaseStage",
        summary = "取消执行研发阶段流水线",
        path = "/appstack/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}/executions/{executionNumber}:cancel",
        method = "post",
        operationType = "write"
    )
    @Operation(summary = "取消执行研发阶段流水线", operationId = "CancelExecutionReleaseStage")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "取消执行研发阶段成功"),
            ApiResponse(responseCode = "404", description = "研发流程 / 研发阶段 / 流水线实例不存在"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}/executions/{executionNumber}:cancel")
    fun cancelExecutionReleaseStage(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @PathVariable executionNumber: String
    ): CancelExecutionResponse {
        val resultJsonNode = flowForwardBizService.handlePopApiForward(
            appName = appName,
            releaseWorkflowSn = releaseWorkflowSn,
            releaseStageSn = releaseStageSn,
            executionNumber = executionNumber,
            operationName = "cancelExecutionReleaseStage"
        )
        return CancelExecutionResponse(
            success = resultJsonNode.get("object").asBoolean()
        )
    }

    @AmpApi(
        name = "GetReleaseStagePipelineRun",
        summary = "获取研发阶段流水线运行实例",
        path = "/appstack/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}/executions/{executionNumber}:getPipelineRun",
        method = "get",
        operationType = "read"
    )
    @Operation(summary = "获取研发阶段流水线运行实例", operationId = "GetReleaseStagePipelineRun")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "获取成功"),
            ApiResponse(responseCode = "404", description = "研发流程 / 研发阶段 / 流水线实例不存在"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}/executions/{executionNumber}:getPipelineRun")
    fun getPipelineRun(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @PathVariable executionNumber: String
    ): GetPipelineRunResponse {
        val resultJsonNode = flowForwardBizService.handlePopApiForward(
            appName = appName,
            releaseWorkflowSn = releaseWorkflowSn,
            releaseStageSn = releaseStageSn,
            executionNumber = executionNumber,
            operationName = "getPipelineRun"
        )
        return GetPipelineRunResponse(
            pipelineRun = resultJsonNode.get("object").let {
                jacksonObjectMapper().treeToValue(it, PipelineRun::class.java)
            }
        )
    }

    @AmpApi(
        name = "PassReleaseStagePipelineValidate",
        summary = "通过人工卡点",
        path = "/appstack/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}/executions/{executionNumber}:passPipelineValidate",
        method = "post",
        operationType = "write"
    )
    @Operation(summary = "通过人工卡点", operationId = "PassReleaseStagePipelineValidate")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "通过成功"),
            ApiResponse(responseCode = "404", description = "研发流程 / 研发阶段 / 流水线实例不存在"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}/executions/{executionNumber}:passPipelineValidate")
    fun passPipelineValidate(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @PathVariable executionNumber: String,
        @RequestParam jobId: String
    ): PassPipelineValidateResponse {
        val resultJsonNode = flowForwardBizService.handlePopApiForward(
            appName = appName,
            releaseWorkflowSn = releaseWorkflowSn,
            releaseStageSn = releaseStageSn,
            executionNumber = executionNumber,
            jobId = jobId,
            operationName = "passPipelineValidate"
        )
        return PassPipelineValidateResponse(
            success = resultJsonNode.get("object").asBoolean()
        )
    }

    @AmpApi(
        name = "RefuseReleaseStagePipelineValidate",
        summary = "拒绝人工卡点",
        path = "/appstack/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}/executions/{executionNumber}:refusePipelineValidate",
        method = "post",
        operationType = "write"
    )
    @Operation(summary = "拒绝人工卡点", operationId = "RefuseReleaseStagePipelineValidate")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "拒绝成功"),
            ApiResponse(responseCode = "404", description = "研发流程 / 研发阶段 / 流水线实例不存在"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}/executions/{executionNumber}:refusePipelineValidate")
    fun refusePipelineValidate(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @PathVariable executionNumber: String,
        @RequestParam jobId: String
    ): RefusePipelineValidateResponse {
        val resultJsonNode = flowForwardBizService.handlePopApiForward(
            appName = appName,
            releaseWorkflowSn = releaseWorkflowSn,
            releaseStageSn = releaseStageSn,
            executionNumber = executionNumber,
            jobId = jobId,
            operationName = "refusePipelineValidate"
        )
        return RefusePipelineValidateResponse(
            success = resultJsonNode.get("object").asBoolean()
        )
    }

    @AmpApi(
        name = "ListAppReleaseStageExecutions",
        summary = "批量查询研发阶段执行记录",
        path = "/appstack/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}/executions",
        method = "get",
        operationType = "read"
    )
    @Operation(summary = "批量查询研发阶段执行记录", operationId = "ListAppReleaseStageRuns")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}/executions")
    fun listAppReleaseStageExecutions(
        @AmpParam(description = "应用名")
        @PathVariable appName: String,
        @AmpParam(description = "研发流程sn")
        @PathVariable releaseWorkflowSn: String,
        @AmpParam(description = "研发阶段sn")
        @PathVariable releaseStageSn: String,
        @ParameterObject paginationQuery: PaginationQuery
    ): PageList<ReleaseStageInstanceVO> {
        return changeRequestStageBizService.listReleaseStageInstances(
            appName = appName,
            releaseWorkflowSn = releaseWorkflowSn,
            releaseStageSn = releaseStageSn,
            paginationQuery = paginationQuery
        )
    }

    @AmpApi(
        name = "ListAppReleaseStageExecutionIntegratedMetadata",
        summary = "查询研发阶段执行记录集成变更信息",
        path = "/appstack/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}/executions/{executionNumber}/integratedMetadata",
        method = "get",
        operationType = "read"
    )
    @Operation(summary = "查询研发阶段执行记录集成变更信息", operationId = "ListAppReleaseStageExecutionIntegratedMetadata")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}/executions/{executionNumber}/integratedMetadata")
    fun listAppReleaseStageExecutionIntegratedMetadata(
        @AmpParam(description = "应用名")
        @PathVariable appName: String,
        @AmpParam(description = "研发流程sn")
        @PathVariable releaseWorkflowSn: String,
        @AmpParam(description = "研发阶段sn")
        @PathVariable releaseStageSn: String,
        @AmpParam(description = "研发阶段的执行记录编号", example = "1")
        @PathVariable executionNumber: Long
    ): List<ReleaseStageInstanceIntegratedMetadataVO> {
        return changeRequestStageBizService.listReleaseStageInstanceIntegratedMetadata(
            appName = appName,
            releaseWorkflowSn = releaseWorkflowSn,
            releaseStageSn = releaseStageSn,
            number = executionNumber
        )
    }
}

