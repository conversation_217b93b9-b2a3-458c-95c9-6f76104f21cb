package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.model.OrgEventRequest
import com.alibabacloud.devops.appstack.apps.biz.model.OrgEventType
import com.alibabacloud.devops.appstack.apps.biz.model.OrganizationEventData
import com.alibabacloud.devops.appstack.apps.biz.model.OrganizationMemberCreateEventData
import com.alibabacloud.devops.appstack.apps.biz.model.OrganizationMemberRemoveEventData
import com.alibabacloud.devops.appstack.apps.biz.model.OrganizationMemberUpdateEventData
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateBizService
import com.alibabacloud.devops.appstack.apps.biz.service.OrgEventService
import com.alibabacloud.devops.appstack.apps.biz.service.PermissionChangedHandlerService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.IamClient
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.config.IamContext
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service

/**
 * <AUTHOR> <EMAIL>
 * @version : OrgEventServiceImpl, v0.1
 * @date : 2021-11-30 10:16
 *
 * @see : https://yuque.antfin.com/aone/base/artmm8
 **/
@Slf4k
@Service
@ConditionalOnProperty(name = ["devops.iam.environment"], havingValue = "aliyun", matchIfMissing = true)
class OrgEventServiceImpl : OrgEventService {

    @Autowired
    lateinit var iamClient: IamClient

    @Autowired
    lateinit var objectMapper: ObjectMapper

    @Autowired
    lateinit var appTemplateBizService: AppTemplateBizService

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var permissionChangedHandlerService: PermissionChangedHandlerService

    override fun handleEvent(eventCode: String, eventId: String, request: OrgEventRequest) {
        when (eventCode) {
            OrgEventType.ORG_CREATE.value -> orgCreateHandler(eventCode, eventId, request)
            OrgEventType.ORG_REMOVE.value -> orgRemoveHandler(eventCode, eventId, request)
            OrgEventType.ORG_MEMBER_CREATE.value -> orgMemberCreateHandler(eventCode, eventId, request)
            OrgEventType.ORG_MEMBER_UPDATE.value -> orgMemberUpdateHandler(eventCode, eventId, request)
            OrgEventType.ORG_MEMBER_REMOVE.value -> orgMemberRemoveHandler(eventCode, eventId, request)
            else -> {
                logger.warn("event: {} has not been supported", eventCode)
            }
        }
    }

    private fun orgCreateHandler(eventCode: String, eventId: String, request: OrgEventRequest) {
        logger.info("handle event: {}, id: {}", eventCode, eventId)
        val organizationEventData = objectMapper.readValue<OrganizationEventData>(request.data)
        val userId = organizationEventData.user?.id ?: organizationEventData.organization.creator?.id ?: return
        setContext(organizationEventData.organization.id, userId)
        iamClient.initConfig()
        // 初始化标签
        coreFacades.labelFacade.preset()
    }

    private fun orgRemoveHandler(eventCode: String, eventId: String, request: OrgEventRequest) {
        TODO("Not yet implemented")
    }

    private fun orgMemberCreateHandler(eventCode: String, eventId: String, request: OrgEventRequest) {
        logger.info("handle event: {}, id: {}", eventCode, eventId)
        val memberCreateEventData = objectMapper.readValue<OrganizationMemberCreateEventData>(request.data)
    }

    private fun orgMemberUpdateHandler(eventCode: String, eventId: String, request: OrgEventRequest) {
        logger.info("handle event: {}, id: {}", eventCode, eventId)
        val memberUpdateEventData = objectMapper.readValue<OrganizationMemberUpdateEventData>(request.data)
    }

    private fun orgMemberRemoveHandler(eventCode: String, eventId: String, request: OrgEventRequest) {
        logger.info("handle event: {}, id: {}", eventCode, eventId)
        val memberRemoveEventData = objectMapper.readValue<OrganizationMemberRemoveEventData>(request.data)
        memberRemoveEventData.member?.userId?.let { permissionChangedHandlerService.removeResourceAuth(it) }
    }

    private fun setContext(orgId: String, userId: String) {
        AuthThreadContext.setTenant(orgId)
        AuthThreadContext.setUserId(userId)
        IamContext.setTenant(orgId)
        IamContext.setOperator(userId)
    }
}