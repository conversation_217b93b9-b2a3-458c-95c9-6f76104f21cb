package com.alibabacloud.devops.appstack.apps.biz.service.system.impl

import com.alibabacloud.devops.appstack.apps.biz.annotation.IsSystem
import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.apps.biz.service.*
import com.alibabacloud.devops.appstack.apps.biz.service.system.SystemBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.common.util.checkNonExist
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.OnlyViewAccessableAppV1OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.OnlyViewAccessableSystemV1OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.App
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppCodeRepo
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppType
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.iam.AppRole
import com.alibabacloud.devops.appstack.libs.model.iam.SystemRole
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.*
import com.alibabacloud.devops.appstack.libs.model.request.AppRequest
import com.alibabacloud.devops.appstack.libs.model.request.SearchAppRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.CreateSystemRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.SearchSystemFilter
import com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateSystemRequest
import com.alibabacloud.devops.appstack.libs.model.response.ng.AppWithSourcesVO
import com.alibabacloud.devops.appstack.libs.model.response.ng.SystemCountVO
import com.alibabacloud.devops.appstack.libs.model.response.ng.SystemWithPersonalFavouritesVO
import com.alibabacloud.devops.appstack.libs.model.vo.AppWithOwnerVO
import com.alibabacloud.devops.appstack.libs.model.vo.ng.System
import com.alibabacloud.devops.appstack.libs.model.vo.toWithOwnerVO
import com.alibabacloud.devops.appstack.libs.org.spring.boot.starter.service.OrgFacades
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import feign.FeignException
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import kotlin.math.ceil

/**
 * @author: <EMAIL>
 * @date: 2023-11-06 10:20
 * @version: SystemBizServiceImpl, v0.1
 **/
@Service
open class SystemBizServiceImpl : SystemBizService {

    @Value("\${service.org.environment}")
    lateinit var environment: String

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var iamService: IamService

    @Autowired
    lateinit var orgConfigItemBizService: OrgConfigItemBizService

    @Autowired
    lateinit var permissionService: PermissionService

    @Autowired
    lateinit var auditLogService: AuditLogService

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var orgFacades: OrgFacades

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun find(systemName: String): SystemWithPersonalFavouritesVO {
        val app = coreFacades.appFacade.find(systemName) ?: throw BizException(ErrorCode.AS_SYSTEM_NOT_FOUND)
        if (app.type == null || app.type != AppType.SYSTEM) {
            throw BizException(ErrorCode.AS_SYSTEM_NOT_FOUND)
        }
        val ownerId = findSystemOwner(systemName)
        return app.toSystemWithPersonalFavouritesVO(ownerId = ownerId)
    }

    override fun search(request: SearchSystemFilter, current: Long, pageSize: Long): Pagination<SystemWithPersonalFavouritesVO> {
        val orgConfigItem = orgConfigItemBizService.find(OrgConfigItem.ONLY_VIEW_ACCESSABLE_SYSTEM)
        // 如果勾选了【仅支持查看我有权限的系统列表】
        if ((orgConfigItem as? OnlyViewAccessableSystemV1OrgConfigItem)?.enable == true) {
            // 如果查询全部但没有查看全部系统权限
            if (request.mine == false
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_SYSTEM_VIEW)
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_SYSTEM_MANAGE)) {
                throw BizException(ErrorCode.AS_PERMISSION_DENIED)
            }
        }
        return if (request.mine != null && request.mine == true) {
            val names = searchMySystemNames(request.nameKeyword, current, pageSize)
            if (names.isEmpty()) {
                Pagination(
                    total = 0,
                    current = current,
                    pageSize = pageSize,
                    pages = 0,
                    records = emptyList()
                )
            } else {
                val total = countMySystems(request.nameKeyword)
                val rawRecords = coreFacades.appFacade.searchPaginated(
                    SearchAppRequest(
                        names = names,
                        isMine = request.mine,
                        isFavoured = request.favoured,
                        types = setOf(AppType.SYSTEM)
                    ),
                    current = 1L,
                    pageSize = names.size.toLong()
                )
                rawRecords.records = rawRecords.records.sortedWith { o1, o2 ->
                    names.indexOf(o1.name) - names.indexOf(o2.name)
                }
                val owners = findSystemOwners(rawRecords.records.map { it.name })
                Pagination(
                    total = total,
                    current = current,
                    pageSize = pageSize,
                    pages = ceil(total.toDouble() / pageSize).toLong(),
                    records = rawRecords.records.map {
                        it.toSystemWithPersonalFavouritesVO(ownerId = owners[it.name])
                    }
                )
            }
        } else {
            val rawRecords = coreFacades.appFacade.searchPaginated(
                SearchAppRequest(
                    search = request.nameKeyword,
                    isMine = request.mine,
                    isFavoured = request.favoured,
                    types = setOf(AppType.SYSTEM)
                ),
                current = current,
                pageSize = pageSize
            )
            val owners = findSystemOwners(rawRecords.records.map { it.name })
            Pagination(
                total = rawRecords.total,
                current = rawRecords.current,
                pageSize = rawRecords.pageSize,
                pages = rawRecords.pages,
                records = rawRecords.records.map {
                    it.toSystemWithPersonalFavouritesVO(ownerId = owners[it.name])
                }
            )
        }
    }

    override fun list(current: Long, pageSize: Long): Pagination<SystemWithPersonalFavouritesVO> {
        return search(SearchSystemFilter(), current = current, pageSize = pageSize)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.ORG_SYSTEM_MANAGE)
        ]
    )
    override fun listRaw(current: Long, pageSize: Long): Pagination<System> {
        val rawRecords = search(SearchSystemFilter(), current = current, pageSize = pageSize)
        return Pagination(
            total = rawRecords.total,
            current = rawRecords.current,
            pageSize = rawRecords.pageSize,
            pages = rawRecords.pages,
            records = rawRecords.records.map {
                System(
                    name = it.name,
                    gmtCreate = it.gmtCreate,
                    creatorId = it.creatorId,
                    description = it.description,
                    ownerId = it.ownerId
                )
            }
        )
    }

    override fun getGroupCount(request: SearchSystemFilter): SystemCountVO {
        val searchAppRequest = SearchAppRequest(
            search = request.nameKeyword,
            isMine = request.mine,
            isFavoured = request.favoured,
            types = setOf(AppType.SYSTEM)
        )
        val orgConfigItem = orgConfigItemBizService.find(OrgConfigItem.ONLY_VIEW_ACCESSABLE_SYSTEM)
        // 如果勾选了【仅支持查看我有权限的系统列表】
        if ((orgConfigItem as? OnlyViewAccessableSystemV1OrgConfigItem)?.enable == true) {
            // 如果查询全部但没有查看全部系统权限
            if (request.mine == false
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_SYSTEM_VIEW)
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_SYSTEM_MANAGE)) {
                searchAppRequest.isMine = true
            }
        }
        searchAppRequest.names = null
        searchAppRequest.isFavoured = null
        val allPagination = coreFacades.appFacade.searchPaginated(searchAppRequest, 1, 10000)
        searchAppRequest.isFavoured = true
        val favouredPagination = coreFacades.appFacade.searchPaginated(searchAppRequest, 1, 10000)
        return SystemCountVO(
            all = allPagination.total,
            mine = countMySystems(search = request.nameKeyword),
            favorite = favouredPagination.total
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_CREATE),
            Access(action = Action.ORG_SYSTEM_MANAGE)
        ]
    )
    override fun create(request: CreateSystemRequest): System {
        val appReq = AppRequest(
            name = request.name,
            ownerId = request.ownerId,
            description = request.description,
            type = AppType.SYSTEM.name
        )
        val ownerId = appReq.ownerId ?: AuthUtil.getUserId()
        // 校验用户存在性
        iamService.checkUserExist(ownerId)
        val app = coreFacades.appFacade.create(appReq)
        iamService.registerResource(ProtocolType.AppStackSystem, app.name, AuthUtil.getTenant(), ownerId)

        // FIXME jiuya.wb VO 层玩太花了……
        return app.toWithOwnerVO(ownerId).toStandardVO()
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_BASIC_SET, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun update(systemName: String, request: UpdateSystemRequest): System {
        // 预检验一次
        val app = coreFacades.appFacade.find(systemName) ?: throw BizException(ErrorCode.AS_SYSTEM_NOT_FOUND)
        if (app.type == null || app.type != AppType.SYSTEM) {
            throw BizException(ErrorCode.AS_SYSTEM_NOT_FOUND)
        }

        // 非权限相关的领域模型变更
        coreFacades.appFacade.update(
            systemName,
            UpdateAppRequest(
                description = request.description
            )
        )

        // 转移拥有者最后做
        if (request.ownerId != null) {
            transferOwner(
                systemName,
                ResourcePlayerRequest(request.ownerId, SubjectType.User)
            )
        }

        return find(systemName)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_DELETE, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun delete(systemName: String): Boolean {
        // NOTE jiuya.wb 这里强依赖的假设是系统和应用不重名，在很长一段时间内应该都不会被违反
        coreFacades.appFacade.disrelateAllApps(systemName)
        workflowControllerFacade.releaseWorkflowFacade.deleteAllByAppName(systemName)
        workflowControllerFacade.appReleaseFacade.deleteAllByAppName(systemName)
        coreFacades.appFacade.delete(systemName)
        iamService.unregisterResource(ProtocolType.AppStackSystem, systemName, AuthUtil.getTenant(), AuthUtil.getUserId())
        return true
    }

    @IsSystem
    override fun listMembers(systemName: String, current: Long, pageSize: Long): Pagination<MemberVO> {
        // 暂时保持原先的一次性查找不变
        // 这里合并了 AppBizApi 和 AppBizService 的相关实现，尽量少在 controller 层加逻辑
        val playerRolesMap = iamService.findPlayerRoles(ProtocolType.AppStackSystem, systemName)
        playerRolesMap.values.forEach { roleList ->
            roleList.sortBy { AppRole.valueOf(it.name).ordinal }
        }

        val resultList = playerRolesMap.map { MemberVO(it.key, it.value) }.sortedWith { o1, o2 ->
            if (o1.roleList.find { it.name == AppRole.owner.name } != null) -1
            else if (o2.roleList.find { it.name == AppRole.owner.name } != null) 1
            else
                o1.displayName.compareTo(o2.displayName)
        }

        return Pagination(
            total = resultList.size.toLong(),
            current = 1L,
            records = resultList
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_MEMBER_SET, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun addRoleMember(systemName: String, roleName: String, playerList: List<ResourcePlayerRequest>): Boolean {
        if (playerList.firstOrNull { it.type !in listOf(SubjectType.User, SubjectType.Group, SubjectType.Team) } != null) {
            throw BizException(ErrorCode.AS_SUBJECT_TYPE_NOT_SUPPORT)
        }
        val role = permissionService.findSystemRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        if (!iamService.checkPlayersExist(playerList)) {
            throw BizException(ErrorCode.AS_BASE_USER_NOT_FOUND)
        }
        iamService.addRoleMember(
            protocolType = ProtocolType.AppStackSystem,
            resourceName = systemName,
            roleName = role.name,
            playerList = playerList
        )
        val audit = MemberAudit(
            target = AuditTarget(id = systemName, name = systemName),
            member = Member(roleName = role.name, userIds = playerList.map { it.id })
        )
        auditLogService.log(OrgEventType.APP_MEMBER_ADD, audit)
        return true
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_MEMBER_SET, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun updateRoleMember(
        systemName: String,
        roleName: String,
        playerList: List<ResourcePlayerRequest>
    ): Boolean {
        if (playerList.firstOrNull { it.type !in listOf(SubjectType.User, SubjectType.Group, SubjectType.Team) } != null) {
            throw BizException(ErrorCode.AS_SUBJECT_TYPE_NOT_SUPPORT)
        }
        val role = permissionService.findSystemRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        if (!iamService.checkPlayersExist(playerList)) {
            throw BizException(ErrorCode.AS_BASE_USER_NOT_FOUND)
        }
        iamService.updateRole(
            protocolType = ProtocolType.AppStackSystem,
            resourceName = systemName,
            roleName = role.name,
            playerList = playerList
        )
        return true
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_MEMBER_SET, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun updateMemberRole(
        systemName: String,
        subjectType: SubjectType,
        subjectId: String,
        roleNameList: List<String>
    ): Boolean {
        if (subjectType !in listOf(SubjectType.User, SubjectType.Group, SubjectType.Team)) {
            throw BizException(ErrorCode.AS_SUBJECT_TYPE_NOT_SUPPORT)
        }
        // 如果用户不存在
        if (!iamService.checkPlayersExist(listOf(ResourcePlayerRequest(subjectId, subjectType)))) {
            throw BizException(ErrorCode.AS_BASE_USER_NOT_FOUND)
        }
        val allRoleNames = permissionService.findAllSystemRoles().map { it.name }
        // 如果有不存在的系统角色
        val unknownRole = roleNameList.firstOrNull { !allRoleNames.contains(it) }
        if (unknownRole != null) {
            throw BizException(ErrorCode.AS_ROLE_NOT_FOUND)
        }
        val actualRoleNameList = roleNameList.filter { it != SystemRole.owner.name }
        val previousRoles = iamService.findPlayerRoles(
            protocolType = ProtocolType.AppStackSystem,
            resourceName = systemName,
        ).mapKeys { it.key.id }[subjectId] ?: emptyList()
        iamService.updatePlayer(
            protocolType = ProtocolType.AppStackSystem,
            resourceName = systemName,
            subjectType = subjectType,
            subjectId = subjectId,
            roleNameList = actualRoleNameList
        )
        val audit = MemberRoleAudit(
            target = AuditTarget(id = systemName, name = systemName),
            member = MemberRole(
                roleNames = actualRoleNameList,
                previousRoleNames = previousRoles.map { it.name },
                userId = subjectId
            )
        )
        val auditType = if (actualRoleNameList.isEmpty()) OrgEventType.APP_MEMBER_DELETE else OrgEventType.APP_MEMBER_MODIFY
        auditLogService.log(auditType, audit)
        return true
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_OWNER_TRANSFER, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun transferOwner(systemName: String, owner: ResourcePlayerRequest): Boolean {
        iamService.checkUserExist(owner.id)
        findSystemOwner(systemName)?.let {
            iamService.updatePlayer(
                protocolType = ProtocolType.AppStackSystem,
                resourceName = systemName,
                subjectType = SubjectType.User,
                subjectId = it,
                roleNameList = listOf(AppRole.developer.name)
            )
        }
        iamService.updatePlayer(
            protocolType = ProtocolType.AppStackSystem,
            resourceName = systemName,
            subjectType = owner.type,
            subjectId = owner.id,
            roleNameList = listOf(AppRole.owner.name)
        )
        return true
    }

    @IsSystem
    override fun favourSystem(systemName: String): Boolean {
        coreFacades.appFacade.favour(systemName)
        return true
    }

    @IsSystem
    override fun disfavourSystem(systemName: String): Boolean {
        coreFacades.appFacade.disfavour(systemName)
        return true
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_BASIC_SET, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun attachAppsToSystem(systemName: String, targetApps: Set<String>): Boolean {
        coreFacades.appFacade.relateApps(systemName, targetApps)
        return true
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_BASIC_SET, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun detachAppsFromSystem(systemName: String, appNames: Set<String>): Boolean {
        coreFacades.appFacade.disrelateApps(systemName, appNames.toList())
        return true
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun listAttachedApps(systemName: String, current: Long, pageSize: Long): Pagination<AppWithSourcesVO> {
        val size = if (pageSize < 1) 10L else pageSize
        val relatedPagination = coreFacades.appFacade.listRelatedAppsPaginated(systemName, "", current, size)
        val appNames = relatedPagination.records.map { it.name }

        val appCodeRepoMap = coreFacades.appCodeRepoFacade.findAllByAppNames(appNames)
        val artifactRepoMap = coreFacades.appArtifactRepoFacade.findAllByAppNames(appNames)
        return Pagination(
            total = relatedPagination.total,
            current = relatedPagination.current,
            pageSize = relatedPagination.pageSize,
            pages = relatedPagination.pages,
            records = relatedPagination.records.map { app ->
                AppWithSourcesVO(
                    name = app.name,
                    gmtCreate = app.gmtCreate!!,
                    creatorId = app.creatorId!!,
                    description = app.description,
                    codeRepos = appCodeRepoMap[app.name]?: emptyList(),
                    artifactRepos = artifactRepoMap[app.name]?: emptyList()
                )
            }
        )
    }

    private fun findSystemOwners(names: List<String>): Map<String, String?> {
        if (names.isEmpty()) return emptyMap()
        return iamService.findRolePlayers(ProtocolType.AppStackSystem, names, AppRole.owner.name).mapValues { it.value[0] }
    }

    private fun findSystemOwner(name: String): String? {
        return findSystemOwners(listOf(name)).values.firstOrNull()
    }

    private fun countMySystems(search: String? = ""): Long {
        return iamService.countResource(
            protocolType = ProtocolType.AppStackSystem,
            subjectType = SubjectType.User,
            subjectId = AuthUtil.getUserId(),
            search = search
        ).toLong()
    }

    private fun searchMySystemNames(search: String? = "", page: Long, size: Long): List<String> {
        return iamService.searchResourceName(
            protocolType = ProtocolType.AppStackSystem,
            subjectType = SubjectType.User,
            subjectId = AuthUtil.getUserId(),
            search = search,
            page = page.toInt(),
            size = size.toInt(),
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun findAllCodeRepos(systemName: String): List<AppCodeRepo> {
        return coreFacades.appCodeRepoFacade.findAll(systemName)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun findAllOrchestrations(systemName: String): List<AppOrchestration> {
        return coreFacades.appOrchestrationFacade.findAll(systemName)
    }
}

fun App.toSystemWithPersonalFavouritesVO(ownerId: String? = null): SystemWithPersonalFavouritesVO {
    return SystemWithPersonalFavouritesVO(
        name = this.name,
        gmtCreate = this.gmtCreate!!,
        creatorId = this.creatorId!!,
        description = this.description,
        favoured = this.favoured,
        ownerId = ownerId
    )
}

fun AppWithOwnerVO.toStandardVO(): System {
    return System(
        name = this.name,
        gmtCreate = this.gmtCreate!!,
        creatorId = this.creatorId!!,
        description = this.description,
        ownerId = this.ownerId
    )
}