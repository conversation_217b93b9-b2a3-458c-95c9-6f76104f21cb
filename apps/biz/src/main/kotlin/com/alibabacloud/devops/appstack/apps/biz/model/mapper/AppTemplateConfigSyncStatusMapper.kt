package com.alibabacloud.devops.appstack.apps.biz.model.mapper

import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateConfigSyncStatusPO
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfigSyncStatus
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.core.mapper.BaseMapper
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Update

/**
 * <AUTHOR>
 * @date 2024-02-15
 */
@Mapper
interface AppTemplateConfigSyncStatusMapper : BaseMapper<AppTemplateConfigSyncStatusPO> {

    @Update(
        """
            UPDATE app_template_config_sync_status
            SET template_sync_status = #{templateSyncStatus}
            WHERE app_template_name = #{templateName}
              AND app_template_config_type = #{configType}
              AND app_template_config_instance_name = #{configInstanceName}
              AND app_name = #{appName}
        """
    )
    fun updateTemplateSyncStatus(
        templateName: String,
        configType: String,
        configInstanceName: String,
        appName: String,
        templateSyncStatus: String
    )

    @Update(
        """
            UPDATE app_template_config_sync_status
            SET instance_update_status = #{instanceUpdateStatus}, last_instance_update_seq_no = #{lastInstanceUpdateSeqNo}
            WHERE app_template_name = #{templateName}
              AND app_template_config_type = #{configType}
              AND app_template_config_instance_name = #{configInstanceName}
              AND app_name = #{appName}
        """
    )
    fun updateInstanceUpdateStatus(
        templateName: String,
        configType: String,
        configInstanceName: String,
        appName: String,
        lastInstanceUpdateSeqNo: Long,
        instanceUpdateStatus: String
    )
}

fun AppTemplateConfigSyncStatusMapper.listByTemplate(
    appTemplateName: String,
    appTemplateConfigType: String,
    appTemplateConfigInstanceName: String,
): List<AppTemplateConfigSyncStatusPO> {
    return selectList(
        QueryWrapper<AppTemplateConfigSyncStatusPO>()
            .eq("app_template_name", appTemplateName)
            .eq("app_template_config_type", appTemplateConfigType)
            .eq("app_template_config_instance_name", appTemplateConfigInstanceName)
    )
}

fun AppTemplateConfigSyncStatusMapper.listByTemplateAndApps(
    appTemplateName: String,
    appTemplateConfigType: String,
    appTemplateConfigInstanceName: String,
    appNames: List<String>
): List<AppTemplateConfigSyncStatusPO> {
    return selectList(
        QueryWrapper<AppTemplateConfigSyncStatusPO>()
            .eq("app_template_name", appTemplateName)
            .eq("app_template_config_type", appTemplateConfigType)
            .eq("app_template_config_instance_name", appTemplateConfigInstanceName)
            .`in`("app_name", appNames)
    )
}

fun AppTemplateConfigSyncStatusMapper.findByTemplateAndApp(
    appTemplateName: String,
    appTemplateConfigType: String,
    appTemplateConfigInstanceName: String,
    appName: String
): AppTemplateConfigSyncStatusPO? {
    return selectOne(
        QueryWrapper<AppTemplateConfigSyncStatusPO>()
            .eq("app_template_name", appTemplateName)
            .eq("app_template_config_type", appTemplateConfigType)
            .eq("app_template_config_instance_name", appTemplateConfigInstanceName)
            .eq("app_name", appName)
    )
}