package com.alibabacloud.devops.appstack.apps.biz.service.client.api

import com.fasterxml.jackson.annotation.JsonProperty
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.HeaderMap
import retrofit2.http.POST
import retrofit2.http.Path

/**
 * <AUTHOR>
 * @date 2022-09-30
 */
@RetrofitClient(baseUrl = "\${service.org.apiHost}")
interface ConfigCenterApi {

    @POST("/configs:list")
    fun listConfigs(
        @HeaderMap headers: Map<String, String>,
        @Body request: ConfigCenterQuery
    ): String

    @GET("/tbs/org/v2/organizations/{orgId}/plan/{appId}:enable")
    fun findOrgPlan(
        @HeaderMap headers: Map<String, String>,
        @Path("orgId") orgId: String,
        @Path("appId") appId: String,
    ): String
}

data class ConfigCenterQuery(
    @JsonProperty("app_id")
    val appId: String
)