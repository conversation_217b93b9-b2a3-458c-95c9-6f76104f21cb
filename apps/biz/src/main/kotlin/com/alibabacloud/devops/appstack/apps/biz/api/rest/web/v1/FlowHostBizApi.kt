package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.FlowHostBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.*
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-07-08 15:15
 * @version: HostGroupBizApi, v0.1
 **/
@Tag(name = "FlowHost", description = "主机组相关 API")
@RestController
@RequestMapping("api/v1/host/flow")
open class FlowHostBizApi {
    @Autowired
    lateinit var flowHostBizService: FlowHostBizService


    @Operation(summary = "创建体验主机")
    @PostMapping("/createExperience")
    fun createExperience(
       @RequestBody hostGroupExperienceDTO: HostGroupExperienceDTO
    ): Response<Long> {
        return Response.success(flowHostBizService.createExperience(hostGroupExperienceDTO))
    }

    @Operation(summary = "查询创建结果")
    @GetMapping("/fetchCreateResult")
    fun fetchCreateResult(
        @RequestParam flowScopeType: FlowScopeType,
        @RequestParam instanceId: String
    ): Response<Long?> {
        return Response.success(flowHostBizService.fetchCreateResult(instanceId,flowScopeType ))
    }


    @Operation(summary = "生成安装命令")
    @GetMapping("/generateInstallCommand")
    fun generateInstallCommand(
        @RequestParam purpose: FlowPurpose,
        @RequestParam flowScopeType: FlowScopeType,
        @RequestParam(required = false, defaultValue = "linux") os: String,
    ): Response<String> {
        val osType = OsType.get(os)
        return Response.success(flowHostBizService.fetchInstallCommand(purpose, flowScopeType, osType))
    }

    @Operation(summary = "查询主机组详情")
    @GetMapping("/findAllByGroupId")
    fun findAllByGroupId(
        @RequestParam groupId: Long
    ): Response<List<Host>> {
        return Response.success(flowHostBizService.findAllByGroupId(groupId))
    }

    @Operation(summary = "查询主机部署日志")
    @GetMapping("/fetchMachineDeployLog")
    fun fetchMachineDeployLog(
        @RequestParam tunnelId: Long,
        @RequestParam machineSn: String,
    ): Response<DeployMachineLog> {
        return Response.success(flowHostBizService.fetchMachineDeployLog(tunnelId, machineSn))
    }

    @Operation(summary = "查询企业是否升级runner")
    @GetMapping("/checkRunnerFeature")
    fun checkRunnerFeature(): Response<Boolean> {
        return Response.success(flowHostBizService.isRunnerFeature())
    }
}