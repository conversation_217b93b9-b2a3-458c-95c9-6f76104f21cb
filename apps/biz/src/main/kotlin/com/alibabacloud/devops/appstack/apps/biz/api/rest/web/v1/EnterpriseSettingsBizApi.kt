package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.EnterpriseSettingsBizService
import com.alibabacloud.devops.appstack.libs.model.request.KubeConfigVO
import com.alibabacloud.devops.appstack.libs.model.request.UpsertAliyunAuthAccountRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpsertKubeConfigRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.AliyunAuthAccountVO
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * @author: <EMAIL>
 * @date: 2022-01-27 14:32
 * @version: EnterpriseSettingsBizApi, v0.1
 **/
@Tag(name = "EnterpriseSettings", description = "企业设置相关 API")
@RestController
@RequestMapping("/api/v1/enterpriseSettings")
open class EnterpriseSettingsBizApi {

    @Autowired
    private lateinit var enterpriseSettingsBizService: EnterpriseSettingsBizService

    @Operation(summary = "列举当前登陆账号下的阿里云服务授权")
    @GetMapping("/AliyunAuthAccounts")
    fun findAllAliyunAuthAccounts(): Response<List<AliyunAuthAccountVO>> {
        return Response.success(enterpriseSettingsBizService.findAllAliyunAuthAccounts())
    }

    @Operation(summary = "创建阿里云服务授权")
    @PostMapping("/AliyunAuthAccounts")
    fun createAliyunAuthAccount(@RequestBody request: UpsertAliyunAuthAccountRequest): Response<AliyunAuthAccountVO> {
        return Response.success(enterpriseSettingsBizService.createAliyunAuthAccount(request))
    }

    @Operation(summary = "更新阿里云服务授权")
    @PutMapping("/AliyunAuthAccounts")
    fun updateAliyunAuthAccount(@RequestBody request: UpsertAliyunAuthAccountRequest): Response<AliyunAuthAccountVO> {
        return Response.success(enterpriseSettingsBizService.updateAliyunAuthAccount(request))
    }

    @Operation(summary = "删除阿里云服务授权")
    @DeleteMapping("/AliyunAuthAccounts/{id}")
    fun deleteAliyunAuthAccount(@PathVariable("id") id: Long): Response<Unit> {
        enterpriseSettingsBizService.deleteAliyunAuthAccount(id)
        return Response.success()
    }

    @Operation(summary = "查询所有KubeConfig")
    @GetMapping("/KubeConfigs")
    fun findAllKubeConfig(): Response<List<KubeConfigVO>> {
        return Response.success(enterpriseSettingsBizService.findAllKubeConfig())
    }

    @Operation(summary = "创建KubeConfig")
    @PostMapping("/KubeConfigs")
    fun createKubeConfig(
        @RequestBody request: UpsertKubeConfigRequest
    ): Response<KubeConfigVO> {
        return Response.success(enterpriseSettingsBizService.createKubeConfig(request))
    }

    @Operation(summary = "更新KubeConfig")
    @PutMapping("/KubeConfigs/{id}")
    fun updateKubeConfig(
        @PathVariable("id") id: Long,
        @RequestBody request: UpsertKubeConfigRequest
    ): Response<KubeConfigVO> {
        return Response.success(enterpriseSettingsBizService.updateKubeConfig(id, request))
    }

    @Operation(summary = "删除KubeConfig")
    @DeleteMapping("/KubeConfigs/{id}")
    fun deleteKubeConfig(
        @PathVariable("id") id: Long,
    ): Response<Unit> {
        enterpriseSettingsBizService.deleteKubeConfig(id)
        return Response.success()
    }
}