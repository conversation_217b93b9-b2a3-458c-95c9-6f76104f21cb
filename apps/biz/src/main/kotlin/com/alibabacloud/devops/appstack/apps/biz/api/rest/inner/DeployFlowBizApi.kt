package com.alibabacloud.devops.appstack.apps.biz.api.rest.inner

import com.alibabacloud.devops.appstack.apps.biz.service.DeployFlowBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Placeholder
import com.alibabacloud.devops.appstack.libs.model.request.FlowCreateChangeOrderRequest
import com.alibabacloud.devops.appstack.libs.model.request.FlowCreateCleanOrderRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderOnFlowVO
import io.swagger.v3.oas.annotations.Hidden
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-03-08 09:41
 * @version: DeployFlowBizApi, v0.1
 **/
@Hidden
@Tag(name = "flowDeploy", description = "部署相关 API")
@RestController
@RequestMapping("/inner/api/deploy/flow")
class DeployFlowBizApi {

    @Autowired
    lateinit var deployFlowBizService: DeployFlowBizService


    @Operation(summary = "从外部流水线创建发布单")
    @PostMapping("/changeOrder")
    fun create(
        @RequestBody flowCreateChangeOrderRequest: FlowCreateChangeOrderRequest
    ): Response<String> {
        return Response.success(deployFlowBizService.createChangeOrder(flowCreateChangeOrderRequest))
    }

    @Operation(summary = "从外部流水线创建环境清理单")
    @PostMapping("/createClean")
    fun createClean(
        @RequestBody flowCreateCleanOrderRequest: FlowCreateCleanOrderRequest
    ): Response<String> {
        return Response.success(deployFlowBizService.createCleanOrder(flowCreateCleanOrderRequest))
    }

    @Operation(summary = "查询flow部署单")
    @GetMapping("/queryFlowTask")
    fun findFlowChangeOrder(
        @RequestParam flowInsId: String,
        @RequestParam(value = "removeFlowInstId", required = false, defaultValue = "false") removeFlowInstId: Boolean,
        @RequestParam(value = "appName", required = false) appName: String?,
        @RequestParam(value = "envName", required = false) envName: String?
    ): Response<ChangeOrderOnFlowVO> {
        return Response.success(deployFlowBizService.findChangeOrder(flowInsId, removeFlowInstId, appName, envName))
    }
}