package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.service.DomainTagBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.app.DomainTag
import com.alibabacloud.devops.appstack.libs.model.request.*
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.response.TagBatchOperationResponse
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import com.alibabacloud.devops.appstack.libs.model.vo.DomainTagWithCreatorVO
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

@Tag(name = "tag", description = "tag 领域 API")
@RestController
@RequestMapping("/api/v2")
open class DomainTagBizApi {

    @Autowired
    lateinit var domainTagBizService: DomainTagBizService

    // 新建tag
    @Operation(summary = "新建标签", operationId = "CreateTag")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "新建标签成功"),
            ApiResponse(responseCode = "409", description = "标签名重复"),
            ApiResponse(responseCode = "422", description = "标签名非法"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/tags")
    fun create(
        @RequestBody domainTag: DomainTag
    ): Response<DomainTag> {
        val res = domainTagBizService.create(domainTag)
        return Response.success(res)
    }

    // 编辑tag
    @Operation(summary = "更新标签", operationId = "UpdateTag")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "更新标签成功"),
            ApiResponse(responseCode = "409", description = "标签名重复"),
            ApiResponse(responseCode = "422", description = "标签名非法"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PutMapping("/tags/{tagName}")
    fun update(
        @PathVariable("tagName") name: String,
        @RequestBody domainTag: DomainTag
    ): Response<DomainTag> {
        val res = domainTagBizService.update(name, domainTag)
        return Response.success(res)
    }

    // 删除tag
    @Operation(summary = "删除标签", operationId = "DeleteTag")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "删除标签成功"),
            ApiResponse(responseCode = "404", description = "标签不存在"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @DeleteMapping("/tags/{tagName}")
    fun delete(@PathVariable("tagName") name: String): Response<Unit> {
        domainTagBizService.delete(name)
        return Response.success()
    }

    // 查询tag列表
    @Operation(summary = "查询标签列表", operationId = "ListTags")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询标签列表成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/tags:search")
    fun list(
        @RequestBody req: TagSearchRequest,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long
    ): Response<PageList<DomainTagWithCreatorVO>> {
        val res = domainTagBizService.list(req, current, pageSize)
        return Response.success(res)
    }

    // 查询tag关联的应用列表
    @Operation(summary = "查询标签关联的应用列表", operationId = "ListAppsByTag")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询标签关联的应用列表成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/tags/{tagName}:listApps")
    fun listAppNamesByTag(
        @PathVariable("tagName") tagName: String
    ): Response<List<String>> {
        val res = domainTagBizService.listAppNamesByTag(tagName)
        return Response.success(res)
    }

    // 查询应用关联的tag列表
    @Operation(summary = "查询应用关联的tag列表", operationId = "ListTagsByAppName")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询应用关联的标签列表成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/tags")
    fun listTagsByApp(
        @PathVariable("appName") appName: String
    ): Response<List<DomainTag>> {
        val res = domainTagBizService.listTagsByAppName(appName)
        return Response.success(res)
    }

    @PostMapping("/tags:listByAppNames")
    fun listTagsByAppNames(
        @RequestBody request: TagSearchByAppsRequest
    ): Response<List<DomainTag>> {
        val res = domainTagBizService.listTagsByAppNames(request.appNames)
        return Response.success(res)
    }

    // 更新应用关联的tag列表
    @Operation(summary = "更新应用关联的tag列表", operationId = "UpdateTagBindings")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "更新应用关联的标签列表成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PutMapping("/apps/{appName}/tags")
    fun updateTagBindings(
        @PathVariable("appName") appName: String,
        @RequestBody req: TagBindingRequest
    ): Response<Unit> {
        domainTagBizService.updateTagBindings(req.tagNames, appName)
        return Response.success()
    }

    // 绑定多个tag到单个应用
    @Operation(summary = "绑定多个tag到单个应用", operationId = "Bind")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "绑定多个标签到单个应用成功"),
            ApiResponse(responseCode = "404", description = "应用不存在"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/apps/{appName}/tags")
    fun bind(
        @PathVariable("appName") appName: String,
        @RequestBody req: TagBindingRequest
    ): Response<Unit> {
        domainTagBizService.bind(req.tagNames, appName)
        return Response.success()
    }

    // 绑定多个tag到多个应用
    @Operation(summary = "绑定多个tag到多个应用", operationId = "BatchBind")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "绑定多个标签到多个应用成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/tags:batchBind")
    fun batchBind(
        @RequestBody req: TagBatchBindingRequest
    ): Response<TagBatchOperationResponse> {
        val res = domainTagBizService.batchBind(req.tagNames, req.appNames)
        return Response.success(res)
    }

    // 从单个应用上解绑多个tag
    @Operation(summary = "从单个应用上解绑多个tag", operationId = "Unbind")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "从单个应用上解绑多个标签成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @DeleteMapping("/apps/{appName}/tags")
    fun unbind(
        @PathVariable("appName") appName: String,
        @RequestBody req: TagBindingRequest
    ): Response<Unit> {
        domainTagBizService.unbind(req.tagNames, appName)
        return Response.success()
    }

    // 从多个应用上解绑多个tag
    @Operation(summary = "从多个应用上解绑多个tag", operationId = "batchUnbind")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "从多个应用上解绑多个标签成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @DeleteMapping("/tags:batchUnbind")
    fun batchUnbind(
        @RequestBody req: TagBatchBindingRequest
    ): Response<TagBatchOperationResponse> {
        val res = domainTagBizService.batchUnbind(req.tagNames, req.appNames)
        return Response.success(res)
    }
}