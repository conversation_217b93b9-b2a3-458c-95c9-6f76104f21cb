package com.alibabacloud.devops.appstack.apps.biz.adapter.ade1

import com.alibabacloud.devops.appstack.apps.biz.adapter.EnvLogChangeOrderAdapter
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.Task
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeDeploymentStrategy
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.toAdeTask
import com.alibabacloud.devops.appstack.libs.model.constant.OpLogI18nCodeEnum
import com.alibabacloud.devops.appstack.libs.model.event.OpLogCode
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2022-03-31
 */
@Service
class Ade1EnvLogChangeOrderAdapter : EnvLogChangeOrderAdapter {

    override fun buildScaleLog(task: Task): List<OpLogCode> {
        val strategy = task.toAdeTask().strategy as AdeDeploymentStrategy
        val fromReplicas = strategy.fromReplicas ?: 0
        val targetReplicas = strategy.targetReplicas!!
        return listOf(
            OpLogCode(
                i18nCode = if (targetReplicas >= fromReplicas) OpLogI18nCodeEnum.ENV_SCALE_UP.value else OpLogI18nCodeEnum.ENV_SCALE_DOWN.value,
                params = listOf(
                    task.name,
                    fromReplicas.toString(),
                    targetReplicas.toString()
                )
            ),
            OpLogCode(i18nCode = OpLogI18nCodeEnum.MARKS_SEMICOLON.value),
        )
    }
}
