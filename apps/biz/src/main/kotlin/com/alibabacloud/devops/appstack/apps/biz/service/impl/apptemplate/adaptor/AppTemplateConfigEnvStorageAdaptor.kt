package com.alibabacloud.devops.appstack.apps.biz.service.impl.apptemplate.adaptor

import com.alibabacloud.devops.appstack.apps.biz.model.AppTemplateConfigReference
import com.alibabacloud.devops.appstack.apps.biz.model.ext.toModel
import com.alibabacloud.devops.appstack.apps.biz.model.ext.toPO
import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateConfigPO
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateConfigReferenceService
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateConfigStorageAdaptor
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.EnvConfiguration
import com.alibabacloud.devops.appstack.libs.model.`do`.app.VariableGroupConfiguration
import com.alibabacloud.devops.appstack.libs.model.`do`.env.EnvTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.globalvar.GlobalVarUsageReference
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.AbstractConfigurationUpsertRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.EnvConfigurationUpsertRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateConfigRequest
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR> <EMAIL>
 * @version : AppTemplateConfigEnvStorageAdaptor, v0.1
 * @date : 2023-10-19 11:48
 **/
@Service
open class AppTemplateConfigEnvStorageAdaptor : AppTemplateConfigStorageAdaptor<EnvConfiguration>() {

    @Autowired
    lateinit var appTemplateConfigReferenceService: AppTemplateConfigReferenceService

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var appTemplateConfigVariableGroupStorageAdaptor: AppTemplateConfigVariableGroupStorageAdaptor

    @Autowired
    lateinit var auditLogService: AuditLogService

    override fun insert(appTemplate: AppTemplate, request: UpsertAppTemplateConfigRequest): AppTemplateConfig {
        val config = super.insert(appTemplate, request)
        rebindReference(previousConfig = null, afterConfig = config)
        return config
    }

    override fun update(
        appTemplate: AppTemplate,
        previous: AppTemplateConfig,
        request: UpsertAppTemplateConfigRequest
    ): AppTemplateConfig {
        val config = super.update(appTemplate, previous, request)
        rebindReference(previousConfig = previous, afterConfig = config)
        return config
    }

    private fun rebindReference(previousConfig: AppTemplateConfig?, afterConfig: AppTemplateConfig) {
        val envConfigs = afterConfig.configuration as EnvConfiguration
        // 增加对资源的使用引用
        val references = envConfigs.envs.mapNotNull {
            if (it.resourcePoolName.isNullOrBlank() || it.deployGroupName.isNullOrBlank()) {
                null
            } else {
                AppTemplateConfigReference(
                    type = AppTemplateConfigReference.TypeEnum.RESOURCE_INSTANCE,
                    id = AppTemplateConfigReference.convertResourceInstanceToReferenceId(
                        it.resourcePoolName!!,
                        it.deployGroupName!!
                    )
                )
            }
        }
        appTemplateConfigReferenceService.reBind(afterConfig.appTemplateName, afterConfig.sn!!, references)
        // 增加对全局变量组的使用引用
        val previousUsageReferences = mutableSetOf<Pair<String, String>>()
        previousConfig?.let { config ->
            (config.configuration as EnvConfiguration).envs.forEach { envTemplate ->
                envTemplate.variableGroups?.filter { it.type == VariableGroup.Type.GLOBAL }?.forEach { variableGroup ->
                    previousUsageReferences.add(Pair(envTemplate.name, variableGroup.name))
                }
            }
        }
        val currentUsageReferences = mutableSetOf<Pair<String, String>>()
        (afterConfig.configuration as EnvConfiguration).envs.forEach { envTemplate ->
            envTemplate.variableGroups?.filter { it.type == VariableGroup.Type.GLOBAL }?.forEach { variableGroup ->
                currentUsageReferences.add(Pair(envTemplate.name, variableGroup.name))
            }
        }
        (previousUsageReferences - currentUsageReferences).forEach { (envName, varName) ->
            coreFacades.globalVarFacade.deleteUsageReference(
                GlobalVarUsageReference(
                    globalVarName = varName,
                    usageRefType = GlobalVarUsageReference.UsageRefType.AppTemplateEnv,
                    usageRefObject = GlobalVarUsageReference.AppTemplateEnvUsageRefObject(
                        appTemplateName = afterConfig.appTemplateName,
                        envName = envName
                    )
                )
            )
        }
        (currentUsageReferences - previousUsageReferences).forEach { (envName, varName) ->
            coreFacades.globalVarFacade.addUsageReference(
                GlobalVarUsageReference(
                    globalVarName = varName,
                    usageRefType = GlobalVarUsageReference.UsageRefType.AppTemplateEnv,
                    usageRefObject = GlobalVarUsageReference.AppTemplateEnvUsageRefObject(
                        appTemplateName = afterConfig.appTemplateName,
                        envName = envName
                    )
                )
            )
        }
    }

    override fun delete(appTemplate: AppTemplate, appTemplateConfigSn: String) {
        val config = find(appTemplate.name)
        config?.let {
            (it.configuration as EnvConfiguration).envs.forEach { envTemplate ->
                envTemplate.variableGroups?.filter { it.type == VariableGroup.Type.GLOBAL }?.forEach { variableGroup ->
                    coreFacades.globalVarFacade.deleteUsageReference(
                        GlobalVarUsageReference(
                            globalVarName = variableGroup.name,
                            usageRefType = GlobalVarUsageReference.UsageRefType.AppTemplateEnv,
                            usageRefObject = GlobalVarUsageReference.AppTemplateEnvUsageRefObject(
                                appTemplateName = appTemplate.name,
                                envName = envTemplate.name
                            )
                        )
                    )
                }
            }
        }
        super.delete(appTemplate, appTemplateConfigSn)
        appTemplateConfigReferenceService.deleteAll(appTemplate.name, appTemplateConfigSn)
    }

    override fun convertToModel(appTemplateConfigPO: AppTemplateConfigPO): AppTemplateConfig {
        val dto = JacksonUtil.jacksonObjectMapper().readValue<EnvConfigDTO>(appTemplateConfigPO.configuration!!)
        val configuration = when (dto) {
            is EnvConfigV1DTO -> EnvConfiguration(envs = dto.fixModel().envTemplates)
            else -> TODO()
        }

        val globalVarNames = configuration.envs.filter { it.variableGroups != null }.flatMap {
            it.variableGroups!!.filter { it.type == VariableGroup.Type.GLOBAL }.map { it.name }
        }
        val templateVarNames = configuration.envs.filter { it.variableGroups != null }.flatMap {
            it.variableGroups!!.filter { it.type == VariableGroup.Type.TEMPLATE }.map { it.name }
        }
        if (globalVarNames.isNotEmpty()) {
            val globalVars = coreFacades.globalVarFacade.findList(globalVarNames)
            configuration.envs.forEach { envTemplate ->
                envTemplate.variableGroups?.filter { it.type == VariableGroup.Type.GLOBAL }?.forEach { variableGroup ->
                    variableGroup.displayName = globalVars.first { it.name == variableGroup.name }.displayName ?: ""
                }
            }
        }
        if (templateVarNames.isNotEmpty()) {
            val templateVarNameMap =
                appTemplateConfigVariableGroupStorageAdaptor.find(appTemplateConfigPO.appTemplateName!!)?.let {
                    (it.configuration as VariableGroupConfiguration).profileMap.mapValues { it.value.displayName }
                } ?: emptyMap()
            configuration.envs.forEach { envTemplate ->
                envTemplate.variableGroups?.filter { it.type == VariableGroup.Type.TEMPLATE }
                    ?.forEach { variableGroup ->
                        variableGroup.displayName = templateVarNameMap[variableGroup.name] ?: ""
                    }
            }
        }
        return appTemplateConfigPO.toModel(configuration)
    }

    override fun convertConfiguration(appTemplateName: String, configuration: AbstractConfigurationUpsertRequest): Any {
        val config = configuration as EnvConfigurationUpsertRequest
        return EnvConfigV1DTO(envTemplates = config.envs)
    }

    override fun configType(): AppTemplateConfig.TypeEnum = AppTemplateConfig.TypeEnum.Env

    override fun logAudit(appTemplate: AppTemplate) {
        auditLogService.commonLog(OrgEventType.APP_TEMPLATE_MODIFY_ENV_AUDIT, appTemplate.name, appTemplate.displayName)
    }

    @JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "version",
        visible = true
    )
    @JsonSubTypes(
        JsonSubTypes.Type(value = EnvConfigV1DTO::class, name = "v1"),
    )
    interface EnvConfigDTO {
        val version: String
    }

    data class EnvConfigV1DTO(
        override val version: String = "v1",
        val envTemplates: List<EnvTemplate>,
    ) : EnvConfigDTO {
        fun fixModel(): EnvConfigV1DTO {
            envTemplates.forEach { it.fixModel() }
            return this
        }
    }
}