package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestWorkflowBizService
import com.alibabacloud.devops.appstack.apps.biz.service.DeployFlowBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Placeholder
import com.alibabacloud.devops.appstack.libs.model.vo.AppVO
import io.swagger.v3.oas.annotations.Hidden
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-05-26 11:57
 * @version: DeployFlowBizApi, v0.1
 **/
@Hidden
@Tag(name = "DeployFlowConfig", description = "flow组件配置用到的接口")
@RestController
@RequestMapping("/api/v1/flow/config")
open class DeployFlowConfigBizApi {

    @Autowired
    lateinit var deployFlowBizService: DeployFlowBizService

    @Autowired
    lateinit var changeRequestWorkflowBizService: ChangeRequestWorkflowBizService

    @Operation(summary = "查应用列表，最多查500个")
    @GetMapping("/findAppList")
    fun create(): FlowResponse<List<AppVO>> {
        return FlowResponse.success(deployFlowBizService.findAppList())
    }

    @Operation(summary = "查应用下环境列表")
    @GetMapping("/findEnvListByAppName")
    fun findFlowChangeOrder(
        @RequestParam(value = "appName", required = false) appName: String?,
    ): FlowResponse<List<Env>> {
        return FlowResponse.success(deployFlowBizService.findEnvListByAppName(appName))
    }

    @Operation(summary = "查询占位符")
    @GetMapping("/findPlaceholder")
    fun findPlaceholder(
        @RequestParam("appName") appName: String,
        @RequestParam("type") type: String = DeployFlowBizService.IMAGE_TYPE,
        @RequestParam("envName") envName: String? = null
    ): FlowResponse<List<Placeholder>> {
        return FlowResponse.success(deployFlowBizService.findPlaceholder(appName, type, envName))
    }

    @Operation(summary = "查询研发流程")
    @GetMapping("/releaseWorkflows")
    fun listReleaseWorkflow(@RequestParam("appName") appName: String): FlowResponse<List<FlowSourceReleaseWorkflow>> {
        val list = changeRequestWorkflowBizService.findAll(appName)
        return FlowResponse(true, list.map { workflow->
            FlowSourceReleaseWorkflow(
                sn = workflow.sn!!,
                name = workflow.name!!,
                stages = workflow.releaseStages.map { stage ->
                    FlowSourceReleaseWorkflow.FlowSourceReleaseStage(
                        sn = stage.sn!!,
                        name = stage.name,
                        pipelineId = stage.pipeline?.engineSn,
                        flowYaml = stage.pipeline?.getPipelineYaml()
                    )
                }
            )
        })
    }

    class FlowResponse<T>(
        var successful: Boolean,
        var `object`: T?
    ) {
        companion object {
            fun <T> success(data: T?): FlowResponse<T> {
                return FlowResponse(true, data)
            }
        }
    }

    data class FlowSourceReleaseWorkflow(
        val sn: String,
        val name: String,
        val stages: List<FlowSourceReleaseStage>
    ) {
        data class FlowSourceReleaseStage(
            val sn: String,
            val name: String,
            val pipelineId: String? = null,
            val flowYaml: String? = null
        )
    }
}