package com.alibabacloud.devops.appstack.apps.biz.model

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonRawValue
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import java.io.IOException
import java.util.*



/**
 * <AUTHOR> <EMAIL>
 * @version : OrgModels, v0.1
 * @date : 2021-12-03 11:56
 **/
data class OrgEventRequest(
    val event: String,
    val appId: String? = null,
    val resourceId: String,
    val resourceType: String,
    @JsonRawValue
    @JsonDeserialize(using = RawJsonDeserializer::class)
    val data: String,
)

class RawJsonDeserializer : JsonDeserializer<String?>() {
    @Throws(IOException::class, JsonProcessingException::class)
    override fun deserialize(jp: JsonParser, ctxt: DeserializationContext?): String {
        val mapper = jp.codec as ObjectMapper
        val node: JsonNode = mapper.readTree(jp)
        return mapper.writeValueAsString(node)
    }
}

data class OrganizationEventData(
    val organization: Organization,
    val user: OrgUser? = null,
)

data class OrganizationMemberCreateEventData(
    val member: List<OrgMember> = emptyList(),
    val organization: Organization,
    val type: String? = null,
)

data class OrganizationMemberUpdateEventData(
    val member: List<OrgMember> = emptyList(),
    val organization: Organization,
    val user: OrgUser? = null,
)

data class OrganizationMemberRemoveEventData(
    val member: OrgMember? = null,
    val organization: Organization,
    val user: OrgUser? = null,
)

data class Organization(
    @JsonAlias("_id", "Id", "ID")
    val id: String,
    val name: String? = null,
    val creator: OrgUser? = null,
)

data class OrgUser(
    @JsonAlias("_id", "Id", "ID")
    val id: String,
    val avatarUrl: String? = null,
    val title: String? = null,
    val email: String? = null,
    val lang: String? = null,
    val name: String? = null,
    val region: String? = null,
    val isRobot: Boolean? = false,
    val phone: String? = null,
    val phoneForLogin: String? = null,
    val countryCode: String? = null,
    val latestActived: Date? = null,
)

data class OrgMember(
    @JsonAlias("_id", "Id", "ID")
    val id: String,
    val name: String? = null,
    val userName: String? = null,
    val memberName: String? = null,
    val avatarUrl: String? = null,
    @JsonAlias("_userId")
    val userId: String? = null,
    @JsonAlias("_memberId")
    val memberId: String? = null,
    @JsonAlias("_roleId")
    val roleId: String? = null,
)

enum class OrgEventType(val value: String) {
    ORG_CREATE("organization.create"),
    ORG_REMOVE("organization.remove"),
    ORG_MEMBER_CREATE("organization.member.create"),
    ORG_MEMBER_UPDATE("organization.member.update"),
    ORG_MEMBER_REMOVE("organization.member.remove"),
    ;
    override fun toString(): String {
        return value
    }
}

enum class OrganizationRoleType {
    project,
    organization
}

data class OrganizationRole(
    @JsonAlias("_id")
    var id: String,
    var name: String,
    @JsonAlias("_organizationId")
    var orgId: String,
    @JsonAlias("_creatorId")
    var creator: String? = null,
    var type: OrganizationRoleType? = OrganizationRoleType.organization,
    var permissions: List<String>,
    var level: Int,
    var isDefault: Boolean,
    var created: Date? = null,
    var updated: Date? = null,
)


@JsonIgnoreProperties(ignoreUnknown = true)
data class OrganizationUserRole(
    @JsonAlias("_id")
    var id: String,
    @JsonAlias("_userId")
    var userId: String,

    var role: Int,
) {
    companion object {
        /***管理员**/
        const val ROLE_MANAGER: Int = 1

        /***拥有者**/
        const val ROLE_OWNER: Int = 2
    }
}


data class OrganizationRoleResponse<T>(
    val result: List<T>,
    var nextPageToken: String? = null,
)

data class OrganizationMemberResponse<T>(
    val result: List<T>,
    val nextPageToken: String? = null,
    val totalSize: Long
)

data class OrganizationMember(
    @JsonAlias("_id")
    val id: String,
    @JsonAlias("_userId")
    val userId: String,
    @JsonAlias("role")
    val roleLevel: Int,
    @JsonAlias("_roleId")
    val roleId: String,
    val name: String,
    val isDisabled: Boolean,
    val isVirtual: Boolean,
)