package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.EnvBizService
import com.alibabacloud.devops.appstack.apps.biz.service.EnvLogBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeDeploymentStrategyV2
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.event.ActionLog
import com.alibabacloud.devops.appstack.libs.model.request.CreateEnvRequest
import com.alibabacloud.devops.appstack.libs.model.request.SearchOpLogRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateEnvRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeableEnvRecordVO
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeableEnvVO
import com.alibabacloud.devops.appstack.libs.model.vo.EnvLogVO
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @date 2021-12-31
 */
@Tag(name = "Env", description = "环境相关 API")
@RestController
@RequestMapping("/api/v1")
open class EnvBizApi {

    @Autowired
    lateinit var envBizService: EnvBizService

    @Autowired
    lateinit var envLogBizService: EnvLogBizService

    @Operation(summary = "创建环境")
    @PostMapping("/apps/{appName}/envs")
    fun createEnv(
        @PathVariable("appName") appName: String,
        @RequestBody req: CreateEnvRequest
    ): Response<Env> {
        val env = envBizService.create(appName, req)
        return Response.success(env)
    }

    @Operation(summary = "修改环境")
    @PutMapping("/apps/{appName}/envs/{envName}")
    fun updateEnv(
        @PathVariable("appName") appName: String,
        @PathVariable("envName") envName: String,
        @RequestBody req: UpdateEnvRequest,
    ): Response<Env> {
        var env = envBizService.find(appName, envName)
        checkExists(env) { ErrorCode.AS_ENV_NOT_FOUND }
        env = envBizService.update(env, req)
        return Response.success(env)
    }

    @Operation(summary = "锁定/解锁环境")
    @PutMapping("/apps/{appName}/envs/{envName}:lock")
    fun lock(
        @PathVariable("appName") appName: String,
        @PathVariable("envName") envName: String,
        @RequestParam(name = "isLock", required = false, defaultValue = "true") isLock: Boolean
    ): Response<Env> {
        var env = envBizService.find(appName, envName)
        checkExists(env) { ErrorCode.AS_ENV_NOT_FOUND }
        env = envBizService.lock(env, isLock)
        return Response.success(env)
    }

    @Operation(summary = "删除环境")
    @DeleteMapping("/apps/{appName}/envs/{envName}")
    fun deleteEnv(
        @PathVariable("appName") appName: String,
        @PathVariable("envName") envName: String,
        @RequestParam(name = "deleteDeployGroup", required = false, defaultValue = "false") deleteDeployGroup: Boolean
    ): Response<Env> {
        var env = envBizService.find(appName, envName)
        checkExists(env) { ErrorCode.AS_ENV_NOT_FOUND }
        env = envBizService.delete(env, deleteDeployGroup)
        return Response.success(env)
    }

    @Operation(summary = "查询环境")
    @GetMapping("/apps/{appName}/envs/{envName}")
    fun findEnv(
        @PathVariable("appName") appName: String,
        @PathVariable("envName") envName: String
    ): Response<Env> {
        return Response.success(envBizService.find(appName, envName))
    }

    @Operation(summary = "查询环境列表")
    @GetMapping("/apps/{appName}/envs")
    fun findAllEnv(
        @PathVariable("appName") appName: String
    ): Response<List<Env>> {
        return Response.success(envBizService.findAll(appName))
    }

    @Operation(summary = "查询操作日志分页列表")
    @GetMapping("/apps/{appName}/envs/{envName}/actionLogs")
    fun findPaginatedActionLog(
        @PathVariable("appName") appName: String,
        @PathVariable("envName") envName: String,
        @RequestParam(name = "pageSize", required = false, defaultValue = "10") pageSize: Long,
        @RequestParam(name = "current", required = false, defaultValue = "1") current: Long
    ): Response<Pagination<ActionLog>> {
        return Response.success(envBizService.findPaginatedActionLog(appName, envName, pageSize, current))
    }

    @Operation(summary = "查询环境最近一次工单")
    @GetMapping("/apps/{appName}/changeableEnvs/{envName}")
    fun findChangeableEnv(
        @PathVariable("appName") appName: String,
        @PathVariable("envName") envName: String
    ): Response<ChangeableEnvVO> {
        val changeableEnv = envBizService.findChangeableEnv(appName, envName)
        checkExists(changeableEnv) { ErrorCode.AS_ENV_NOT_FOUND }
        return Response.success(changeableEnv)
    }

    @GetMapping("/apps/{appName}/changeableEnvs")
    fun findAllChangeableEnvs(@PathVariable appName: String, ): Response<List<ChangeableEnvRecordVO>> {
        return Response.success(envBizService.findAllChangeableEnvs(appName))
    }

    @Operation(summary = "查询环境操作记录")
    @PostMapping("/apps/{appName}/envs/{envName}/logs")
    fun findEnvLog(
        @PathVariable("appName") appName: String,
        @PathVariable("envName") envName: String,
        @RequestBody searchOpLogRequest: SearchOpLogRequest,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<EnvLogVO>> {
        searchOpLogRequest.targetSnList = listOf("$appName::$envName")
        return Response.success(
            envLogBizService.findPaginated(
                searchOpLogRequest,
                current,
                pageSize
            )
        )
    }

    @Operation(summary = "查询环境部署策略表单信息，只给前端渲染用")
    @GetMapping("/apps/{appName}/envs/{envName}/strategyForm")
    fun findEnvStrategyForm(
        @PathVariable("appName") appName: String,
        @PathVariable("envName") envName: String,
    ): Response<EnvStrategyForm> {
        return Response.success(
            envBizService.findEnvStrategyForm(appName, envName)
        )
    }

}

@JsonIgnoreProperties(ignoreUnknown = true)
data class EnvStrategyForm(
    val replicasManagement: List<Env.Spec.ReplicasManagement>,
    val locator: List<LocatorVO>,
    val featureGates: FeatureGates? = null
) {
    @JsonIgnoreProperties(ignoreUnknown = true)
    class LocatorVO(
        val targetReplicas: Int?,
        val targetReplicasEditable: Boolean? = true,
        val batchType: List<AdeDeploymentStrategyV2.BatchStep.Type>,
        val locator: String? = null,
    )

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class FeatureGates(
        val agentAvailable: Boolean
    )
}