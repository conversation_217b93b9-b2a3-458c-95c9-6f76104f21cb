package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.apps.biz.service.AppBizService
import com.alibabacloud.devops.appstack.apps.biz.service.UserService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.constant.Header
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.PageListVO
import com.alibabacloud.devops.appstack.libs.model.`do`.app.App
import com.alibabacloud.devops.appstack.libs.model.request.ng.AddMembersRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.ApplicationQuery
import com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateMemberRequest
import com.alibabacloud.devops.appstack.libs.model.request.pop.UpdateAppRequest
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import com.alibabacloud.devops.appstack.libs.model.vo.ng.PaginationVO
import com.alibabacloud.devops.appstack.libs.model.vo.ng.toVO
import com.alibabacloud.devops.appstack.libs.model.vo.pop.ApplicationVO
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.api.annotations.ParameterObject
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.web.bind.annotation.*
import javax.servlet.http.HttpServletRequest

@Tag(name = "应用", description = "应用领域 OpenAPI")
@RestController
@RequestMapping("/api/v2")
@ConditionalOnProperty(name = ["devops.iam.environment"], havingValue = "aliyun", matchIfMissing = true)
open class AppPOPApi {

    @Autowired
    lateinit var appBizService: AppBizService

    @Autowired
    lateinit var userService: UserService

    @Operation(summary = "查找应用详情")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已按名称查找到应用详情"),
            ApiResponse(responseCode = "404", description = "未查找到应用详情"),
            ApiResponse(responseCode = "500", description = "服务端内部异常")
        ]
    )
    @GetMapping("/apps/{appName}")
    fun findApplication(
        @Schema(description = "应用名", example = "my-web-service", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("appName") name: String,
        request: HttpServletRequest
    ): ApplicationVO {
        val app = appBizService.find(name = name) ?: throw BizException(errorCode = ErrorCode.AS_APP_NOT_FOUND)
        return app.toVO(userService, request.getHeader(Header.POP_REQUEST_ID), appBizService)
    }

    @Operation(summary = "分页查找应用详情")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "分页查找完成（包括未查找到对象的情况）"),
            ApiResponse(responseCode = "500", description = "服务端内部异常")
        ]
    )
    @GetMapping("/apps:search")
    fun listApplications(@ParameterObject query: ApplicationQuery, request: HttpServletRequest): PageListVO<ApplicationVO> {
        val apps = appBizService.list(query)
        val requestId = request.getHeader(Header.POP_REQUEST_ID)
        return PageListVO(nextToken = apps.nextToken, data = apps.data.map { it.toVO(userService, null, appBizService) }, requestId = requestId)
    }

    @Operation(summary = "更新应用", operationId = "UpdateApplication")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "更新成功"),
            ApiResponse(responseCode = "404", description = "未查找到指定的应用"),
            ApiResponse(responseCode = "500", description = "服务端内部异常")
        ]
    )
    @PutMapping("/apps/{appName}")
    fun updateApplication(
        @Schema(description = "应用名", example = "my-web-service", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("appName") name: String,
        @RequestBody req: UpdateAppRequest,
        request: HttpServletRequest
    ): ApplicationVO {
        // 预校验一次
        appBizService.find(name) ?: throw BizException(ErrorCode.AS_APP_NOT_FOUND)

        if (req.ownerAccountId != null) {
            appBizService.transferOwner(
                name,
                ResourcePlayerRequest.builder()
                    .id(userService.getUserIdByOpenId(req.ownerAccountId!!))
                    .type(SubjectType.User)
                    .build()
            )
        }
        return appBizService.find(name)?.toVO(userService, request.getHeader(Header.POP_REQUEST_ID), appBizService) ?: throw BizException(ErrorCode.AS_APP_NOT_FOUND)
    }

    @Operation(summary = "查找应用成员列表", operationId = "ListApplicationMembers")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "分页查找完成（包括未查找到对象的情况）"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @GetMapping("/apps/{appName}/members")
    fun listAppMembers(
        @Schema(description = "应用名", example = "my-demo-app", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("appName") appName: String,
        request: HttpServletRequest
    ): PaginationVO<MemberVO> {
        val result = appBizService.listMembers(appName = appName, current = 1, pageSize = 100)
        // 更新用户id为accountId
        result.records.forEach {
            if (it.type == SubjectType.User) {
                it.id = userService.getAliyunOpenIdByUserId(it.id)
            }
        }
        return result.toVO(request.getHeader(Header.POP_REQUEST_ID))
    }

    @Operation(summary = "添加应用成员", operationId = "CreateAppMembers")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "添加应用成员成功"),
            ApiResponse(responseCode = "403", description = "没权限设置拥有者"),
            ApiResponse(responseCode = "404", description = "角色未找到"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PostMapping("/apps/{appName}/members")
    fun createAppMembers(
        @Schema(description = "应用名", example = "my-demo-app", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("appName") appName: String,
        @RequestBody addMembersRequest: AddMembersRequest,
        request: HttpServletRequest
    ): String {
        // 从 accountId 转成用户id
        addMembersRequest.playerList.forEach {
            if (it.type == SubjectType.User) {
                it.id = userService.getUserIdByOpenId(it.id)
            }
        }
        addMembersRequest.roleNames.forEach {
            appBizService.addRoleMember(
                appName = appName,
                roleName = it,
                playerList = addMembersRequest.playerList,
            )
        }
        return request.getHeader(Header.POP_REQUEST_ID)
    }

    @Operation(summary = "删除应用成员", operationId = "DeleteAppMember")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "删除应用成员成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @DeleteMapping("/apps/{appName}/members")
    fun deleteAppMember(
        @Schema(description = "应用名", example = "my-demo-app", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("appName") appName: String,
        @Schema(description = "成员类型", example = "User", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @RequestParam subjectType: SubjectType,
        @Schema(description = "成员id", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @RequestParam subjectId: String,
        request: HttpServletRequest
    ): String {
        var targetSubjectId = subjectId
        // 从 accountId 转成用户id
        if (subjectType == SubjectType.User) {
            targetSubjectId = userService.getUserIdByOpenId(subjectId)
        }
        appBizService.updateMemberRole(
            appName = appName,
            subjectType = subjectType,
            subjectId = targetSubjectId,
            roleNameList = emptyList()
        )
        return request.getHeader(Header.POP_REQUEST_ID)
    }

    @Operation(summary = "更新应用成员", operationId = "UpdateAppMember")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "更新应用成员角色成功"),
            ApiResponse(responseCode = "404", description = "应用角色未找到"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PutMapping("/apps/{appName}/members")
    fun updateAppMemberRole(
        @Schema(description = "应用名", example = "my-demo-app", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("appName") appName: String,
        @RequestBody updateMemberRequest: UpdateMemberRequest,
        request: HttpServletRequest
    ): String {
        // 从 accountId 转成用户id
        if (updateMemberRequest.player.type == SubjectType.User) {
            updateMemberRequest.player.id = userService.getUserIdByOpenId(updateMemberRequest.player.id)
        }
        appBizService.updateMemberRole(
            appName = appName,
            subjectType = updateMemberRequest.player.type,
            subjectId =  updateMemberRequest.player.id,
            roleNameList = updateMemberRequest.roleNames
        )
        return request.getHeader(Header.POP_REQUEST_ID)
    }
}

fun App.toVO(userService: UserService, requestId: String?, appBizService: AppBizService?): ApplicationVO {
    val appTemplate = appBizService?.let { appBizService.findTemplate(this.name) }
    return ApplicationVO(
        name = this.name,
        gmtCreate = this.gmtCreate!!,
        creatorAccountId = userService.getAliyunOpenIdByUserId(this.creatorId!!),
        description = this.description,
        requestId = requestId,
        appTemplateName = appTemplate?.name,
        appTemplateDisplayName = appTemplate?.displayName
    )
}