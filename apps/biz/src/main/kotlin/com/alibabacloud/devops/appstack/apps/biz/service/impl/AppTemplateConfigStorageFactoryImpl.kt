package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateConfigStorageFactory
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateConfigStorageAdaptor
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AbstractConfiguration
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import org.springframework.beans.factory.BeanFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR> <EMAIL>
 * @version : AppTemplateConfigFactory, v0.1
 * @date : 2023-10-18 14:24
 **/
@Service
open class AppTemplateConfigStorageFactoryImpl : AppTemplateConfigStorageFactory {

    @Autowired
    lateinit var beanFactory: BeanFactory
    override fun build(
        type: AppTemplateConfig.TypeEnum,
    ): AppTemplateConfigStorageAdaptor<out AbstractConfiguration> {
        val beanName = "appTemplateConfig${type.name}StorageAdaptor"
        return beanFactory.getBean(beanName) as AppTemplateConfigStorageAdaptor<out AbstractConfiguration>
    }
}