package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.App
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.app.FunctionPoint
import com.alibabacloud.devops.appstack.libs.model.request.AppRequest
import com.alibabacloud.devops.appstack.libs.model.request.SearchAppRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.AddMembersRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.ApplicationQuery
import com.alibabacloud.devops.appstack.libs.model.request.ng.MemberOperateResult
import com.alibabacloud.devops.appstack.libs.model.vo.*
import com.alibabacloud.devops.appstack.libs.model.vo.ng.Application
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.ResourcePlayer
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest

/**
 * @author: <EMAIL>
 * @date: 2021-12-16 13:58
 * @version: AppBizService, v0.1
 **/
interface AppBizService {
    fun find(name: String): AppBaseInfoVO?
    fun create(appReq: AppRequest): App
    fun update(name: String, appReq: UpdateAppRequest)
    fun delete(name: String)

    fun searchPaginated(searchAppRequest: SearchAppRequest, current: Long, pageSize: Long): Pagination<AppSearchVO>
    fun list(query: ApplicationQuery): PageList<AppVO>
    fun groupCount(searchAppRequest: SearchAppRequest): AppCountVO

    fun findAllMember(appName: String): Map<ResourcePlayer, List<Role>>

    fun listMembers(appName: String, current: Long, pageSize: Long): Pagination<MemberVO>
    fun addRoleMember(appName: String, roleName: String, playerList: List<ResourcePlayerRequest>)
    fun updateRoleMember(appName: String, roleName: String, playerList: List<ResourcePlayerRequest>)
    fun updateMemberRole(appName: String, subjectId: String, subjectType: SubjectType, roleNameList: List<String>)
    fun transferOwner(appName: String, player: ResourcePlayerRequest)
    fun favour(name: String)
    fun disfavour(name: String)
    fun findFunctionPoints(name: String): List<FunctionPoint>
    fun findTemplate(name: String): AppTemplate?
    fun bindTemplate(appName: String, templateName: String)
    fun unbindTemplate(appName: String)
    fun listAdminedAppsPaginated(search: String, current: Long, pageSize: Long): Pagination<Application>

    fun listRelatedSystemsPaginated(name: String, search: String, current: Long, pageSize: Long): Pagination<Application>

    fun batchAddMembers(appNames: List<String>, addMembersRequest: AddMembersRequest): List<MemberOperateResult>

    fun batchDeleteMembers(appNames: List<String>, players: List<ResourcePlayerRequest>) : List<MemberOperateResult>

    fun deleteMember(appName: String, playerList: List<ResourcePlayerRequest>)
}