package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.AuditItemBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.model.`do`.audit.AuditItem
import com.alibabacloud.devops.appstack.libs.model.request.audit.splc.AuditCallbackRequest
import com.alibabacloud.devops.appstack.libs.model.request.audit.splc.AuditSubmitRequest
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2022-11-29 10:57
 * @version: AuditItemBizServiceImpl, v0.1
 **/
@Service
@Slf4k
class AuditItemBizServiceImpl : AuditItemBizService {

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    override fun submit(req: AuditSubmitRequest): AuditItem? {
        return workflowControllerFacade.auditItemFacade.submit(req)
    }

    override fun find(type: AuditItem.Type, refType: AuditItem.RefType, refSn: String): AuditItem? {
        return workflowControllerFacade.auditItemFacade.find(type, refType, refSn)
    }

    override fun callback(req: AuditCallbackRequest) {
        return workflowControllerFacade.auditItemFacade.callback(req)
    }

    override fun findAll(refType: AuditItem.RefType, refSn: String): List<AuditItem> {
        return workflowControllerFacade.auditItemFacade.findAll(refType, refSn)
    }

    override fun findByRefs(refType: AuditItem.RefType, refSns: List<String>): Map<String, List<AuditItem>> {
        return workflowControllerFacade.auditItemFacade.findByRefs(refType, refSns)
    }
}