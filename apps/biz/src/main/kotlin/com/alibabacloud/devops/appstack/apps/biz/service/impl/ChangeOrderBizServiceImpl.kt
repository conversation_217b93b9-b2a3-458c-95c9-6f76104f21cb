package com.alibabacloud.devops.appstack.apps.biz.service.impl


import com.alibabacloud.devops.appstack.apps.biz.annotation.ApiMetricCounter
import com.alibabacloud.devops.appstack.apps.biz.model.DeployFlow
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderBizService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderInputBizService
import com.alibabacloud.devops.appstack.apps.biz.service.DeployFlowService
import com.alibabacloud.devops.appstack.apps.biz.service.EnvLogBizService
import com.alibabacloud.devops.appstack.libs.change.controller.spring.boot.starter.service.ChangeControllerFacades
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.*
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeTaskForm
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.dto.changecontroller.TaskStrategyDTO
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.org.event.EnvOrderAudit
import com.alibabacloud.devops.appstack.libs.model.org.event.Order
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateChangeOrderRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateDeployInputRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.SearchChangeOrderRequest
import com.alibabacloud.devops.appstack.libs.model.vo.*
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.aop.framework.AopContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.text.SimpleDateFormat
import java.util.*

/**
 * <AUTHOR>
 * @date 2022-02-22
 */
@Slf4k
@Service
open class ChangeOrderBizServiceImpl : ChangeOrderBizService {

    @Autowired
    lateinit var changeControllerFacades: ChangeControllerFacades

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var envLogBizService: EnvLogBizService

    @Autowired
    lateinit var deployFlowService: DeployFlowService

    @Autowired
    lateinit var changeOrderInputBizService: ChangeOrderInputBizService

    @Autowired
    lateinit var auditLogService: AuditLogService

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    override fun find(sn: String): ChangeOrderVO? {
        return changeControllerFacades.changeOrderFacade.find(sn)
    }

    override fun dryRun(createChangeOrderRequest: CreateChangeOrderRequest): ChangeOrder {
        return changeControllerFacades.changeOrderFacade.dryRun(createChangeOrderRequest)
    }

    @ApiMetricCounter(metricName = "submit_change_order")
    override fun create(createChangeOrderRequest: CreateChangeOrderRequest): ChangeOrderVO {
        val changeOrderInput = changeOrderInputBizService.find(createChangeOrderRequest.changeOrderInputSn)
        checkExists(changeOrderInput) {
            // NOTE jiuya.wb 创建工单的前序一定是 input 存在，若不存在则是严重的问题
            logger.error("[ChangeOrderBizServiceImpl] ChangeOrderInput not found: " +
                "request=${jacksonObjectMapper().writeValueAsString(createChangeOrderRequest)}")
            ErrorCode.AS_CHANGE_ORDER_INPUT_NOT_EXISTED
        }
        val envList = coreFacades.envFacade.findAll(changeOrderInput.appName)
            .filter { changeOrderInput.envValues.keys.contains(it.name) }
        val dynamicProxy = AopContext.currentProxy() as ChangeOrderBizServiceImpl
        val changeOrder = when (changeOrderInput.type) {
            ChangeOrder.Type.Deploy -> dynamicProxy.createDeployOrder(envList, createChangeOrderRequest)
            ChangeOrder.Type.Rollback -> dynamicProxy.createRollbackOrder(envList, createChangeOrderRequest)
            ChangeOrder.Type.Scale -> dynamicProxy.createScaleOrder(envList, createChangeOrderRequest)
            ChangeOrder.Type.Destroy -> dynamicProxy.createDestroyOrder(envList, createChangeOrderRequest)
        }
        envLogBizService.recordCreateChangeOrderAction(changeOrder)
        createChangeOrderRequest.flowInsId?.let {
            // 这里统一收口来自Flow的创建，可以包含手动跳转部署单页面的情况
            deployFlowService.create(
                DeployFlow(
                    appName = changeOrder.appName,
                    envName = changeOrder.jobs[0].envName,
                    objectType = DeployFlow.ObjectType.FLOW_APP_STACK,
                    objectId = it,
                    changeOrderSn = changeOrder.sn!!
                )
            )
        }
        return change(
            changeOrder.sn!!, ChangeOrderAction(
                action = ChangeAction.Action.START,
                sn = changeOrder.sn!!,
            )
        )
    }

    override fun check(createChangeOrderRequest: CreateChangeOrderRequest): List<InvalidChangeItem> {
        return changeControllerFacades.changeOrderFacade.check(createChangeOrderRequest)
    }

    /**
     * 通过openApi部署
     */
    override fun create(appName: String, request: CreateChangeOrderReq): ChangeOrderVO {
        val createDeployInputRequest = CreateDeployInputRequest(
            name = request.changeOrderName,
            appName = appName,
            type = request.type,
            version = SimpleDateFormat("yyyyMMddHHmmss-SSS").format(Date()),
            description = request.description ?: "通过OpenApi部署",
            orchestrationSha = request.orchestrationRevisionSha,
            envValues = request.envs.mapValues { entry ->
                entry.value.values.toMutableMap()
            }
        )
        val changeOrderForm = changeOrderInputBizService.create(createDeployInputRequest)
        changeOrderForm.taskForms.values.flatten().forEach { taskForm ->
            if (taskForm.behavior == TaskFormBehavior.Add && (taskForm as AdeTaskForm).kind == TaskContext.Kind.Deployment.name) {
                // 增加Deployment需要手动创建发布单
                throw BizException(ErrorCode.AS_ORC_COMP_NEW_DEPLOYMENT)
            }
        }
        val taskStrategyList = request.envs.keys.flatMap { envName ->
            val latestChangeOrder = changeControllerFacades.changeableEnvFacade.findLatestChangeOrder(appName, envName)
            val job = latestChangeOrder?.jobs?.firstOrNull { it.envName == envName }
            checkExists(job) {
                ErrorCode.AS_CHANGE_ORDER_JOB_FIRST_DEPLOY
            }
            job.stages.flatMap { it.tasks }.map {
                // 默认取上一次部署策略
                TaskStrategyDTO(
                    envName,
                    it.locator ?: "",
                    strategies = jacksonObjectMapper().readValue(it.strategyContent)
                )
            }
        }
        val createChangeOrderRequest = CreateChangeOrderRequest(
            changeOrderInputSn = changeOrderForm.changeOrderInputSn,
            taskStrategies = taskStrategyList,
            sourceType = ChangeOrder.SourceType.OPEN_API.name,
        )
        return create(createChangeOrderRequest)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ENV_DEPLOY),
        ]
    )
    open fun createDeployOrder(envs: List<Env>, request: CreateChangeOrderRequest): ChangeOrder {
        val changeOrder = changeControllerFacades.changeOrderFacade.create(request)
        envAudit(OrgEventType.ENV_DEPLOY, envs, changeOrder)
        return changeOrder
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ENV_ROLLBACK),
        ]
    )
    open fun createRollbackOrder(envs: List<Env>, request: CreateChangeOrderRequest): ChangeOrder {
        val changeOrder = changeControllerFacades.changeOrderFacade.create(request)
        envAudit(OrgEventType.ENV_ROLLBACK, envs, changeOrder)
        return changeOrder
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ENV_SCALE),
        ]
    )
    open fun createScaleOrder(envs: List<Env>, request: CreateChangeOrderRequest): ChangeOrder {
        val changeOrder = changeControllerFacades.changeOrderFacade.create(request)
        envAudit(OrgEventType.ENV_SCALE, envs, changeOrder)
        return changeOrder
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ENV_DELETE),
        ]
    )
    open fun createDestroyOrder(envs: List<Env>, request: CreateChangeOrderRequest): ChangeOrder {
        return changeControllerFacades.changeOrderFacade.create(request)
    }

    override fun change(sn: String, changeAction: ChangeAction): ChangeOrderVO {
        val changeOrder = find(sn)
        checkExists(changeOrder) { ErrorCode.AS_CHANGE_ORDER_NOT_EXISTED }
        val envNames = changeOrder.jobs.map { it.envName }
        val envList = coreFacades.envFacade.findAll(changeOrder.appName).filter { envNames.contains(it.name) }
        return (AopContext.currentProxy() as ChangeOrderBizServiceImpl).change(envList, changeOrder, changeAction)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ENV_DEPLOY),
            Access(action = Action.ENV_ROLLBACK),
            Access(action = Action.ENV_SCALE),
        ]
    )
    open fun change(envs: List<Env>, changeOrder: ChangeOrderVO, changeAction: ChangeAction): ChangeOrderVO {
        if (changeAction.action == ChangeAction.Action.STOP) {
            envAudit(OrgEventType.ENV_ORDER_TERMINATE, envs, changeOrder)
        }
        return changeControllerFacades.changeOrderFacade.change(changeOrder.sn, changeAction)
    }

    override fun findPaginated(
        appName: String,
        envNames: List<String>,
        jobTypes: List<Job.Type>,
        jobStates: List<Job.State>,
        current: Long,
        pageSize: Long,
    ): Pagination<ChangeOrderListRecord> {
        return changeControllerFacades.changeOrderFacade.findPaginated(
            appName = appName,
            envNames = envNames,
            jobTypes = jobTypes,
            jobStates = jobStates,
            current = current,
            pageSize = pageSize
        )
    }

    override fun searchPaginated(
        searchChangeOrderRequest: SearchChangeOrderRequest,
        current: Long,
        pageSize: Long
    ): Pagination<ChangeOrderListRecord> {
        val orderPage = changeControllerFacades.changeOrderFacade.searchPaginated(
            searchChangeOrderRequest = searchChangeOrderRequest,
            current = current,
            pageSize = pageSize
        )
        val changeOrderSnList = orderPage.records.map { it.sn }
        val deployFlowMap = deployFlowService.listByChangeOrderSns(changeOrderSnList).associateBy { it.changeOrderSn }
        orderPage.records.forEach { record ->
            record.source = deployFlowMap[record.sn]?.source
        }
        return orderPage
    }

    override fun findTaskExecutionLog(changeOrderSn: String, jobSn: String, stageSn: String, taskSn: String): String {
        return changeControllerFacades.taskFacade.fetchExecutionLog(taskSn)
    }

    override fun findPaginatedVersion(
        appName: String,
        envNames: List<String>,
        creators: List<String>,
        current: Long,
        pageSize: Long,
    ): Pagination<ChangeOrderVersionRecord> {
        return changeControllerFacades.changeOrderFacade.findPaginatedVersion(
            appName = appName,
            envNames = envNames,
            creators = creators,
            current = current,
            pageSize = pageSize,
        )
    }

    override fun findPaginatedJobLog(sns: List<String>, current: Long, pageSize: Long): Pagination<JobLog> {
        return changeControllerFacades.jobFacade.findPaginatedLog(sns, current, pageSize)
    }

    override fun findJobDiff(sn: String): Pair<JobRecord, JobRecord> {
        return changeControllerFacades.jobFacade.diff(sn)
    }

    override fun findRecordByVersion(appName: String, version: String): ChangeOrderVersionRecord? {
        return changeControllerFacades.changeOrderFacade.findRecordByVersion(appName, version)
    }

    override fun findBySns(appName: String, sns: List<String>): List<ChangeOrderRecord> {
        return changeControllerFacades.changeOrderFacade.findChangeOrders(appName, sns)
    }

    private fun envAudit(type: OrgEventType, envs: List<Env>, changeOrder: ChangeOrder) {
        val audit = EnvOrderAudit(
            target = AuditTarget(id = changeOrder.appName, name = changeOrder.appName),
            order = Order(name = changeOrder.name, id = changeOrder.sn!!, envNames = envs.map { it.name })
        )
        auditLogService.log(type, audit)
    }

    private fun envAudit(type: OrgEventType, envs: List<Env>, changeOrder: ChangeOrderVO) {
        val audit = EnvOrderAudit(
            target = AuditTarget(id = changeOrder.appName, name = changeOrder.appName),
            order = Order(name = changeOrder.name, id = changeOrder.sn, envNames = envs.map { it.name })
        )
        auditLogService.log(type, audit)
    }

    override fun findEnvOccupyList(
        appName: String,
        envNames: String
    ): List<StageEnvOccupy> {
        val envNames = envNames.split(",")
        if (envNames.isEmpty()) {
            return emptyList()
        }
        val envs = coreFacades.envFacade.findAll(appName)
        return envNames.mapNotNull { envName ->
            val changeOrder = changeControllerFacades.changeOrderFacade.last(appName = appName, envName = envName)
            if (changeOrder?.sourceType != null && changeOrder.sourceSn != null) {
                if (changeOrder.sourceType == ChangeOrder.SourceType.FLOW) {
                    val occupyReleaseStage =
                        workflowControllerFacade.releaseStageFacade.findBriefByProcessEngineSn(changeOrder.sourceSn!!)
                    if (null != occupyReleaseStage) {
                        StageEnvOccupy(
                            sourceType = ChangeOrder.SourceType.WORKFLOW_STAGE,
                            sourceSn = occupyReleaseStage.sn,
                            envName = envName,
                            envDisplayName = envs.firstOrNull { it.name == envName }?.displayName,
                            gmtCreate = changeOrder.gmtCreate,
                            creator = changeOrder.creator,
                            deployType = changeOrder.type,
                            changeOrderSn = changeOrder.sn,
                        )
                    } else {
                        StageEnvOccupy(
                            sourceType = changeOrder.sourceType,
                            sourceSn = changeOrder.sourceSn,
                            envName = envName,
                            envDisplayName = envs.firstOrNull { it.name == envName }?.displayName,
                            gmtCreate = changeOrder.gmtCreate,
                            creator = changeOrder.creator,
                            deployType = changeOrder.type,
                            changeOrderSn = changeOrder.sn,
                        )
                    }
                } else if (changeOrder.sourceType == ChangeOrder.SourceType.CUSTOMIZE) {
                    StageEnvOccupy(
                        sourceType = ChangeOrder.SourceType.CUSTOMIZE,
                        envName = envName,
                        envDisplayName = envs.firstOrNull { it.name == envName }?.displayName,
                        gmtCreate = changeOrder.gmtCreate,
                        creator = changeOrder.creator,
                        deployType = changeOrder.type,
                        changeOrderSn = changeOrder.sn,
                    )
                } else {
                    null
                }
            } else {
                null
            }
        }
    }

    override fun findJobBriefByChangeOrder(changeOrderSn: String): List<JobBriefRecordVO> {
        return changeControllerFacades.changeOrderFacade.findJobBriefByChangeOrder(changeOrderSn)
    }
}