package com.alibabacloud.devops.appstack.apps.biz.service.facade

import com.alibabacloud.devops.appstack.apps.biz.service.client.api.EnterpriseSettingsApi
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.model.request.KubeConfigVO
import com.alibabacloud.devops.appstack.libs.model.request.UpsertAliyunAuthAccountRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpsertKubeConfigRequest
import com.alibabacloud.devops.appstack.libs.model.vo.AliyunAuthAccountVO
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import retrofit2.HttpException

/**
 * @author: <EMAIL>
 * @date: 2022-01-27 11:25
 * @version: EnterpriseSettingsFacade, v0.1
 **/
@Slf4k
@Service
class EnterpriseSettingsFacade {

    @Autowired
    private lateinit var enterpriseSettingsApi: EnterpriseSettingsApi

    fun findAllAliyunAuthAccounts(): List<AliyunAuthAccountVO> {
        val tenantId = AuthUtil.getTenant()
        val userId = AuthUtil.getUserId()
        logger.info("find aliyun auth accounts for: userId=$userId, tenant=$tenantId")
        return try {
            val response = enterpriseSettingsApi.findAllAliyunAuthAccounts(tenantId, userId)
            response.content ?: emptyList()
        } catch (e: Throwable) {
            logger.error("failed to invoke findAllAliyunAuthAccounts(), msg=${e.message}", e)
            emptyList()
        }
    }

    fun createAliyunAuthAccount(request: UpsertAliyunAuthAccountRequest): AliyunAuthAccountVO? {
        val tenantId = AuthUtil.getTenant()
        val userId = AuthUtil.getUserId()
        logger.info("create aliyun auth account for: userId=$userId, tenant=$tenantId")
        return try {
            enterpriseSettingsApi.createAliyunAuthAccount(
                tenantId = tenantId,
                userId = userId,
                upsertAliyunAuthAccountRequest = request
            )
        } catch (e: Throwable) {
            logger.error("failed to invoke createAliynAuthAccount(), msg=${e.message}", e)
            null
        }
    }

    fun upsertAliyunAuthAccount(request: UpsertAliyunAuthAccountRequest): AliyunAuthAccountVO? {
        val tenantId = AuthUtil.getTenant()
        val userId = AuthUtil.getUserId()
        logger.info("upsert aliyun auth account for: userId=$userId, tenant=$tenantId")
        return try {
            enterpriseSettingsApi.upsertAliyunAuthAccount(
                tenantId = tenantId,
                userId = userId,
                upsertAliyunAuthAccountRequest = request
            )
        } catch (e: Throwable) {
            logger.error("failed to invoke upsertAliynAuthAccount(), msg=${e.message}", e)
            null
        }
    }

    fun deleteAliyunAuthAccount(id: Long) {
        val tenantId = AuthUtil.getTenant()
        val userId = AuthUtil.getUserId()
        logger.info("delete aliyun auth account for: userId=$userId, tenant=$tenantId, id=$id")
        try {
            enterpriseSettingsApi.deleteAliyunAuthAccount(
                tenantId = tenantId,
                userId = userId,
                id = id
            )
        } catch (e: Throwable) {
            logger.error("failed to invoke deleteAliynAuthAccount(), msg=${e.message}", e)
            throw BizException(ErrorCode.AS_UNKNOWN)
        }
    }

    fun findAllKubeConfig(): List<KubeConfigVO> {
        val tenantId = AuthUtil.getTenant()
        val userId = AuthUtil.getUserId()
        logger.info("find all kube config for: userId=$userId, tenant=$tenantId")
        return try {
            val response = enterpriseSettingsApi.findAllKubeConfig(
                tenantId = tenantId,
                userId = userId,
            )
            response.content ?: emptyList()
        } catch (e: Throwable) {
            logger.error("failed to invoke findAllKubeConfig(), msg=${e.message}", e)
            emptyList()
        }
    }

    fun createKubeConfig(upsertKubeConfigRequest: UpsertKubeConfigRequest): KubeConfigVO {
        val tenantId = AuthUtil.getTenant()
        val userId = AuthUtil.getUserId()
        logger.info("create kube config for: userId=$userId, tenant=$tenantId")
        return try {
            enterpriseSettingsApi.createKubeConfig(
                tenantId = tenantId,
                userId = userId,
                upsertKubeConfigRequest = upsertKubeConfigRequest
            )
        } catch (e: HttpException) {
            if (e.code() == 409) {
                logger.error("createKubeConfig HttpException error:", e)
                throw BizException(ErrorCode.AS_ENTERPRISE_SETTING_KUBECONFIG_ALREADY_EXIST)
            } else {
                throw e
            }
        } catch (e: Throwable) {
            logger.error("failed to invoke createKubeConfig(), msg=${e.message}", e)
            throw BizException(ErrorCode.AS_ENTERPRISE_SETTING_KUBECONFIG_VALIDATE_FAILED)
        }
    }

    fun updateKubeConfig(id: Long, upsertKubeConfigRequest: UpsertKubeConfigRequest): KubeConfigVO? {
        val tenantId = AuthUtil.getTenant()
        val userId = AuthUtil.getUserId()
        logger.info("update kube config for: userId=$userId, tenant=$tenantId")
        return try {
            enterpriseSettingsApi.updateKubeConfig(
                tenantId = tenantId,
                userId = userId,
                id = id,
                upsertKubeConfigRequest = upsertKubeConfigRequest
            )
        } catch (e: HttpException) {
            if (e.code() == 409) {
                logger.error("updateKubeConfig HttpException error:", e)
                throw BizException(ErrorCode.AS_ENTERPRISE_SETTING_KUBECONFIG_ALREADY_EXIST)
            } else {
                throw e
            }
        } catch (e: Throwable) {
            logger.error("failed to invoke updateKubeConfig(), msg=${e.message}", e)
            throw BizException(ErrorCode.AS_ENTERPRISE_SETTING_KUBECONFIG_VALIDATE_FAILED)
        }
    }

    fun deleteKubeConfig(id: Long) {
        val tenantId = AuthUtil.getTenant()
        val userId = AuthUtil.getUserId()
        logger.info("delete kube config id=$id for: userId=$userId, tenant=$tenantId")
        try {
            enterpriseSettingsApi.deleteKubeConfig(
                tenantId = tenantId,
                userId = userId,
                id = id,
            )
        } catch (e: Throwable) {
            logger.error("failed to invoke deleteKubeConfig(), msg=${e.message}", e)
        }
    }

}