package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.config.WebSocketConfig
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderCreateWebSocketService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import org.springframework.web.socket.CloseStatus
import org.springframework.web.socket.TextMessage
import org.springframework.web.socket.WebSocketSession
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentSkipListSet

class ChangeOrderCreateWebSocketServiceImpl : ChangeOrderCreateWebSocketService() {
    companion object {
        val envKeyToSessionSetMap: ConcurrentHashMap<String, ConcurrentSkipListSet<String>> = ConcurrentHashMap()
        val sessionToEnvKeySetMap: ConcurrentHashMap<String, ConcurrentSkipListSet<String>> = ConcurrentHashMap()
        val sessionMap: ConcurrentHashMap<String, WebSocketSession> = ConcurrentHashMap()
    }

    override fun notifyAppEnvDeployChanged(appName: String, envName: String, content: String) {
        val orgId = AuthUtil.getTenant()
        val appEnvOrgIdKey = "${appName}_${envName}_$orgId"
        val sessionIdSet = envKeyToSessionSetMap[appEnvOrgIdKey]
        sessionIdSet?.forEach {
            try {
                sessionMap[it]?.sendMessage(TextMessage(content))
                logger.info("Notify State Changed for appName[$appName] envName[$envName] sessionId[$it] content[$content]")
            } catch (exception: Exception) {
                val session = sessionMap.remove(it)
                logger.info("Session[${session?.id}] has been closed, Remove it")
                envKeyToSessionSetMap[appEnvOrgIdKey]?.remove(it)
                try {
                    session?.close()
                } catch (exception: Exception) {

                }
            }
        }
    }

    override fun afterConnectionEstablished(session: WebSocketSession) {
        getEnvsKey(session).forEach { oneEnvKey ->
            sessionMap[session.id] = session
            envKeyToSessionSetMap.putIfAbsent(oneEnvKey, ConcurrentSkipListSet(setOf(session.id)))?.add(session.id)
            sessionToEnvKeySetMap.putIfAbsent(session.id, ConcurrentSkipListSet(setOf(oneEnvKey)))?.add(oneEnvKey)
            logger.info("WebSocket Client Connected for envKey[$oneEnvKey] Session[${session.id}]")
        }
    }

    override fun afterConnectionClosed(session: WebSocketSession, status: CloseStatus) {
        logger.info("WebSocket Client DisConnected for Session[$session.id] StatusCode[${status.code}]")
        sessionMap.remove(session.id)
        sessionToEnvKeySetMap[session.id]?.forEach {
            envKeyToSessionSetMap[it]?.remove(session.id)
        }
        envKeyToSessionSetMap.remove(session.id)
    }

    override fun handleTextMessage(session: WebSocketSession, message: TextMessage) {
    }

    private fun getEnvsKey(session: WebSocketSession): List<String> {
        val appName = session.attributes[WebSocketConfig.APP_NAME_KEY] ?: return emptyList()
        val envNames = session.attributes[WebSocketConfig.ENV_NAMES_KEY]?.toString() ?: return emptyList()
        val orgId = session.attributes[WebSocketConfig.ORG_ID_KEY] ?: return emptyList()
        return envNames.split(",").map { "${appName}_${it}_$orgId" }
    }
}