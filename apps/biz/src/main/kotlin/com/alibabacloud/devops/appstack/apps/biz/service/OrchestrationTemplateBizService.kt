package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.InvalidChangeItem
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.template.OrchestrationTemplate
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateCheckRequest
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateCopyRequest
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateCreateRequest
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateUpdateRequest

/**
 * <AUTHOR> <EMAIL>
 * @version : OrgOrchestrationBizService, v0.1
 * @date : 2023-01-03 16:07
 **/
interface OrchestrationTemplateBizService {

    fun findAll(scopes: List<OrchestrationTemplate.TemplateScopeEnum>): List<OrchestrationTemplate>

    fun create(request: OrchestrationTemplateCreateRequest): OrchestrationTemplate

    fun copy(requestTemplate: OrchestrationTemplateCopyRequest): OrchestrationTemplate

    fun update(sn: String, templateUpdateRequest: OrchestrationTemplateUpdateRequest): OrchestrationTemplate

    fun delete(sn: String): Boolean

    fun check(request: OrchestrationTemplateCheckRequest): List<InvalidChangeItem>
}