package com.alibabacloud.devops.appstack.apps.biz.model.po

import com.alibabacloud.devops.appstack.libs.common.annotation.Comment
import com.alibabacloud.devops.appstack.libs.common.model.BasePO
import com.baomidou.mybatisplus.annotation.TableName
import lombok.Data
import javax.persistence.Column
import javax.persistence.Index
import javax.persistence.Table

/**
 * <AUTHOR>
 * @date 2023-11-09
 */
@TableName("app_template_config_references")
@Comment("AppTemplateConfigReferences")
@Table(
    indexes = [
        Index(unique = true, columnList = "org_id(64),is_deleted(32),reference_type(32),reference_id(64),app_template_config_sn(64)"),
        Index(columnList = "org_id(64),is_deleted(32),app_template_config_sn(64)"),
    ]
)
@Data
class AppTemplateConfigReferencePO : BasePO(){

    @Comment("应用模板name")
    @Column(columnDefinition = "varchar(255)", nullable = false)
    lateinit var appTemplateName: String

    @Comment("应用模板配置sn")
    @Column(columnDefinition = "varchar(255)", nullable = false)
    lateinit var appTemplateConfigSn: String

    @Comment("引用的类型")
    @Column(columnDefinition = "varchar(32)", nullable = false)
    var referenceType: String? = null

    @Comment("引用的id")
    @Column(columnDefinition = "varchar(255)", nullable = false)
    var referenceId: String? = null
}