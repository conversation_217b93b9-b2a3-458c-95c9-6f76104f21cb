package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.forward.flow

import com.alibabacloud.devops.appstack.apps.biz.service.FlowForwardBizService
import com.fasterxml.jackson.databind.JsonNode
import io.swagger.v3.oas.annotations.Hidden
import io.swagger.v3.oas.annotations.tags.Tag
import kotlinx.coroutines.flow.flow
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RestController
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 * <AUTHOR>
 * @create 2023/11/15 7:42 AM
 **/
@Hidden
@Tag(name = "flowForward", description = "Flow转发 API")
@RestController
@RequestMapping("/forward/flow")
class FlowForwardApi {

    @Autowired
    lateinit var flowForwardBizService: FlowForwardBizService

    @PostMapping("/tb/permissions/checkPermissions")
    fun checkPermissions(request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        return flowForwardBizService.checkPermissions(request, response)
    }

    @GetMapping("/flow/service/componentInst/latestSuccess")
    fun getLatestSuccess(request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        return flowForwardBizService.getLatestSuccess(request, response)
    }

    @GetMapping("/flow/service/flow/retryAction")
    fun retryAction(request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        return flowForwardBizService.retryAction(request, response)
    }

    @GetMapping("/execution-component/command")
    fun componentCommand(request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        return flowForwardBizService.componentCommand(request, response)
    }

    @GetMapping("/execution-component/skip")
    fun skipAction(request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        return flowForwardBizService.skipAction(request, response)
    }

    @GetMapping("/ec/ajax/pipelines/queryByRegion")
    fun queryByRegion(request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        return flowForwardBizService.handleCommon(request, response)
    }

    @GetMapping("/ec/ajax/pipelines/{id}")
    fun getPipelineWithParams(@PathVariable id: String, request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        return flowForwardBizService.getPipelineWithParams(id, request, response)
    }

    @RequestMapping(value = [
        "/ec/ajax/pipelines/{id}/**",
        "/ec/ajax/pipeline/{id}/**"
    ], method = [RequestMethod.GET, RequestMethod.POST])
    fun handlePipeline(@PathVariable id: String, request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        return flowForwardBizService.handlePipeline(id, request, response)
    }

    @RequestMapping("/**", method = [RequestMethod.GET, RequestMethod.POST])
    fun handleCommon(request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        return flowForwardBizService.handleCommon(request, response)
    }
}