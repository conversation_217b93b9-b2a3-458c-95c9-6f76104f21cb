package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.AppOrchestrationBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.Label
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.InvalidChangeItem
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.Orchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.OrchestrationLabeledRevision
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.k8s.LocatorInfo
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.request.AppOrchestrationCheckRequest
import com.alibabacloud.devops.appstack.libs.model.request.AppOrchestrationCreateRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppOrchestrationRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.AppOrchestrationDiffVO
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import javax.servlet.http.HttpServletResponse

/**
 * @author: <EMAIL>
 * @date: 2021-12-15 20:43
 * @version: AppOrchestrationBizApi, v0.1
 **/
@Tag(name = "AppOrchestration", description = "应用编排相关 API")
@RestController
@RequestMapping("/api/v1/apps/{appName}")
open class AppOrchestrationBizApi {

    @Autowired
    lateinit var appOrchestrationBizService: AppOrchestrationBizService

    @GetMapping("/orchestrations/{sn}")
    fun findAppOrchestration(
        @PathVariable("appName") appName: String,
        @PathVariable("sn") sn: String,
        @RequestParam("tagName", required = false) tagName: String? = null,
        @RequestParam("sha", required = false) sha: String? = null
    ): Response<AppOrchestration> {
        return Response.success(appOrchestrationBizService.find(appName, sn, tagName, sha))
    }

    @GetMapping("/orchestrations:all")
    fun findAllAppOrchestration(
        @PathVariable("appName") appName: String,
    ): Response<List<AppOrchestration>> {
        return Response.success(appOrchestrationBizService.findAll(appName))
    }

    @PostMapping("/orchestrations")
    fun createAppOrchestration(
        @PathVariable("appName") appName: String,
        @RequestBody appOrchestrationCreateRequest: AppOrchestrationCreateRequest,
    ): Response<AppOrchestration> {
        return Response.success(appOrchestrationBizService.create(appName, appOrchestrationCreateRequest))
    }

    @PostMapping("/orchestrations/{sn}")
    fun updateAppOrchestration(
        @PathVariable("appName") appName: String,
        @PathVariable("sn") sn: String,
        @RequestBody updateAppOrchestrationRequest: UpdateAppOrchestrationRequest,
    ): Response<AppOrchestration> {
        return Response.success(appOrchestrationBizService.update(appName, sn, updateAppOrchestrationRequest))
    }


    @DeleteMapping("/orchestrations/{sn}")
    fun deleteAppOrchestration(
        @PathVariable("appName") appName: String,
        @PathVariable("sn") sn: String
    ): Response<Boolean> {
        appOrchestrationBizService.delete(appName, sn)
        return Response.success(true)
    }

    @GetMapping("/orchestrations/{sn}:revisions")
    fun findPaginatedRevision(
        @PathVariable("appName") appName: String,
        @PathVariable("sn") sn: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<Revision>> {
        return Response.success(appOrchestrationBizService.findPaginatedRevision(appName, sn, current, pageSize))
    }

    @GetMapping("/orchestrations/{sn}:revisionWithLabel")
    fun findPaginatedRevisionAndLabel(
        @PathVariable("appName") appName: String,
        @PathVariable("sn") sn: String,
        @RequestParam("envName", required = false) envName: String?,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<OrchestrationLabeledRevision>> {
        return Response.success(appOrchestrationBizService.findPaginatedRevisionAndLabel(appName, sn, envName, current, pageSize))
    }

    @GetMapping("/orchestrations/{sn}:compare")
    fun compareAppOrchestration(
        @PathVariable("appName") appName: String,
        @PathVariable("sn") sn: String,
        @RequestParam("beforeRevisionSha") beforeRevisionSha: String,
        @RequestParam("afterRevisionSha") afterRevisionSha: String
    ): Response<Triple<AppOrchestrationDiffVO, AppOrchestrationDiffVO, List<DiffItem<String>>>> {
        return Response.success(
            appOrchestrationBizService.compare(appName, sn, beforeRevisionSha, afterRevisionSha)
        )
    }

    @GetMapping("/orchestrations:export")
    fun export(
        @PathVariable("appName") appName: String,
        @RequestParam("suitableResourceType") suitableResourceType: ResourceTypeEnum,
        @RequestParam("format") format: Orchestration.FormatEnum,
        @RequestParam("sha") sha: String,
        @RequestParam renderType: Orchestration.RenderTypeEnum,
        @RequestParam(required = false) profileName: String = "",
        response: HttpServletResponse,
    ): Response<Boolean?> {
        appOrchestrationBizService.exportAppOrchestration(
            appName,
            suitableResourceType,
            format,
            sha,
            renderType,
            profileName,
            response
        )
        return Response.success(true)
    }

    @PostMapping("/orchestrations:check")
    fun checkAppOrchestration(
        @PathVariable("appName") appName: String,
        @RequestBody appOrchestrationCheckRequest: AppOrchestrationCheckRequest
    ): Response<List<InvalidChangeItem>> {
        return Response.success(
            appOrchestrationBizService.check(appName, appOrchestrationCheckRequest)
        )
    }

    @PostMapping("/orchestrations/{sn}:bindLabel")
    fun bindLabels(
        @PathVariable("appName") appName: String,
        @PathVariable sn: String,
        @RequestParam("sha") sha: String,
        @RequestBody labels: List<Label>,
    ): Response<Unit> {
        return Response.success(
            appOrchestrationBizService.bindLabels(appName, sn, sha, labels)
        )
    }

    @GetMapping("/orchestrations:getLocatorList")
    fun getLocatorList(
        @PathVariable("appName") appName: String,
        @RequestParam("envName") envName: String,
    ): Response<List<LocatorInfo>> {
        return Response.success(
            appOrchestrationBizService.getLocatorList(appName, envName)
        )
    }
}