package com.alibabacloud.devops.appstack.apps.biz.model.ext

import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplatePO
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate

/**
 * <AUTHOR>
 * @create 2023/10/16 7:02 PM
 **/
fun AppTemplate.toPO() : AppTemplatePO {
    val appTemplatePO = AppTemplatePO()
    appTemplatePO.name = name
    appTemplatePO.displayName = displayName
    appTemplatePO.description = description
    appTemplatePO.cover = cover
    return appTemplatePO
}

fun AppTemplatePO.toModel() : AppTemplate {
    return AppTemplate(
        name = name,
        displayName = displayName,
        description = description,
        cover = cover,
        creatorId = creator,
        gmtCreate = gmtCreate,
        modifierId = modifier,
        gmtModified = gmtModified
    )
}