package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.apps.biz.model.vo.ChangeRequestVO
import com.alibabacloud.devops.appstack.libs.model.`do`.OrderEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.SortEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.ChangeRequest
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.WorkflowMetadata
import com.alibabacloud.devops.appstack.libs.model.request.cr.CreateChangeRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.QueryAppChangeRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.UpdateChangeRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.TransferChangeOwnerRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.QueryChangeRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.ChangeRequestSearch
import com.alibabacloud.devops.appstack.libs.model.request.ng.MetadataSearch
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeRequestDetailVO
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeRequestExecutionVO

interface ChangeRequestBizService {

    fun create(appName: String, request: CreateChangeRequest): ChangeRequest
    fun find(appName: String, sn: String): ChangeRequestDetailVO
    fun close(appName: String, sn: String)
    fun finish(appName: String, sn: String)
    fun update(appName: String, sn: String, request: UpdateChangeRequest): ChangeRequest?
    fun transferOwner(appName: String, sn: String, request: TransferChangeOwnerRequest): ChangeRequest?
    fun findReleaseMetadata(appName: String, releaseWorkflowSn: String, crSn: String): List<WorkflowMetadata>

    fun findByOrgPaginated(
        request: QueryChangeRequest,
        current: Long,
        pageSize: Long,
        order: OrderEnum,
        sort: SortEnum
    ): Pagination<ChangeRequest>

    /**
     * 与 findByOrgPaginated 权限管控不同，单独暴露一个 service
     */
    fun findByAppPaginated(
        appName: String,
        request: QueryAppChangeRequest,
        current: Long,
        pageSize: Long,
        order: OrderEnum,
        sort: SortEnum
    ): Pagination<ChangeRequest>

    fun findBySns(appName: String, sns: String): List<ChangeRequestDetailVO>
    fun list(changeRequestSearch: ChangeRequestSearch): PageList<ChangeRequest>
    fun listChangeRequestMetadata(
        appName: String,
        sn: String,
        query: MetadataSearch,
    ): Pagination<ChangeRequestExecutionVO>
}