package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.AppCodeRepoBizService
import com.alibabacloud.devops.appstack.apps.biz.service.AppSourceBizService
import com.alibabacloud.devops.appstack.apps.biz.service.impl.handler.appsource.AppSourceBizServiceHandler
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.request.ng.CreateAppSourceRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateAppSourceRequest
import com.alibabacloud.devops.appstack.libs.model.vo.ng.AppSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2023-10-12 10:50
 * @version: AppSourceBizServiceImpl, v0.1
 **/
@Service
class AppSourceBizServiceImpl @Autowired constructor(
    handlers: List<AppSourceBizServiceHandler>,
    val appCodeRepoBizService: AppCodeRepoBizService
) : AppSourceBizService {

    private val handlerMap = handlers.associateBy { it.type }

    override fun create(appName: String, requestEntity: CreateAppSourceRequest): AppSource {
        val handler = handlerMap[requestEntity.type] ?: throw BizException(ErrorCode.AS_APP_SOURCE_TYPE_UNSUPPORTED)
        return handler.create(appName, requestEntity)
    }

    override fun update(appName: String, requestEntity: UpdateAppSourceRequest): AppSource {
        val handler = handlerMap[requestEntity.type] ?: throw BizException(ErrorCode.AS_APP_SOURCE_TYPE_UNSUPPORTED)
        return handler.update(appName, requestEntity)
    }

    override fun delete(appName: String, sn: String): Boolean {
        // NOTE jiuya.wb 删除实现目前暂时无法统一，因为应用代码源记录的存储已经单独分出一个表以及明确的领域 ORM 了
        // 暂时的做法是只处理代码源
        // 理想的形态，则是统一应用源 ORM，先查询类型后单独触发删除
        // 视情况，handler 机制可能下沉到 core 中；比较好的重构时机，是添加制品源或其他源的时候
        // 另外，保留 handler 的 delete 逻辑有助于添加后处理（删除应用源不一定只限于 DB 删，可能还需要触发一些外部行为）
        appCodeRepoBizService.delete(appName = appName, sn = sn)
        return true
    }

}