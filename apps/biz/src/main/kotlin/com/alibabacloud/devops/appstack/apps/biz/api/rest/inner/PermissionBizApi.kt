package com.alibabacloud.devops.appstack.apps.biz.api.rest.inner

import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.iam.constant.ProtocolType
import io.swagger.v3.oas.annotations.Hidden
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR> <EMAIL>
 * @version : PermissionBizApi, v0.1
 * @date : 2023-10-23 21:26
 **/
@Hidden
@Tag(name = "Appstack Permission", description = "权限相关 API")
@RestController
@RequestMapping("/inner/api/permission")
class PermissionBizApi {

    @Autowired
    lateinit var iamService: IamService

    @Operation(summary = "判断当前用户是否有应用的「管理度量报表」权限")
    @GetMapping("/apps/{appName}/actions/APP_METRICS_MANAGE")
    fun findAppAction(
        @PathVariable("appName") appName: String,
    ): Response<Boolean> {
        var can = iamService.can(protocolType = ProtocolType.AppStackApp, appName, Action.APP_METRICS_MANAGE)
        if (!can) {
            can = iamService.can(protocolType = ProtocolType.AppStack, "any", Action.ORG_APP_MANAGE)
        }
        return Response.success(can)
    }
}