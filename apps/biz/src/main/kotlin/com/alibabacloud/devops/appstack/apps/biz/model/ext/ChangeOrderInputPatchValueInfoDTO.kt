package com.alibabacloud.devops.appstack.apps.biz.model.ext

import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.AppOrchestrationSource
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.DeployGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.RevisionVariableGroup

/**
 * <AUTHOR>
 * @date 2023-04-20
 */
data class ChangeOrderInputPatchValueInfoDTO(
    val orchestrationSn: String?,
    val orchestration: AppOrchestration?,
    val orchestrationSource: AppOrchestrationSource?,
    val profiles: List<RevisionVariableGroup>?,
    val deployGroup: DeployGroup?,
    val patchValues: Map<String, String>
)