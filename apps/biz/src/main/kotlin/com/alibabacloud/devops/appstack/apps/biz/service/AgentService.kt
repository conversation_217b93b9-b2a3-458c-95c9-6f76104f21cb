package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.apps.biz.model.AgentStatus
import com.alibabacloud.devops.appstack.apps.biz.model.AgentTask
import com.alibabacloud.devops.appstack.apps.biz.model.JobLog

interface AgentService {

    /**
     * 创建安装agent 任务
     */
    fun installAgent(poolName: String, instanceName: String, version: String): AgentTask

    /**
     * 获取最新的安装任务
     */
    fun findLatestInstallTask(poolName: String, instanceName: String): AgentTask?

    /**
     * 查询最新的安装日志
     */
    fun findLatestInstallLog(poolName: String, instanceName: String): JobLog?

    /**
     * 更新任务状态
     */
    fun updateTaskStatus(jobId: String, status: String)

    /**
     * 查看安装状态
     */
    fun findStatus(poolName: String, instanceName: String): AgentStatus


    /**
     * 删除安装任务
     */
    fun delete(poolName: String, instanceName: String)

    fun cancelAgentInstallation(poolName: String, instanceName: String)
}