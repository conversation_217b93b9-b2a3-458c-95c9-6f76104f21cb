package com.alibabacloud.devops.appstack.apps.biz.service.system

import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ExecutePipelineResult
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStage
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.*
import com.alibabacloud.devops.appstack.libs.model.vo.ReleaseIntegratedMetadata

/**
 * <AUTHOR>
 * @date 2023-11-08
 */
interface ReleaseStageBizService {
    fun create(systemName: String, releaseWorkflowSn: String, request: CreateReleaseStageBizRequest): ReleaseStage
    fun findAll(systemName: String, releaseWorkflowSn: String): List<ReleaseStage>
    fun find(systemName: String, releaseWorkflowSn: String, releaseStageSn: String): ReleaseStage
    fun update(systemName: String, releaseWorkflowSn: String, releaseStageSn: String, request: UpdateReleaseStageRequest): ReleaseStage
    fun delete(systemName: String, releaseWorkflowSn: String, releaseStageSn: String)
    fun updatePipeline(systemName: String, releaseWorkflowSn: String, releaseStageSn: String, request: UpdateReleaseStagePipelineRequest): ReleaseStage
    fun executePipeline(systemName: String, releaseWorkflowSn: String, releaseStageSn: String, request: ExecuteReleaseStagePipelineRequest): ExecutePipelineResult
    fun getPipelineParams(systemName: String, releaseWorkflowSn: String, releaseStageSn: String, appReleaseSn: String?): MutableMap<String, Any>
    fun findReleaseStageCrMetadata(systemName: String, releaseWorkflowSn: String, releaseStageSn: String, request: QueryReleaseStageCrMetadataRequest): ReleaseIntegratedMetadata?
    fun findPreviousMetadata(
        systemName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String
    ): ReleaseIntegratedMetadata?

    fun findStageEnvList(releaseStageSn: String): List<String>

    fun findVariableGroups(systemName: String, releaseWorkflowSn: String, releaseStageSn: String): List<VariableGroup>

}