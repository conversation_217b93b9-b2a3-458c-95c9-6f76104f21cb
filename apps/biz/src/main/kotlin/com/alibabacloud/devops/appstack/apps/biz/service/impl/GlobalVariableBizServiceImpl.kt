package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.GlobalVariableBizService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.globalvar.GlobalVar
import com.alibabacloud.devops.appstack.libs.model.`do`.globalvar.GlobalVarRevisionContent
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.CreateGlobalVarRequest
import com.alibabacloud.devops.appstack.libs.model.request.CreateGlobalVariableRequest
import com.alibabacloud.devops.appstack.libs.model.request.GlobalVariable
import com.alibabacloud.devops.appstack.libs.model.request.UpdateGlobalVarContentRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateGlobalVariableRequest
import com.alibabacloud.devops.iam.constant.ProtocolType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR> <EMAIL>
 * @version : GlobalVariableBizServiceImpl, v0.1
 * @date : 2024-01-29 13:44
 **/
@Service
open class GlobalVariableBizServiceImpl : GlobalVariableBizService {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var iamService: IamService

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_VIEW),
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_VIEW, resourceArgIndex = 0),
            Access(action = Action.VAR_EDIT, resourceArgIndex = 0),
            Access(action = Action.VAR_USE, resourceArgIndex = 0),
        ]
    )
    override fun find(name: String, revisionSha: String?): GlobalVariable {
        val globalVar = coreFacades.globalVarFacade.find(name) ?: throw BizException(ErrorCode.AS_GLOBAL_VAR_NOT_FOUND)
        val content = coreFacades.globalVarFacade.findContent(name, revisionSha)
        return convert(globalVar, content)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_CREATE),
            Access(action = Action.ORG_VAR_MANAGE),
        ]
    )
    override fun create(req: CreateGlobalVariableRequest): GlobalVariable {
        val ownerId = if (req.ownerId.isNullOrBlank() || req.ownerId.isNullOrEmpty()) {
            logger.info("ownerId is null, use current user instead: userId=${AuthUtil.getUserId()}")
            AuthUtil.getUserId()
        } else {
            req.ownerId!!
        }
        iamService.checkUserExist(ownerId)

        val globalVar = coreFacades.globalVarFacade.create(
            CreateGlobalVarRequest(
                name = req.name,
                displayName = req.displayName,
                ownerId = req.ownerId
            )
        )
        iamService.registerResource(
            protocolType = ProtocolType.AppStackVar,
            resourceName = globalVar.name,
            orgId = AuthUtil.getTenant(),
            ownerId = ownerId
        )
        var content = coreFacades.globalVarFacade.findContent(globalVar.name)
        if (!req.content.isNullOrEmpty()) {
            content = coreFacades.globalVarFacade.updateContent(
                req.name,
                UpdateGlobalVarContentRequest(
                    globalVarName = globalVar.name,
                    content = req.content!!,
                    fromRevision = content.revision!!,
                    message = req.message,
                )
            )
        }
        return convert(globalVar, content)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_EDIT, resourceArgIndex = 0),
        ]
    )
    override fun update(name: String, req: UpdateGlobalVariableRequest): GlobalVariable {
        val globalVar = coreFacades.globalVarFacade.find(name) ?: throw BizException(ErrorCode.AS_GLOBAL_VAR_NOT_FOUND)
        val globalVarContent = try {
            coreFacades.globalVarFacade.findContent(name, req.fromRevisionSha)
        } catch (e: Exception) {
            throw BizException(ErrorCode.AS_GLOBAL_VAR_NOT_FOUND)
        }
        val request = UpdateGlobalVarContentRequest(
            globalVarName = name,
            content = req.content,
            fromRevision = globalVarContent.revision!!,
            message = req.message
        )
        val content = coreFacades.globalVarFacade.updateContent(name, request)
        return convert(globalVar, content)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_VAR_MANAGE),
            Access(action = Action.VAR_DELETE, resourceArgIndex = 0)
        ]
    )
    override fun delete(name: String): Boolean {
        val listUsageReference = coreFacades.globalVarFacade.findUsageReferences(listOf(name))
        if (listUsageReference.isNotEmpty()) {
            throw BizException(ErrorCode.AS_USAGE_REFERENCES_EXISTS)
        }
        coreFacades.globalVarFacade.delete(name)
        return true
    }

    private fun convert(globalVar: GlobalVar, content: GlobalVarRevisionContent): GlobalVariable {
        return GlobalVariable(
            name = globalVar.name,
            displayName = globalVar.displayName,
            creator = globalVar.creator,
            gmtCreate = globalVar.gmtCreate,
            modifier = globalVar.modifier,
            gmtModified = globalVar.gmtModified,
            revision = content.revision,
            content = content.content
        )
    }
}