package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestWorkflowBizService
import com.alibabacloud.devops.appstack.apps.biz.service.WorkflowBaseService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowBriefVO
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowRevision
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.org.event.ReleaseWorkflowAudit
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.CreateReleaseWorkflowBizRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.SortReleaseWorkflowRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.UpdateReleaseWorkflowRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.ga.AppTemplateWorkflowRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2023-11-08
 */
@Service
open class ChangeRequestWorkflowBizServiceImpl : ChangeRequestWorkflowBizService {

    @Autowired
    lateinit var workflowBaseService: WorkflowBaseService

    @Autowired
    lateinit var auditLogService: AuditLogService

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun create(appName: String, request: CreateReleaseWorkflowBizRequest): ReleaseWorkflow {
        request.type = ReleaseWorkflow.TypeEnum.CR
        val workflow = workflowBaseService.create(appName, request)
        val audit = ReleaseWorkflowAudit(
            target = AuditTarget(id = workflow.sn!!, name = workflow.name!!),
            appName = appName
        )
        auditLogService.log(OrgEventType.RELEASE_WORKFLOW_CREATE, audit)
        return workflow
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun create(appName: String, request: AppTemplateWorkflowRequest): ReleaseWorkflowRevision {
        val workflow = workflowBaseService.create(appName, request)
        val audit = ReleaseWorkflowAudit(
            target = AuditTarget(id = workflow.content.sn!!, name = workflow.content.name!!),
            appName = appName
        )
        auditLogService.log(OrgEventType.RELEASE_WORKFLOW_CREATE, audit)
        return workflow
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    override fun findAll(appName: String): List<ReleaseWorkflow> {
        return workflowBaseService.findAll(appName, ReleaseWorkflow.TypeEnum.CR)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    override fun findAllBrief(appName: String): List<ReleaseWorkflowBriefVO> {
        return workflowBaseService.findAllBrief(appName, ReleaseWorkflow.TypeEnum.CR)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    override fun find(appName: String, sn: String): ReleaseWorkflow {
        return workflowBaseService.find(appName, sn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    override fun findWithRevision(appName: String, sn: String): ReleaseWorkflowRevision? {
        return workflowBaseService.findWithRevision(appName, sn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    override fun findYamlBySha(appName: String, sn: String, sha: String): String {
        return workflowBaseService.findYamlBySha(appName, sn, sha)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    override fun listRevisionPaginated(
        appName: String,
        sn: String,
        current: Long,
        pageSize: Long,
    ): Pagination<Revision> {
        return workflowBaseService.listRevisionPaginated(appName, sn, current, pageSize)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    override fun compare(
        appName: String,
        sn: String,
        beforeRevisionSha: String,
        afterRevisionSha: String,
    ): Triple<String, String, List<DiffItem<String>>> {
        return workflowBaseService.compare(appName, sn, beforeRevisionSha, afterRevisionSha)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    override fun update(
        appName: String,
        sn: String,
        appTemplateWorkflowRequest: AppTemplateWorkflowRequest,
    ): ReleaseWorkflowRevision {
        val workflow = workflowBaseService.update(appName, sn, appTemplateWorkflowRequest)
        val audit = ReleaseWorkflowAudit(
            target = AuditTarget(id = workflow.content.sn!!, name = workflow.content.name!!),
            appName = appName
        )
        auditLogService.log(OrgEventType.RELEASE_WORKFLOW_MODIFY, audit)
        return workflow
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    override fun update(
        appName: String,
        sn: String,
        updateReleaseWorkflowRequest: UpdateReleaseWorkflowRequest,
    ): ReleaseWorkflow {
        val workflow = workflowBaseService.update(appName, sn, updateReleaseWorkflowRequest)
        val audit = ReleaseWorkflowAudit(
            target = AuditTarget(id = workflow.sn!!, name = workflow.name!!),
            appName = appName
        )
        auditLogService.log(OrgEventType.RELEASE_WORKFLOW_MODIFY, audit)
        return workflow
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    override fun delete(appName: String, sn: String):ReleaseWorkflow {
        val workflow = workflowBaseService.delete(appName, sn)
        val audit = ReleaseWorkflowAudit(
            target = AuditTarget(id = workflow.sn!!, name = workflow.name!!),
            appName = appName
        )
        auditLogService.log(OrgEventType.RELEASE_WORKFLOW_DELETE, audit)
        return workflow
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    override fun sort(appName: String, request: SortReleaseWorkflowRequest): List<ReleaseWorkflow> {
        request.type = ReleaseWorkflow.TypeEnum.CR
        return workflowBaseService.sort(appName, request)
    }
}