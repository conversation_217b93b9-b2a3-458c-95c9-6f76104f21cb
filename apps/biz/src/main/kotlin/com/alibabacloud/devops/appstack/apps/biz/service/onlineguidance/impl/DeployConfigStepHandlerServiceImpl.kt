package com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.impl

import com.alibabacloud.devops.appstack.apps.biz.model.State
import com.alibabacloud.devops.appstack.apps.biz.model.Step
import com.alibabacloud.devops.appstack.apps.biz.model.StepProgress
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.StepHandlerService
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppBuiltInOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppExternalCodeupOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppExternalGitlabOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR> liyebin.lyb
 * @date : 2022/8/26 2:45 PM
 */
@Service
class DeployConfigStepHandlerServiceImpl : StepHandlerService {

    @Autowired
    lateinit var coreFacades: CoreFacades

    override fun handle(appName: String, step: Step): StepProgress {
        val appOrchestrations = coreFacades.appOrchestrationFacade.findAll(appName)
        val state: State = calculateOrchestrationState(appOrchestrations)
        return StepProgress(
            needUpdate = step.state != state,
            step = Step(
                name = step.name,
                state = state
            )
        )
    }

    fun calculateOrchestrationState(appOrchestrations: List<AppOrchestration>): State {
        appOrchestrations.forEach { appOrchestration ->
            when (appOrchestration) {
                is AppBuiltInOrchestration -> {
                    if (appOrchestration.componentList.isNotEmpty()) {
                        return State.FINISH
                    }
                }
                is AppExternalCodeupOrchestration, is AppExternalGitlabOrchestration -> {
                    return State.FINISH
                }
            }
        }
        return State.INIT
    }
}