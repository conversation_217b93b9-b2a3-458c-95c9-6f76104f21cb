package com.alibabacloud.devops.appstack.apps.biz.service.impl.handler.deploy

import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderInputPatchValueHandler
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderInput
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppBuiltInOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Placeholder
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.RevisionVariableGroup
import com.alibabacloud.devops.appstack.libs.model.constant.SystemVariable
import org.springframework.stereotype.Service

/**
 * 提取占位符中的变量，使用环境变量组或系统预置变量替代
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Service
class VariableChangeOrderInputPatchValueHandlerForDeploy : ChangeOrderInputPatchValueHandler {

    override fun handle(
        env: Env,
        orchestration: AppOrchestration?,
        profiles: List<RevisionVariableGroup>,
        stableChangeOrderInput: ChangeOrderInput?,
        customPatchValues: Map<String, String>,
        result: MutableMap<String, String>
    ) {
        val vars = profiles.map { it.vars }
        val variableValues = mutableMapOf<String, Any>()
        vars.forEach {
            variableValues.putAll(it.map { Pair(it.key, it.value) })
        }
        variableValues.putAll(SystemVariable.build(env.appName, env.name))
        orchestration?.let {
            orchestration as AppBuiltInOrchestration
            orchestration.placeholderList.filter { it.valueSource == Placeholder.ValueSource.VARIABLE }.forEach {
                result[it.name] = variableValues[it.value].toString()
            }
        }
    }

    override fun suitableDeployType() = listOf(ChangeOrder.Type.Deploy)

    override fun order() = 1
}