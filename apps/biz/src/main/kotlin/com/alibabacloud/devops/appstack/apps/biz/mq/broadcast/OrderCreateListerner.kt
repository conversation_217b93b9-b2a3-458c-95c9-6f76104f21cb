package com.alibabacloud.devops.appstack.apps.biz.mq.broadcast

import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderBizService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderCreateWebSocketService
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.BroadcastListener
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.BroadcastTagEnum
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.OrderCreateBody
import com.alibabacloud.devops.appstack.libs.change.controller.spring.boot.starter.service.ChangeControllerFacades
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2023-01-17 17:15
 * @version: OrderCreateListerner, v0.1
 **/
@Slf4k
@Service
class OrderCreateListener : BroadcastListener {

    @Autowired
    lateinit var changeControllerFacades: ChangeControllerFacades

    @Autowired
    lateinit var jobCreateWebSocketService: ChangeOrderCreateWebSocketService

    @Autowired
    lateinit var changeOrderBizService: ChangeOrderBizService
    override fun expectedTag(): BroadcastTagEnum {
        return BroadcastTagEnum.ORDER_CREATE
    }

    override fun handle(body: ByteArray) {
        logger.info("Received broadcast content: ${String(body)}")
        val content = jacksonObjectMapper().readValue(body, OrderCreateBody::class.java)
        AuthThreadContext.setTenant(content.tenant)
        AuthThreadContext.setUserId(content.operator)
        notify(content.changeOrderSn)
    }

    private fun notify(changeOrderSn: String) {
        val changeOrderVO = changeControllerFacades.changeOrderFacade.find(changeOrderSn)
        changeOrderVO?.jobs?.forEach { job ->
            jobCreateWebSocketService.notifyAppEnvDeployChanged(
                appName = changeOrderVO.appName,
                envName = job.envName,
                content = jacksonObjectMapper().writeValueAsString(
                    changeOrderBizService.findEnvOccupyList(
                        appName = changeOrderVO.appName,
                        envNames = job.envName
                    )
                )
            )
        }
    }
}