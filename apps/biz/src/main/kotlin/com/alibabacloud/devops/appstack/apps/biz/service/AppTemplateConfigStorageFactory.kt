package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.app.AbstractConfiguration
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig

/**
 * <AUTHOR> <EMAIL>
 * @version : AppTemplateConfigStorageFactory, v0.1
 * @date : 2023-10-18 14:24
 **/
interface AppTemplateConfigStorageFactory {
    fun build(
        type: AppTemplateConfig.TypeEnum,
    ): AppTemplateConfigStorageAdaptor<out AbstractConfiguration>
}