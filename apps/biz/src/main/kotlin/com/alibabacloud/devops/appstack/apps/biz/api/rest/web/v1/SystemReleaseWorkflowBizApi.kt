package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.system.ReleaseWorkflowBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowRevision
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.CreateReleaseWorkflowBizRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.SortReleaseWorkflowRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.UpdateReleaseWorkflowRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @date 2022-06-26
 */
@Tag(name = "SysteReleaseWorkflow", description = "系统发布流程相关 API")
@RestController
@RequestMapping("/api/v1")
open class SystemReleaseWorkflowBizApi {

    @Autowired
    lateinit var releaseWorkflowBizService: ReleaseWorkflowBizService

    @Operation(summary = "创建发布流程")
    @PostMapping("/systems/{systemName}/releaseWorkflows")
    fun createSystemReleaseWorkflow(
        @PathVariable systemName: String,
        @RequestBody request: CreateReleaseWorkflowBizRequest
    ): Response<ReleaseWorkflow> {
        return Response.success(releaseWorkflowBizService.create(systemName = systemName, request = request))
    }

    @Operation(summary = "查找所有发布流程")
    @GetMapping("/systems/{systemName}/releaseWorkflows")
    fun findSystemAllReleaseWorkflow(
        @PathVariable systemName: String,
        @RequestParam(required = false) type: ReleaseWorkflow.TypeEnum?,
    ): Response<List<ReleaseWorkflow>> {
        return Response.success(releaseWorkflowBizService.findAll(systemName))
    }

    @Operation(summary = "查找发布流程")
    @GetMapping("/systems/{systemName}/releaseWorkflows/{sn}")
    fun findSystemReleaseWorkflow(
        @PathVariable systemName: String, @PathVariable sn: String
    ): Response<ReleaseWorkflow> {
        return Response.success(releaseWorkflowBizService.find(systemName = systemName, sn = sn))
    }

    @Operation(summary = "查看发布流程版本")
    @GetMapping("/systems/{systemName}/releaseWorkflows/{sn}:revision")
    fun getSystemReleaseWorkflowRevision(
        @PathVariable systemName: String, @PathVariable sn: String
    ): Response<ReleaseWorkflowRevision?> {
        return Response.success(releaseWorkflowBizService.findWithRevision(systemName, sn))
    }

    @Operation(summary = "查看发布流程版本内容")
    @GetMapping("/systems/{systemName}/releaseWorkflows/{sn}/revisions/{sha}")
    fun getSystemReleaseWorkflowYamlBySha(
        @PathVariable systemName: String, @PathVariable sn: String, @PathVariable sha: String
    ): Response<String> {
        return Response.success(releaseWorkflowBizService.findYamlBySha(systemName, sn, sha))
    }

    @Operation(summary = "分页查找发布流程版本列表")
    @GetMapping("/systems/{systemName}/releaseWorkflows/{sn}/revisions")
    fun findSystemReleaseWorkflowRevisionPaginated(
        @PathVariable systemName: String, @PathVariable sn: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<Revision>> {
        return Response.success(releaseWorkflowBizService.listRevisionPaginated(systemName, sn, current, pageSize))
    }

    @Operation(summary = "发布流程版本比较")
    @GetMapping("/systems/{systemName}/releaseWorkflows/{sn}/compare")
    fun compare(
        @PathVariable systemName: String, @PathVariable sn: String,
        @RequestParam beforeRevisionSha: String,
        @RequestParam afterRevisionSha: String,
    ): Response<Triple<String, String, List<DiffItem<String>>>> {
        return Response.success(releaseWorkflowBizService.compare(systemName, sn, beforeRevisionSha, afterRevisionSha))
    }

    @Operation(summary = "修改发布流程")
    @PutMapping("/systems/{systemName}/releaseWorkflows/{sn}")
    fun updateSystemReleaseWorkflow(
        @PathVariable systemName: String, @PathVariable sn: String,
        @RequestBody updateReleaseWorkflowRequest: UpdateReleaseWorkflowRequest
    ): Response<ReleaseWorkflow> {
        return Response.success(
            releaseWorkflowBizService.update(
                systemName = systemName,
                sn = sn,
                updateReleaseWorkflowRequest = updateReleaseWorkflowRequest
            )
        )
    }

    @Operation(summary = "删除发布流程")
    @DeleteMapping("/systems/{systemName}/releaseWorkflows/{sn}")
    fun deleteSystemReleaseWorkflow(
        @PathVariable systemName: String, @PathVariable sn: String
    ): Response<Unit> {
        releaseWorkflowBizService.delete(systemName = systemName, sn = sn)
        return Response.success()
    }

    @Operation(summary = "调整发布流程顺序")
    @PutMapping("/systems/{systemName}/releaseWorkflows:order")
    fun sortSystemReleaseWorkflow(
        @PathVariable systemName: String,
        @RequestBody request: SortReleaseWorkflowRequest
    ): Response<List<ReleaseWorkflow>> {
        return Response.success(releaseWorkflowBizService.sort(systemName = systemName, request = request))
    }

}