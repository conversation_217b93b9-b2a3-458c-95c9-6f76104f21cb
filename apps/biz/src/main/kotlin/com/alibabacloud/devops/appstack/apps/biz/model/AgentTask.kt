package com.alibabacloud.devops.appstack.apps.biz.model;

import lombok.Data
import java.util.Date

@Data
class AgentTask {
    var taskNo: String? = null
    var jobId: Long? = null
    var refObj: String? = null
    var refObjName: String? = null
    var status: String? = null
    var gmtCreate: Date? = null
}

data class AgentStatus(val state: State, var upgradeState: UpgradeState? = null) {
    enum class State {
        NOT_INSTALLED,
        INSTALLING,
        SUCCESS,
        ABNORMAL,
    }

    enum class UpgradeState {
        WITHOUT_OAM
    }
}