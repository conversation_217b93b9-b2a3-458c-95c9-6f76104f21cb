package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.app.DomainTag
import com.alibabacloud.devops.appstack.libs.model.request.TagBatchUnbindingRequest
import com.alibabacloud.devops.appstack.libs.model.request.TagSearchRequest
import com.alibabacloud.devops.appstack.libs.model.response.TagBatchOperationResponse
import com.alibabacloud.devops.appstack.libs.model.vo.DomainTagWithCreatorVO


interface DomainTagBizService {
    fun create(domainTag: DomainTag): DomainTag
    fun update(name: String, domainTag: DomainTag): DomainTag
    fun delete(name: String)
    fun list(req: TagSearchRequest, current: Long, pageSize: Long): PageList<DomainTagWithCreatorVO>
    fun listAppNamesByTag(tagName: String): List<String>
    fun listTagsByAppName(appName: String): List<DomainTag>
    fun listTagsByAppNames(appNames: List<String>): List<DomainTag>
    fun updateTagBindings(tagNames: List<String>, appName: String)
    fun bind(tagNames: List<String>, appName: String)
    fun batchBind(tagNames: List<String>, appNames: List<String>): TagBatchOperationResponse
    fun unbind(tagNames: List<String>, appName: String)
    fun batchUnbind(tagNames: List<String>, appNames: List<String>): TagBatchOperationResponse
}