package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.globalvar.GlobalVarRevisionContent
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.request.CreateGlobalVarRequest
import com.alibabacloud.devops.appstack.libs.model.request.SearchGlobalVarRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateGlobalVarContentRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateGlobalVarRequest
import com.alibabacloud.devops.appstack.libs.model.response.GlobalVarVO
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.ResourcePlayer
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest

/**
 * @author: <EMAIL>
 * @date: 2023-11-20 17:01
 * @version: GlobalVarBizService, v0.1
 **/
interface GlobalVarBizService {

    fun find(name: String): GlobalVarVO?

    fun listPaginated(current: Long, pageSize: Long, req: SearchGlobalVarRequest): Pagination<GlobalVarVO>

    fun create(req: CreateGlobalVarRequest): GlobalVarVO

    fun update(name: String, req: UpdateGlobalVarRequest): GlobalVarVO

    fun delete(name: String): Boolean

    fun findContent(name: String, revisionSha: String?): GlobalVarRevisionContent?

    fun findGlobalVarContentByTag(name: String, tag: String): GlobalVarRevisionContent

    fun updateContent(name: String, req: UpdateGlobalVarContentRequest): GlobalVarRevisionContent

    fun listContentRevisionPaginated(
        name: String,
        current: Long,
        pageSize: Long,
    ): Pagination<Revision>

    fun compare(
        name: String,
        beforeRevisionSha: String,
        afterRevisionSha: String,
    ): Pair<GlobalVarRevisionContent?, GlobalVarRevisionContent?>

    fun findAllMember(name: String): Map<ResourcePlayer, List<Role>>
    fun addRoleMember(name: String, role: Role, playerList: List<ResourcePlayerRequest>)
    fun updateRoleMember(name: String, role: Role, playerList: List<ResourcePlayerRequest>)
    fun transferOwner(name: String, player: ResourcePlayerRequest)
    fun updateMemberRole(
        name: String,
        subjectId: String,
        subjectType: SubjectType,
        roleNameList: List<String>,
    )

    fun listPaginatedCanUse(current: Long, pageSize: Long, req: SearchGlobalVarRequest): Pagination<GlobalVarVO>
}