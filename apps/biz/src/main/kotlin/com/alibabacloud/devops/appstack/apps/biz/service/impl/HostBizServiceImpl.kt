package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.DeployGroupBizService
import com.alibabacloud.devops.appstack.apps.biz.service.EnvBizService
import com.alibabacloud.devops.appstack.apps.biz.service.FlowHostBizService
import com.alibabacloud.devops.appstack.apps.biz.service.HostBizService
import com.alibabacloud.devops.appstack.apps.biz.service.ResourceBizService
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceInstance
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceItemProperty
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.toSimpleHost
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.FlowPurpose
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.FlowScopeType
import com.alibabacloud.devops.appstack.libs.model.constant.Default
import com.alibabacloud.devops.appstack.libs.model.request.FlowEcsQueryCondition
import com.alibabacloud.devops.appstack.libs.model.request.FlowExperienceQueryCondition
import com.alibabacloud.devops.appstack.libs.model.request.FlowSelfQueryCondition
import com.alibabacloud.devops.appstack.libs.model.request.QueryHostRequest
import com.alibabacloud.devops.appstack.libs.model.vo.HostVO
import com.aliyun.openservices.shade.io.netty.util.internal.StringUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2022-07-13 17:45
 * @version: HostBizServiceImpl, v0.1
 **/
@Service
class HostBizServiceImpl : HostBizService {

    @Autowired
    lateinit var flowHostBizService: FlowHostBizService

    @Autowired
    lateinit var resourceBizService: ResourceBizService

    @Autowired
    lateinit var resourceDeployGroupBizService: DeployGroupBizService

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var envBizService: EnvBizService

    override fun findAll(queryHostRequest: QueryHostRequest): List<HostVO> {
        val hostList = when (queryHostRequest.queryCondition) {
            is FlowEcsQueryCondition -> {
                val queryCondition = queryHostRequest.queryCondition as FlowEcsQueryCondition
                flowHostBizService.findEcs(
                    queryCondition.serviceConnectionId,
                    queryCondition.aliyunRegionId,
                    queryCondition.flowScopeType,
                    queryCondition.checkAllEcsMachine,
                    queryCondition.sns
                )
            }
            is FlowSelfQueryCondition -> {
                val queryCondition = queryHostRequest.queryCondition as FlowSelfQueryCondition
                flowHostBizService.findSelf(
                    queryCondition.purpose,
                    queryCondition.flowScopeType,
                    queryCondition.sns
                )
            }
            is FlowExperienceQueryCondition -> {
                val queryCondition = queryHostRequest.queryCondition as FlowExperienceQueryCondition
                flowHostBizService.findSelf(
                    FlowPurpose.DEPLOY_GROUP,
                    FlowScopeType.appStack,
                    queryCondition.sns
                )
            }
            else -> emptyList()
        }
        val instanceList = resourceBizService.findAllInstance(
            Default.POOLNAME,
            "${ResourceInstance.InstanceConfigType.ECS},${ResourceInstance.InstanceConfigType.FLOW_SELF_HOST}"
        )
        val nameToDisplayName = instanceList.associate { it.name to it.displayName }
        val hostSnToInstanceDisplayNameListMap = mutableMapOf<String, MutableList<String>>()
         instanceList.flatMap { it.resourceItemList }.forEach {
            val host = it.toSimpleHost(it.itemSpec)
            if(hostSnToInstanceDisplayNameListMap.containsKey(host.sn)){
                hostSnToInstanceDisplayNameListMap[host.sn]?.add(nameToDisplayName[it.instanceName]!!)
            }else{
                hostSnToInstanceDisplayNameListMap[host.sn] = mutableListOf(nameToDisplayName[it.instanceName]!!)
            }
        }

        val itemSnToHostSnMap = instanceList.flatMap { it.resourceItemList }.associate {
            it.sn to (it.toSimpleHost(it.itemSpec)).sn
        }
        val hostSnToGroupNameMap = mutableMapOf<String, MutableList<String>>()
        if (!StringUtil.isNullOrEmpty(queryHostRequest.instanceName)) {
            val deployGroupVOList =
                resourceDeployGroupBizService.findAllByInstance(Default.POOLNAME, queryHostRequest.instanceName!!)
            deployGroupVOList.forEach { deployGroupVO ->
                deployGroupVO.claimList.flatMap { it.itemSnList }.forEach { itemSn ->
                    val hostSn = itemSnToHostSnMap[itemSn]!!
                    if (hostSnToGroupNameMap.containsKey(hostSn)){
                        hostSnToGroupNameMap[hostSn]?.add(deployGroupVO.displayName + "(" + deployGroupVO.name + ")")
                    }else{
                        hostSnToGroupNameMap[hostSn] = mutableListOf(deployGroupVO.displayName+ "(" + deployGroupVO.name + ")")
                    }
                }
            }
        }

        val snToChangeOrderSnMap =
            if(!StringUtil.isNullOrEmpty(queryHostRequest.appName) && !StringUtil.isNullOrEmpty(queryHostRequest.envName)){
                val changeableEnvVO = envBizService.findChangeableEnv(queryHostRequest.appName!!, queryHostRequest.envName!!)
                val itemSnList = (changeableEnvVO?.deployGroup?.claimList?.first()?.itemSnList) ?: emptyList()
                coreFacades.resourceItemFacade.findBySns(itemSnList, ResourceItemProperty.fetchLatestChangeOrderTypeKey(queryHostRequest.appName!!, queryHostRequest.envName!!)).associate { itemSnToHostSnMap[it.resourceItemSn] to it.value }
            } else {  mutableMapOf()

            }
        return hostList.map {
            it.toVO(
                hostSnToInstanceDisplayNameListMap[it.sn]?.joinToString("|"),
                hostSnToGroupNameMap[it.sn]?.joinToString("|"),
                snToChangeOrderSnMap[it.sn]
            )
        }
    }
}