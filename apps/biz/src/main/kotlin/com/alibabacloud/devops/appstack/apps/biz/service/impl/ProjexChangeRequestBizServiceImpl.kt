package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.OrgConfigItemBizService
import com.alibabacloud.devops.appstack.apps.biz.service.ProjexChangeRequestBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.ChangeRequestMustBindWorkItemV1OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.response.cr.ProjexWorkItem
import com.alibabacloud.devops.appstack.libs.projex.spring.boot.starter.service.ProjexFacade
import com.alibabacloud.devops.appstack.libs.projex.spring.boot.starter.service.facade.WorkItemFacade
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class ProjexChangeRequestBizServiceImpl : ProjexChangeRequestBizService {

    @Autowired
    lateinit var projexFacade: ProjexFacade

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var orgConfigItemBizService: OrgConfigItemBizService

    override fun recommend(): List<ProjexWorkItem> {
        return projexFacade.workItemFacade.recommend()
    }

    override fun searchByNameOrId(query: String): List<ProjexWorkItem> {
        return projexFacade.workItemFacade.searchByNameOrId(query)
    }

    override fun searchByCrSn(appName: String, crSn: String): List<ProjexWorkItem> {
        return projexFacade.workItemFacade.searchByRelated(WorkItemFacade.RelatedTypeEnum.ChangeRequest, crSn)
    }

    override fun bindCrToWorkItem(workItemId: String, crSn: String): Boolean {
        val bindCrToWorkItem =
            projexFacade.workItemFacade.bindToWorkItem(workItemId, WorkItemFacade.RelatedTypeEnum.ChangeRequest, crSn)
        val changeRequest = workflowControllerFacade.changeRequestFacade.find(crSn)
        workflowControllerFacade.changeRequestFacade.update(changeRequest)
        return bindCrToWorkItem
    }

    override fun unbindCrToWorkItem(workItemId: String, crSn: String): Boolean {
        val orgConfigItem = orgConfigItemBizService.find(OrgConfigItem.CHANGE_REQUEST_MUST_BIND_WORKITEM)
        if ((orgConfigItem as? ChangeRequestMustBindWorkItemV1OrgConfigItem)?.enable == true) {
            val workItemList =
                projexFacade.workItemFacade.searchByRelated(WorkItemFacade.RelatedTypeEnum.ChangeRequest, crSn)
            if (workItemList.size <= 1) {
                throw BizException(ErrorCode.AS_CR_MUST_BIND_WORKITEM)
            }
        }
        val unbindCrToWorkItem =
            projexFacade.workItemFacade.unbindToWorkItem(workItemId, WorkItemFacade.RelatedTypeEnum.ChangeRequest, crSn)
        val changeRequest = workflowControllerFacade.changeRequestFacade.find(crSn)
        workflowControllerFacade.changeRequestFacade.update(changeRequest)
        return unbindCrToWorkItem
    }
}