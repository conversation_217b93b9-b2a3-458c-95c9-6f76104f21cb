package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.Label
import com.alibabacloud.devops.appstack.libs.model.constant.Default
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-03-21 16:39
 * @version: LabelBizApi, v0.1
 **/
@Tag(name = "Label", description = "标签相关 API")
@RequestMapping("/api/v1/labels")
@RestController
open class LabelBizApi {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Operation(summary = "查询标签")
    @GetMapping("/")
    fun findAllLable(
        @RequestParam(required = false, name = "namespace", defaultValue = Default.NAMESPACE) namespace: String,
        @RequestParam(required = false, name = "name") name: String?,
        @RequestParam(required = false, name = "value") value: String?,
    ): Response<List<Label>> {
        return Response.success(coreFacades.labelFacade.findAll(namespace, name, value))
    }
}