package com.alibabacloud.devops.appstack.apps.biz.config

import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateSyncWebSocketService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderCreateWebSocketService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderWebSocketService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestWebSocketService
import com.alibabacloud.devops.appstack.apps.biz.service.impl.AppTemplateSyncWebSocketServiceImpl
import com.alibabacloud.devops.appstack.apps.biz.service.impl.ChangeOrderCreateWebSocketServiceImpl
import com.alibabacloud.devops.appstack.apps.biz.service.impl.ChangeOrderWebSocketServiceImpl
import com.alibabacloud.devops.appstack.apps.biz.service.impl.ChangeRequestWebSocketServiceImpl
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.server.ServerHttpRequest
import org.springframework.http.server.ServerHttpResponse
import org.springframework.web.socket.WebSocketHandler
import org.springframework.web.socket.config.annotation.EnableWebSocket
import org.springframework.web.socket.config.annotation.WebSocketConfigurer
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry
import org.springframework.web.socket.handler.ExceptionWebSocketHandlerDecorator
import org.springframework.web.socket.server.HandshakeInterceptor


/**
 * <AUTHOR>
 * @date 2022-01-28
 */
@Configuration
@EnableWebSocket
open class WebSocketConfig : WebSocketConfigurer {

    companion object {
        val CHANGE_ORDER_SN_KEY = "changeOrderSn"
        val RELEASE_STAGE_SN_KEY = "releaseStageSn"
        val APP_NAME_KEY = "appName"
        val ENV_NAMES_KEY = "envNames"
        val ORG_ID_KEY = "orgId"
        val TRANSACTION_ID ="transactionId"

        val CHANGE_ORDER_PATH = "/ws/changeOrders"
        val RELEASE_STAGE_CR_PATH = "/ws/changeRequests"
        val ENV_OCCUPY_PATH = "/ws/envOccupy"
        val APP_TEMPLATE_SYNC_PROGRESS = "/ws/appTemplateSyncProgress"
    }

    @Value("\${devops.appstack.baseDomain}")
    var baseDomain: String = ""


    override fun registerWebSocketHandlers(registry: WebSocketHandlerRegistry) {
        logger.info("WebSocketConfig_baseDomain: ${baseDomain}")

        registry
            .addHandler(ExceptionWebSocketHandlerDecorator(changeOrderHandler()), CHANGE_ORDER_PATH)
            .addHandler(ExceptionWebSocketHandlerDecorator(changeRequestHandler()), RELEASE_STAGE_CR_PATH)
            .addHandler(ExceptionWebSocketHandlerDecorator(changeOrderCreateHandler()), ENV_OCCUPY_PATH)
            .addHandler(ExceptionWebSocketHandlerDecorator(appTemplateSyncHandler()), APP_TEMPLATE_SYNC_PROGRESS)
            .addInterceptors(WebSocketAuthInterceptor())
            .setAllowedOriginPatterns("*.${baseDomain}")
            .withSockJS()
            .setSupressCors(true)
    }

    @Bean
    open fun changeOrderHandler(): ChangeOrderWebSocketService {
        return ChangeOrderWebSocketServiceImpl()
    }

    @Bean
    open fun changeRequestHandler(): ChangeRequestWebSocketService {
        return ChangeRequestWebSocketServiceImpl()
    }

    @Bean
    open fun changeOrderCreateHandler(): ChangeOrderCreateWebSocketService {
        return ChangeOrderCreateWebSocketServiceImpl()
    }

    @Bean
    open fun appTemplateSyncHandler(): AppTemplateSyncWebSocketService {
        return AppTemplateSyncWebSocketServiceImpl()
    }

}

private class WebSocketAuthInterceptor : HandshakeInterceptor {

    override fun beforeHandshake(
        request: ServerHttpRequest,
        response: ServerHttpResponse,
        wsHandler: WebSocketHandler,
        attributes: MutableMap<String, Any>
    ): Boolean {
        val paramMap = getQueryMap(request.uri.query)
        if (request.uri.toString().contains(WebSocketConfig.CHANGE_ORDER_PATH, ignoreCase = true)) {
            val changeOrderSn = paramMap.get(WebSocketConfig.CHANGE_ORDER_SN_KEY)
            changeOrderSn?.let {
                attributes.put(WebSocketConfig.CHANGE_ORDER_SN_KEY, changeOrderSn)
                return true
            }
        } else if (request.uri.toString().contains(WebSocketConfig.RELEASE_STAGE_CR_PATH, ignoreCase = true)) {
            val releaseStageSn = paramMap.get(WebSocketConfig.RELEASE_STAGE_SN_KEY)
            releaseStageSn?.let {
                attributes.put(WebSocketConfig.RELEASE_STAGE_SN_KEY, releaseStageSn)
                return true
            }
        } else if (request.uri.toString().contains(WebSocketConfig.ENV_OCCUPY_PATH, ignoreCase = true)) {
            val appName = paramMap[WebSocketConfig.APP_NAME_KEY]
            val envName = paramMap[WebSocketConfig.ENV_NAMES_KEY]
            if (null != appName && null != envName) {
                attributes[WebSocketConfig.APP_NAME_KEY] = appName
                attributes[WebSocketConfig.ENV_NAMES_KEY] = envName
                attributes[WebSocketConfig.ORG_ID_KEY] = AuthUtil.getTenant()
                return true
            }
        }else if(request.uri.toString().contains(WebSocketConfig.APP_TEMPLATE_SYNC_PROGRESS, ignoreCase = true)){
            val transactionId = paramMap[WebSocketConfig.TRANSACTION_ID]
            if(null != transactionId){
                attributes[WebSocketConfig.TRANSACTION_ID] = transactionId
            }
            return true
        }
        return false
    }

    override fun afterHandshake(
        request: ServerHttpRequest,
        response: ServerHttpResponse,
        wsHandler: WebSocketHandler,
        exception: Exception?
    ) {
    }

    fun getQueryMap(query: String?): Map<String, String> {
        val params = query?.split("&")?.toTypedArray()
        val map: MutableMap<String, String> = HashMap()
        params?.let {
            for (param in params) {
                val p = param.split("=").toTypedArray()
                val name = p[0]
                if (p.size > 1) {
                    val value = p[1]
                    map[name] = value
                }
            }
        }
        return map
    }
}