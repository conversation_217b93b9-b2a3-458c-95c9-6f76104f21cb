package com.alibabacloud.devops.appstack.apps.biz.service.client.api

import com.alibabacloud.devops.appstack.apps.biz.model.JobLog
import com.alibabacloud.devops.appstack.libs.model.response.FlowApiResponse
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.HeaderMap
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

@RetrofitClient(baseUrl = "\${service.flow-execution.url}", callTimeoutMs = 20 * 1000)
interface FlowApi {

    @FormUrlEncoded
    @POST("executionJob/job")
    fun executeJob(
        @HeaderMap headers: Map<String, String>,
        @Field("params") params: String,
        @Field("buildSystem") buildSystem: String,
        @Field("callbackUrl") callBackUrl: String,
        @Field("callerName") callerName: String,
        @Field("timestamp") timestamp: String,
        @Field("sign") sign: String,
    ): FlowApiResponse<Long>

    @GET("executionJob/job/{jobId}/process/2")
    fun queryJobLogs(
        @HeaderMap headers: Map<String, String>,
        @Path("jobId") jobId: String,
        @Query("callerName") callerName: String,
        @Query("timestamp") timestamp: String,
        @Query("sign") sign: String,
    ): FlowApiResponse<JobLog>

}