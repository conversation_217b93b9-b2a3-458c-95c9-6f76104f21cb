package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.DeployGroupBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.DeployGroup
import com.alibabacloud.devops.appstack.libs.model.request.UpsertDeployGroupRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-08-02 10:59
 * @version: DeployGroupBizApi, v0.1
 **/
@Tag(name = "ResourceDeployGroup", description = "部署组相关 API")
@RestController
@RequestMapping("/api/v1")
open class ResourceDeployGroupBizApi {

    @Autowired
    lateinit var deployGroupBizService: DeployGroupBizService

    @Autowired
    private lateinit var coreFacades: CoreFacades

    @Operation(summary = "创建部署组")
    @PostMapping("/pools/{poolName}/deployGroups")
    fun createResourceDeployGroup(
        @PathVariable("poolName") poolName: String,
        @RequestBody upsertDeployGroupRequest: UpsertDeployGroupRequest
    ): Response<DeployGroup> {
        return Response.success(deployGroupBizService.create(poolName, upsertDeployGroupRequest))
    }

    @Operation(summary = "查找部署组")
    @GetMapping("/pools/{poolName}/deployGroups/{name}")
    fun findResourceDeployGroup(
        @PathVariable("poolName") poolName: String,
        @PathVariable("name") name: String
    ): Response<DeployGroup>{
        return Response.success(deployGroupBizService.find(poolName, name))
    }

    @Operation(summary = "更新部署组")
    @PutMapping("/pools/{poolName}/deployGroups/{name}")
    fun updateResourceDeployGroup(
        @PathVariable("poolName") poolName: String,
        @PathVariable("name") name: String,
        @RequestBody upsertDeployGroupRequest: UpsertDeployGroupRequest
    ): Response<DeployGroup>{
        return Response.success(deployGroupBizService.update(poolName, name, upsertDeployGroupRequest))
    }

    @Operation(summary = "删除部署组")
    @DeleteMapping("/pools/{poolName}/deployGroups/{name}")
    fun deleteResourceDeployGroup(
        @PathVariable("poolName") poolName: String,
        @PathVariable("name") name: String
    ): Response<Unit> {
        deployGroupBizService.delete(poolName, name)
        return Response.success()
    }

    @Operation(summary = "部署组关联环境")
    @GetMapping("/pools/{poolName}/deployGroups/{name}:findEnv")
    fun findRelatedEnvInGroup(
        @PathVariable("poolName") poolName: String,
        @PathVariable("name") name: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<Env>> {
        return Response.success(
            coreFacades.resourceFacade.findRelatedEnvInGroup(
                poolName,
                name,
                current,
                pageSize
            )
        )
    }

    @Operation(summary = "查找可用的部署组")
    @GetMapping("/pools/{poolName}/deployGroups:available")
    fun findAllDeployGroupByType(
        @PathVariable("poolName") poolName: String,
        @RequestParam type: String
    ): Response<List<DeployGroup>> {
        logger.info("findAllDeployGroupByType Deprecated log")
        return Response.success(deployGroupBizService.findAllByType(poolName, type))
    }

}

























