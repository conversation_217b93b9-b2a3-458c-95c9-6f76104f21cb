package com.alibabacloud.devops.appstack.apps.biz.model

/**
 * <AUTHOR>
 * @date 2023-11-09
 */
data class AppTemplateConfigReference(
    val type: TypeEnum,
    val id: String
) {
    enum class TypeEnum {
        RESOURCE_INSTANCE
    }

    companion object {
        fun convertResourceInstanceToReferenceId(resourcePoolName: String, deployGroupName: String) =
            "$resourcePoolName::$deployGroupName"
    }
}

data class AppTemplateNameAndConfig(
    val appTemplateName: String,
    val appTemplateConfigSn: String
)