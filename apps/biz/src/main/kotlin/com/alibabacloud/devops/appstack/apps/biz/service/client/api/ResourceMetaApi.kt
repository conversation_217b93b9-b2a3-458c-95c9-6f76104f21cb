package com.alibabacloud.devops.appstack.apps.biz.service.client.api

import com.alibabacloud.devops.appstack.libs.model.`do`.resource.DeployGroup
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.ResourceInstanceVO
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.HeaderMap
import retrofit2.http.PUT
import retrofit2.http.Path

/**
 * @author: <EMAIL>
 * @date: 2021-09-27 20:21
 * @version: AppApi, v0.1
 **/
@RetrofitClient(baseUrl = "http://appstack-core/api/v1/")
interface ResourceMetaApi {

    @GET("resources/pools/{poolName}/instances/{instanceName}")
    fun findInstance(
        @HeaderMap headers: Map<String, String>,
        @Path("poolName") poolName: String,
        @Path("instanceName") instanceName: String
    ): Response<ResourceInstanceVO>

    @GET("resources/pools/{poolName}/groups")
    fun findAllDeployGroup(
        @HeaderMap headers: Map<String, String>,
        @Path("poolName") poolName: String
    ): Response<List<DeployGroup>>

    @PUT("/api/v1/resources/pools/{poolName}/groups/{groupName}")
    fun updateDeployGroup(
        @HeaderMap headers: Map<String, String>,
        @Path("poolName") poolName: String,
        @Path("groupName") groupName: String,
        @Body deployGroup: DeployGroup
    ): Response<DeployGroup>
}