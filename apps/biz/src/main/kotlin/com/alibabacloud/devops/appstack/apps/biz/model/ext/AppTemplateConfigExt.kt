package com.alibabacloud.devops.appstack.apps.biz.model.ext

import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateConfigPO
import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateConfigSyncStatusPO
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil
import com.alibabacloud.devops.appstack.libs.common.util.UuidUtils
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AbstractConfiguration
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AbstractModeSetting
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfigSyncStatus
import org.apache.commons.lang3.StringUtils

/**
 * <AUTHOR> <EMAIL>
 * @version : AppTemplateConfigExt, v0.1
 * @date : 2023-10-17 11:08
 **/

fun AppTemplateConfigPO.toModel(configuration: AbstractConfiguration): AppTemplateConfig {
    return AppTemplateConfig(
        sn = sn!!,
        appTemplateName = appTemplateName!!,
        type = AppTemplateConfig.TypeEnum.valueOf(type!!),
        modeSetting = JacksonUtil.jacksonObjectMapper().readValue(modeSetting, AbstractModeSetting::class.java),
        configuration = configuration,
    )
}

fun AppTemplateConfig.toPO(configuration: String): AppTemplateConfigPO {
    val po = AppTemplateConfigPO()
    po.sn = sn ?: UuidUtils.getUuid()
    po.appTemplateName = appTemplateName
    po.type = type.name
    po.modeSetting = JacksonUtil.jacksonObjectMapper().writeValueAsString(modeSetting)
    po.configuration = configuration
    return po
}

fun AppTemplateConfigSyncStatusPO.toModel(): AppTemplateConfigSyncStatus {
    return AppTemplateConfigSyncStatus(
        appTemplateName = appTemplateName,
        appTemplateConfigType = AppTemplateConfig.TypeEnum.valueOf(appTemplateConfigType),
        appTemplateConfigInstanceName = appTemplateConfigInstanceName,
        appName = appName,
        lastInstanceUpdateSeqNo = lastInstanceUpdateSeqNo,
        templateSyncStatus = if (StringUtils.isNotBlank(templateSyncStatus))
            JacksonUtil.jacksonObjectMapper()
                .readValue(templateSyncStatus, AppTemplateConfigSyncStatus.TemplateSyncStatus::class.java) else null,
        instanceUpdateStatus = if (StringUtils.isNotBlank(instanceUpdateStatus))
            JacksonUtil.jacksonObjectMapper()
                .readValue(instanceUpdateStatus, AppTemplateConfigSyncStatus.InstanceUpdateStatus::class.java) else null,
    )
}

fun AppTemplateConfigSyncStatus.toPO(): AppTemplateConfigSyncStatusPO{
    val po = AppTemplateConfigSyncStatusPO()
    po.appTemplateName = appTemplateName
    po.appTemplateConfigType = appTemplateConfigType.name
    po.appTemplateConfigInstanceName = appTemplateConfigInstanceName
    po.appName = appName
    po.lastInstanceUpdateSeqNo = lastInstanceUpdateSeqNo
    po.templateSyncStatus = templateSyncStatus?.let { JacksonUtil.jacksonObjectMapper().writeValueAsString(it) }
    po.instanceUpdateStatus = instanceUpdateStatus?.let { JacksonUtil.jacksonObjectMapper().writeValueAsString(it) }
    return po
}