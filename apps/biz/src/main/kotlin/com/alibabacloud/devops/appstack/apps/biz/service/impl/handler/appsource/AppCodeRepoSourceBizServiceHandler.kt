package com.alibabacloud.devops.appstack.apps.biz.service.impl.handler.appsource

import com.alibabacloud.devops.appstack.apps.biz.service.AppCodeRepoBizService
import com.alibabacloud.devops.appstack.libs.model.request.CreateAppCodeRepoRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppCodeRepoRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.CreateAppCodeRepoSourceRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.CreateAppSourceRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateAppCodeRepoSourceRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateAppSourceRequest
import com.alibabacloud.devops.appstack.libs.model.vo.ng.AppSource
import com.alibabacloud.devops.appstack.libs.model.vo.ng.CodeRepoSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2023-10-12 10:53
 * @version: AppCodeRepoSourceBizServiceHandler, v0.1
 **/
@Service
class AppCodeRepoSourceBizServiceHandler : AppSourceBizServiceHandler {

    @Autowired
    private lateinit var appCodeRepoBizService: AppCodeRepoBizService

    override val type: String
        get() = AppSource.APP_SOURCE_CODE_REPO

    override fun create(appName: String, requestEntity: CreateAppSourceRequest): AppSource {
        val typedRequestEntity = requestEntity as CreateAppCodeRepoSourceRequest
        val appCodeRepo = appCodeRepoBizService.create(
            appName = appName,
            createAppCodeRepoRequest = CreateAppCodeRepoRequest(
                name = typedRequestEntity.name,
                repoUrl = typedRequestEntity.repoUrl,
                identifier = typedRequestEntity.identifier,
                repoContext = typedRequestEntity.repoContext,
                connectionConfig = typedRequestEntity.connectionConfig
            )
        )
        return CodeRepoSource.fromLegacyModel(appCodeRepo)
    }

    override fun update(appName: String, requestEntity: UpdateAppSourceRequest): AppSource {
        val typedRequestEntity = requestEntity as UpdateAppCodeRepoSourceRequest
        val targetCodeRepoSource = appCodeRepoBizService.find(appName = appName, sn = typedRequestEntity.sn)
        val appCodeRepo = appCodeRepoBizService.update(
            appName = appName,
            sn = typedRequestEntity.sn,
            updateAppCodeRepoRequest = UpdateAppCodeRepoRequest(
                name = typedRequestEntity.name ?: targetCodeRepoSource.name,
                repoUrl = typedRequestEntity.repoUrl ?: targetCodeRepoSource.repoUrl,
                identifier = typedRequestEntity.identifier ?: targetCodeRepoSource.identifier,
                repoContext = typedRequestEntity.repoContext ?: targetCodeRepoSource.repoContext,
                connectionConfig = typedRequestEntity.connectionConfig ?: targetCodeRepoSource.connectionConfig
            )
        )
        return CodeRepoSource.fromLegacyModel(appCodeRepo)
    }

    override fun delete(appName: String, sn: String): Boolean {
        appCodeRepoBizService.delete(appName = appName, sn = sn)
        return true
    }

}