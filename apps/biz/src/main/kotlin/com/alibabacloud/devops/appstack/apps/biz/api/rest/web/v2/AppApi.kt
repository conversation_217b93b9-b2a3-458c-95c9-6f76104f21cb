package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.apps.biz.service.AppBizService
import com.alibabacloud.devops.appstack.apps.biz.service.AppCodeRepoBizService
import com.alibabacloud.devops.appstack.apps.biz.service.AppSourceBizService
import com.alibabacloud.devops.appstack.apps.biz.service.PermissionService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.toStandardVo
import com.alibabacloud.devops.appstack.libs.model.request.AppRequest
import com.alibabacloud.devops.appstack.libs.model.request.CreateAppRequest
import com.alibabacloud.devops.appstack.libs.model.request.PaginationQuery
import com.alibabacloud.devops.appstack.libs.model.request.ng.AddMembersRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.ApplicationQuery
import com.alibabacloud.devops.appstack.libs.model.request.ng.CreateAppSourceRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateAppRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateMemberRequest
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import com.alibabacloud.devops.appstack.libs.model.vo.AppWithFunctionPointsVO
import com.alibabacloud.devops.appstack.libs.model.vo.ng.AppSource
import com.alibabacloud.devops.appstack.libs.model.vo.ng.Application
import com.alibabacloud.devops.appstack.libs.model.vo.ng.CodeRepoSource
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import com.aliyun.amp.plugin.annotation.AmpApi
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.api.annotations.ParameterObject
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2023-09-01 16:23
 * @version: AppApi, v0.1
 **/
@Tag(name = "应用", description = "应用领域 OpenAPI")
@RestController
@RequestMapping("/api/v2")
@ConditionalOnProperty(name = ["devops.iam.environment"], havingValue = "vpc", matchIfMissing = false)
open class AppApi {

    @Autowired
    lateinit var appBizService: AppBizService

    @Autowired
    lateinit var appCodeRepoBizService: AppCodeRepoBizService

    @Autowired
    lateinit var appSourceBizService: AppSourceBizService

    @Autowired
    lateinit var permissionService: PermissionService

    @VpcApi
    @Operation(summary = "创建应用", operationId = "CreateApplication")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "创建应用成功"),
            ApiResponse(
                responseCode = "409",
                description = "应用重名",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "422",
                description = "未指定应用名",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "应用名非法 / 应用创建失败 / 服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/apps")
    fun createApplication(@RequestBody req: CreateAppRequest): Application {
        val app = appBizService.create(
            AppRequest(
                name = req.name,
                description = req.description,
                ownerId = req.ownerId,
                appTemplateName = req.appTemplateName
            )
        )
        return app.toStandardVo(appBizService.findTemplate(app.name))
    }

    @VpcApi
    @Operation(summary = "更新应用", operationId = "UpdateApplication")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "更新成功"),
            ApiResponse(
                responseCode = "404",
                description = "未查找到指定的应用",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PutMapping("/apps/{appName}")
    fun updateApplication(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") name: String,
        @RequestBody req: UpdateAppRequest,
    ): Application {
        // 预校验一次
        appBizService.find(name) ?: throw BizException(ErrorCode.AS_APP_NOT_FOUND)

        if (req.ownerId != null) {
            appBizService.transferOwner(
                name,
                ResourcePlayerRequest.builder()
                    .id(req.ownerId)
                    .type(SubjectType.User)
                    .build()
            )
        }
        return appBizService.find(name)?.toStandardVo(null) ?: throw BizException(ErrorCode.AS_APP_NOT_FOUND)
    }

    @VpcApi
    @Operation(summary = "查找应用详情", operationId = "GetApplication")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已按名称查找到应用详情"),
            ApiResponse(
                responseCode = "404",
                description = "未查找到应用详情",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}")
    fun findApplication(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") name: String,
    ): Application {
        val app = appBizService.find(name = name) ?: throw BizException(errorCode = ErrorCode.AS_APP_NOT_FOUND)
        val findTemplate = appBizService.findTemplate(name)
        return app.toStandardVo(findTemplate)
    }

    @VpcApi
    @Operation(summary = "分页查找应用详情", operationId = "ListApplications")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "分页查找完成（包括未查找到对象的情况）"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps:search")
    fun listApplications(@ParameterObject query: ApplicationQuery): PageList<Application> {
        val apps = appBizService.list(query)
        return PageList(
            nextToken = apps.nextToken,
            data = apps.data.map { it.toStandardVo(appBizService.findTemplate(it.name)) })
    }

    @VpcApi
    @Operation(summary = "分页查找应用源详情", operationId = "ListApplicationSources")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "分页查找完成（包括未查找到对象的情况）"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/sources")
    fun listAppSources(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @ParameterObject query: PaginationQuery,
    ): PageList<AppSource> {
        val legacyCodeRepoSources = appCodeRepoBizService.list(appName, query)
        return PageList(
            nextToken = legacyCodeRepoSources.nextToken,
            data = legacyCodeRepoSources.data.map { CodeRepoSource.fromLegacyModel(it) }
        )
    }

    @VpcApi
    @Operation(summary = "创建应用源", operationId = "CreateApplicationSource")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "成功创建应用源"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/apps/{appName}/sources")
    fun createAppSource(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @RequestBody requestEntity: CreateAppSourceRequest,
    ): AppSource {
        return appSourceBizService.create(appName, requestEntity)
    }

    @VpcApi
    @Operation(summary = "查找应用成员列表", operationId = "ListApplicationMembers")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "分页查找完成（包括未查找到对象的情况）"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/members")
    fun listAppMembers(
        @Schema(
            description = "应用名",
            example = "my-demo-app",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @Schema(description = "当前页号（从 1 开始，默认取 1）", example = "1", type = "integer", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @Schema(description = "分页记录数（默认 10 条）", example = "10", type = "integer", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Pagination<MemberVO> {
        return appBizService.listMembers(appName = appName, current = current, pageSize = pageSize)
    }

    @VpcApi
    @Operation(summary = "添加应用成员", operationId = "CreateAppMembers")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "添加应用成员成功"),
            ApiResponse(responseCode = "403", description = "没权限设置拥有者"),
            ApiResponse(responseCode = "404", description = "角色未找到"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/apps/{appName}/members")
    fun createAppMembers(
        @Schema(
            description = "应用名",
            example = "my-demo-app",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @RequestBody addMembersRequest: AddMembersRequest,
    ): Boolean {
        addMembersRequest.roleNames.forEach {
            appBizService.addRoleMember(
                appName = appName,
                roleName = it,
                playerList = addMembersRequest.playerList,
            )
        }
        return true
    }

    @VpcApi
    @Operation(summary = "删除应用成员", operationId = "DeleteAppMember")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "删除应用成员成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @DeleteMapping("/apps/{appName}/members")
    fun deleteAppMember(
        @Schema(
            description = "应用名",
            example = "my-demo-app",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @Schema(
            description = "成员类型",
            example = "User",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @RequestParam subjectType: SubjectType,
        @Schema(description = "成员id", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @RequestParam subjectId: String,
    ): Boolean {
        appBizService.updateMemberRole(
            appName = appName,
            subjectType = subjectType,
            subjectId = subjectId,
            roleNameList = emptyList()
        )
        return true
    }

    @VpcApi
    @Operation(summary = "更新应用成员", operationId = "UpdateAppMember")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "更新应用成员角色成功"),
            ApiResponse(responseCode = "404", description = "应用角色未找到"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PutMapping("/apps/{appName}/members")
    fun updateAppMemberRole(
        @Schema(
            description = "应用名",
            example = "my-demo-app",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @RequestBody updateMemberRequest: UpdateMemberRequest,
    ): Boolean {
        appBizService.updateMemberRole(
            appName = appName,
            subjectType = updateMemberRequest.player.type,
            subjectId = updateMemberRequest.player.id,
            roleNameList = updateMemberRequest.roleNames
        )
        return true
    }
}

fun AppWithFunctionPointsVO.toStandardVo(): Application {
    return Application(
        name = this.name,
        gmtCreate = this.gmtCreate!!,
        creatorId = this.creatorId!!,
        description = this.description
    )
}