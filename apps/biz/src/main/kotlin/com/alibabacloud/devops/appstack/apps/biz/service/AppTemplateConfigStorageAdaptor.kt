package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.apps.biz.model.mapper.AppTemplateConfigMapper
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.deleteByNameAndType
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.find
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.update
import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateConfigPO
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil.jacksonObjectMapper
import com.alibabacloud.devops.appstack.libs.common.util.UuidUtils
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AbstractConfiguration
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AbstractModeSetting
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.AbstractConfigurationUpsertRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateConfigRequest
import org.springframework.beans.factory.annotation.Autowired

/**
 * <AUTHOR> <EMAIL>
 * @version : AppTemplateConfigService, v0.1
 * @date : 2023-10-18 11:45
 **/
abstract class AppTemplateConfigStorageAdaptor<T : AbstractConfiguration> {

    @Autowired
    lateinit var appTemplateConfigMapper: AppTemplateConfigMapper


    open fun find(appTemplateName: String): AppTemplateConfig? {
        val po = appTemplateConfigMapper.find(appTemplateName, configType().name) ?: return null
        return convertToModel(po)
    }

    open fun insert(appTemplate: AppTemplate, request: UpsertAppTemplateConfigRequest): AppTemplateConfig {
        checkExists(request.configuration) { ErrorCode.AS_BAD_REQUEST }
        checkExists(request.modeSetting) { ErrorCode.AS_BAD_REQUEST }
        val po = AppTemplateConfigPO().apply {
            this.appTemplateName = appTemplate.name
            this.type = configType().name
            this.modeSetting = jacksonObjectMapper().writeValueAsString(request.modeSetting)
            this.configuration =
                jacksonObjectMapper().writeValueAsString(convertConfiguration(appTemplate.name, request.configuration!!))
        }
        po.sn = UuidUtils.getUuid()
        appTemplateConfigMapper.insert(po)
        logAudit(appTemplate)
        return convertToModel(po)
    }

    open fun update(
        appTemplate: AppTemplate,
        previous: AppTemplateConfig,
        request: UpsertAppTemplateConfigRequest
    ): AppTemplateConfig {
        val previousPO = appTemplateConfigMapper.find(previous.appTemplateName, configType().name)
        checkExists(previousPO) { ErrorCode.AS_APP_TEMPLATE_CONFIG_NOT_FOUND }
        request.modeSetting?.let {
            previousPO.modeSetting = jacksonObjectMapper().writeValueAsString(it)
        }
        request.configuration?.let {
            val configuration = convertConfiguration(previous.appTemplateName, it)
            previousPO.configuration = jacksonObjectMapper().writeValueAsString(configuration)
        }
        appTemplateConfigMapper.update(previousPO)
        logAudit(appTemplate)
        return find(previous.appTemplateName)!!
    }

    open fun delete(appTemplate: AppTemplate, appTemplateConfigSn: String) {
        appTemplateConfigMapper.deleteByNameAndType(appTemplate.name, configType().name)
        logAudit(appTemplate)
    }

    abstract fun convertToModel(appTemplateConfigPO: AppTemplateConfigPO): AppTemplateConfig
    abstract fun convertConfiguration(appTemplateName: String, configuration: AbstractConfigurationUpsertRequest): Any
    abstract fun configType(): AppTemplateConfig.TypeEnum
    abstract fun logAudit(appTemplate: AppTemplate)
}