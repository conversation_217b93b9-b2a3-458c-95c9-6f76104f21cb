package com.alibabacloud.devops.appstack.apps.biz.aspect

import com.alibabacloud.devops.appstack.apps.biz.service.UserService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.constant.LoginType
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.model.vo.VO
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @date 2024-05-20
 */
@Aspect
@Slf4k
@Component
open class AmpApiAspect {

    @Autowired
    private lateinit var userService: UserService

    @Around("@annotation(com.aliyun.amp.plugin.annotation.AmpApi)")
    fun around(joinPoint: ProceedingJoinPoint): Any? {
        val result = joinPoint.proceed()
        val loginType = AuthThreadContext.getLoginType()
        if(LoginType.POP == loginType){
            if(result is VO){
                result.convert2AliyunPopObject(userService::getAliyunOpenIdByUserId)
            }else if (result is List<*>){
                result.forEach {
                    if(it is VO){
                        it.convert2AliyunPopObject(userService::getAliyunOpenIdByUserId)
                    }
                }
            }
        }
        return result
    }
}