package com.alibabacloud.devops.appstack.apps.biz.model.mapper

import com.alibabacloud.devops.appstack.apps.biz.model.po.OrgConfigItemPO
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.core.mapper.BaseMapper
import org.apache.ibatis.annotations.Mapper

/**
 * <AUTHOR>
 * @create 2023/12/21 9:45 PM
 **/
@Mapper
interface OrgConfigItemMapper: BaseMapper<OrgConfigItemPO>

fun OrgConfigItemMapper.find(name: String, version: String) =
    selectOne(QueryWrapper<OrgConfigItemPO>().eq("name", name).eq("version", version))

fun OrgConfigItemMapper.update(orgConfigItemPO: OrgConfigItemPO) =
    update(
        orgConfigItemPO,
        QueryWrapper<OrgConfigItemPO>()
            .eq("name", orgConfigItemPO.name)
            .eq("version", orgConfigItemPO.version)
    )