package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.StageBaseService
import com.alibabacloud.devops.appstack.apps.biz.service.WorkflowBaseService
import com.alibabacloud.devops.appstack.apps.biz.service.StageRuleConfigBaseService
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.StageRuleConfig
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.org.event.ReleaseStageAudit
import com.alibabacloud.devops.appstack.libs.model.request.temp.StageRuleConfigRequest
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
open class StageRuleConfigBaseServiceImpl : StageRuleConfigBaseService {

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var releaseWorkflowBaseService: WorkflowBaseService

    @Autowired
    lateinit var stageBaseService: StageBaseService

    @Autowired
    lateinit var auditLogService: AuditLogService


    override fun find(appName: String, releaseWorkflowSn: String, releaseStageSn: String): StageRuleConfig? {
        return workflowControllerFacade.stageRuleConfigFacade.findWithCheck(appName, releaseWorkflowSn, releaseStageSn)
    }

    override fun upsert(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        stageRuleConfigRequest: StageRuleConfigRequest
    ): StageRuleConfig? {
        val releaseWorkflow = releaseWorkflowBaseService.find(appName = appName, sn = releaseWorkflowSn)
        val releaseStage = stageBaseService.find(appName, releaseWorkflowSn, releaseStageSn)
        val stageRuleConfig = workflowControllerFacade.stageRuleConfigFacade.upsert(
            releaseWorkflowSn,
            releaseStageSn,
            stageRuleConfigRequest
        )
        val audit = ReleaseStageAudit(
            target = AuditTarget(id = releaseWorkflowSn, name = releaseWorkflow.name ?: releaseWorkflowSn),
            appName = appName,
            releaseStage = releaseStage.name
        )
        auditLogService.log(OrgEventType.RELEASE_WORKFLOW_STAGE_RULE_MODIFY, audit)
        return stageRuleConfig
    }
}