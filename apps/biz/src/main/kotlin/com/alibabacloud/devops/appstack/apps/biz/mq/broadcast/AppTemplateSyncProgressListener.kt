package com.alibabacloud.devops.appstack.apps.biz.mq.broadcast

import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateSyncWebSocketService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.BroadcastListener
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.AppTemplateSyncProgressBody
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.BroadcastTagEnum
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2024-02-29
 */
@Slf4k
@Service
class AppTemplateSyncProgressListener : BroadcastListener {

    @Autowired
    lateinit var appTemplateSyncWebSocketService: AppTemplateSyncWebSocketService

    override fun expectedTag(): BroadcastTagEnum {
        return BroadcastTagEnum.APP_TEMPLATE_SYNC_PROGRESS
    }

    override fun handle(body: ByteArray) {
        logger.info("Received broadcast content: ${String(body)}")
        val content = jacksonObjectMapper().readValue(body, AppTemplateSyncProgressBody::class.java)
        AuthThreadContext.setTenant(content.tenant)
        AuthThreadContext.setUserId(content.operator)
        appTemplateSyncWebSocketService.syncProgress(transactionId = content.transactionId, progress = content.progress)
    }

}