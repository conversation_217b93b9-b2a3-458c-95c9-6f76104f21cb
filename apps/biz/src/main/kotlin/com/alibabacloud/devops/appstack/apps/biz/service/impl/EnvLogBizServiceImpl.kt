package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.EnvLogBizService
import com.alibabacloud.devops.appstack.apps.biz.service.EnvLogChangeOrderAdapterFactory
import com.alibabacloud.devops.appstack.libs.change.controller.spring.boot.starter.service.ChangeControllerFacades
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.constant.OpLogConstants
import com.alibabacloud.devops.appstack.libs.model.constant.OpLogI18nCodeEnum
import com.alibabacloud.devops.appstack.libs.model.constant.buildEnvTargetSn
import com.alibabacloud.devops.appstack.libs.model.event.OpLog
import com.alibabacloud.devops.appstack.libs.model.event.OpLogCode
import com.alibabacloud.devops.appstack.libs.model.request.OpLogRequest
import com.alibabacloud.devops.appstack.libs.model.request.SearchOpLogRequest
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderContext
import com.alibabacloud.devops.appstack.libs.model.vo.EnvLogVO
import com.alibabacloud.devops.appstack.libs.model.vo.JobContext
import com.alibabacloud.devops.appstack.libs.operation.log.spring.boot.starter.service.OperationLogFacades
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.util.StringUtils
import java.util.Objects

/**
 * @author: <EMAIL>
 * @date: 2022-03-03 16:04
 * @version: EnvLogBizServiceImpl, v0.1
 **/
@Service
class EnvLogBizServiceImpl : EnvLogBizService {

    @Autowired
    lateinit var operationLogFacades: OperationLogFacades

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var changeControllerFacades: ChangeControllerFacades

    @Autowired
    lateinit var envLogChangeOrderAdapterFactory: EnvLogChangeOrderAdapterFactory

    override fun recordCreateEnvAction(env: Env): EnvLogVO {
        val opLogRequest = OpLogRequest(
            operator = AuthUtil.getUserId(),
            action = OpLog.Action.ENV_CREATE,
            targetType = OpLog.TargetType.ENV,
            targetSn = buildEnvTargetSn(env.appName, env.name),
            state = OpLog.State.SUCCESS,
            codeList = listOf(OpLogCode(OpLogI18nCodeEnum.CREATE_ENV_SUCCESS.value, getCreateEnvParams(env))),
            context = mapOf()
        )
        return convertToEnvLogVO(operationLogFacades.opLogFacade.create(opLogRequest))
    }

    override fun recordLockEnvAction(env: Env, isLock: Boolean): EnvLogVO {
        val action = if (isLock) OpLog.Action.ENV_LOCK else OpLog.Action.ENV_UNLOCK
        val i18nCode =
            if (isLock) OpLogI18nCodeEnum.LOCK_ENV_SUCCESS.value else OpLogI18nCodeEnum.UNLOCK_ENV_SUCCESS.value
        val opLogRequest = OpLogRequest(
            operator = AuthUtil.getUserId(),
            action = action,
            targetType = OpLog.TargetType.ENV,
            targetSn = buildEnvTargetSn(env.appName, env.name),
            state = OpLog.State.SUCCESS,
            codeList = listOf(OpLogCode(i18nCode = i18nCode)),
            context = mapOf()
        )
        return convertToEnvLogVO(operationLogFacades.opLogFacade.create(opLogRequest))
    }


    override fun recordUpdateEnvAction(env: Env, previousEnv: Env): EnvLogVO? {
        val codeList: List<OpLogCode> = buildUpdateEnvCodeList(previousEnv, env)
        if (codeList.isEmpty()) {
            return null
        }

        val opLogRequest = OpLogRequest(
            operator = AuthUtil.getUserId(),
            action = OpLog.Action.ENV_UPDATE,
            targetType = OpLog.TargetType.ENV,
            targetSn = buildEnvTargetSn(env.appName, env.name),
            state = OpLog.State.SUCCESS,
            codeList = codeList,
            context = mapOf()
        )
        return convertToEnvLogVO(operationLogFacades.opLogFacade.create(opLogRequest))
    }

    override fun recordDeleteEnvAction(
        env: Env,
        changeOrderContext: ChangeOrderContext?
    ): EnvLogVO {
        val context = mutableMapOf<String, Any>()
        changeOrderContext?.let {
            context.put(OpLogConstants.CONTEXT_CHANGE_ORDER, it)
        }
        val opLogRequest = OpLogRequest(
            operator = AuthUtil.getUserId(),
            action = OpLog.Action.ENV_DELETE,
            targetType = OpLog.TargetType.ENV,
            targetSn = buildEnvTargetSn(env.appName, env.name),
            state = OpLog.State.SUCCESS,
            codeList = listOf(OpLogCode(i18nCode = OpLogI18nCodeEnum.DELETE_ENV_SUCCESS.value)),
            context = context
        )
        return convertToEnvLogVO(operationLogFacades.opLogFacade.create(opLogRequest))
    }

    override fun recordCreateChangeOrderAction(changeOrder: ChangeOrder): List<EnvLogVO> {
        val envJobsMap = changeOrder.jobs.groupBy { it.envName }
        return envJobsMap.keys.mapNotNull {
            recordEnvChangeOrderAction(changeOrder.appName, it, changeOrder)
        }
    }

    override fun findPaginated(
        searchOpLogRequest: SearchOpLogRequest,
        current: Long,
        pageSize: Long
    ): Pagination<EnvLogVO> {
        val pagination = operationLogFacades.opLogFacade.findPaginated(
            searchOpLogRequest,
            current,
            pageSize
        )
        return Pagination(
            total = pagination.total,
            current = pagination.current,
            pageSize = pagination.pageSize,
            records = pagination.records.map {
                updateContext(it)
                convertToEnvLogVO(it)
            }
        )
    }

    private fun getCreateEnvParams(env: Env): List<String> {
        val envName = env.name
        val envDisplayName = env.displayName
        val profileName = buildProfileDisplayName(env)
        val labels =
            if (env.labelList.isNotEmpty()) env.labelList.joinToString(separator = OpLogConstants.LABEL_OUTER_SEPERATOR) { it.displayName + OpLogConstants.LABEL_INNER_SEPERATOR + it.displayValue } else OpLogConstants.DEFAULT_VALUE
        val description = env.description ?: OpLogConstants.DEFAULT_VALUE
        return listOf(
            envName,
            envDisplayName,
            fetchResourceDisplayName(env.resourcePoolName, env.deployGroupName),
            profileName,
            labels,
            description
        )
    }

    private fun updateContext(opLog: OpLog): OpLog {
        val contextMap = mutableMapOf<String, Any>()
        val changeOrderContextContent = opLog.context[OpLogConstants.CONTEXT_CHANGE_ORDER]
        changeOrderContextContent?.let { context ->
            val changeOrderContext = jacksonObjectMapper().readValue(
                jacksonObjectMapper().writeValueAsString(context),
                ChangeOrderContext::class.java
            )
            // retrieve latest state (if changeOrder is not finish)
            val previousChangeOrderState = changeOrderContext.state
            if (!previousChangeOrderState.isFinish()) {
                changeControllerFacades.changeOrderFacade.find(changeOrderContext.sn)?.let { changeOrder ->
                    val job =
                        changeOrder.jobs.first { it.name == changeOrderContext.job.name && it.type == changeOrderContext.job.type }
                    changeOrderContext.state = changeOrder.state
                    changeOrderContext.job.state = job.state
                    job.nextJob?.let {
                        changeOrderContext.job.nextJob = JobContext(
                            name = it.name,
                            sn = it.sn,
                            type = it.type,
                            state = it.state
                        )
                    }
                    opLog.context[OpLogConstants.CONTEXT_CHANGE_ORDER] = changeOrderContext
                    return operationLogFacades.opLogFacade.update(opLog)
                }
            }
            contextMap.put(OpLogConstants.CONTEXT_CHANGE_ORDER, changeOrderContext)
        }
        return opLog
    }

    private fun convertToEnvLogVO(opLog: OpLog): EnvLogVO {
        val contextMap = mutableMapOf<String, Any>()
        val changeOrderContextContent = opLog.context[OpLogConstants.CONTEXT_CHANGE_ORDER]
        changeOrderContextContent?.let {
            contextMap[OpLogConstants.CONTEXT_CHANGE_ORDER] = jacksonObjectMapper().readValue(
                jacksonObjectMapper().writeValueAsString(it), ChangeOrderContext::class.java
            )
        }

        return EnvLogVO(
            gmtCreate = opLog.gmtCreate,
            operator = opLog.operator,
            action = opLog.action,
            state = opLog.state,
            message = opLog.message!!,
            context = contextMap
        )
    }

    private fun recordEnvChangeOrderAction(appName: String, envName: String, changeOrder: ChangeOrder): EnvLogVO? {
        val action: OpLog.Action
        val codeList = mutableListOf<OpLogCode>()
        val job = changeOrder.jobs.first {
            it.envName == envName
        }
        when (changeOrder.type) {
            ChangeOrder.Type.Deploy -> {
                action = OpLog.Action.ENV_DEPLOY
                codeList.add(
                    OpLogCode(
                        i18nCode = OpLogI18nCodeEnum.ENV_DEPLOY.value,
                        params = listOf(changeOrder.version)
                    )
                )
            }
            ChangeOrder.Type.Rollback -> {
                action = OpLog.Action.ENV_ROLLBACK
                codeList.add(
                    OpLogCode(
                        i18nCode = OpLogI18nCodeEnum.ENV_ROLLBACK.value,
                        params = listOf(changeOrder.version)
                    )
                )
            }
            ChangeOrder.Type.Scale -> {
                action = OpLog.Action.ENV_SCALE
                codeList.addAll(
                    changeOrder.jobs.filter {
                        it.envName == envName
                    }.flatMap {
                        it.stages
                    }.flatMap {
                        it.tasks
                    }.flatMap {
                        envLogChangeOrderAdapterFactory.build(it.engineType, it.resourceType).buildScaleLog(it)
                    }
                )
            }
            else -> {
                logger.error("Unsupport record env action for ChangeOrder.Type[${changeOrder.type}]")
                return null
            }
        }

        val opLogRequest = OpLogRequest(
            operator = AuthUtil.getUserId(),
            action = action,
            targetType = OpLog.TargetType.ENV,
            targetSn = buildEnvTargetSn(appName, envName),
            state = OpLog.State.SUCCESS,
            codeList = codeList,
            context = mapOf(
                OpLogConstants.CONTEXT_CHANGE_ORDER to ChangeOrderContext(
                    name = changeOrder.name,
                    sn = changeOrder.sn!!,
                    state = changeOrder.state,
                    type = changeOrder.type,
                    job = JobContext(
                        name = job.name,
                        sn = job.sn!!,
                        state = job.state,
                        type = job.type
                    )
                )
            )
        )
        return convertToEnvLogVO(operationLogFacades.opLogFacade.create(opLogRequest))
    }

    private fun buildUpdateEnvCodeList(previousEnv: Env, env: Env): List<OpLogCode> {
        val codeList = mutableListOf<OpLogCode>()

        if (!Objects.equals(previousEnv.displayName, env.displayName)) {
            codeList.add(OpLogCode(i18nCode = OpLogI18nCodeEnum.UPDATE_ENV_DISPLAY_NAME.value, listOf(env.displayName)))
        }

        if (previousEnv.deployGroupName != env.deployGroupName && StringUtils.hasText(env.deployGroupName)) {

            codeList.add(
                OpLogCode(
                    i18nCode = OpLogI18nCodeEnum.UPDATE_ENV_DEPLOY_GROUP.value,
                    params = listOf(fetchResourceDisplayName(env.resourcePoolName, env.deployGroupName))
                )
            )
        }

        val beforeLabels =
            previousEnv.labelList.map { it.name + OpLogConstants.LABEL_INNER_SEPERATOR + it.value }.sorted()
                .joinToString(separator = ",")
        val labels = env.labelList.map { it.name + OpLogConstants.LABEL_INNER_SEPERATOR + it.value }.sorted()
            .joinToString(separator = ",")
        if (StringUtils.hasText(beforeLabels) && !StringUtils.hasText(labels)) {
            codeList.add(OpLogCode(i18nCode = OpLogI18nCodeEnum.DELETE_ENV_LABEL.value))
        } else if (!Objects.equals(beforeLabels, labels)) {
            val newLabels =
                if (env.labelList.isNotEmpty()) env.labelList.joinToString(separator = OpLogConstants.LABEL_OUTER_SEPERATOR) { it.displayName + OpLogConstants.LABEL_INNER_SEPERATOR + it.displayValue } else OpLogConstants.DEFAULT_VALUE
            codeList.add(OpLogCode(i18nCode = OpLogI18nCodeEnum.UPDATE_ENV_LABEL.value, listOf(newLabels)))
        }

        if (StringUtils.hasText(previousEnv.description) && !StringUtils.hasText(env.description)) {
            codeList.add(OpLogCode(i18nCode = OpLogI18nCodeEnum.DELETE_ENV_DESCRIPTION.value))
        } else if (!Objects.equals(previousEnv.description, env.description)) {
            codeList.add(
                OpLogCode(
                    i18nCode = OpLogI18nCodeEnum.UPDATE_ENV_DESCRIPTION.value,
                    listOf(env.description ?: "")
                )
            )
        }

        if (!previousEnv.profiles.isNullOrEmpty() && env.profiles.isNullOrEmpty()) {
            codeList.add(OpLogCode(i18nCode = OpLogI18nCodeEnum.DELETE_ENV_PROFILE.value))
        } else if (!Objects.equals(previousEnv.profiles, env.profiles)) {
            codeList.add(
                OpLogCode(
                    i18nCode = OpLogI18nCodeEnum.UPDATE_ENV_PROFILE.value,
                    listOf(buildProfileDisplayName(env))
                )
            )
        }
        return codeList
    }

    private fun fetchResourceDisplayName(resourcePoolName: String?, deployGroupName: String?): String {
        val deployGroup = if (resourcePoolName.isNullOrBlank() || deployGroupName.isNullOrBlank()) {
            null
        } else {
            coreFacades.deployGroupFacade.find(resourcePoolName, deployGroupName)
        }
        return if (deployGroup != null) "${deployGroup.displayName}[${deployGroupName}]" else OpLogConstants.DEFAULT_VALUE
    }

    private fun buildProfileDisplayName(env: Env): String {
        return if (env.profiles.isNullOrEmpty()) {
            OpLogConstants.DEFAULT_VALUE
        } else {
            env.profiles!!.map { it.displayName?:"" }.joinToString("、")
        }
    }

}