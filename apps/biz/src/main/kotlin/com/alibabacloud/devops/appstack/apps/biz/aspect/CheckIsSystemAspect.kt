package com.alibabacloud.devops.appstack.apps.biz.aspect

import com.alibabacloud.devops.appstack.apps.biz.annotation.IsSystem
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.model.ErrorEntry
import com.alibabacloud.devops.appstack.libs.common.util.checkBizWithEntry
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppType
import org.aspectj.lang.JoinPoint
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.annotation.Before
import org.aspectj.lang.reflect.MethodSignature
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.annotation.Order
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @date 2023-11-10
 */
@Aspect
@Slf4k
@Component
@Order(2)
open class  CheckIsSystemAspect {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Before("@annotation(com.alibabacloud.devops.appstack.apps.biz.annotation.IsSystem)")
    fun preCheck(joinPoint: JoinPoint) {
        val signature = joinPoint.signature as MethodSignature
        val method = signature.method
        val annotation: IsSystem = method.getAnnotation(IsSystem::class.java)
        val systemName = joinPoint.args[annotation.systemNameIndex] as String
        val system = coreFacades.appFacade.find(systemName)
        checkBizWithEntry(system?.type == AppType.SYSTEM){
            ErrorEntry(code = ErrorCode.AS_SYSTEM_NOT_FOUND)
        }
    }
}