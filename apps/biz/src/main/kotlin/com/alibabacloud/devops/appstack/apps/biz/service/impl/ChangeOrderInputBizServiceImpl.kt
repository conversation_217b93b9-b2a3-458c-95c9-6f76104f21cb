package com.alibabacloud.devops.appstack.apps.biz.service.impl


import com.alibabacloud.devops.appstack.apps.biz.model.ext.ChangeOrderInputPatchValueInfoDTO
import com.alibabacloud.devops.appstack.apps.biz.model.vo.DeliveryDiffInfo
import com.alibabacloud.devops.appstack.apps.biz.model.vo.DeployDiffInfo
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderInputBizService
import com.alibabacloud.devops.appstack.libs.change.controller.spring.boot.starter.service.ChangeControllerFacades
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.constant.SystemVariable
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.AppOrchestrationSource
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderForm
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderInput
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderInputForm
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.Orchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppBuiltInOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Placeholder
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateChangeOrderInputRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateDeployInputRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateDestroyInputRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateRollbackInputRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateScaleInputRequest
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderCheckVO
import com.alibabacloud.devops.appstack.libs.model.vo.EnvRecord
import com.alibabacloud.devops.appstack.resource.manager.spring.boot.starter.ResourceManagerFacades
import org.springframework.aop.framework.AopContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2022-03-21 22:20
 * @version: ChangeOrderInputBizServiceImpl, v0.1
 **/
@Service
open class ChangeOrderInputBizServiceImpl : ChangeOrderInputBizService {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var changeControllerFacades: ChangeControllerFacades

    @Autowired
    lateinit var resourceManagerFacades: ResourceManagerFacades

    @Autowired
    lateinit var changeOrderInputPatchValueHandlerChain: ChangeOrderInputPatchValueHandlerChain

    override fun findAllForms(
        appName: String,
        envNameList: List<String>,
        type: ChangeOrder.Type,
        sha: String?,
    ): List<ChangeOrderInputForm> {
        return envNameList.map { envName ->
            val env = coreFacades.envFacade.find(appName, envName)
            checkExists(env) { ErrorCode.AS_ENV_NOT_FOUND }
            (AopContext.currentProxy() as ChangeOrderInputBizServiceImpl).findForms(env, type, sha)
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ENV_DEPLOY),
            Access(action = Action.ENV_ROLLBACK),
            Access(action = Action.ENV_SCALE),
            Access(action = Action.ENV_DELETE),
        ]
    )
    open fun findForms(env: Env, type: ChangeOrder.Type, sha: String?): ChangeOrderInputForm {
        val changeOrderInputPatchValueInfoDTO = getPatchValues(env, type, emptyMap(), sha)
        changeOrderInputPatchValueInfoDTO.orchestration?.let { it ->
            it as AppBuiltInOrchestration
            val patchValues = changeOrderInputPatchValueInfoDTO.patchValues
            it.placeholderList.forEach { placeholder ->
                if (patchValues.containsKey(placeholder.name)) {
                    placeholder.value = patchValues.getValue(placeholder.name).toString()
                }
            }
            if (it.componentList.any { it.kind.isBlank() }) {
                throw BizException(errorCode = ErrorCode.AS_ORC_COMP_KIND_NOT_FOUND, mapOf("componentName" to it.componentList.first { it.kind.isBlank() }.name))
            }
        }

        return ChangeOrderInputForm(
            env = env,
            appOrchestrationSn = changeOrderInputPatchValueInfoDTO.orchestrationSn,
            appOrchestration = changeOrderInputPatchValueInfoDTO.orchestration,
            appOrchestrationSource = changeOrderInputPatchValueInfoDTO.orchestrationSource,
            profiles = changeOrderInputPatchValueInfoDTO.profiles,
            deployGroup = changeOrderInputPatchValueInfoDTO.deployGroup
        )
    }

    override fun getPatchValues(
        env: Env,
        type: ChangeOrder.Type,
        customPatchValues: Map<String, String>,
        sha: String?,
    ): ChangeOrderInputPatchValueInfoDTO {
        // 确定环境资源类型
        val deployGroup = if (env.resourcePoolName.isNullOrBlank() || env.deployGroupName.isNullOrBlank()) {
            null
        } else {
            coreFacades.deployGroupFacade.find(env.resourcePoolName!!, env.deployGroupName!!)
        }
        val rsType = if (deployGroup?.claimList?.firstOrNull()?.belong2Host() == true) {
            ResourceTypeEnum.HOST
        } else {
            ResourceTypeEnum.KUBERNETES
        }

        var appOrchestration = coreFacades.appOrchestrationFacade.findAll(env.appName).firstOrNull {
            it.suitableResourceTypes.contains(rsType) && it.format == Orchestration.FormatEnum.MANIFEST
        }
        val orchestrationSn = appOrchestration?.sn
        var orchestrationFrom: AppOrchestrationSource? = null
        if (appOrchestration != null) {
            appOrchestration = if (sha.isNullOrBlank()) {
                // 获取环境最近一次工单使用的编排版本
                val changeOrder =
                    changeControllerFacades.changeableEnvFacade.findLatestChangeOrder(env.appName, env.name)
                if (changeOrder != null) {
                    val tag = changeOrder.tag
                    orchestrationFrom = AppOrchestrationSource.LAST_DEPLOY
                    val find = coreFacades.appOrchestrationFacade.find(
                        appName = env.appName,
                        sn = appOrchestration.sn,
                        tagName = tag,
                        sha = null
                    )
                    if (find is AppBuiltInOrchestration && !find.labelList.containsAll(env.labelList)) {
                        //内置编排校验label
                        null
                    } else {
                        find
                    }
                } else {
                    orchestrationFrom = AppOrchestrationSource.LATEST_AVAILABLE
                    coreFacades.appOrchestrationFacade.findLatest(env.appName, appOrchestration.sn, env.labelList)
                }
            } else {
                orchestrationFrom = AppOrchestrationSource.SPECIFIC_SHA
                val find = coreFacades.appOrchestrationFacade.find(
                    appName = env.appName,
                    sn = appOrchestration.sn,
                    tagName = null,
                    sha = sha
                )
                if (find is AppBuiltInOrchestration && !find.labelList.containsAll(env.labelList)) {
                    //内置编排校验label
                    throw BizException(errorCode = ErrorCode.AS_APP_ORC_NOT_MATCH_LABEL)
                } else {
                    find
                }
            }
        }

        val revisionVariableGroups = coreFacades.envFacade.listEnvRevisionVariableGroups(env.appName, env.name)

        val stableChangeOrderInput =
            changeControllerFacades.changeableEnvFacade.findStableChangeOrderInput(env.appName, env.name)
        val patchValues = changeOrderInputPatchValueHandlerChain.handle(
            env = env,
            type = type,
            orchestration = appOrchestration,
            profiles = revisionVariableGroups,
            stableChangeOrderInput = stableChangeOrderInput,
            customPatchValues = customPatchValues
        )
        return ChangeOrderInputPatchValueInfoDTO(
            orchestrationSn = orchestrationSn,
            orchestration = appOrchestration,
            orchestrationSource = orchestrationFrom,
            profiles = revisionVariableGroups,
            deployGroup = deployGroup,
            patchValues = patchValues
        )
    }

    override fun create(createChangeOrderInputRequest: CreateChangeOrderInputRequest): ChangeOrderForm {
        val envList = coreFacades.envFacade.findAll(createChangeOrderInputRequest.appName)
        val dynamicProxy = AopContext.currentProxy() as ChangeOrderInputBizServiceImpl
        return when (createChangeOrderInputRequest) {
            is CreateDeployInputRequest -> {
                val envs = envList.filter { createChangeOrderInputRequest.envValues.keys.contains(it.name) }
                dynamicProxy.createDeploy(envs, createChangeOrderInputRequest)
            }

            is CreateRollbackInputRequest -> {
                val envs = envList.filter { createChangeOrderInputRequest.envNames.contains(it.name) }
                dynamicProxy.createRollback(envs, createChangeOrderInputRequest)
            }

            is CreateScaleInputRequest -> {
                val env = envList.first { createChangeOrderInputRequest.envName == it.name }
                dynamicProxy.createScale(env, createChangeOrderInputRequest)
            }

            is CreateDestroyInputRequest -> {
                val env = envList.first { createChangeOrderInputRequest.envName == it.name }
                dynamicProxy.createDestroy(env, createChangeOrderInputRequest)
            }

            else -> {
                throw BizException(ErrorCode.AS_CHANGE_NOT_ALLOW)
            }
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ENV_DEPLOY),
        ]
    )
    open fun createDeploy(envs: List<Env>, createChangeOrderInputRequest: CreateDeployInputRequest): ChangeOrderForm {
        val changeOrderInput = changeControllerFacades.changeOrderInputFacade.create(createChangeOrderInputRequest)
        return convertInputToForm(envs, changeOrderInput)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ENV_ROLLBACK),
        ]
    )
    open fun createRollback(
        envs: List<Env>,
        createChangeOrderInputRequest: CreateRollbackInputRequest,
    ): ChangeOrderForm {
        val changeOrderInput = changeControllerFacades.changeOrderInputFacade.create(createChangeOrderInputRequest)
        return convertInputToForm(envs, changeOrderInput)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ENV_SCALE),
        ]
    )
    open fun createScale(env: Env, createChangeOrderInputRequest: CreateScaleInputRequest): ChangeOrderForm {
        val changeOrderInput = changeControllerFacades.changeOrderInputFacade.create(createChangeOrderInputRequest)
        return convertInputToForm(listOf(env), changeOrderInput)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ENV_DELETE),
        ]
    )
    open fun createDestroy(env: Env, createChangeOrderInputRequest: CreateDestroyInputRequest): ChangeOrderForm {
        val changeOrderInput = changeControllerFacades.changeOrderInputFacade.create(createChangeOrderInputRequest)
        return convertInputToForm(listOf(env), changeOrderInput)
    }

    override fun check(appName: String, envNames: List<String>, type: ChangeOrder.Type): ChangeOrderCheckVO {
        val envCheckMap = changeControllerFacades.changeOrderInputFacade.createCheck(appName, envNames, type)
        return ChangeOrderCheckVO(envCheckResults = envCheckMap)
    }

    private fun convertInputToForm(envList: List<Env>, changeOrderInput: ChangeOrderInput): ChangeOrderForm {
        val envRelatedInfos = envList.associate { env ->
            // 确定环境资源类型
            val deployGroup = if (env.resourcePoolName.isNullOrBlank() || env.deployGroupName.isNullOrBlank()) {
                null
            } else {
                coreFacades.deployGroupFacade.find(env.resourcePoolName!!, env.deployGroupName!!)
            }
            val rsType = if (deployGroup?.claimList?.firstOrNull()?.belong2Host() == true) {
                ResourceTypeEnum.HOST
            } else {
                ResourceTypeEnum.KUBERNETES
            }

            val appOrchestration = coreFacades.appOrchestrationFacade.findAll(env.appName).firstOrNull {
                it.suitableResourceTypes.contains(rsType) && it.format == Orchestration.FormatEnum.MANIFEST
            }
            val profile = coreFacades.envFacade.listEnvRevisionVariableGroups(env.appName, env.name)
            Pair(env.name, Triple(appOrchestration, deployGroup, profile))
        }

        val taskForms = changeControllerFacades.changeOrderInputFacade.findTaskForms(changeOrderInput.sn!!)
        return ChangeOrderForm(
            changeOrderInputSn = changeOrderInput.sn!!,
            version = changeOrderInput.version,
            taskForms = taskForms.mapValues {
                it.value.filter { it.isComponent() }
            },
            appOrchestrations = envRelatedInfos.entries.associate { entry ->
                Pair(entry.key, entry.value.first)
            },
            profiles = envRelatedInfos.entries.associate { entry ->
                Pair(entry.key, entry.value.third)
            },
            deployGroups = envRelatedInfos.entries.associate { entry ->
                Pair(entry.key, entry.value.second)
            },
            envs = envList.associate { it.name to EnvRecord(it) }
        )
    }

    override fun find(sn: String): ChangeOrderInput? {
        return changeControllerFacades.changeOrderInputFacade.find(sn)
    }

    override fun findDeliveries(sn: String, envName: String, locator: String): String {
        return changeControllerFacades.changeOrderInputFacade.findDeliveries(sn, envName, locator)
    }

    override fun diffDelivery(sn: String, appName: String, envName: String, locator: String): DeliveryDiffInfo {
        val delivery = try {
            changeControllerFacades.changeOrderInputFacade.findDeliveries(sn, envName, locator)
        } catch (e: BizException) {
            null
        }
        val yamlFromIaC = changeControllerFacades.changeableEnvFacade.findYamlFromIaC(appName, envName, locator)
        return DeliveryDiffInfo(locator = locator, delivery = delivery, lastDeployYaml = yamlFromIaC)
    }

    override fun diffDeployInput(appName: String, envName: String): List<DeployDiffInfo> {
        val env = coreFacades.envFacade.find(appName = appName, envName = envName) ?: return emptyList()
        val envVars = coreFacades.envFacade.listEnvRevisionVariableGroups(appName, envName).flatMap { it.vars }
        val vars = SystemVariable.build(env.appName, env.name) + envVars.associate { it.key to it.value }
        val stableChangeOrderInput =
            changeControllerFacades.changeableEnvFacade.findStableChangeOrderInput(env.appName, env.name)
                ?: return emptyList()
        val resourceType = changeControllerFacades.changeableEnvFacade.find(appName, envName)?.resourceType
        val deployValues = stableChangeOrderInput.envValues[env.name] ?: mutableMapOf()
        val orchestration = coreFacades.appOrchestrationFacade.findAll(env.appName)
            .firstOrNull { it.suitableResourceTypes.contains(resourceType) && it.format == Orchestration.FormatEnum.MANIFEST }
            ?: return emptyList()
        val placeholders = (orchestration as AppBuiltInOrchestration).placeholderList

        try {
            val stableOrchestration = coreFacades.appOrchestrationFacade.find(
                appName,
                orchestration.sn,
                tagName = stableChangeOrderInput.tag,
                sha = null
            )
            var stablePlaceholders = emptyList<Placeholder>()
            if (stableOrchestration != null) {
                stableOrchestration as AppBuiltInOrchestration
                stablePlaceholders = stableOrchestration.placeholderList
            }

            val stableVars = SystemVariable.build(env.appName, env.name) + (coreFacades.variableFacade.findAllByTagName(
                stableChangeOrderInput.tag
            ).lastOrNull()?.profileMap?.get(envName)?.vars ?: emptyList()).associate { it.key to it.value }
            stablePlaceholders.forEach {
                if (!deployValues.containsKey(it.name)) {
                    deployValues[it.name] = if (it.valueSource == Placeholder.ValueSource.VARIABLE) {
                        stableVars[it.value]?.toString() ?: ""
                    } else {
                        it.value
                    }
                }
            }
        } catch (e: Exception) {
            logger.error("diffDeployInput error:", e)
        }

        val deployList = deployValues.mapNotNull { (oneKey, value) ->
            if (placeholders.none { it.name == oneKey } && !oneKey.startsWith("image.") && !oneKey.startsWith(
                    "artifact."
                )) {
                DeployDiffInfo(
                    name = oneKey,
                    lastDeployValue = value.toString(),
                    orchestrationValue = ""
                )
            } else {
                null
            }
        }

        return (placeholders.mapNotNull {
            if (!it.name.startsWith("image.") && !it.name.startsWith("artifact.")) {
                val deployValue = if (deployValues.containsKey(it.name)) {
                    deployValues[it.name].toString()
                } else {
                    ""
                }
                val orchestrationValue = if (it.valueSource == Placeholder.ValueSource.VARIABLE) {
                    vars[it.value]?.toString() ?: ""
                } else {
                    it.value
                }

                DeployDiffInfo(
                    name = it.name,
                    lastDeployValue = deployValue,
                    orchestrationValue = orchestrationValue
                )
            } else {
                null
            }
        } + deployList)
    }
}