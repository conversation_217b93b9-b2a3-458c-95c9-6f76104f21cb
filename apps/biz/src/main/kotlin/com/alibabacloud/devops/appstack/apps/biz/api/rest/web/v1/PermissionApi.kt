package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.model.vo.AppActionVO
import com.alibabacloud.devops.appstack.apps.biz.model.vo.SystemActionVO
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.apps.biz.service.PermissionService
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR> <EMAIL>
 * @version : PermissionApi, v0.1
 * @date : 2021-12-15 21:02
 **/
@Tag(name = "Permission", description = "权限相关 API")
@RestController
@RequestMapping("/api/v1/permissions")
open class PermissionApi {

    @Autowired
    lateinit var permissionService: PermissionService

    @Autowired
    lateinit var iamService: IamService

    @Operation(summary = "获取当前用户企业级权限点")
    @GetMapping("/org/actions")
    fun findOrgAction(): Response<List<String>> {
        return Response.success(permissionService.findOrgActionAllowed())
    }

    @Operation(summary = "获取当前用户在应用下所有权限点(包含企业级权限)")
    @GetMapping("/apps/{appName}/actions")
    fun findAppAction(
        @PathVariable("appName") appName: String,
    ): Response<AppActionVO> {
        return Response.success(permissionService.findAppActionAllowed(appName))
    }

    @Operation(summary = "获取当前用户在系统下所有权限点(包含企业级权限)")
    @GetMapping("/systems/{systemName}/actions")
    fun findSystemAction(
        @PathVariable("systemName") systemName: String
    ): Response<SystemActionVO> {
        return Response.success(permissionService.findSystemActionAllowed(systemName))
    }

    @Operation(summary = "获取当前用户在资源下的所有权限点")
    @GetMapping("/pools/{poolName}/instances/{instanceName}")
    fun findResAction(
        @PathVariable("poolName") poolName: String,
        @PathVariable("instanceName") instanceName: String,
    ): Response<List<String>> {
        return Response.success(permissionService.findResActionAllowed(poolName, instanceName))
    }

    @Operation(summary = "获取当前用户在变量组下的所有权限点")
    @GetMapping("/var/{name}/actions")
    fun findVarAction(
        @PathVariable("name") varName: String,
    ): Response<List<String>> {
        return Response.success(permissionService.findVarActionAllowed(varName))
    }

    @Operation(summary = "获取当前用户在应用模板下的所有权限点")
    @GetMapping("/appTemplate/{name}/actions")
    fun findAppTemplateAction(
        @PathVariable("name") appTemplateName: String,
    ): Response<List<String>> {
        return Response.success(permissionService.findAppTemplateActionAllowed(appTemplateName))
    }

    @Operation(summary = "初始化权限配置, 手工运行（备用）")
    @PostMapping("/initConfig")
    fun initConfig(): Response<Unit> {
        return Response.success(iamService.rebuildIamConfig())
    }
}