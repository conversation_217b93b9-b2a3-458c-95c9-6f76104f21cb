package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.model.vo.DeliveryDiffInfo
import com.alibabacloud.devops.appstack.apps.biz.model.vo.DeployDiffInfo
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderInputBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderForm
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderInputForm
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.ChangeOrderCheckRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateChangeOrderInputRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderCheckVO
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-03-21 16:25
 * @version: ChangeOrderInputBizApi, v0.1
 **/
@Tag(name = "ChangeOrderInput", description = "工单输入相关 API")
@RestController
@RequestMapping("/api/v1")
open class ChangeOrderInputBizApi {

    @Autowired
    lateinit var changeOrderInputBizService: ChangeOrderInputBizService

    @Operation(summary = "获取工单输入表单列表")
    @GetMapping("/apps/{appName}/changeOrderInputForms")
    fun findAllForms(
        @PathVariable appName: String,
        @RequestParam envNames: List<String>,
        @RequestParam("type", required = false, defaultValue = "Deploy") type: String,
        @RequestParam("sha", required = false) sha: String? = null,
    ): Response<List<ChangeOrderInputForm>> {
        return Response.success(
            changeOrderInputBizService.findAllForms(
                appName,
                envNames,
                ChangeOrder.Type.valueOf(type),
                sha,
            )
        )
    }

    @Operation(summary = "创建工单输入")
    @PostMapping("/changeOrderInputs")
    fun createChangeOrderInput(
        @RequestBody createChangeOrderInputRequest: CreateChangeOrderInputRequest
    ): Response<ChangeOrderForm> {
        return Response.success(changeOrderInputBizService.create(createChangeOrderInputRequest))
    }

    @Operation(summary = "查看工单产物")
    @GetMapping("/changeOrderInputs/{sn}/deliveries")
    fun findDeliveries(
        @PathVariable sn: String,
        @RequestParam envName: String,
        @RequestParam locator: String,
    ): Response<String> {
        return Response.success(changeOrderInputBizService.findDeliveries(sn, envName, locator))
    }

    @Operation(summary = "查看工单产物Diff")
    @GetMapping("/changeOrderInputs/{sn}/deliveries:diff")
    fun diffDeliveries(
        @PathVariable sn: String,
        @RequestParam appName: String,
        @RequestParam envName: String,
        @RequestParam locator: String,
    ): Response<DeliveryDiffInfo> {
        return Response.success(changeOrderInputBizService.diffDelivery(sn, appName, envName, locator))
    }

    @Operation(summary = "工单创建校验")
    @PostMapping("/changeOrderInputs:check")
    fun checkChangeOrderInput(@RequestBody request: ChangeOrderCheckRequest): Response<ChangeOrderCheckVO> {
        return Response.success(changeOrderInputBizService.check(request.appName, request.envNames, request.type))
    }

    @Operation(summary = "查看本次编排变化")
    @GetMapping("/apps/{appName}/changeOrderInputs:diffDeployInput")
    fun diffDeployInput(
        @PathVariable appName: String,
        @RequestParam envName: String
    ): Response<List<DeployDiffInfo>> {
        return Response.success(changeOrderInputBizService.diffDeployInput(appName, envName))
    }
}