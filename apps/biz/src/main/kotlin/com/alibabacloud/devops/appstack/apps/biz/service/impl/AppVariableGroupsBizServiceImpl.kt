package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.annotation.IsApp
import com.alibabacloud.devops.appstack.apps.biz.service.AppVariableGroupsBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.Variable
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Branch
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.ProfileRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateVariableGroupDryRunRequest
import com.alibabacloud.devops.appstack.libs.model.vo.VariableStorageVO
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2023-12-19
 */
@Service
open class AppVariableGroupsBizServiceImpl : AppVariableGroupsBizService {
    @Autowired
    lateinit var coreFacades: CoreFacades

    @IsApp
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_VARIABLE_CREATE, resourceArgIndex = 0),
            Access(action = Action.APP_VARIABLE_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun createProfile(appName: String, profileRequest: ProfileRequest): VariableStorageVO {
        return coreFacades.variableFacade.createProfile(appName, profileRequest)
    }

    @IsApp
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_VARIABLE_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun updateProfile(appName: String, profileRequest: ProfileRequest): VariableStorageVO {
        return coreFacades.variableFacade.updateProfile(appName, profileRequest)
    }

    @IsApp
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_VARIABLE_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun findAllProfiles(appName: String): VariableStorageVO? {
        return coreFacades.variableFacade.find(appName)
    }

    @IsApp
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_VARIABLE_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun deleteProfile(appName: String, profileName: String) {
        val storage = findAllProfiles(appName)
        checkExists(storage) { ErrorCode.AS_VAR_PROFILE_NOT_FOUND }
        checkExists(storage.profileMap[profileName], profileName) { ErrorCode.AS_VAR_PROFILE_NOT_FOUND }

        val profileRequest = ProfileRequest(name = profileName)
        profileRequest.fromRevisionSha = storage.revision.sha
        profileRequest.branchName = Branch.TRUNK
        profileRequest.message = "删除变量组"
        coreFacades.variableFacade.deleteProfile(appName, profileRequest)
    }

    @IsApp
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_VARIABLE_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun findProfilesRevisions(appName: String, current: Long, pageSize: Long): Pagination<Revision> {
        return coreFacades.variableFacade.findPaginatedRevision(appName, current, pageSize)
    }

    @IsApp
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_VARIABLE_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun findProfilesByRevisionSha(appName: String, revisionSha: String): VariableStorageVO {
        return coreFacades.variableFacade.findByRevisionSha(appName, revisionSha)
    }

    @IsApp
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_VARIABLE_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun findProfilesByTag(appName: String, tagName: String): VariableStorageVO {
        return coreFacades.variableFacade.findByTag(appName, tagName)
    }

    @IsApp
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_VARIABLE_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun compare(
        appName: String,
        beforeRevisionSha: String,
        afterRevisionSha: String
    ): Triple<VariableStorageVO, VariableStorageVO, List<DiffItem<String>>> {
        return coreFacades.variableFacade.compare(appName, beforeRevisionSha, afterRevisionSha)
    }

    @IsApp
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_VARIABLE_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun updateDryrun(appName: String, profileName: String, request: UpdateVariableGroupDryRunRequest): List<Variable> {
        val currentProfile = coreFacades.variableFacade.findProfileVO(appName, profileName)
        return when (request.mode) {
            UpdateVariableGroupDryRunRequest.Mode.OVERRIDE_KEY_VALUE -> request.variables
            UpdateVariableGroupDryRunRequest.Mode.APPEND_KEY -> dryRunWithAppendKey(currentProfile?.vars
                ?: emptyList(), request.variables)
        }
    }

    private fun dryRunWithAppendKey(originalVars: List<Variable>, newVars: List<Variable>): List<Variable> {
        val result = originalVars.toMutableList()
        newVars.forEach {
            if (!result.map { it.key }.contains(it.key)) {
                result.add(it)
            }
        }
        return result
    }
}