package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import com.alibabacloud.devops.appstack.apps.biz.service.system.ReleaseWorkflowBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.CreateReleaseWorkflowBizRequest
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 * @create 2023/12/26 4:08 PM
 **/
@Tag(name = "发布流程", description = "发布流程相关API")
@RestController
@RequestMapping("/api/v2")
open class SystemReleaseWorkflowApi {

    @Autowired
    lateinit var releaseWorkflowBizService: ReleaseWorkflowBizService

    @VpcApi
    @Operation(summary = "查找系统下所有的发布流程", operationId = "ListSystemAllReleaseWorkflows")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查找系统下所有的发布流程成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @GetMapping("/systems/{systemName}/releaseWorkflows")
    fun listSystemAllReleaseWorkflows(
        @PathVariable systemName: String,
    ): List<ReleaseWorkflow> {
        return releaseWorkflowBizService.findAll(systemName)
    }

    @VpcApi
    @Operation(summary = "创建发布流程", operationId = "CreateSystemReleaseWorkflows")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "创建系统发布流程成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/systems/{systemName}/releaseWorkflows")
    fun createSystemReleaseWorkflow(
        @PathVariable systemName: String,
        @RequestBody request: CreateReleaseWorkflowRequest,
    ): ReleaseWorkflow {

        return releaseWorkflowBizService.create(
            systemName, CreateReleaseWorkflowBizRequest(
                name = request.name,
                templateSn = request.templateSn,
                note = request.note
            )
        )
    }

}

data class CreateReleaseWorkflowRequest(
    val name: String,
    val templateSn: String? = null,
    val note: String? = null,
)