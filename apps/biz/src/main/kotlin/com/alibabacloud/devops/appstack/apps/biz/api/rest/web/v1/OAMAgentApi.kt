package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.model.AgentStatus
import com.alibabacloud.devops.appstack.apps.biz.model.AgentTask
import com.alibabacloud.devops.appstack.apps.biz.model.JobLog
import com.alibabacloud.devops.appstack.apps.biz.service.AgentService
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@Tag(name = "OAMAgent", description = "OAM Agent 相关 API")
@RestController
@RequestMapping("/api/v1/agents")
open class OAMAgentApi {

    @Autowired
    lateinit var agentService: AgentService

    @PostMapping("/{poolName}/{instanceName}:install")
    fun installAgent(
        @PathVariable("poolName") poolName: String,
        @PathVariable("instanceName") instanceName: String,
        @RequestParam("version", required = false, defaultValue = "0.0.1") version: String,
    ): Response<AgentTask> {
        val task = agentService.installAgent(poolName, instanceName, version)
        return Response.success(task)
    }

    @GetMapping("/{poolName}/{instanceName}/tasks:latest")
    fun findLatestInstallTasks(
        @PathVariable("poolName") poolName: String,
        @PathVariable("instanceName") instanceName: String,
    ): Response<AgentTask?> {
        return Response.success(agentService.findLatestInstallTask(poolName, instanceName))
    }

    @GetMapping("/{poolName}/{instanceName}/tasks:latestLog")
    fun findLatestInstallTaskLog(
        @PathVariable("poolName") poolName: String,
        @PathVariable("instanceName") instanceName: String,
    ): Response<JobLog> {
        return Response.success(agentService.findLatestInstallLog(poolName, instanceName))
    }

    @GetMapping("/{poolName}/{instanceName}:status")
    fun status(
        @PathVariable("poolName") poolName: String,
        @PathVariable("instanceName") instanceName: String,
    ): Response<AgentStatus> {
        return Response.success(agentService.findStatus(poolName, instanceName))
    }

    @PostMapping("/{poolName}/{instanceName}:cancel")
    fun cancelAgentInstallation(
        @PathVariable("poolName") poolName: String,
        @PathVariable("instanceName") instanceName: String,
    ): Response<Unit> {
        return Response.success(agentService.cancelAgentInstallation(poolName, instanceName))
    }
}