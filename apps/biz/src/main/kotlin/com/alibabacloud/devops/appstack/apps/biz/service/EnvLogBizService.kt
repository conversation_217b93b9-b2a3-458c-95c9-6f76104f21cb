package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.request.SearchOpLogRequest
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderContext
import com.alibabacloud.devops.appstack.libs.model.vo.EnvLogVO

/**
 * @author: <EMAIL>
 * @date: 2022-03-03 16:02
 * @version: EnvLogBizService, v0.1
 **/
interface EnvLogBizService {

    fun recordCreateEnvAction(env: Env): EnvLogVO

    fun recordLockEnvAction(env: Env, isLock: Boolean): EnvLogVO

    fun recordUpdateEnvAction(env: Env, previousEnv: Env): EnvLogVO?

    fun recordDeleteEnvAction(
        env: Env,
        changeOrderContext: ChangeOrderContext? = null,
    ): EnvLogVO

    fun recordCreateChangeOrderAction(changeOrder: ChangeOrder): List<EnvLogVO>

    fun findPaginated(searchOpLogRequest: SearchOpLogRequest, current: Long, pageSize: Long): Pagination<EnvLogVO>

}