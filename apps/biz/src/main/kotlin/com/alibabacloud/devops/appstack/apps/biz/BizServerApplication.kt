package com.alibabacloud.devops.appstack.apps.biz

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.context.annotation.EnableAspectJAutoProxy

/**
 * @author: ji<PERSON><PERSON>@taobao.com
 * @date: 2021-09-27 13:07
 * @version: BizServerApplication, v0.1
 **/
@SpringBootApplication
@EnableFeignClients
@EnableAspectJAutoProxy(exposeProxy = true)
open class BizServerApplication

fun main(args: Array<String>) {
    runApplication<BizServerApplication>(*args)
}