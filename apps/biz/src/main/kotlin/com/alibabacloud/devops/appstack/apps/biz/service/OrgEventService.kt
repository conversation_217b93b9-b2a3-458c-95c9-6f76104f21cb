package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.apps.biz.model.OrgEventRequest
import com.alibabacloud.devops.appstack.apps.biz.model.OrgV5EventRequest


/**
 * <AUTHOR> <EMAIL>
 * @version : OrgCallbackService, v0.1
 * @date : 2021-11-30 10:10
 **/
interface OrgEventService {

    fun handleEvent(eventCode: String, eventId: String, request: OrgEventRequest) {}

    fun handleV5Event(eventType: String, eventId: String, request: OrgV5EventRequest) {}

}