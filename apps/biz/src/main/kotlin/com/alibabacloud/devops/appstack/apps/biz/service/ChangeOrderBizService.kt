package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.*
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateChangeOrderRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.SearchChangeOrderRequest
import com.alibabacloud.devops.appstack.libs.model.vo.*

/**
 * <AUTHOR>
 * @date 2022-02-22
 */
interface ChangeOrderBizService {
    fun dryRun(createChangeOrderRequest: CreateChangeOrderRequest): ChangeOrder
    fun create(createChangeOrderRequest: CreateChangeOrderRequest): ChangeOrderVO
    fun check(createChangeOrderRequest: CreateChangeOrderRequest): List<InvalidChangeItem>
    fun create(appName: String, request: CreateChangeOrderReq): ChangeOrderVO
    fun find(sn: String): ChangeOrderVO?
    fun change(sn: String, changeAction: ChangeAction): ChangeOrderVO
    fun findPaginated(
        appName: String,
        envNames: List<String> = emptyList(),
        jobTypes: List<Job.Type> = emptyList(),
        jobStates: List<Job.State> = emptyList(),
        current: Long = 1L,
        pageSize: Long = 10L,
    ): Pagination<ChangeOrderListRecord>
    fun findPaginatedVersion(
        appName: String,
        envNames: List<String>,
        creators: List<String>,
        current: Long,
        pageSize: Long,
    ): Pagination<ChangeOrderVersionRecord>
    fun findPaginatedJobLog(sns: List<String>, current: Long = 1L, pageSize: Long = 10L): Pagination<JobLog>
    fun findJobDiff(sn: String): Pair<JobRecord, JobRecord>
    fun findRecordByVersion(appName: String, version: String): ChangeOrderVersionRecord?
    fun findBySns(appName: String, sns: List<String>): List<ChangeOrderRecord>
    fun findEnvOccupyList(appName: String, envNames: String): List<StageEnvOccupy>
    fun searchPaginated(
        searchChangeOrderRequest: SearchChangeOrderRequest,
        current: Long = 1L,
        pageSize: Long = 10L
    ): Pagination<ChangeOrderListRecord>

    fun findTaskExecutionLog(
        changeOrderSn: String,
        jobSn: String,
        stageSn: String,
        taskSn: String
    ): String

    fun findJobBriefByChangeOrder(changeOrderSn: String): List<JobBriefRecordVO>
}