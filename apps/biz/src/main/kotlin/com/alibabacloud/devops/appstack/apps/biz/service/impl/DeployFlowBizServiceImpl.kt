package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.annotation.ApiMetricCounter
import com.alibabacloud.devops.appstack.apps.biz.model.DeployFlow
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderBizService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderInputBizService
import com.alibabacloud.devops.appstack.apps.biz.service.DeployFlowBizService
import com.alibabacloud.devops.appstack.apps.biz.service.DeployFlowBizService.Companion.COMMON_ARTIFACT_PREFIX
import com.alibabacloud.devops.appstack.apps.biz.service.DeployFlowBizService.Companion.IMAGE_PREFIX
import com.alibabacloud.devops.appstack.apps.biz.service.DeployFlowBizService.Companion.V1_IMAGE_NAME
import com.alibabacloud.devops.appstack.apps.biz.service.DeployFlowService
import com.alibabacloud.devops.appstack.libs.change.controller.spring.boot.starter.service.ChangeControllerFacades
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.model.ErrorEntry
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkBiz
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderForm
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.Job
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.TaskContext
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.TaskFormBehavior
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeTaskForm
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.env.EnvDTO
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.Orchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppBuiltInOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Placeholder
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import com.alibabacloud.devops.appstack.libs.model.dto.changecontroller.TaskStrategyDTO
import com.alibabacloud.devops.appstack.libs.model.request.CustomParamItem
import com.alibabacloud.devops.appstack.libs.model.request.FlowCreateChangeOrderRequest
import com.alibabacloud.devops.appstack.libs.model.request.FlowCreateCleanOrderRequest
import com.alibabacloud.devops.appstack.libs.model.request.ImageMapItem
import com.alibabacloud.devops.appstack.libs.model.request.SearchAppRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateChangeOrderInputRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateChangeOrderRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateDeployInputRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.CreateDestroyInputRequest
import com.alibabacloud.devops.appstack.libs.model.vo.AppVO
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderOnFlow
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderOnFlowVO
import com.alibabacloud.devops.appstack.libs.model.vo.TaskOnFlow
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.text.SimpleDateFormat
import java.util.*

/**
 * @author: <EMAIL>
 * @date: 2022-03-08 09:56
 * @version: DeployFlowBizServiceImpl, v0.1
 **/
@Service
@Slf4k
open class DeployFlowBizServiceImpl : DeployFlowBizService {

    private lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var changeControllerFacades: ChangeControllerFacades

    @Autowired
    lateinit var changeOrderBizService: ChangeOrderBizService

    @Autowired
    lateinit var changeOrderInputBizService: ChangeOrderInputBizService

    @Autowired
    lateinit var deployFlowService: DeployFlowService

    @Autowired
    fun setCoreFacades(coreFacades: CoreFacades) {
        this.coreFacades = coreFacades
    }

    /**
     *
     * 1。找到最近成功的部署单，并检查是否符合部署条件
     * 2。组装changeOrderInput｜changeOrder，并创建
     * 3。创建deployFlowBiz
     */
    @ApiMetricCounter(metricName = "submit_change_order_from_flow")
    override fun createChangeOrder(flowCreateChangeOrderRequest: FlowCreateChangeOrderRequest): String {
        logger.info(
            "DeployFlowBizServiceImpl createChangeOrder: ${
                jacksonObjectMapper().writeValueAsString(
                    flowCreateChangeOrderRequest
                )
            }"
        )
        val appName = flowCreateChangeOrderRequest.appName
        val envName = flowCreateChangeOrderRequest.envName
        // 查找上一个非环境清理单的部署单

        val env = coreFacades.envFacade.find(appName, envName)
        checkExists(env?.deployGroupName) {
            ErrorCode.AS_DEPLOY_GROUP_NOT_FOUND
        }
        //如果是升级环境需要到页面上进行部署
        checkBiz(env?.spec?.migrateState == null) {
            ErrorEntry(ErrorCode.AS_ENV_MIGRATE_BY_FLOW_FAILED)
        }

        // 老工单用开始
        val latestChangeOrder = changeControllerFacades.changeOrderFacade.last(
            appName,
            envName,
            listOf(Job.Type.Deploy, Job.Type.Revert, Job.Type.Scale, Job.Type.Rollback),
            emptyList(),
        )
        var job:Job? = null
        // 老工单用结束
        val createDeployInputRequest =
            if (null != flowCreateChangeOrderRequest.imageUrl) {
                logger.info("Deprecated appstack deploy step: appName: $appName envName: $envName orgId: ${AuthUtil.getTenant()}")
                // 老工单
                checkExists(latestChangeOrder) { ErrorCode.AS_CHANGE_ORDER_JOB_NOT_EXISTED }
                job = latestChangeOrder.jobs.first { it.envName == envName }
                checkBiz(job.type == Job.Type.Deploy) {
                    ErrorEntry(ErrorCode.AS_CHANGE_ORDER_JOB_NOT_DEPLOY)
                }
                checkBiz(job.state == Job.State.SUCCESS || flowCreateChangeOrderRequest.ignoreLatestFailed!!) {
                    ErrorEntry(ErrorCode.AS_CHANGE_ORDER_JOB_NOT_SUCCESS)
                }
                buildCreateOrderInputRequest(
                    job,
                    latestChangeOrder.version,
                    flowCreateChangeOrderRequest.imageUrl!!
                )
            } else {
                // 新工单校验上一次部署是否失败, 找不到上个单忽略继续往下
                val lastJob = latestChangeOrder?.jobs?.firstOrNull { it.envName == envName }
                if (null != lastJob) {
                    checkBiz(lastJob.state == Job.State.SUCCESS || flowCreateChangeOrderRequest.ignoreLatestFailed!!) {
                        ErrorEntry(ErrorCode.AS_CHANGE_ORDER_JOB_NOT_SUCCESS)
                    }
                }

                // 新工单，新逻辑：以编排为准
                fixSingleImage(env!!, flowCreateChangeOrderRequest.imageList)
                buildCreateOrderInputRequest(
                    env,
                    flowCreateChangeOrderRequest.imageList,
                    flowCreateChangeOrderRequest.customParamList,
                    flowCreateChangeOrderRequest.ignoreNewOrchestration
                )
            }
        val changeOrderForm = changeOrderInputBizService.create(createDeployInputRequest)
        changeOrderForm.taskForms.values.flatten().forEach { taskForm ->
            if (taskForm.behavior == TaskFormBehavior.Add && (taskForm as AdeTaskForm).kind == TaskContext.Kind.Deployment.name) {
                // 增加Deployment需要手动创建发布单
                throw BizException(ErrorCode.AS_ORC_COMP_NEW_DEPLOYMENT)
            } else if ((taskForm as AdeTaskForm).kind.isBlank()) {
                // kind为空部署单会卡住，所以拦截
                throw BizException(ErrorCode.AS_ORC_COMP_KIND_NOT_FOUND, mapOf("componentName" to taskForm.name))
            }
        }

        val createChangeOrderRequest = buildCreateChangeOrderRequest(
            job,
            inputSn = changeOrderForm.changeOrderInputSn,
            flowInsId = flowCreateChangeOrderRequest.pipelineId,
            sourceType = flowCreateChangeOrderRequest.sourceType,
            sourceSn = flowCreateChangeOrderRequest.sourceSn,
            changeOrderForm
        )
        val changeOrder = changeOrderBizService.create(createChangeOrderRequest)
        return changeOrder.sn
    }

    override fun createCleanOrder(flowCreateCleanOrderRequest: FlowCreateCleanOrderRequest): String {
        logger.info(
            "DeployFlowBizServiceImpl createCleanOrder: ${
                jacksonObjectMapper().writeValueAsString(
                    flowCreateCleanOrderRequest
                )
            }"
        )
        val appName = flowCreateCleanOrderRequest.appName
        val envName = flowCreateCleanOrderRequest.envName
        val latestChangeOrder = changeControllerFacades.changeableEnvFacade.findLatestChangeOrder(appName, envName)
        checkExists(latestChangeOrder) { ErrorCode.AS_CHANGE_ORDER_JOB_NOT_EXISTED }
        val job = latestChangeOrder.jobs.first { it.envName == envName }

        val createCleanOrderRequest: CreateChangeOrderInputRequest = CreateDestroyInputRequest(
            appName = appName,
            envName = envName,
            name = SimpleDateFormat("yyyyMMddHHmmss").format(Date()) + "-删除-flow",
            description = "通过Flow删除环境",
            deleteEnv = flowCreateCleanOrderRequest.deleteEnv
        )
        val changeOrderForm = changeOrderInputBizService.create(createCleanOrderRequest)

        val createChangeOrderRequest = buildCreateChangeOrderRequest(
            job,
            inputSn = changeOrderForm.changeOrderInputSn,
            flowInsId = flowCreateCleanOrderRequest.pipelineId,
            sourceType = flowCreateCleanOrderRequest.sourceType,
            sourceSn = flowCreateCleanOrderRequest.sourceSn,
            null
        )
        val changeOrder = changeOrderBizService.create(createChangeOrderRequest)
        return changeOrder.sn
    }

    private fun buildCreateOrderInputRequest(
        env: Env,
        imageList: List<ImageMapItem>?,
        customParamList: List<CustomParamItem>?,
        ignoreNewOrchestration: Boolean? = false,
    ): CreateDeployInputRequest {
        val customValues = mutableMapOf<String, String>()
        customParamList?.forEach {
            customValues[it.name] = it.value
        }
        imageList?.let {
            val deployGroup = coreFacades.deployGroupFacade.find(env.resourcePoolName!!, env.deployGroupName!!)
            if (deployGroup.claimList.firstOrNull() != null && deployGroup.claimList.first().belong2Host()) {
                it.forEach {
                    customValues[COMMON_ARTIFACT_PREFIX + it.name] = it.value
                }
            } else {
                it.forEach {
                    customValues[IMAGE_PREFIX + it.name] = it.value
                }
            }
        }

        val patchInfo =
            changeOrderInputBizService.getPatchValues(env, ChangeOrder.Type.Deploy, customValues, sha = null)
        logger.info("patchInfo ${jacksonObjectMapper().writeValueAsString(patchInfo)}")
        checkExists(patchInfo.orchestration) { ErrorCode.AS_ORC_NOT_FOUND }
        val latest =
            coreFacades.appOrchestrationFacade.findLatest(env.appName, patchInfo.orchestration.sn, env.labelList)
        checkBiz(patchInfo.orchestration.revision!!.sha == latest?.revision?.sha || ignoreNewOrchestration!!) {
            // appstack-flow-deloy步骤插件，捕获错误
            ErrorEntry(ErrorCode.AS_ORC_REVISION_OUTDATED_FOR_FLOW)
        }
        val sha = latest?.revision?.sha!!
        val patchValues =
            if (patchInfo.orchestration.revision!!.sha != latest.revision?.sha && ignoreNewOrchestration!!) {
                val newPatchInfo =
                    changeOrderInputBizService.getPatchValues(env, ChangeOrder.Type.Deploy, customValues, sha)
                newPatchInfo.patchValues
            } else {
                patchInfo.patchValues
            }
        logger.info("patchValues ${jacksonObjectMapper().writeValueAsString(patchValues)}")

        return CreateDeployInputRequest(
            name = SimpleDateFormat("yyyyMMddHHmmss").format(Date()) + "-部署-flow",
            appName = env.appName,
            type = ChangeOrder.Type.Deploy,
            version = SimpleDateFormat("yyyyMMddHHmmss-SSS").format(Date()),
            description = "通过Flow自动部署",
            envValues = mapOf(env.name to patchValues.toMutableMap()),
            orchestrationSha = sha
        )
    }

    override fun findChangeOrder(
        flowInsId: String,
        removeFlowInstId: Boolean,
        appName: String?,
        envName: String?,
    ): ChangeOrderOnFlowVO {
        var env: EnvDTO? = null
        // 老版部署组件不传appName, envName参数
        if (appName != null && envName != null) {
            env = coreFacades.envFacade.findBrief(appName, envName)
            env?.let {
                env.lockBy?.let { return ChangeOrderOnFlowVO(envLocked = true) }
                env.deployGroupName ?: ChangeOrderOnFlowVO(deployGroupNotFound = true)
            }
        }

        val deployFlow = deployFlowService.find(DeployFlow.ObjectType.FLOW_APP_STACK, flowInsId, appName, envName)
        deployFlow
            ?: return if (appName != null && envName != null && env == null) ChangeOrderOnFlowVO(envNotFound = true) else ChangeOrderOnFlowVO()
        if (removeFlowInstId) {
            deployFlowService.deleteByObjectTypeAndObjectId(deployFlow.objectType, deployFlow.objectId)
            return ChangeOrderOnFlowVO()
        }

        val jobs = changeControllerFacades.changeOrderFacade.findJobBriefByChangeOrder(deployFlow.changeOrderSn)
        if (jobs.isEmpty()) {
            throw BizException(ErrorCode.AS_CHANGE_ORDER_NOT_EXISTED)
        }
        val actualOnlyOneJob = jobs.first()

        // 如果非删除工单的场景下，找不到环境，则返回环境未找到
        if (appName != null && envName != null && env == null && actualOnlyOneJob.changeOrderType != ChangeOrder.Type.Destroy) {
            return ChangeOrderOnFlowVO(envNotFound = true)
        }

        val changeOrderOnFlow = ChangeOrderOnFlow(
            id = actualOnlyOneJob.changeOrderSn,
            status = actualOnlyOneJob.changeOrderState.name
        )
        val taskOnFlow = TaskOnFlow(actualOnlyOneJob.state.name)

        return ChangeOrderOnFlowVO(
            changeOrder = changeOrderOnFlow,
            envTasks = listOf(taskOnFlow)
        )
    }

    private fun buildCreateChangeOrderRequest(
        job: Job?,
        inputSn: String,
        flowInsId: String?,
        sourceType: String?,
        sourceSn: String?,
        changeOrderForm: ChangeOrderForm?,
    ): CreateChangeOrderRequest {
        val list = if (null != job) {
            job.stages.map { it.tasks }.flatten().map {
                TaskStrategyDTO(job.envName, it.locator ?: "", strategies = jacksonObjectMapper().readValue(it.strategyContent))
            }
        } else if (null != changeOrderForm) {
            val dtoList = mutableListOf<TaskStrategyDTO>()
            changeOrderForm.taskForms.forEach { envName, taskFormList ->
                taskFormList.forEach { oneTaskForm ->
                    dtoList.add(
                        TaskStrategyDTO(
                            oneTaskForm.envName,
                            oneTaskForm.locator,
                            strategies = oneTaskForm.items.filter { it.defaultValue!= null }.associate { Pair(it.name, it.defaultValue!!) }.toMutableMap()
                        )
                    )
                }
            }
            dtoList
        } else {
            throw BizException(ErrorCode.AS_STRATEGY_BATCH_STEP_TYPE_NOT_SUPPORTED)
        }
        return CreateChangeOrderRequest(
            changeOrderInputSn = inputSn,
            taskStrategies = list,
            flowInsId = flowInsId,
            sourceType = sourceType,
            sourceSn = sourceSn,
        )
    }

    private fun buildCreateOrderInputRequest(
        job: Job,
        version: String,
        actualArtifactUrl: String,
    ): CreateDeployInputRequest {
        val latestChangeOrderInput = changeControllerFacades.changeOrderInputFacade.find(job.appName, version)
        checkExists(latestChangeOrderInput) {
            logger.error("[DeployFlowBizServiceImpl] ChangeOrderInput not found: job=${jacksonObjectMapper().writeValueAsString(job)}, version=$version, artifact=$actualArtifactUrl")
            ErrorCode.AS_CHANGE_ORDER_INPUT_NOT_EXISTED
        }
        val appOrchestration = coreFacades.appOrchestrationFacade.findAll(job.appName).find {
            it.format == Orchestration.FormatEnum.MANIFEST && it.suitableResourceTypes.contains(job.resourceType)
        }
        checkExists(appOrchestration, job.appName) { ErrorCode.AS_ORC_NOT_FOUND }
        appOrchestration as AppBuiltInOrchestration

        val envValues = latestChangeOrderInput.envValues[job.envName]
        val substitutedEnvValues = envValues?.entries?.associate {
            if (it.key.startsWith(IMAGE_PREFIX) || it.key.startsWith(COMMON_ARTIFACT_PREFIX)) {
                it.key to actualArtifactUrl
            } else {
                it.key to it.value
            }
        } ?: emptyMap()
        return CreateDeployInputRequest(
            name = SimpleDateFormat("yyyyMMddHHmmss").format(Date()) + "-部署-flow",
            appName = job.appName,
            type = ChangeOrder.Type.Deploy,
            version = SimpleDateFormat("yyyyMMddHHmmss-SSS").format(Date()),
            description = "通过Flow自动部署",
            envValues = mapOf(job.envName to substitutedEnvValues.toMutableMap()),
            orchestrationSha = appOrchestration.revision!!.sha
        )
    }

    override fun findPlaceholder(appName: String, type: String, envName: String?): List<Placeholder> {
        return try {
            var resourceType = if (StringUtils.isNotEmpty(envName)) {
                changeControllerFacades.changeableEnvFacade.find(appName, envName!!)?.resourceType
            } else null
            var placeholderList = coreFacades.appOrchestrationFacade.findAll(appName).filter {
                it.format == Orchestration.FormatEnum.MANIFEST
            }.flatMap {
                it as AppBuiltInOrchestration
                it.placeholderList
            }
            if (ResourceTypeEnum.BePending == resourceType) {
                resourceType = ResourceTypeEnum.KUBERNETES
            }
            resourceType?.let {
                placeholderList = placeholderList.filter { it.rsType == resourceType }
            }

            if (type == DeployFlowBizService.IMAGE_TYPE) {
                val imagePlaceholderList = placeholderList.filter {
                    if (envName == null) {
                        it.name.startsWith(IMAGE_PREFIX) || it.name.startsWith(COMMON_ARTIFACT_PREFIX)
                    } else {
                        val changeableEnv = changeControllerFacades.changeableEnvFacade.find(appName, envName)
                        // NOTE jiuya.wb 在指定了环境且能查找到基线的情况下，按照部署架构确定制品过滤方式；如果无法确定，则尝试滤出所有制品
                        if (changeableEnv != null) {
                            when (changeableEnv.resourceType) {
                                ResourceTypeEnum.HOST -> it.name.startsWith(COMMON_ARTIFACT_PREFIX)
                                ResourceTypeEnum.KUBERNETES -> it.name.startsWith(IMAGE_PREFIX)
                                else -> it.name.startsWith(IMAGE_PREFIX) || it.name.startsWith(
                                    COMMON_ARTIFACT_PREFIX
                                )
                            }
                        } else {
                            it.name.startsWith(IMAGE_PREFIX) || it.name.startsWith(COMMON_ARTIFACT_PREFIX)
                        }
                    }
                }
                imagePlaceholderList.forEach {
                    when {
                        it.name.startsWith(IMAGE_PREFIX) -> it.name =
                            it.name.replace(IMAGE_PREFIX, StringUtils.EMPTY)

                        it.name.startsWith(COMMON_ARTIFACT_PREFIX) -> it.name =
                            it.name.replace(COMMON_ARTIFACT_PREFIX, StringUtils.EMPTY)

                        else -> {
                        }
                    }
                }
                imagePlaceholderList
            } else {
                val placeholders = placeholderList
                    .filter { !it.name.startsWith(IMAGE_PREFIX) && !it.name.startsWith(COMMON_ARTIFACT_PREFIX) && (it.predefined == null || !it.predefined!!) }
                    .filter { it.overridable }
                placeholders.forEach { if (it.valueSource == Placeholder.ValueSource.VARIABLE) it.value = "" }
                placeholders
            }

        } catch (e: Exception) {
            emptyList()
        }
    }

    override fun findAppList(): List<AppVO> {
        var page = 0L
        val pageSize = 50L
        var isContinue: Boolean

        val retList: MutableList<AppVO> = mutableListOf()
        do {
            page++
            val records = coreFacades.appFacade.searchPaginated(
                searchAppRequest = SearchAppRequest(),
                current = page,
                pageSize = pageSize
            ).records
            retList.addAll(records)
            isContinue = records.isNotEmpty() && records.size >= pageSize
        } while (isContinue && page <= 10)

        return retList
    }

    override fun findEnvListByAppName(appName: String?): List<Env> {
        return if (null == appName)
            emptyList()
        else
            try {
                val envList = coreFacades.envFacade.findAll(appName)
                // 组件要求后面坠上真实值用于帮助用户通过手动输入填写
                envList.forEach { it.displayName = it.displayName + "-" + it.name }
                envList
            } catch (e: Exception) {
                emptyList()
            }
    }

    //  如果：应用编排中只有一个占位符 && 制品只有一个 && 制品的名称是image
    //      则需要让传入的制品的值，可以匹配上现在编排中唯一的镜像占位符；
    open fun fixSingleImage(env: Env, imageList: List<ImageMapItem>?) {
        if (imageList != null && imageList.size == 1 && imageList.first().name == V1_IMAGE_NAME) {
            // 确定环境资源类型
            val deployGroup = if (env.resourcePoolName.isNullOrBlank() || env.deployGroupName.isNullOrBlank()) {
                null
            } else {
                coreFacades.deployGroupFacade.find(env.resourcePoolName!!, env.deployGroupName!!)
            }
            val rsType = if (deployGroup?.claimList?.firstOrNull()?.belong2Host() == true) {
                ResourceTypeEnum.HOST
            } else {
                ResourceTypeEnum.KUBERNETES
            }
            val appOrchestration = coreFacades.appOrchestrationFacade.findAll(env.appName).firstOrNull {
                it.suitableResourceTypes.contains(rsType) && it.format == Orchestration.FormatEnum.MANIFEST
            }
            appOrchestration as AppBuiltInOrchestration
            val imagePlaceholders = appOrchestration.placeholderList.filter {
                it.predefined == true && it.name.startsWith(IMAGE_PREFIX)
            }
            if (imagePlaceholders.size == 1) {
                imageList.first().name = imagePlaceholders.first().name.substring(IMAGE_PREFIX.length)
            }
        }
    }
}
