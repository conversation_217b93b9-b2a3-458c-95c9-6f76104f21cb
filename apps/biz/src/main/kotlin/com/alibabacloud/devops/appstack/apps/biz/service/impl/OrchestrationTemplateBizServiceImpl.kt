package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.OrchestrationTemplateBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.constant.SystemVariable
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.InvalidChangeItem
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.Orchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.KubernetesCheckOutputItem
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Placeholder
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.template.OrchestrationTemplate
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateCheckRequest
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateCopyRequest
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateCreateRequest
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateUpdateRequest
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateVerifyRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR> <EMAIL>
 * @version : OrgOrchestrationBizServiceImpl, v0.1
 * @date : 2023-01-03 16:11
 **/
@Service
open class OrchestrationTemplateBizServiceImpl(
    @Autowired
    override var i18nMessageService: I18nMessageService,
) : OrchestrationTemplateBizService, OrchestrationBaseBizService(i18nMessageService) {

    private lateinit var coreFacades: CoreFacades

    @Autowired
    fun setCoreFacades(coreFacades: CoreFacades) {
        this.coreFacades = coreFacades
    }

    @Autowired
    lateinit var auditLogService: AuditLogService

    @Can(
        accessList = [
            Access(action = Action.ORG_TEMPLATE_VIEW),
            Access(action = Action.ORG_TEMPLATE_MANAGE)
        ]
    )
    override fun findAll(scopes: List<OrchestrationTemplate.TemplateScopeEnum>): List<OrchestrationTemplate> {
        return coreFacades.orchestrationTemplateFacade.findAll(scopes)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_TEMPLATE_CREATE),
            Access(action = Action.ORG_TEMPLATE_MANAGE)
        ]
    )
    override fun create(request: OrchestrationTemplateCreateRequest): OrchestrationTemplate {
        val orchestrationTemplate = coreFacades.orchestrationTemplateFacade.init(request)
        return orchestrationTemplate
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_TEMPLATE_MANAGE)
        ]
    )
    override fun update(sn: String, templateUpdateRequest: OrchestrationTemplateUpdateRequest): OrchestrationTemplate {
        val orchestrationTemplate = coreFacades.orchestrationTemplateFacade.update(sn, templateUpdateRequest)
        return orchestrationTemplate
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_TEMPLATE_CREATE),
            Access(action = Action.ORG_TEMPLATE_MANAGE)
        ]
    )
    override fun copy(requestTemplate: OrchestrationTemplateCopyRequest): OrchestrationTemplate {
        val orchestrationTemplate = coreFacades.orchestrationTemplateFacade.copy(requestTemplate)
        return orchestrationTemplate
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_TEMPLATE_MANAGE)
        ]
    )
    override fun delete(sn: String): Boolean {
        val template = coreFacades.orchestrationTemplateFacade.find(sn)
        checkExists(template) {  ErrorCode.AS_ORC_TEMPLATE_NOT_FOUND }
        coreFacades.orchestrationTemplateFacade.delete(sn)
        return true
    }

    override fun check(request: OrchestrationTemplateCheckRequest): List<InvalidChangeItem> {
        if (request.componentList.isEmpty()) {
            return emptyList()
        }
        if (request.format == Orchestration.FormatEnum.MANIFEST) {
            val patchValues = request.placeholderList.mapNotNull {
                if (it.valueSource == Placeholder.ValueSource.NULL) {
                    val value: Any = when (it.type) {
                        Placeholder.Type.STRING -> "NULL"
                        Placeholder.Type.NUMBER -> 1
                        Placeholder.Type.FLOAT -> 1.0F
                        Placeholder.Type.BOOLEAN -> true
                        Placeholder.Type.OBJECT -> Any()
                        else -> "unsupported type"
                    }
                    return@mapNotNull Pair(it.name, value)
                } else if (it.name.startsWith("image.") || it.name.startsWith("artifact.")) {
                    return@mapNotNull Pair(it.name, "dummy-image")
                } else {
                    return@mapNotNull null
                }
            }.toMap()

            val resourceType = request.componentList.first().type
            val appNamePlaceholder = request.placeholderList.find {
                it.predefined == true && it.valueSource == Placeholder.ValueSource.VARIABLE && it.value == SystemVariable.APPSTACK_APP_NAME && it.rsType == resourceType
            }
            val placeholderList = if (appNamePlaceholder == null) {
                // 允许编排模板缺少{{ .AppStack.appName }}占位符，在应用编排实例化过程中会被替换成应用名
                request.placeholderList + listOf(
                    Placeholder(
                        name = "appName",
                        description = "应用名",
                        value = SystemVariable.APPSTACK_APP_NAME,
                        valueSource = Placeholder.ValueSource.VARIABLE,
                        rsType = resourceType,
                        overridable = false,
                        predefined = true,
                        type = Placeholder.Type.STRING
                    )
                )
            } else {
                request.placeholderList
            }

            val verifyRequest = OrchestrationTemplateVerifyRequest(
                componentList = request.componentList,
                placeholderList = placeholderList,
                patchValues = patchValues
            )
            val checkOutput = coreFacades.orchestrationTemplateFacade.check(verifyRequest)
            val checkOutputItems = checkOutput.items as List<KubernetesCheckOutputItem>
            return checkInvalid(checkOutputItems, request.componentList)
        } else {
            // 其他格式暂时不支持检查
            return emptyList()
        }
    }
}