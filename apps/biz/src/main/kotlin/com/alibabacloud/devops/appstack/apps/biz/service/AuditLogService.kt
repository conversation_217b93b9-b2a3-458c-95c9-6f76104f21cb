package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditLog

/**
 * <AUTHOR> <EMAIL>
 * @version : AuditLogService, v0.1
 * @date : 2022-12-07 20:57
 **/
interface AuditLogService {

    fun log(eventType: OrgEventType, auditLog: AuditLog)

    fun commonLog(eventType: OrgEventType, id: String, name: String)
}