package com.alibabacloud.devops.appstack.apps.biz.model.mapper

import com.alibabacloud.devops.appstack.apps.biz.model.po.AgentTaskPO
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.core.mapper.BaseMapper
import org.apache.ibatis.annotations.Mapper

@Mapper
interface AgentTaskMapper : BaseMapper<AgentTaskPO> {
}

fun AgentTaskMapper.findLatestByRefName(refName: String): AgentTaskPO? {
    return this.selectOne(
        QueryWrapper<AgentTaskPO>()
            .eq("ref_obj_name", refName)
            .orderByDesc("gmt_create").last("limit 1")
    )
}

fun AgentTaskMapper.findTaskByJobId(jobId: String): AgentTaskPO? {
    return this.selectOne(QueryWrapper<AgentTaskPO>().eq("job_id", jobId))
}

fun AgentTaskMapper.deleteTaskByRefName(refName: String): Int {
    return this.delete(QueryWrapper<AgentTaskPO>()
        .eq("ref_obj_name", refName))
}
