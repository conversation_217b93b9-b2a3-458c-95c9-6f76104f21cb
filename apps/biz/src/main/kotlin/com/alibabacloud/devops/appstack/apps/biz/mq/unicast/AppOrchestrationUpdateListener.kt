package com.alibabacloud.devops.appstack.apps.biz.mq.unicast

import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateService
import com.alibabacloud.devops.appstack.apps.biz.service.impl.AppTemplateOrchestrationConfigBizServiceImpl
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.common.filter.TraceFilter
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.config.IamContext
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfigSyncStatus
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.Orchestration
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.UnicastListener
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.AppOrchestrationUpdateBody
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.UnicastTagEnum
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

/**
 * <AUTHOR> <EMAIL>
 * @version : AppOrchestrationUpdateListener, v0.1
 * @date : 2024-02-19 14:43
 **/
@Slf4k
@Service
class AppOrchestrationUpdateListener: UnicastListener {

    @Autowired
    lateinit var appTemplateService: AppTemplateService

    @Value("\${devops.appstack.unicast.groupId}")
    lateinit var groupId: String

    @Value("\${devops.appstack.unicast.topic}")
    lateinit var topic: String

    override fun expectedGroupId(): String {
        return groupId
    }

    override fun expectedTopic(): String {
        return topic
    }

    override fun expectedTag(): UnicastTagEnum {
        return UnicastTagEnum.APP_ORCHESTRATION_UPDATE
    }

    override fun handle(body: ByteArray) {
        logger.info("Receive AppOrchestrationUpdate content: ${String(body)}")
        val  content = jacksonObjectMapper().readValue(body, AppOrchestrationUpdateBody::class.java)
        // 非内置编排不处理
        if (content.orchestrationStorageType != Orchestration.StorageTypeEnum.BUILTIN) return
        AuthThreadContext.setTenant(content.tenant)
        AuthThreadContext.setUserId(content.operator)
        AuthThreadContext.setTraceId(content.traceId)
        MDC.put(TraceFilter.TRACE_CONTEXT_KEY, content.traceId)
        IamContext.setTenant(content.tenant)
        IamContext.setOperator(content.operator)

        val appTemplateName = appTemplateService.findAppTemplateNameBindedByApp(content.appName) ?: return
        val configSyncStatus = appTemplateService.findConfigSyncStatus(
            templateName = appTemplateName,
            configType = AppTemplateConfig.TypeEnum.Orchestration,
            configInstanceName = AppTemplateOrchestrationConfigBizServiceImpl.DEFAULT_APP_TEMPLATE_ORCHESTRATION_INSTANCE_NAME,
            appName = content.appName
        )

        if (configSyncStatus == null) {
            // 没有模板同步任务，直接返回
            return
        }

        appTemplateService.updateInstanceUpdateStatus(
            templateName = appTemplateName,
            configType = AppTemplateConfig.TypeEnum.Orchestration,
            configInstanceName = AppTemplateOrchestrationConfigBizServiceImpl.DEFAULT_APP_TEMPLATE_ORCHESTRATION_INSTANCE_NAME,
            appName = content.appName,
            lastInstanceUpdateSeqNo = System.currentTimeMillis(),
            instanceUpdateStatus = AppTemplateConfigSyncStatus.InstanceUpdateStatus(
                actualAppRevisionSha = content.orchestrationSha,
                actualAppRevisionGmtModified = content.orchestrationGmtModified,
                actualAppRevisionCommitMessage = content.orchestrationCommitMessage,
                actualAppRevisionCommitAuthor = if(content.orchestrationSha == null) null else content.operator,
            )
        )

    }
}