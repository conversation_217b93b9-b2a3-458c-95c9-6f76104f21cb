package com.alibabacloud.devops.appstack.apps.biz.model.vo

import com.alibabacloud.devops.appstack.libs.model.`do`.cr.ChangeRequest
import com.alibabacloud.devops.appstack.libs.model.vo.VO
import com.aliyun.amp.plugin.annotation.AmpParam
import java.util.*

/**
 * @author: <EMAIL>
 * @date: 2024-05-29 19:37
 * @version: ChangeRequestVO, v0.1
 **/
data class ChangeRequestVO(
    @field:AmpParam(description = "唯一标识符")
    var sn: String,
    @field:AmpParam(description = "应用名")
    var appName: String,
    @field:AmpParam(description = "状态")
    var state: String,
    @field:AmpParam(description = "变更名称")
    var name: String,
    @field:AmpParam(description = "变更状态")
    var type: String,
    @field:AmpParam(description = "代码仓库唯一标识")
    val appCodeRepoSn: String? = null,
    @field:AmpParam(description = "变更代码分支")
    val branch: String? = null,

    @field:AmpParam(description = "拥有者云效id")
    var ownerId: String? = null,
    @field:AmpParam(description = "拥有者的阿里云账号pk")
    var ownerAccountId: String? = null,

    @field:AmpParam(description = "代码分支源分支")
    val originBranch: String? = null,
    @field:AmpParam(description = "代码分支源分支版本")
    val originBranchRevisionSha: String? = null,

    @field:AmpParam(description = "创建者者云效id")
    var creatorId: String? = null,
    @field:AmpParam(description = "创建者的阿里云账号pk")
    var creatorAccountId: String? = null,

    @field:AmpParam(description = "创建时间")
    val gmtCreate: Date,
    @field:AmpParam(description = "修改时间")
    val gmtModified: Date,
    @field:AmpParam(description = "结束后是否自动删除分支")
    val autoDeleteBranchWhenEnd: Boolean = false,
) : VO() {
    override fun convert2AliyunPopObject(convertUserId2AliyunPK: (String) -> String) {
        ownerId?.let {
            ownerAccountId = convertUserId2AliyunPK(it)

        }
        ownerId = null

        creatorId?.let {
            creatorAccountId = convertUserId2AliyunPK(it)

        }
        creatorId = null
    }
}

fun ChangeRequest.toChangeRequestVO(): ChangeRequestVO {
    return ChangeRequestVO(
        sn = sn!!,
        appName = appName,
        state = state.name,
        name = name,
        type = type.name,
        appCodeRepoSn = appCodeRepoSn,
        branch = branch,
        ownerId = ownerId,
        originBranch = originBranch,
        originBranchRevisionSha = originBranchRevision,
        gmtCreate = gmtCreate,
        gmtModified = gmtModified,
        autoDeleteBranchWhenEnd = extInfo.autoDeleteBranchWhenEnd
    )
}