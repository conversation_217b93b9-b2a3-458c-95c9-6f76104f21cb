package com.alibabacloud.devops.appstack.apps.biz.service.impl.handler.flowPermission

import com.alibabacloud.devops.appstack.apps.biz.service.FlowPermissionHandler
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.config.protocol.EnvProtocol
import com.alibabacloud.devops.appstack.libs.model.constant.FlowPermission
import com.alibabacloud.devops.appstack.libs.model.`do`.app.App
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppType
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageBriefVO
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.model.request.CanRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @create 2023/12/8 2:34 PM
 **/
@Service
class SystemReleaseWorkflowRunPermissionHandler : FlowPermissionHandler {

    @Autowired
    lateinit var iamService: IamService

    override fun handle(app: App, stage: ReleaseStageBriefVO): Boolean {
        val env = Env()
        env.appName = app.name
        env.labelList = stage.labels.toMutableList()
        val labelList = env.labelList.map { it.value}
        return if (labelList.isEmpty()) {
            true
        } else {
            val canRequest = CanRequest()
            canRequest.params = mapOf(
                EnvProtocol.CAN_REQUEST_TYPE to labelList
            )
            iamService.can(ProtocolType.AppStackSystem, app.name, Action.SYSTEM_RELEASE_WORKFLOW_RUN, ProtocolType.AppStackSystemLabel, canRequest)
        }
    }

    override fun suitableAppTypes() = listOf(AppType.SYSTEM)

    override fun suitableWorkflowTypes() = listOf(ReleaseWorkflow.TypeEnum.APP_RELEASE)

    override fun suitablePermissions() = listOf(FlowPermission.PERMISSION_PIPELINE_RUN)

    override fun order() = 2

}