package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import com.alibabacloud.devops.appstack.apps.biz.service.AppVariableGroupsBizService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderBizService
import com.alibabacloud.devops.appstack.apps.biz.service.GlobalVarBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.*
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderVO
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR> <EMAIL>
 * @version : ChangeOrderApi, v0.1
 * @date : 2024-01-24 15:57
 **/
@Tag(name = "部署单", description = "部署单领域 API")
@RestController
@RequestMapping("/api/v2")
open class ChangeOrderOpenApi {

    @Autowired
    lateinit var changeOrderBizService: ChangeOrderBizService

    @Autowired
    lateinit var appVariableGroupsBizService: AppVariableGroupsBizService

    @Autowired
    lateinit var globalVarBizService: GlobalVarBizService

    @VpcApi
    @Operation(summary = "读取部署单使用的物料和工单状态", operationId = "GetChangeOrder")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已查找到工单信息"),
            ApiResponse(
                responseCode = "404",
                description = "未找到工单信息",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/changeOrders/{changeOrderSn}")
    fun getOrderSummary(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @PathVariable("changeOrderSn") changeOrderSn: String,
    ): ChangeOrderSummary {
        val changeOrderVO = changeOrderBizService.find(changeOrderSn)
            ?: throw BizException(errorCode = ErrorCode.AS_CHANGE_ORDER_NOT_EXISTED)

        val jobs = changeOrderVO.jobs.map { jobVO ->
            JobSummary(
                envName = jobVO.envName,
                appOrchestration = jobVO.appOrchestration!!,
                variableGroups = jobVO.profiles?.mapNotNull { tagVarGroup ->
                    val sha =  when(tagVarGroup.type){
                        VariableGroup.Type.APP -> appVariableGroupsBizService.findProfilesByTag(appName, tagVarGroup.tag).revision.sha
                        VariableGroup.Type.GLOBAL -> globalVarBizService.findGlobalVarContentByTag(tagVarGroup.name, tagVarGroup.tag).revision?.sha
                        VariableGroup.Type.TEMPLATE -> null
                    }
                    sha?.let {
                        TypedVariableGroup(
                            type = tagVarGroup.type,
                            name = tagVarGroup.name,
                            revisionSha = it,
                        )
                    }
                } ?: emptyList(),
                stages = jobVO.stages.map { stageVO ->
                    StageSummary(
                        tasks = stageVO.tasks.map { taskVO ->
                            TaskSummary(
                                state = taskVO.state,
                                spec = taskVO.spec,
                                errorMessage = taskVO.errorMessage,
                                behavior = taskVO.behavior
                            )
                        }
                    )
                }
            )
        }

        return ChangeOrderSummary(
            name = changeOrderVO.name,
            sn = changeOrderVO.sn,
            version = changeOrderVO.version,
            description = changeOrderVO.description,
            type = changeOrderVO.type,
            state = changeOrderVO.state,
            startedAt = changeOrderVO.startedAt,
            endedAt = changeOrderVO.endedAt,
            creator = changeOrderVO.creator,
            gmtCreate = changeOrderVO.gmtCreate,
            jobs = jobs,
        )
    }

    @VpcApi
    @Operation(summary = "创建部署单", operationId = "CreateChangeOrder")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已创建工单"),
            ApiResponse(
                responseCode = "404",
                description = "未找到应用或环境信息",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/apps/{appName}/changeOrders")
    fun createOrder(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @RequestBody request: CreateChangeOrderReq,
    ): ChangeOrderVO {
        return changeOrderBizService.create(appName, request)
    }

}