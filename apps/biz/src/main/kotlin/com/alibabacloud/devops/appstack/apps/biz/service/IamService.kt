package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.MemberRole
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.RolePolicy
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.Resource
import com.alibabacloud.devops.iam.model.ResourcePlayer
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.RoleWithPlayer
import com.alibabacloud.devops.iam.model.request.CanRequest
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
/**
 * <AUTHOR> <EMAIL>
 * @version : IamService, v0.1
 * @date : 2021-11-29 16:14
 **/
interface IamService {

    fun rebuildIamConfig()

    fun registerResource(protocolType: ProtocolType, resourceName: String, orgId: String, ownerId: String)

    fun unregisterResource(protocolType: ProtocolType, resourceName: String, orgId: String, operatorId: String)

    fun findRole(protocolType: ProtocolType, roleName: String): Role?

    fun findPlayerRoles(protocolType: ProtocolType, resourceName: String): Map<ResourcePlayer, MutableList<Role>>

    fun findRolePlayers(protocolType: ProtocolType, resourceNameList: List<String>, roleNameList: List<String>) : Map<String, List<RoleWithPlayer>>

    fun findRolePlayers(protocolType: ProtocolType, resourceNameList: List<String>, roleName: String): Map<String, List<String>>

    fun addRoleMember(
        protocolType: ProtocolType,
        resourceName: String,
        roleName: String,
        playerList: List<ResourcePlayerRequest>,
    )

    fun updateRole(
        protocolType: ProtocolType,
        resourceName: String,
        roleName: String,
        playerList: List<ResourcePlayerRequest>,
    )

    fun updatePlayer(
        protocolType: ProtocolType,
        resourceName: String,
        subjectId: String,
        subjectType: SubjectType,
        roleNameList: List<String>,
    )

    fun countResource(
        protocolType: ProtocolType,
        subjectId: String,
        subjectType: SubjectType,
        search: String? = "",
    ): Int

    fun searchResourceName(
        protocolType: ProtocolType,
        subjectId: String,
        subjectType: SubjectType,
        search: String? = "",
        page: Int,
        size: Int,
    ): List<String>

    @Deprecated("接口有性能问题, 容易超时， 慎用")
    fun searchResource(
        protocolType: ProtocolType,
        subjectId: String,
        subjectType: SubjectType,
        search: String? = "",
        page: Int,
        size: Int,
        containPlayers: Boolean = false,
    ): List<Resource>

    fun countResourceWithPolicy(
        protocolType: ProtocolType,
        subjectId: String,
        subjectType: SubjectType,
        policyName: String,
        search: String? = "",
    ): Int

    fun searchResourceWithPolicy(
        protocolType: ProtocolType,
        subjectId: String,
        subjectType: SubjectType,
        policyName: String,
        search: String? = "",
        page: Int,
        size: Int,
    ): List<String>

    fun searchResourceWithPolicy(
        protocolType: ProtocolType,
        playersList: List<ResourcePlayerRequest>,
        policyName: String,
        search: String? = "",
        page: Int,
        size: Int,
    ): List<String>

    fun can(protocolType: ProtocolType, resourceName: String, action: Action): Boolean

    fun can(protocolType: ProtocolType, resourceName: String, action: Action, abacProtocolType: ProtocolType, canRequest: CanRequest): Boolean

    fun fetchRolePermission(protocolType: ProtocolType, filter: List<Action>): List<RolePolicy>

    fun fetchOrgMembers(): List<MemberRole>

    fun checkPlayersExist(players: List<ResourcePlayerRequest>): Boolean

    fun deletePlayer(subjectType: SubjectType, subjectId: String)

    fun getTeamPlayer(orgId: String, userId: String, containSuperior: Boolean = true): List<ResourcePlayerRequest>

    fun getGroupPlayer(orgId: String, userId: String): List<ResourcePlayerRequest>

    fun checkUserExist(userId: String)

    fun listUserIdsByTeam(orgId: String, teamId: String): List<String>

    fun listUserIdsByGroup(orgId: String, groupId: String): List<String>
}