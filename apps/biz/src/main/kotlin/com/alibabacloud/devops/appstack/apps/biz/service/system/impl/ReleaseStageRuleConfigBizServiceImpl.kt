package com.alibabacloud.devops.appstack.apps.biz.service.system.impl

import com.alibabacloud.devops.appstack.apps.biz.annotation.IsSystem
import com.alibabacloud.devops.appstack.apps.biz.service.system.ReleaseStageRuleConfigBizService
import com.alibabacloud.devops.appstack.apps.biz.service.StageRuleConfigBaseService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.StageRuleConfig
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.temp.StageRuleConfigRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2023-11-08
 */
@Service
open class ReleaseStageRuleConfigBizServiceImpl: ReleaseStageRuleConfigBizService {

    @Autowired
    lateinit var stageRuleConfigBaseService: StageRuleConfigBaseService

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    @IsSystem
    override fun find(systemName: String, releaseWorkflowSn: String, releaseStageSn: String): StageRuleConfig? {
        return stageRuleConfigBaseService.find(systemName, releaseWorkflowSn, releaseStageSn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun upsert(
        systemName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        stageRuleConfigRequest: StageRuleConfigRequest
    ): StageRuleConfig? {
        return stageRuleConfigBaseService.upsert(systemName, releaseWorkflowSn, releaseStageSn, stageRuleConfigRequest)
    }
}