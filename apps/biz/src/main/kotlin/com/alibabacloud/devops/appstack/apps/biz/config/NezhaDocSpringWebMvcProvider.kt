package com.alibabacloud.devops.appstack.apps.biz.config

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import org.springdoc.webmvc.core.SpringWebMvcProvider
import org.springframework.core.annotation.AnnotationUtils
import org.springframework.web.method.HandlerMethod
import org.springframework.web.servlet.mvc.method.RequestMappingInfo
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping
import java.util.function.BinaryOperator
import java.util.function.Function
import java.util.function.Supplier
import java.util.stream.Collectors

/**
 * <AUTHOR>
 * @date 2024-05-21
 */
class NezhaDocSpringWebMvcProvider: SpringWebMvcProvider() {

    override fun getHandlerMethods(): MutableMap<Any?, Any?> {
        if (handlerMethods == null) {
            val beansOfTypeRequestMappingHandlerMapping = applicationContext.getBeansOfType(
                RequestMappingHandlerMapping::class.java
            )
            handlerMethods = beansOfTypeRequestMappingHandlerMapping.values.stream()
                .map { obj: RequestMappingHandlerMapping -> obj.handlerMethods }
                .map { obj: Map<RequestMappingInfo, HandlerMethod> -> obj.entries }
                .flatMap { obj: Set<Map.Entry<RequestMappingInfo, HandlerMethod>> -> obj.stream() }
                .filter{ entry->
                    val method = entry.value.method
                    AnnotationUtils.getAnnotation(method, VpcApi::class.java)!= null
                }
                .collect(
                    Collectors.toMap(
                        Function { (key): Map.Entry<RequestMappingInfo, HandlerMethod> -> key },
                        Function { (_, value): Map.Entry<RequestMappingInfo, HandlerMethod> -> value },
                        BinaryOperator { a1: HandlerMethod, a2: HandlerMethod? -> a1 },
                        Supplier { LinkedHashMap() })
                )
        }

        return handlerMethods
    }
}