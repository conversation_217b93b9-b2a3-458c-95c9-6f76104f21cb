package com.alibabacloud.devops.appstack.apps.biz.api.rest.hook.flow

import com.alibabacloud.devops.appstack.apps.biz.model.JobCallback
import com.alibabacloud.devops.appstack.apps.biz.service.AgentService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.swagger.v3.oas.annotations.Hidden
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@Hidden
@Tag(name = "FlowCallback", description = "Flow 回调 API")
@RestController
@RequestMapping("/callbackapi/task")
@Slf4k
class FlowCallbackApi {

    @Autowired
    lateinit var agentService: AgentService

    @PostMapping("/agent-install/callback")
    fun handleStatusCallback(
        @RequestParam("orgId") orgId: String,
        @RequestBody callback: JobCallback,
    ) {
        logger.info("flow callback ${orgId} ${jacksonObjectMapper().writeValueAsString(callback)}")
        AuthThreadContext.setTenant(orgId)
        agentService.updateTaskStatus("${callback.id}", callback.status)
    }
}