package com.alibabacloud.devops.appstack.apps.biz.service.client.api

import com.alibabacloud.devops.appstack.libs.model.response.FlowApiResponse
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.databind.node.ArrayNode
import com.fasterxml.jackson.databind.node.ObjectNode
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient
import retrofit2.http.*

/**
 * <AUTHOR> liyebin.lyb
 * @date : 2022/8/30 3:22 PM
 */

@RetrofitClient(baseUrl = "\${service.flow-service-connection.url}")
interface FlowPipelineApi {

    companion object {
        const val REF_OBJECT_TYPE = "APPSTACK_APP_ID"
    }

    /**
     * 查找应用下关联流水线
     */
    @GET("/inner/api/pop/v2/pipelines/queryByRegion")
    fun queryByRegion(
        @HeaderMap headers: Map<String, String>,
        @Query("refObjectType") refObjectType: String = REF_OBJECT_TYPE,
        @Query("refObjectId") refObjectId: String,
        @Query("pageSize") pageSize: Int = 100,
        @Query("pageStart") pageStart: Int = 0,
    ): FlowApiResponse<FlowPipelineResponse>


    /**
     * 移除流水线关联
     */
    @FormUrlEncoded
    @POST("/inner/api/pop/v2/pipelines/relation/delete")
    fun removePipelineBinding(
        @HeaderMap headers: Map<String, String>,
        @Field("refObjectType") refObjectType: String = REF_OBJECT_TYPE,
        @Field("refObjectId") refObjectId: String,
        @Field("pipelineId") pipelineId: String
    ): FlowApiResponse<Boolean>

    @GET("/inner/api/pipelines/{id}")
    fun get(
        @HeaderMap headers: Map<String, String>,
        @Path("id") id: Long,
    ): FlowApiResponse<FlowPipelineInfo>

}

@JsonIgnoreProperties(ignoreUnknown = true)
data class FlowPipelineResponse(
    val total: Long,
    val dataList: List<FlowPipeline>
) {

    @JsonIgnoreProperties(ignoreUnknown = true)
    data class FlowPipeline(
        val pipelineId: String,
        val pipelineName: String,
    )
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class FlowPipelineInfo(
    val pipelineObjRelList: ArrayNode,
    val pipelineVo: ObjectNode
)
