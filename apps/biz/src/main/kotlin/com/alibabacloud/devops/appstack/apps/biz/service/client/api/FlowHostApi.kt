package com.alibabacloud.devops.appstack.apps.biz.service.client.api

import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.DeployMachineLog
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.EcsHostOnFlow
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ExperienceHostOnFlow
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ResponseHostGroupOnFlow
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ResponseHostOnFlow
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.SelfHostOnFlow
import com.alibabacloud.devops.appstack.libs.model.response.FlowApiResponse
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient
import retrofit2.http.GET
import retrofit2.http.HeaderMap
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * @author: <EMAIL>
 * @date: 2022-07-08 15:07
 * @version: FlowHostGroupApi, v0.1
 **/
@RetrofitClient(baseUrl = "\${service.flow-service-connection.url}", callTimeoutMs = 20 * 1000)
interface FlowHostApi {

    @POST("/inner/api/v2/deploy/machine/group/demo/ecs")
    fun createExperience(
        @HeaderMap header: Map<String, String>,
        @Query("aliyunRegion") aliyunRegion: String,
        @Query("machineGroupName") machineGroupName: String,
        @Query("accessKeyId") accessKeyId: String,
        @Query("accessSecret") accessSecret: String,
        @Query("instanceId") instanceId: String,
        @Query("scope") scope: String
    ): FlowApiResponse<Long>

    @GET("/inner/api/v2/deploy/machine/group/demo/ecs/result")
    fun fetchCreateExperienceResult(
        @HeaderMap header: Map<String, String>,
        @Query("instanceId") instanceId: String,
        @Query("scope") scope: String
    ): FlowApiResponse<Long?>


    @GET("/inner/api/v2/aliyun/ecs/list")
    fun findEcs(
        @HeaderMap headers: Map<String, String>,
        @Query("scId") scId: Long,
        @Query("aliyunRegionId") aliyunRegionId: String,
        @Query("scope") scope: String,
        @Query("checkAllEcsMachine") checkAllEcsMachine: Boolean,
        @Query("instanceIds") instanceIds: String?,
    ): FlowApiResponse<ResponseHostOnFlow<EcsHostOnFlow>>

    @GET("/inner/api/v2/staragent/machine/list")
    fun findSelfHost(
        @HeaderMap header: Map<String, String>,
        @Query("purpose") purpose: String,
        @Query("scope") scope: String,
        @Query("instanceIds") instanceIds: String?
    ): FlowApiResponse<List<SelfHostOnFlow>>

    @GET("/inner/api/v2/staragent/machine/getAddMachineCommand")
    fun fetchInstallCommand(
        @HeaderMap header: Map<String, String>,
        @Query("purpose") purpose: String,
        @Query("scope") scope: String,
        @Query("os") os: String
    ): FlowApiResponse<String>

    @GET("/inner/api/v2/deploy/machine/group/fetch")
    fun findAllByGroupId(
        @HeaderMap headers: Map<String, String>,
        @Query("groupId") groupId: Long,
        @Query("checkMachineStatus") checkMachineStatus: Boolean
    ): FlowApiResponse<ResponseHostGroupOnFlow<ExperienceHostOnFlow>>

    @POST("/inner/api/pop/deploy/order/getMachineLog")
    fun fetchMachineDeployLog(
        @HeaderMap headers: Map<String, String>,
        @Query("deployOrderId") deployOrderId: Long,
        @Query("machineSn") machineSn: String
    ): FlowApiResponse<DeployMachineLog>

    @GET("/inner/api/v2/build/isRunnerFeature?feature=deploy_runner")
    fun isRunnerFeature(
        @HeaderMap header: Map<String, String>
    ): FlowApiResponse<Boolean>
}