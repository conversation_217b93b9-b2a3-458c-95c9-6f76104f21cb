package com.alibabacloud.devops.appstack.apps.biz.model.ext

import com.alibabacloud.devops.appstack.apps.biz.model.po.OrgConfigItemPO
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil.jacksonObjectMapper
import com.alibabacloud.devops.appstack.libs.model.`do`.OrgConfigItem

/**
 * <AUTHOR>
 * @date 2023-12-23
 */
fun OrgConfigItem.toPO(): OrgConfigItemPO {
    val po = OrgConfigItemPO()
    po.name = name
    po.version = version.name
    po.content = jacksonObjectMapper().writeValueAsString(parseAttributes())
    return po
}

fun OrgConfigItemPO.toModel(): OrgConfigItem {
    val attributes = jacksonObjectMapper().readValue(content!!, Map::class.java).toMutableMap()
    attributes[OrgConfigItem::name.name] = name!!
    attributes[OrgConfigItem::version.name] = version!!
    return jacksonObjectMapper().readValue(
        jacksonObjectMapper().writeValueAsString(attributes),
        OrgConfigItem::class.java
    )
}