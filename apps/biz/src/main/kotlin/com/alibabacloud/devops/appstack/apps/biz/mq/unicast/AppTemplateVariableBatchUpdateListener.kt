package com.alibabacloud.devops.appstack.apps.biz.mq.unicast

import com.alibabacloud.devops.appstack.apps.biz.service.AppVariableGroupsBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.filter.TraceFilter
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.config.IamContext
import com.alibabacloud.devops.appstack.libs.model.`do`.apptemplate.AppTemplateSyncProgress
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.Profile
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.Variable
import com.alibabacloud.devops.appstack.libs.model.request.ProfileRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateVariableGroupDryRunRequest
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.BroadcastSender
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.UnicastListener
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.AppTemplateSyncProgressBody
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.AppTemplateVariableGroupBatchUpdateBody
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.BroadcastTagEnum
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.UnicastTagEnum
import com.alibabacloud.devops.iam.exception.IamForbiddenException
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.apache.commons.lang3.StringUtils
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.lang.Exception

@Slf4k
@Service
class AppTemplateVariableBatchUpdateListener : UnicastListener {

    @Autowired
    lateinit var appVariableGroupsBizService: AppVariableGroupsBizService

    @Autowired
    lateinit var broadcastSender: BroadcastSender

    @Autowired
    lateinit var i18nMessageService: I18nMessageService

    @Value("\${devops.appstack.unicast.groupId}")
    lateinit var groupId: String

    @Value("\${devops.appstack.unicast.topic}")
    lateinit var topic: String

    override fun expectedGroupId(): String {
        return groupId
    }

    override fun expectedTopic(): String {
        return topic
    }

    override fun expectedTag(): UnicastTagEnum {
        return UnicastTagEnum.APPTEMPLATE_VARIABLE_BATCH_UPDATE
    }

    override fun handle(body: ByteArray) {
        logger.info("Receive AppTemplateVariableBatchUpdate content: ${String(body)}")
        val content = jacksonObjectMapper().readValue(body, AppTemplateVariableGroupBatchUpdateBody::class.java)
        AuthThreadContext.setTenant(content.tenant)
        AuthThreadContext.setUserId(content.operator)
        AuthThreadContext.setTraceId(content.traceId)
        AuthThreadContext.setUserIP(content.ip)
        AuthThreadContext.setLocale(content.locale)
        MDC.put(TraceFilter.TRACE_CONTEXT_KEY, content.traceId)
        IamContext.setTenant(content.tenant)
        IamContext.setOperator(content.operator)

        content.appNames.forEachIndexed { index, appName ->
            try {
                val errorMessage = updateAppVariable(templateName = content.appTemplateName, appName = appName, mode = content.mode, profile = content.profile, commitMsg = content.commitMsg)
                broadcastSender.send(
                    BroadcastTagEnum.APP_TEMPLATE_SYNC_PROGRESS,
                    jacksonObjectMapper().writeValueAsBytes(
                        AppTemplateSyncProgressBody(
                            transactionId = content.transactionId,
                            progress = AppTemplateSyncProgress(
                                totalCount = content.appNames.size,
                                currentIndex = index+1,
                                appSyncResult = AppTemplateSyncProgress.AppSyncResult(
                                    appName = appName,
                                    success = StringUtils.isBlank(errorMessage),
                                    errorMessage = errorMessage
                                )
                            )
                        )
                    )
                )
            } catch (e: Exception) {
                logger.error(
                    "Loop BatchUpdate AppTemplate variable fail for appName[$appName], templateName[${content.appTemplateName}]",
                    e
                )
            }
        }
    }

    private fun updateAppVariable(templateName: String, appName: String, mode: UpdateVariableGroupDryRunRequest.Mode, profile: Profile, commitMsg: String): String? {
        try {
            val appProfile = appVariableGroupsBizService.findAllProfiles(appName)
            val profileRequest = ProfileRequest(
                name = profile.name,
                displayName = appProfile?.profileMap?.get(profile.name)?.displayName ?: profile.displayName,
                vars = appVariableGroupsBizService.updateDryrun(appName, profile.name, UpdateVariableGroupDryRunRequest(mode = mode, variables = profile.vars)),
            ).apply {
                this.fromRevisionSha = appProfile?.revision?.sha
                this.message = commitMsg
            }
            // 新建变量组
            if (appProfile == null || !appProfile.profileMap.containsKey(profile.name)) {
                appVariableGroupsBizService.createProfile(appName, profileRequest)
            } else {
                // 更新变量组
                appVariableGroupsBizService.updateProfile(appName, profileRequest)
            }
            return null
        } catch (e: Exception) {
            logger.error("AppTemplate Variable BatchUpdate Error. appName: $appName, templateName: $templateName", e)
            return if (e is BizException) {
                i18nMessageService.getErrorMessage(e)
            } else if (e is IamForbiddenException) {
                i18nMessageService.getErrorMessage(BizException(errorCode = ErrorCode.AS_PERMISSION_DENIED))
            } else {
                i18nMessageService.getErrorMessage(BizException(errorCode = ErrorCode.AS_UNKNOWN))
            }
        }
    }


}