package com.alibabacloud.devops.appstack.apps.biz.model.ext

import com.alibabacloud.devops.appstack.apps.biz.model.OnlineGuidance
import com.alibabacloud.devops.appstack.apps.biz.model.po.OnlineGuidancePO
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue

/**
 * @author: <EMAIL>
 * @date: 2022-08-25 22:20
 * @version: OnlineGuidance, v0.1
 **/

fun OnlineGuidancePO.toModel() = OnlineGuidance(
    name = name!!,
    appName = appName,
    stepList = jacksonObjectMapper().readValue(context!!)
)

fun OnlineGuidance.toPO(): OnlineGuidancePO {
    val onlineGuidancePO = OnlineGuidancePO()
    onlineGuidancePO.name = name
    onlineGuidancePO.appName = appName!!
    onlineGuidancePO.context = jacksonObjectMapper().writeValueAsString(stepList)
    return onlineGuidancePO
}