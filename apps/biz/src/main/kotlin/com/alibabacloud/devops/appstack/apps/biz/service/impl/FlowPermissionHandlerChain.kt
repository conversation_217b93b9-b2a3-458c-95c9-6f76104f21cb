package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.FlowPermissionHandler
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.constant.FlowPermission
import com.alibabacloud.devops.appstack.libs.model.`do`.app.App
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageBriefVO
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowBriefVO
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @create 2023/12/8 11:56 AM
 **/
@Service
class FlowPermissionHandlerChain {

    @Autowired
    lateinit var handlers: List<FlowPermissionHandler>

    fun handle(
        permission: FlowPermission,
        app: App,
        workflow: ReleaseWorkflowBriefVO,
        stage: ReleaseStageBriefVO
    ): Boolean {
        logger.info("handle forwardFlow permission: $permission, app: ${app.name}, workflowSn: ${workflow.sn} stageSn: ${stage.sn}")

        val currentHandlers = handlers.filter {
            it.suitablePermissions().contains(permission)
                    && it.suitableAppTypes().contains(app.type)
                    && it.suitableWorkflowTypes().contains(workflow.type)
        }.sortedBy { it.order() }

        if (currentHandlers.isEmpty()) {
            logger.error("failed to forwardFlow: unSupported Permission check: Permission: ${permission.name}, appName: ${app.name}, workflowSn: ${workflow.sn} stageSn: ${stage.sn}")
        }

        var exception: Throwable? = null
        val handler = currentHandlers.firstOrNull {
            var res: Boolean
            try {
                res = it.handle(app, stage)
                logger.info("handle forwardFlow by handler: ${it.javaClass} permission: $permission, app: ${app.name}, workflowSn: ${workflow.sn} stageSn: ${stage.sn} result:$res")
            } catch (e: Throwable) {
                logger.error("failed to forwardFlow: checkPermission: Permission: ${permission.name}, appName: ${app.name}, workflowSn: ${workflow.sn} stageSn: ${stage.sn}", e)
                exception = e
                res = false
            }
            res
        }
        // 如果验权失败，并且有异常，则抛出
        if (handler == null && exception != null) {
            throw BizException(ErrorCode.AS_IAM_CHECK_FAILED, exception!!.message)
        }
        return handler != null
    }

}