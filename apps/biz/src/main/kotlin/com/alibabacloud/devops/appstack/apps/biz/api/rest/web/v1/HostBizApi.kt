package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.HostBizService
import com.alibabacloud.devops.appstack.libs.model.request.QueryHostRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.HostVO
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-07-13 17:23
 * @version: HostBizApi, v0.1
 **/
@Tag(name = "Host", description = "主机组相关 API")
@RestController
@RequestMapping("api/v1")
open class HostBizApi {
    @Autowired
    lateinit var hostBizService: HostBizService

    @Operation(summary = "查找主机列表")
    @PostMapping("/hosts:search")
    fun findAllHost(
        @RequestBody queryHostRequest: QueryHostRequest
    ): Response<List<HostVO>> {
        return Response.success(
            hostBizService.findAll(queryHostRequest)
        )
    }
}