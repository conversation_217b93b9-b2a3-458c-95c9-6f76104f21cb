package com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.impl

import com.alibabacloud.devops.appstack.apps.biz.config.FlowAuthConfiguration
import com.alibabacloud.devops.appstack.apps.biz.model.State
import com.alibabacloud.devops.appstack.apps.biz.model.Step
import com.alibabacloud.devops.appstack.apps.biz.model.StepProgress
import com.alibabacloud.devops.appstack.apps.biz.service.client.api.FlowPipelineApi
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.StepHandlerService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.model.ErrorEntry
import com.alibabacloud.devops.appstack.libs.common.util.TokenUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkBiz
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR> liyebin.lyb
 * @date : 2022/8/26 2:46 PM
 */
@Service
class WorkflowConfigStepHandlerServiceImpl : StepHandlerService {

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var flowV1PipelineApi: FlowPipelineApi

    @Autowired
    lateinit var flowAuthConfig: FlowAuthConfiguration

    override fun handle(appName: String, step: Step): StepProgress {
        return if (hasWorkflow(appName) || hasFlowPipeline(appName)) {
            StepProgress(
                needUpdate = true,
                step = Step(
                    name = step.name,
                    state = State.FINISH
                )
            )
        } else {
            StepProgress(needUpdate = false, step = Step(name = step.name, state = step.state))
        }
    }

    private fun hasWorkflow(appName: String): Boolean {
        return workflowControllerFacade.releaseWorkflowFacade.findAllBrief(appName).isNotEmpty()
    }

    private fun hasFlowPipeline(refObjectId: String): Boolean {
        val response = flowV1PipelineApi.queryByRegion(headers = getHeaders(), refObjectId = refObjectId)
        checkExists(response) {
            ErrorCode.AS_HTTP_CALL_ERROR
        }
        checkBiz(response.successful) {
            ErrorEntry(code = ErrorCode.AS_RES_CALL_FLOW_V1_PIPELINE_FAILED, message = response.errorMessage)
        }
        return if (response.data != null) {
            response.data!!.dataList.isNotEmpty()
        } else {
            false
        }
    }

    private fun getHeaders(): Map<String, String> {
        return TokenUtil.generateAuthHeaders(flowAuthConfig.appId, flowAuthConfig.appSecret)
    }
}