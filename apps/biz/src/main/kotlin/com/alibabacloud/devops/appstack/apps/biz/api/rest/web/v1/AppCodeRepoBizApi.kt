package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.AppCodeRepoBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppCodeRepo
import com.alibabacloud.devops.appstack.libs.model.request.CreateAppCodeRepoRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppCodeRepoRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-06-23 15:13
 * @version: AppCodeRepoBizApi, v0.1
 **/
@Tag(name = "AppCodeRepo", description = "应用代码仓库 API")
@RestController
@RequestMapping("/api/v1")
open class AppCodeRepoBizApi {

    @Autowired
    lateinit var appCodeRepoBizService: AppCodeRepoBizService

    @Operation(summary = "创建应用代码仓库")
    @PostMapping("/apps/{appName}/codeRepos")
    fun createAppCodeRepo(
        @PathVariable appName: String,
        @RequestBody createAppCodeRepoRequest: CreateAppCodeRepoRequest
    ): Response<AppCodeRepo> {
        return Response.success(appCodeRepoBizService.create(appName, createAppCodeRepoRequest))
    }

    @Operation(summary = "删除应用代码仓库")
    @DeleteMapping("/apps/{appName}/codeRepos/{sn}")
    fun deleteAppCodeRepo(
        @PathVariable appName: String,
        @PathVariable sn: String,
    ): Response<AppCodeRepo> {
        appCodeRepoBizService.delete(appName, sn)
        return Response.success()
    }

    @Operation(summary = "修改应用代码仓库")
    @PutMapping("/apps/{appName}/codeRepos/{sn}")
    fun updateAppCodeRepo(
        @PathVariable appName: String,
        @PathVariable sn: String,
        @RequestBody updateAppCodeRepoRequest: UpdateAppCodeRepoRequest
    ): Response<AppCodeRepo> {
        return Response.success(appCodeRepoBizService.update(appName, sn, updateAppCodeRepoRequest))
    }

    @Operation(summary = "查找应用下的代码仓库列表")
    @GetMapping("/apps/{appName}/codeRepos")
    fun findAllAppCodeRepo(@PathVariable appName: String): Response<List<AppCodeRepo>> {
        return Response.success(appCodeRepoBizService.findAll(appName))
    }

    @Operation(summary = "查找单个应用代码仓库")
    @GetMapping("/apps/{appName}/codeRepos/{sn}")
    fun findAppCodeRepo(@PathVariable appName: String, @PathVariable sn: String): Response<AppCodeRepo> {
        return Response.success(appCodeRepoBizService.find(appName, sn))
    }

}
