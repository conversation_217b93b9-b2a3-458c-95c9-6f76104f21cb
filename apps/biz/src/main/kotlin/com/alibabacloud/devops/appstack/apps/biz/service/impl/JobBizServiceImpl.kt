package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.DeployFlowService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.apps.biz.service.JobBizService
import com.alibabacloud.devops.appstack.apps.biz.service.OrgConfigItemBizService
import com.alibabacloud.devops.appstack.libs.change.controller.spring.boot.starter.service.ChangeControllerFacades
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.model.`do`.OnlyViewAccessableAppV1OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.SearchAppJobRequest
import com.alibabacloud.devops.appstack.libs.model.request.changecontroller.SearchEnvJobRequest
import com.alibabacloud.devops.appstack.libs.model.vo.JobRecord
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class JobBizServiceImpl : JobBizService {

    @Autowired
    lateinit var changeControllerFacades: ChangeControllerFacades

    @Autowired
    lateinit var deployFlowService: DeployFlowService

    @Autowired
    lateinit var orgConfigItemBizService: OrgConfigItemBizService

    @Autowired
    lateinit var iamService: IamService

    override fun searchEnvJobPaginated(
        searchEnvJobRequest: SearchEnvJobRequest,
        current: Long,
        pageSize: Long,
    ): Pagination<JobRecord> {
        val searchEnvJobPaginated = changeControllerFacades.jobFacade.searchEnvJobPaginated(
            searchEnvJobRequest = searchEnvJobRequest,
            current = current,
            pageSize = pageSize
        )
        val deployFlowMap =
            deployFlowService.listByChangeOrderSns(searchEnvJobPaginated.records.map { it.changeOrderSn })
                .associateBy { it.changeOrderSn }
        searchEnvJobPaginated.records.forEach { record ->
            record.source = deployFlowMap[record.changeOrderSn]?.source
        }
        return searchEnvJobPaginated
    }

    override fun searchAppJobPaginated(
        searchAppJobRequest: SearchAppJobRequest,
        current: Long,
        pageSize: Long,
    ): Pagination<JobRecord> {
        // 判断权限
        // 应用列表非空时，先判断企业级权限
        if (!searchAppJobRequest.appNames.isNullOrEmpty()) {
            // 先判断企业级权限，再判断应用权限
            if (!iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_VIEW)
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_MANAGE)
                && searchAppJobRequest.appNames!!.firstOrNull { !iamService.can(ProtocolType.AppStackApp, it, Action.APP_VIEW) } != null) {
                throw BizException(ErrorCode.AS_PERMISSION_DENIED)
            }
        } else {
            // 企业下查看部署单
            val orgConfigItem = orgConfigItemBizService.find(OrgConfigItem.ONLY_VIEW_ACCESSABLE_APP)
            // 如果勾选了【仅支持查看我有权限的应用列表】
            if ((orgConfigItem as? OnlyViewAccessableAppV1OrgConfigItem)?.enable == true) {
                // 如果查询全部但没有查看全部应用权限
                if (searchAppJobRequest.creators?.isEmpty() != false
                    && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_VIEW)
                    && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_MANAGE)) {
                    // 只查询我有权限的应用
                    searchAppJobRequest.appNames = iamService.searchResourceName(
                        protocolType = ProtocolType.AppStackApp,
                        subjectType = SubjectType.User,
                        subjectId = AuthUtil.getUserId(),
                        search = searchAppJobRequest.search,
                        page = 1,
                        size = 1000,
                    )
                }
            }
        }
        val searchAppJobPaginated = changeControllerFacades.jobFacade.searchAppJobPaginated(
            searchAppJobRequest = searchAppJobRequest,
            current = current,
            pageSize = pageSize
        )
        val deployFlowMap =
            deployFlowService.listByChangeOrderSns(searchAppJobPaginated.records.map { it.changeOrderSn })
                .associateBy { it.changeOrderSn }
        searchAppJobPaginated.records.forEach { record ->
            record.source = deployFlowMap[record.changeOrderSn]?.source
        }
        return searchAppJobPaginated
    }
}