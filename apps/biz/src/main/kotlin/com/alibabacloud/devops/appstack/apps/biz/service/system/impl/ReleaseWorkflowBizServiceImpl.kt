package com.alibabacloud.devops.appstack.apps.biz.service.system.impl

import com.alibabacloud.devops.appstack.apps.biz.annotation.IsSystem
import com.alibabacloud.devops.appstack.apps.biz.service.system.ReleaseWorkflowBizService
import com.alibabacloud.devops.appstack.apps.biz.service.WorkflowBaseService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowRevision
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.CreateReleaseWorkflowBizRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.SortReleaseWorkflowRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.UpdateReleaseWorkflowRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2023-11-08
 */
@Service
open class ReleaseWorkflowBizServiceImpl: ReleaseWorkflowBizService {

    @Autowired
    lateinit var workflowBaseService: WorkflowBaseService

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    @IsSystem
    override fun create(systemName: String, request: CreateReleaseWorkflowBizRequest): ReleaseWorkflow {
        request.type = ReleaseWorkflow.TypeEnum.APP_RELEASE
        return workflowBaseService.create(systemName, request)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun findAll(systemName: String): List<ReleaseWorkflow> {
        return workflowBaseService.findAll(systemName, ReleaseWorkflow.TypeEnum.APP_RELEASE)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun find(systemName: String, sn: String): ReleaseWorkflow {
        return workflowBaseService.find(systemName, sn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun findWithRevision(systemName: String, sn: String): ReleaseWorkflowRevision? {
        return workflowBaseService.findWithRevision(systemName, sn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun findYamlBySha(systemName: String, sn: String, sha: String): String {
        return workflowBaseService.findYamlBySha(systemName, sn, sha)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun listRevisionPaginated(systemName: String, sn: String, current: Long, pageSize: Long): Pagination<Revision> {
        return workflowBaseService.listRevisionPaginated(systemName, sn, current, pageSize)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun compare(
        systemName: String,
        sn: String,
        beforeRevisionSha: String,
        afterRevisionSha: String
    ): Triple<String, String, List<DiffItem<String>>> {
        return workflowBaseService.compare(systemName, sn, beforeRevisionSha, afterRevisionSha)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun update(
        systemName: String,
        sn: String,
        updateReleaseWorkflowRequest: UpdateReleaseWorkflowRequest
    ): ReleaseWorkflow {
        return workflowBaseService.update(systemName, sn, updateReleaseWorkflowRequest)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun delete(systemName: String, sn: String) {
        workflowBaseService.delete(systemName, sn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    @IsSystem
    override fun sort(systemName: String, request: SortReleaseWorkflowRequest): List<ReleaseWorkflow> {
        request.type = ReleaseWorkflow.TypeEnum.APP_RELEASE
        return workflowBaseService.sort(systemName, request)
    }
}