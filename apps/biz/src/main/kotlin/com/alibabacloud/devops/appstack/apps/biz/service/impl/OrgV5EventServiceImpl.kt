package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibaba.aone.framework.tbs.app.sdk.webhook.model.v1.MemberCreated
import com.alibaba.aone.framework.tbs.app.sdk.webhook.model.v1.MemberDeleted
import com.alibaba.aone.framework.tbs.app.sdk.webhook.model.v1.MemberUpdated
import com.alibaba.aone.framework.tbs.app.sdk.webhook.model.v1.OrgCreated
import com.alibaba.aone.framework.tbs.app.sdk.webhook.model.v1.RoleShipCreated
import com.alibaba.aone.framework.tbs.app.sdk.webhook.model.v1.RoleShipDeleted
import com.alibaba.fastjson.JSON
import com.alibabacloud.devops.appstack.apps.biz.model.OrgV5EventRequest
import com.alibabacloud.devops.appstack.apps.biz.model.OrgV5EventType
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateBizService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.apps.biz.service.OrgEventService
import com.alibabacloud.devops.appstack.apps.biz.service.PermissionChangedHandlerService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.IamClient
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.config.IamContext
import com.alibabacloud.devops.iam.constant.SubjectType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service

/**
 * <AUTHOR> <EMAIL>
 * @version : OrgEventServiceImpl, v0.1
 * @date : 2021-11-30 10:16
 *
 * @see : https://yuque.antfin.com/aone/base/gu74gn  https://yuque.antfin.com/aone/base/ebr7wi#P3ACH
 **/
@Slf4k
@Service
@ConditionalOnProperty(name = ["devops.iam.environment"], havingValue = "vpc", matchIfMissing = false)
class OrgV5EventServiceImpl : OrgEventService {

    @Autowired
    lateinit var iamClient: IamClient

    @Autowired
    lateinit var iamService: IamService

    @Autowired
    lateinit var permissionChangedHandlerService: PermissionChangedHandlerService

    @Autowired
    lateinit var appTemplateBizService: AppTemplateBizService

    @Autowired
    lateinit var coreFacades: CoreFacades

    override fun handleV5Event(eventType: String, eventId: String, request: OrgV5EventRequest) {
        when (eventType) {
            OrgV5EventType.ORG_CREATE.value -> orgCreateHandler(eventType, eventId, request)
            OrgV5EventType.ORG_REMOVE.value -> orgRemoveHandler(eventType, eventId, request)
            OrgV5EventType.ORG_MEMBER_CREATE.value -> orgMemberCreateHandler(eventType, eventId, request)
            OrgV5EventType.ORG_MEMBER_UPDATE.value -> orgMemberUpdateHandler(eventType, eventId, request)
            OrgV5EventType.ORG_MEMBER_REMOVE.value -> orgMemberRemoveHandler(eventType, eventId, request)
            OrgV5EventType.ORG_ROLESHIP_CREATED.value -> orgRoleShipCreatedHandler(eventType, eventId, request)
            OrgV5EventType.ORG_ROLESHIP_DELETED.value -> orgRoleShipDeletedHandler(eventType, eventId, request)
            else -> {
                logger.warn("event: {} has not been supported", eventType)
            }
        }
    }

    private fun orgCreateHandler(eventCode: String, eventId: String, request: OrgV5EventRequest) {
        logger.info("handle event: {}, id: {}", eventCode, eventId)
        val orgCreatedEventData = JSON.parseObject(request.data, OrgCreated::class.java)
        val orgId = orgCreatedEventData.org.id
        setContext(orgId, request.operator.userId)
        iamClient.initConfig()

        // 初始化预置应用编排模板
        appTemplateBizService.preset()

        // 初始化标签
        coreFacades.labelFacade.preset()
    }

    private fun orgRemoveHandler(eventCode: String, eventId: String, request: OrgV5EventRequest) {
        TODO("Not yet implemented")
    }

    private fun orgMemberCreateHandler(eventCode: String, eventId: String, request: OrgV5EventRequest) {
        logger.info("handle event: {}, id: {}", eventCode, eventId)
        val memberCreateEventData = JSON.parseObject(request.data, MemberCreated::class.java)
        val orgId = memberCreateEventData.orgMember.organizationId
        setContext(orgId, request.operator.userId)
    }

    private fun orgMemberUpdateHandler(eventCode: String, eventId: String, request: OrgV5EventRequest) {
        logger.info("handle event: {}, id: {}", eventCode, eventId)
        val memberUpdateEventData = JSON.parseObject(request.data, MemberUpdated::class.java)
        val orgId = memberUpdateEventData.orgMember.organizationId
        setContext(orgId, request.operator.userId)
    }

    private fun orgMemberRemoveHandler(eventCode: String, eventId: String, request: OrgV5EventRequest) {
        logger.info("handle event: {}, id: {}", eventCode, eventId)
        val memberDeletedEventData = JSON.parseObject(request.data, MemberDeleted::class.java)
        val orgId = memberDeletedEventData.orgMember.organizationId
        setContext(orgId, request.operator.userId)
        iamService.deletePlayer(SubjectType.User, memberDeletedEventData.orgMember.userId)
    }

    private fun orgRoleShipCreatedHandler(eventCode: String, eventId: String, request: OrgV5EventRequest) {
        logger.info("handle event: {}, id: {}", eventCode, eventId)
        val roleShipCreated = JSON.parseObject(request.data, RoleShipCreated::class.java)
        val orgId = roleShipCreated.org.id
        setContext(orgId, request.operator.userId)
    }

    private fun orgRoleShipDeletedHandler(eventCode: String, eventId: String, request: OrgV5EventRequest) {
        logger.info("handle event: {}, id: {}", eventCode, eventId)
        val roleShipDeleted = JSON.parseObject(request.data, RoleShipDeleted::class.java)
        val orgId = roleShipDeleted.org.id
        setContext(orgId, request.operator.userId)
    }

    private fun setContext(orgId: String, userId: String){
        AuthThreadContext.setTenant(orgId)
        AuthThreadContext.setUserId(userId)
        IamContext.setTenant(orgId)
        IamContext.setOperator(userId)
    }

}