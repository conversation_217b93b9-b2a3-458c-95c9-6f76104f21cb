package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderInput
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.RevisionVariableGroup

/**
 * <AUTHOR>
 * @date 2023-04-20
 */
interface ChangeOrderInputPatchValueHandler {
    fun handle(
        env: Env,
        orchestration: AppOrchestration?,
        profiles: List<RevisionVariableGroup>,
        stableChangeOrderInput: ChangeOrderInput?,
        customPatchValues: Map<String, String>,
        result: MutableMap<String, String>
    )

    fun suitableDeployType(): List<ChangeOrder.Type>
    fun order(): Int
}