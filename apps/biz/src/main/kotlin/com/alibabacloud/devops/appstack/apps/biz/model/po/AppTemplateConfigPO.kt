package com.alibabacloud.devops.appstack.apps.biz.model.po

import com.alibabacloud.devops.appstack.libs.common.annotation.Comment
import com.alibabacloud.devops.appstack.libs.common.model.BasePO
import com.baomidou.mybatisplus.annotation.TableName
import javax.persistence.Column
import javax.persistence.Index
import javax.persistence.Table

/**
 * <AUTHOR> <EMAIL>
 * @version : AppTemplateConfigPO, v0.1
 * @date : 2023-10-16 17:11
 **/
@TableName("app_template_configs")
@Comment("应用模板配置")
@Table(
    indexes = [
        Index(columnList = "org_id(64),app_template_name(64),is_deleted(32),type(64)", unique = true),
        Index(columnList = "org_id(64),is_deleted(32),sn(64)", unique = true)
    ]
)
open class AppTemplateConfigPO : BasePO() {

    @Comment("sn")
    var sn: String? = null

    @Comment("应用模板name")
    var appTemplateName: String? = null

    @Comment("配置项类型")
    var type: String? = null

    @Comment("管控模式")
    @Column(columnDefinition = "text")
    var modeSetting: String? = null

    @Comment("配置")
    @Column(columnDefinition = "longtext")
    var configuration: String? = null

}