package com.alibabacloud.devops.appstack.apps.biz.model.mapper

import com.alibabacloud.devops.appstack.apps.biz.model.po.AgentPO
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.core.mapper.BaseMapper
import org.apache.ibatis.annotations.Mapper

@Mapper
interface AgentMapper : BaseMapper<AgentPO> {
}

fun AgentMapper.findLatestByTypeVersion(type: String, version: String): AgentPO? {
    val list = selectList(QueryWrapper<AgentPO>().eq("type", type).eq("version", version))
    if (list.isEmpty()) {
        return null
    }
    return list.first()
}