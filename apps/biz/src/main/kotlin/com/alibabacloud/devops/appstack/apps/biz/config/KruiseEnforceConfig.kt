package com.alibabacloud.devops.appstack.apps.biz.config

import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Configuration

/**
 * @author: <EMAIL>
 * @date: 2024-08-06 16:14
 * @version: KruiseEnforceConfig, v0.1
 **/
@Configuration
class KruiseEnforceConfig {
    @Value("\${ade2.kruise-enforce.black-orgs:none}")
    lateinit var blackOrgIds: Array<String>

    @Value("\${ade2.kruise-enforce.ratio-limit:0}")
    lateinit var ratioLimitRaw: String
}