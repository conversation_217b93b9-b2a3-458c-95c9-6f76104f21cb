package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestStageRuleConfigBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.StageRuleConfig
import com.alibabacloud.devops.appstack.libs.model.request.temp.StageRuleConfigRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-08-01 19:11
 * @version: StageRuleConfigBizApi, v0.1
 **/
@Tag(name = "StageRuleConfig", description = "流程阶段规则配置相关API")
@RestController
@RequestMapping("/api/v1")
open class ChangeRequestStageRuleConfigBizApi {

    @Autowired
    lateinit var changeRequestStageRuleConfigBizService: ChangeRequestStageRuleConfigBizService

    @Operation(summary = "查找流程阶段的规则配置")
    @GetMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}/ruleConfig")
    fun findReleaseStageRuleConfig(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String
    ): Response<StageRuleConfig> {
        return Response.success(changeRequestStageRuleConfigBizService.find(appName, releaseWorkflowSn, releaseStageSn))
    }

    @Operation(summary = "更新流程阶段的规则配置")
    @PutMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}/ruleConfig")
    fun upsertReleaseStageRuleConfig(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @RequestBody stageRuleConfigRequest: StageRuleConfigRequest,
    ): Response<StageRuleConfig> {
        return Response.success(
            changeRequestStageRuleConfigBizService.upsert(
                appName,
                releaseWorkflowSn,
                releaseStageSn,
                stageRuleConfigRequest
            )
        )
    }
}