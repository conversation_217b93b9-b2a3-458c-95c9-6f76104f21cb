package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.config.FlowExecutionConfiguration
import com.alibabacloud.devops.appstack.apps.biz.model.AgentStatus
import com.alibabacloud.devops.appstack.apps.biz.model.AgentTask
import com.alibabacloud.devops.appstack.apps.biz.model.FlowTaskStatus
import com.alibabacloud.devops.appstack.apps.biz.model.JobLog
import com.alibabacloud.devops.appstack.apps.biz.model.RunCommand
import com.alibabacloud.devops.appstack.apps.biz.model.ext.toModel
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.AgentMapper
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.AgentTaskMapper
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.deleteTaskByRefName
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.findLatestByRefName
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.findLatestByTypeVersion
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.findTaskByJobId
import com.alibabacloud.devops.appstack.apps.biz.model.po.AgentPO
import com.alibabacloud.devops.appstack.apps.biz.model.po.AgentTaskPO
import com.alibabacloud.devops.appstack.apps.biz.service.AgentService
import com.alibabacloud.devops.appstack.apps.biz.service.DeployGroupBizService
import com.alibabacloud.devops.appstack.apps.biz.service.client.api.FlowApi
import com.alibabacloud.devops.appstack.apps.biz.service.client.api.ResourceMetaApi
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.model.ErrorEntry
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkBiz
import com.alibabacloud.devops.appstack.libs.common.util.checkBizWithCode
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.env.SimpleEnv
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.response.FlowApiResponse
import com.alibabacloud.devops.appstack.libs.model.vo.ResourceInstanceVO
import com.alibabacloud.devops.appstack.resource.manager.spring.boot.starter.ResourceManagerFacades
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.common.hash.Hashing.md5
import io.fabric8.kubernetes.api.model.Config
import io.fabric8.kubernetes.client.utils.Serialization
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import java.nio.charset.StandardCharsets
import java.util.*

@Service
@Slf4k
@ConditionalOnProperty(name = ["devops.appstack.environment"], havingValue = "aliyun", matchIfMissing = true)
open class AgentServiceImpl : AgentService {

    @Autowired
    lateinit var agentMapper: AgentMapper

    @Autowired
    lateinit var agentTaskMapper: AgentTaskMapper

    @Autowired
    lateinit var resourceMetaApi: ResourceMetaApi

    @Autowired
    lateinit var resourceManagerFacades: ResourceManagerFacades

    @Autowired
    lateinit var flowApi: FlowApi

    @Autowired
    lateinit var flowExecutionConfiguration: FlowExecutionConfiguration

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var deployGroupBizService: DeployGroupBizService

    companion object {
        const val KRUISE_ROLLOUT_AGENT_VERSION = "0.0.3"
        const val FLOW_CALLER = "rm"
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_RESOURCE_IMPORT),
            Access(action = Action.ORG_RESOURCE_MANAGE)
        ]
    )
    override fun installAgent(poolName: String, instanceName: String, version: String): AgentTask {
        val instance = findInstance(poolName, instanceName)
        val agent = getLatestAgentByResourceTypeVersion(
            instance.type.name,
             KRUISE_ROLLOUT_AGENT_VERSION
        )
        checkExists(agent) {
            ErrorCode.AS_BIZ_AGENT_NOT_FOUND
        }
        val configRes =
            resourceManagerFacades.resourceProxyFacade.findKubeConfig(getResourcePath(poolName, instanceName))
        checkBizWithCode(configRes != null) {
            ErrorCode.AS_RESOURCE_KUBE_CONFIG_FETCH_FAILED
        }
        val envs = getOamRelatedEnvList(poolName, instanceName)
        updateEnvWithoutOam(envs, Env.Spec.WithoutOamState.WAITING)
        val installTask = createInstallTask(poolName, instanceName, agent, configRes!!)
        // todo 当agent安装回调修复以后，这里要换成到回调里面去判断
        updateEnvWithoutOam(envs, Env.Spec.WithoutOamState.ON)
        return installTask
    }

    private fun getOamRelatedEnvList(poolName: String, instanceName: String): List<SimpleEnv> {
        val simpleEnvs = deployGroupBizService.findAllByInstance(poolName, instanceName).flatMap { oneDeployGroup ->
            oneDeployGroup.relateAppToEnvMap?.values?.flatten() ?: emptyList()
        }
        return simpleEnvs
    }

    private fun updateEnvWithoutOam(simpleEnvs: List<SimpleEnv>, state: Env.Spec.WithoutOamState) {
        simpleEnvs.forEach {
            coreFacades.envFacade.updateEnvWithoutOam(it.appName, it.name, state)
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_RESOURCE_VIEW),
            Access(action = Action.ORG_RESOURCE_MANAGE)
        ]
    )
    override fun findLatestInstallTask(poolName: String, instanceName: String): AgentTask? {
        val latestTaskPO = agentTaskMapper.findLatestByRefName("$poolName:$instanceName")
        latestTaskPO?.let {
            return it.toModel()
        }
        return null
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_RESOURCE_VIEW),
            Access(action = Action.ORG_RESOURCE_MANAGE)
        ]
    )
    override fun findLatestInstallLog(poolName: String, instanceName: String): JobLog? {
        val task = findLatestInstallTask(poolName, instanceName)
        task?.let {
            val (timestamp, sign: String) = sign(FLOW_CALLER)
            val res = flowApi.queryJobLogs(genFlowHeaders(), "${task.jobId}", FLOW_CALLER, timestamp, sign)
            return res.data
        }
        return null
    }

    // 仅暴露给flow做回调，不验权
    override fun updateTaskStatus(jobId: String, status: String) {
        val task = agentTaskMapper.findTaskByJobId(jobId)
        task?.let {
            it.status = status
        }
        agentTaskMapper.updateById(task)
    }

    override fun findStatus(poolName: String, instanceName: String): AgentStatus {
        val agentStatus = getAgentDeploymentStatus(poolName, instanceName)
        if (agentStatus.state == AgentStatus.State.SUCCESS) {
            return agentStatus
        }
        val agentTask = findLatestInstallTask(poolName, instanceName)
            ?: return AgentStatus(state = AgentStatus.State.NOT_INSTALLED)
        return if (agentTask.status == FlowTaskStatus.INIT.name || agentTask.status == FlowTaskStatus.PENDING.name || agentTask.status == FlowTaskStatus.RUNNING.name) {
            AgentStatus(state = AgentStatus.State.INSTALLING)
        } else {
            AgentStatus(state = AgentStatus.State.ABNORMAL)
        }
    }

    private fun getAgentDeploymentStatus(
        poolName: String,
        instanceName: String,
        withoutOam: Boolean = true,
    ): AgentStatus {
        logger.info("findStatus poolName: $poolName instanceName:$instanceName withoutOam: $withoutOam")
        val agentDeploymentStatus = if (withoutOam) {
            resourceManagerFacades.kruiseRolloutFacade.find(getResourcePath(poolName, instanceName))
        } else {
            resourceManagerFacades.kubeVelaFacade.find(getResourcePath(poolName, instanceName))
        }
        agentDeploymentStatus?.let {
            if ((it.status.readyReplicas ?: 0) > 0 && (it.status.availableReplicas ?: 0) > 0) {
                return AgentStatus(AgentStatus.State.SUCCESS)
            }
        }
        if (withoutOam) {
            val withOamStatus = getAgentDeploymentStatus(poolName, instanceName, false)
            logger.info("findStatus withOamStatus state:${withOamStatus.state} upgradeState:${withOamStatus.upgradeState}")
            if (withOamStatus.state == AgentStatus.State.SUCCESS) {
                withOamStatus.upgradeState = AgentStatus.UpgradeState.WITHOUT_OAM
            }
            return withOamStatus
        }
        return AgentStatus(AgentStatus.State.ABNORMAL)
    }

    override fun delete(poolName: String, instanceName: String) {
        agentTaskMapper.deleteTaskByRefName("$poolName:$instanceName")
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_RESOURCE_IMPORT),
            Access(action = Action.ORG_RESOURCE_MANAGE)
        ]
    )
    override fun cancelAgentInstallation(poolName: String, instanceName: String) {
        val latestTask = findLatestInstallTask(poolName, instanceName) ?: return
        if (latestTask.status != AgentStatus.State.SUCCESS.name && latestTask.status != AgentStatus.State.ABNORMAL.name) {
            delete(poolName, instanceName)
        }
    }

    private fun getResourcePath(poolName: String, instanceName: String): String {
        return "RI:$poolName:$instanceName"
    }

    private fun createInstallTask(
        poolName: String,
        instanceName: String,
        agent: AgentPO,
        kubeConfig: Config,
    ): AgentTask {
        val configFile = String(Base64.getEncoder().encode(Serialization.asYaml(kubeConfig).toByteArray()))
        val latestTask = findLatestInstallTask(poolName, instanceName)
        if (latestTask != null) {
            latestTask.gmtCreate?.time?.let {
                checkBizWithCode(System.currentTimeMillis() > (it + 3 * 60 * 1000)) {
                    ErrorCode.AS_BIZ_AGENT_INSTALL_TOO_FAST
                }
            }
        }
        val (timestamp, sign: String) = sign(FLOW_CALLER)
        val res = callFlowExecuteJob(agent, configFile, timestamp, sign)

        logger.info("flow result ${jacksonObjectMapper().writeValueAsString(res)}")
        checkBiz(res.successful) {
            ErrorEntry(ErrorCode.AS_BIZ_AGENT_INSTALL_FAILED, res.errorMessage)
        }
        val agentTask = AgentTaskPO()

        agentTask.jobId = res.data
        agentTask.refObj = "ResourceInstance"
        agentTask.refObjName = "$poolName:$instanceName"
        agentTask.taskNo = UUID.randomUUID().toString()
        agentTask.status = FlowTaskStatus.INIT.toString()
        agentTask.status = FlowTaskStatus.INIT.toString()
        agentTaskMapper.insert(agentTask)
        return agentTask.toModel()
    }

    protected open fun callFlowExecuteJob(
        agent: AgentPO,
        configFile: String,
        timestamp: String,
        sign: String,
    ): FlowApiResponse<Long> {
        val installScript = agent.installScript
        return flowApi.executeJob(
            genFlowHeaders(),
            covertCommandToParams(
                RunCommand(
                    "curl -o- $installScript | bash",
                    mapOf("KUBE_CONFIG" to configFile)
                )
            ),
            "BUILD_ENGINE",
            generateCallBackUrl(),
            FLOW_CALLER,
            timestamp, sign
        )
    }

    protected fun genFlowHeaders(): Map<String, String> {
        val headers = mutableMapOf<String, String>()
        headers["X-Site-From"] = "teambition"
        headers["Ao-User-Staff-Id"] = AuthUtil.getUserId()
        headers["X-Tenant-Id"] = AuthUtil.getTenant()
        return headers
    }

    protected fun generateCallBackUrl(): String {
        logger.info("callback url: ${flowExecutionConfiguration.callbackURL}?orgId=${AuthUtil.getTenant()}")
        return "${flowExecutionConfiguration.callbackURL}?orgId=${AuthUtil.getTenant()}"
    }

    private fun getLatestAgentByResourceTypeVersion(type: String, version: String): AgentPO? {
        return agentMapper.findLatestByTypeVersion(type, version)
    }

    open protected fun covertCommandToParams(command: RunCommand): String {
        val step = HashMap<String, String>()
        step.putAll(command.environments)
        step.putAll(
            mapOf(
                "stepType" to flowExecutionConfiguration.execStep,
                "stepIdentifier" to "whatever",
                "command" to command.command
            )
        )
        return jacksonObjectMapper().writeValueAsString(mapOf("steps" to listOf(step)))
    }


    private fun findInstance(poolName: String, instanceName: String): ResourceInstanceVO {
        try {
            val res = resourceMetaApi.findInstance(AuthUtil.getHeaders(), poolName, instanceName)
            checkExists(res, mapOf("name" to instanceName)) {
                ErrorCode.AS_RES_INST_NOT_FOUND
            }
            if (res.success && res.data != null) {
                return res.data!!
            } else {
                throw throw BizException(ErrorCode.AS_RES_INST_NOT_FOUND, res.errorMessage)
            }
        } catch (e: Exception) {
            throw BizException(ErrorCode.AS_RES_INST_NOT_FOUND, e.message)
        }
    }

    private fun sign(callerName: String): Pair<String, String> {
        val timestamp = System.currentTimeMillis().toString()
        val sign: String = genSign(callerName, timestamp, "", flowExecutionConfiguration.secret)
        return Pair(timestamp, sign)
    }

    private fun genSign(callerName: String, timestamp: String, signParam: String, secret: String): String {
        val combine = secret + callerName + timestamp + signParam + secret
        @Suppress("DEPRECATION")
        return md5().hashString(combine, StandardCharsets.UTF_8).toString().toUpperCase()
    }

}