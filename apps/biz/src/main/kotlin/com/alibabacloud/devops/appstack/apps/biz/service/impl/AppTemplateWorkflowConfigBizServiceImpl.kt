package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.model.ext.toModel
import com.alibabacloud.devops.appstack.apps.biz.model.ext.toPO
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.*
import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateConfigSyncStatusPO
import com.alibabacloud.devops.appstack.apps.biz.model.vo.AppTemplateWorkflowRevisionVO
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateService
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateWorkflowConfigBizService
import com.alibabacloud.devops.appstack.apps.biz.service.impl.apptemplate.adaptor.AppTemplateConfigReleaseWorkflowStorageAdaptor
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.UuidUtils
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfigSyncStatus
import com.alibabacloud.devops.appstack.libs.model.`do`.app.ReleaseWorkflowConfiguration
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ga.AppTemplateWorkflowRevision
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.sync.AppTemplateSyncRequest
import com.alibabacloud.devops.appstack.libs.model.response.apptemplate.sync.AppTemplateItemSyncStatus
import com.alibabacloud.devops.appstack.libs.model.response.apptemplate.sync.AppTemplateSyncResponse
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.UnicastSender
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.AppTemplateReleaseWorkflowSyncBody
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.UnicastTagEnum
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2024-03-22 16:38
 * @version: AppTemplateWorkflowConfigBizServiceImpl, v0.1
 **/
@Service
class AppTemplateWorkflowConfigBizServiceImpl : AppTemplateWorkflowConfigBizService {

    @Autowired
    lateinit var appTemplateConfigReleaseWorkflowStorageAdaptor: AppTemplateConfigReleaseWorkflowStorageAdaptor

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var appTemplateConfigSyncStatusMapper: AppTemplateConfigSyncStatusMapper

    @Autowired
    lateinit var unicastSender: UnicastSender

    @Autowired
    lateinit var appTemplateUsageRelationMapper: AppTemplateUsageRelationMapper

    @Autowired
    lateinit var appTemplateService: AppTemplateService

    override fun findPaginatedRevision(
        appTemplateName: String,
        name: String,
        current: Long,
        pageSize: Long,
    ): Pagination<AppTemplateWorkflowRevisionVO> {
        val revisions = appTemplateConfigReleaseWorkflowStorageAdaptor.findPaginatedRevision(
            appTemplateName,
            name,
            current,
            pageSize
        )

        val syncStatus = appTemplateConfigSyncStatusMapper.listByTemplate(
            appTemplateName,
            AppTemplateConfig.TypeEnum.ReleaseWorkflow.name,
            name,
        ).map { it.toModel() }

        val map = syncStatus.filter {
            it.templateSyncStatus?.lastAppliedTemplateRevisionSha != null
                && it.templateSyncStatus?.lastAppliedAppRevisionSha == it.instanceUpdateStatus?.actualAppRevisionSha
        }.groupBy {
            it.templateSyncStatus?.lastAppliedTemplateRevisionSha
        }.mapValues { entry ->
            entry.value.map { it.appName }
        }

        return Pagination(
            total = revisions.total,
            current = revisions.current,
            pageSize = revisions.pageSize,
            pages = revisions.pages,
            records = revisions.records.map { oneRevision ->
                AppTemplateWorkflowRevisionVO(
                    repoMeta = oneRevision.repoMeta,
                    sha = oneRevision.sha,
                    message = oneRevision.message,
                    author = oneRevision.author,
                    refs = oneRevision.refs,
                    commitTime = oneRevision.commitTime,
                    appNames = map[oneRevision.sha] ?: emptyList(),
                )
            }
        )
    }

    override fun deleteByName(appTemplateName: String, name: String, appTemplateConfig: String?) {
        val appTemplate = appTemplateService.find(appTemplateName)
        workflowControllerFacade.appTemplateWorkflowFacade.deleteByName(appTemplateName, name)
        appTemplateConfig?.let {
            val templateConfig = appTemplateConfigReleaseWorkflowStorageAdaptor.find(appTemplateName)
            if ((templateConfig?.configuration as ReleaseWorkflowConfiguration).appTemplateWorkflowList.isEmpty()) {
                appTemplateConfigReleaseWorkflowStorageAdaptor.delete(appTemplate, appTemplateConfig)
            }
        }
    }

    override fun compare(
        appTemplateName: String,
        name: String,
        beforeRevisionSha: String,
        afterRevisionSha: String,
    ): Triple<String, String, List<DiffItem<String>>> {
        return workflowControllerFacade.appTemplateWorkflowFacade.compare(
            appTemplateName,
            name,
            beforeRevisionSha,
            afterRevisionSha
        )
    }

    override fun sync2Apps(name: String, request: AppTemplateSyncRequest) {
        // 检查版本
        workflowControllerFacade.appTemplateWorkflowFacade.findByName(name, request.refSn, request.revisionSha)
        if (request.appNames.isEmpty()) {
            throw BizException(ErrorCode.AS_APP_NOT_FOUND)
        }
        checkAndInitSyncStatus(name, request)
        unicastSender.send(
            UnicastTagEnum.APPTEMPLATE_RELEASE_WORKFLOW_SYNC,
            jacksonObjectMapper().writeValueAsBytes(
                AppTemplateReleaseWorkflowSyncBody(
                    appTemplateName = name,
                    refSn = request.refSn,
                    revisionSha = request.revisionSha,
                    appNames = request.appNames,
                    commitMsg = request.commitMsg,
                    transactionId = request.transactionId ?: UuidUtils.getUuid()
                )
            )
        )
    }

    override fun findByName(appTemplateName: String, name: String, sha: String?): AppTemplateWorkflowRevision {
        return workflowControllerFacade.appTemplateWorkflowFacade.findByName(appTemplateName, name, sha)
    }

    override fun syncStatus(templateName: String, name: String): AppTemplateSyncResponse {
        // 校验并补齐同步状态数据
        checkAndInitSyncStatus(templateName, name)

        val workflowRevisionable =
            workflowControllerFacade.appTemplateWorkflowFacade.findByName(templateName, name, null)
        val latestRevisionSha = workflowRevisionable.revision.sha

        val syncStatus = appTemplateConfigSyncStatusMapper.listByTemplate(
            templateName,
            AppTemplateConfig.TypeEnum.ReleaseWorkflow.name,
            name,
        ).map { it.toModel() }


        val statusList = syncStatus.map {
            val processSyncSuccess = (it.templateSyncStatus?.lastAppliedTemplateRevisionSha == latestRevisionSha
                    && it.templateSyncStatus?.lastAppliedAppRevisionSha != null
                    && it.templateSyncStatus?.lastAppliedAppRevisionSha == it.instanceUpdateStatus?.actualAppRevisionSha)

            AppTemplateItemSyncStatus(
                appName = it.appName,
                revisionSha = it.instanceUpdateStatus?.actualAppRevisionSha,
                lastAppliedTemplateRevisionSha = it.templateSyncStatus?.lastAppliedTemplateRevisionSha,
                lastSyncErrorMessage = it.templateSyncStatus?.lastSyncErrorMessage,
                commitMessage = it.instanceUpdateStatus?.actualAppRevisionCommitMessage,
                commitTime = it.instanceUpdateStatus?.actualAppRevisionGmtModified,
                author = it.instanceUpdateStatus?.actualAppRevisionCommitAuthor,
                syncSuccess = processSyncSuccess
            )
        }
        return AppTemplateSyncResponse(
            templateName = templateName,
            latestRevisionSha = latestRevisionSha,
            statusList = statusList,
            successCount = statusList.count { it.syncSuccess }
        )
    }

    override fun compareWithApp(
        appTemplateName: String,
        name: String,
        appName: String,
    ): Triple<String, String, List<DiffItem<String>>> {
        return workflowControllerFacade.appTemplateWorkflowFacade.compareWithApp(
            appTemplateName,
            name,
            appName
        )
    }

    private fun checkAndInitSyncStatus(templateName: String, workflowName: String) {
        val syncStatusPOs = appTemplateConfigSyncStatusMapper.listByTemplate(
            templateName,
            AppTemplateConfig.TypeEnum.ReleaseWorkflow.name,
            workflowName
        )
        val findPaginatedByAppTemplateName =
            appTemplateUsageRelationMapper.findAllByAppTemplateName(templateName)
        val appNames = findPaginatedByAppTemplateName.map { it.appName }
        completeSyncStatus(templateName, workflowName, syncStatusPOs, appNames)
    }

    /**
     *  fixme 应用多个编排模板时，此校验不成立
     */
    private fun checkAndInitSyncStatus(templateName: String, request: AppTemplateSyncRequest) {
        val syncStatusPOs = appTemplateConfigSyncStatusMapper.listByTemplateAndApps(
            templateName,
            AppTemplateConfig.TypeEnum.ReleaseWorkflow.name,
            request.refSn,
            request.appNames
        )
        completeSyncStatus(templateName, request.refSn, syncStatusPOs, request.appNames)
    }

    private fun completeSyncStatus(
        templateName: String,
        workflowName: String,
        syncStatusPOs: List<AppTemplateConfigSyncStatusPO>,
        appNameList: List<String>,
    ) {
        val intersect = appNameList.intersect(syncStatusPOs.map { it.appName }.toSet())
        val appNames = appNameList - intersect
        appNames.forEach { appName ->
            val find = appTemplateConfigSyncStatusMapper.findByTemplateAndApp(
                templateName,
                AppTemplateConfig.TypeEnum.ReleaseWorkflow.name,
                workflowName,
                appName
            )
            if (null == find) {
                // add syncStatus
                val po = AppTemplateConfigSyncStatus(
                    appTemplateName = templateName,
                    appTemplateConfigType = AppTemplateConfig.TypeEnum.ReleaseWorkflow,
                    appTemplateConfigInstanceName = workflowName,
                    appName = appName,
                ).toPO()
                appTemplateConfigSyncStatusMapper.insert(po)
            }
        }
        syncStatusPOs.filter { !intersect.contains(it.appName) }.forEach { syncStatusPO ->
            // delete syncStatus
            appTemplateConfigSyncStatusMapper.deleteById(syncStatusPO.id)
        }
    }
}