package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.apps.biz.service.GlobalVarBizService
import com.alibabacloud.devops.appstack.apps.biz.service.PermissionService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.globalvar.GlobalVarRevisionContent
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.iam.VarRole
import com.alibabacloud.devops.appstack.libs.model.request.CreateGlobalVarRequest
import com.alibabacloud.devops.appstack.libs.model.request.SearchGlobalVarRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateGlobalVarRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateGlobalVarContentRequest
import com.alibabacloud.devops.appstack.libs.model.response.GlobalVarVO
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2023-11-20 16:58
 * @version: GlobalVarBizApi, v0.1
 **/
@Tag(name = "GlobalVar", description = "全局变量 API")
@RestController
@RequestMapping("/api/v1/globalVars")
open class GlobalVarBizApi {

    @Autowired
    lateinit var globalVarBizService: GlobalVarBizService

    @Autowired
    lateinit var permissionService: PermissionService

    @GetMapping("/{name}")
    fun findGlobalVar(@PathVariable("name") name: String): Response<GlobalVarVO> {
        return Response.success(globalVarBizService.find(name))
    }

    @PostMapping("/:search")
    fun findGlobalVarsPaginated(
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
        @RequestBody req: SearchGlobalVarRequest,
    ): Response<Pagination<GlobalVarVO>> {
        return Response.success(globalVarBizService.listPaginated(current, pageSize, req))
    }

    @PostMapping("")
    fun createGlobalVar(@RequestBody req: CreateGlobalVarRequest): Response<GlobalVarVO> {
        return Response.success(globalVarBizService.create(req))
    }

    @PutMapping("/{name}")
    fun updateGlobalVar(
        @PathVariable("name") name: String,
        @RequestBody req: UpdateGlobalVarRequest,
    ): Response<GlobalVarVO> {

        return Response.success(globalVarBizService.update(name, req))
    }

    @DeleteMapping("/{name}")
    fun deleteGlobalVar(@PathVariable("name") name: String): Response<Unit> {
        globalVarBizService.delete(name)
        return Response.success()
    }

    @GetMapping("/{name}/content")
    fun findGlobalVarContent(
        @PathVariable("name") name: String,
        @RequestParam("revisionSha", required = false) revisionSha: String?,
    ): Response<GlobalVarRevisionContent> {
        return Response.success(globalVarBizService.findContent(name, revisionSha))
    }

    @PutMapping("/{name}/content")
    fun updateGlobalVarContent(
        @PathVariable("name") name: String,
        @RequestBody req: UpdateGlobalVarContentRequest,
    ): Response<GlobalVarRevisionContent> {
        return Response.success(globalVarBizService.updateContent(name, req))
    }

    @GetMapping("/{name}/revisions")
    fun findGlobalVarRevisionPaginated(
        @PathVariable("name") name: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<Revision>> {
        return Response.success(globalVarBizService.listContentRevisionPaginated(name, current, pageSize))
    }

    @GetMapping("/{name}/tags/{tag}")
    fun findGlobalVarContentByTag(
        @PathVariable("name") name: String,
        @PathVariable("tag") tag: String
    ): Response<GlobalVarRevisionContent> {
        return Response.success(globalVarBizService.findGlobalVarContentByTag(name, tag))
    }

    @GetMapping("/{name}/revisions:diff")
    fun compareGlobalVarContentByRevisions(
        @PathVariable("name") name: String,
        @RequestParam("beforeRevisionSha") beforeRevisionSha: String,
        @RequestParam("afterRevisionSha") afterRevisionSha: String,
    ): Response<Pair<GlobalVarRevisionContent?, GlobalVarRevisionContent?>> {
        return Response.success(globalVarBizService.compare(name, beforeRevisionSha, afterRevisionSha))
    }

    @GetMapping("/{name}/members")
    fun findGlobalVarMember(@PathVariable("name") name: String): Response<List<MemberVO>> {
        val allMemberMap = globalVarBizService.findAllMember(name)
        val list = allMemberMap.map { MemberVO(it.key, it.value) }.sortedWith { o1, o2 ->
            if (o1.roleList.find { it.name == VarRole.owner.name } != null) -1
            else if (o2.roleList.find { it.name == VarRole.owner.name } != null) 1
            else
                o1.displayName.compareTo(o2.displayName)
        }
        return Response.success(list)
    }

    @PutMapping("/{name}/types/{type}/members/{member}")
    fun updateVarMemberRole(
        @PathVariable("name") name: String,
        @PathVariable("type") subjectType: SubjectType,
        @PathVariable("member") subjectId: String,
        @RequestBody roleNameList: List<String>,
    ): Response<Boolean> {
        globalVarBizService.updateMemberRole(
            name = name,
            subjectType = subjectType,
            subjectId = subjectId,
            roleNameList = roleNameList,
        )
        return Response.success(true)
    }

    @PostMapping("/{name}/roles/{roleName}")
    fun addVarMember(
        @PathVariable("name") name: String,
        @PathVariable("roleName") roleName: String,
        @RequestBody playerList: List<ResourcePlayerRequest>,
    ): Response<Boolean> {
        val role = permissionService.findVarRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        globalVarBizService.addRoleMember(
            name = name,
            role = role,
            playerList = playerList,
        )
        return Response.success(true)
    }

    @PutMapping("/{name}/roles/{roleName}")
    fun updateRoleMember(
        @PathVariable("name") name: String,
        @PathVariable("roleName") roleName: String,
        @RequestBody playerList: List<ResourcePlayerRequest>,
    ): Response<Boolean> {
        val role = permissionService.findVarRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        globalVarBizService.updateRoleMember(
            name = name,
            role = role,
            playerList = playerList,
        )
        return Response.success(true)
    }

    @PutMapping("/{name}:owner")
    fun transferOwner(
        @PathVariable("name") name: String,
        @RequestBody owner: ResourcePlayerRequest,
    ): Response<Boolean> {
        globalVarBizService.transferOwner(
            name = name,
            player = owner
        )
        return Response.success(true)
    }


    @PostMapping(":usable")
    fun findGlobalVarsPaginatedCanUse(
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
        @RequestBody req: SearchGlobalVarRequest,
    ): Response<Pagination<GlobalVarVO>> {
        return Response.success(globalVarBizService.listPaginatedCanUse(current, pageSize, req))
    }

}