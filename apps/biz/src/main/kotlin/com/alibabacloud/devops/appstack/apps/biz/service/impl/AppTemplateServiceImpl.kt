package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.model.ext.toModel
import com.alibabacloud.devops.appstack.apps.biz.model.ext.toPO
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.*
import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateUsageRelationPO
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.LocaleUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfigSyncStatus
import com.fasterxml.jackson.databind.node.ArrayNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2024-02-15
 */
@Service
class AppTemplateServiceImpl : AppTemplateService {

    companion object {
        const val PRESET_TEMPLATE_NAME_PREFIX = "PRESET_"
        const val PRESET_TEMPLATE_PATH_FORMAT = "/preset/apptemplate/%s_%s.json"
        val PRESET_TEMPLATE_NAMES = listOf(
            "PRESET_SPRING_BOOT",
            "PRESET_SPRING_AI",
            "PRESET_SPRING_AI_SAE",
            "PRESET_INGRESS_GRAY"
        )
    }

    @Autowired
    private lateinit var appTemplateMapper: AppTemplateMapper

    @Autowired
    private lateinit var appTemplateUsageRelationMapper: AppTemplateUsageRelationMapper

    @Autowired
    lateinit var appTemplateConfigSyncStatusMapper: AppTemplateConfigSyncStatusMapper

    override fun find(templateName: String): AppTemplate {
        if (PRESET_TEMPLATE_NAMES.contains(templateName)) {
            val template = findPresetPaginated("", 1, 100).records.firstOrNull { it.name == templateName }
            if (template != null) {
                return template
            }
        }
        val po = appTemplateMapper.findByName(templateName)
        checkExists(po) { ErrorCode.AS_APP_TEMPLATE_NOT_FOUND }
        return po.toModel()
    }

    override fun bindAppTemplateToApp(templateName: String, appName: String) {
        val appTemplateUsageRelationPO = AppTemplateUsageRelationPO()
        appTemplateUsageRelationPO.appName = appName
        appTemplateUsageRelationPO.appTemplateName = templateName
        appTemplateUsageRelationMapper.insert(appTemplateUsageRelationPO)
    }

    override fun findAppTemplateNameBindedByApp(appName: String): String? {
        val po = appTemplateUsageRelationMapper.findByAppName(appName)
        return po?.appTemplateName
    }

    override fun findAppTemplateBindedByAppList(appNames: List<String>): Map<String, AppTemplate> {
        val pos = appTemplateUsageRelationMapper.findByAppNames(appNames)
        val appTemplateNames = pos.map { it.appTemplateName }.distinct()
        if (appTemplateNames.isEmpty()) {
            return emptyMap()
        }
        val appTemplateMap =
            appTemplateMapper.searchByNames(appTemplateNames).map { it.toModel() }.associateBy { it.name }.toMutableMap()
        if (appTemplateNames.any { PRESET_TEMPLATE_NAMES.contains(it) }) {
            findPresetPaginated("", 1, 100).records.forEach {
                if(appTemplateNames.contains(it.name)) {
                    appTemplateMap[it.name] = it
                }
            }

        }
        val ret = mutableMapOf<String, AppTemplate>()
        pos.forEach { onePo ->
            val appTemplate = appTemplateMap[onePo.appTemplateName]
            if (appTemplate != null) {
                ret[onePo.appName] = appTemplate
            }
        }
        return ret
    }

    override fun unbindAppTemplateFromApp(templateName: String, appName: String) {
        appTemplateUsageRelationMapper.deleteByMap(
            mapOf(
                "app_name" to appName,
                "app_template_name" to templateName
            )
        )
        appTemplateConfigSyncStatusMapper.deleteByMap(
            mapOf(
                "app_template_name" to templateName,
                "app_name" to appName
            )
        )
    }

    override fun findConfigSyncStatus(
        templateName: String,
        configType: AppTemplateConfig.TypeEnum,
        configInstanceName: String,
        appName: String
    ): AppTemplateConfigSyncStatus? {
        return appTemplateConfigSyncStatusMapper.findByTemplateAndApp(
            templateName,
            configType.name,
            configInstanceName,
            appName
        )?.toModel()
    }

    override fun initSyncStatus(
        appTemplateName: String,
        appTemplateConfigType: AppTemplateConfig.TypeEnum,
        appTemplateConfigInstanceName: String,
        appName: String
    ) {
        val find = findConfigSyncStatus(
            appTemplateName,
            appTemplateConfigType,
            appTemplateConfigInstanceName,
            appName
        )
        if (find == null) {
            val po = AppTemplateConfigSyncStatus(
                appTemplateName = appTemplateName,
                appTemplateConfigType = appTemplateConfigType,
                appTemplateConfigInstanceName = appTemplateConfigInstanceName,
                appName = appName,
            ).toPO()
            appTemplateConfigSyncStatusMapper.insert(po)
        }
    }

    override fun updateTemplateSyncStatus(
        templateName: String,
        configType: AppTemplateConfig.TypeEnum,
        configInstanceName: String,
        appName: String,
        templateSyncStatus: AppTemplateConfigSyncStatus.TemplateSyncStatus
    ) {
        appTemplateConfigSyncStatusMapper.updateTemplateSyncStatus(
            templateName,
            configType.name,
            configInstanceName,
            appName,
            jacksonObjectMapper().writeValueAsString(templateSyncStatus)
        )
    }

    override fun updateInstanceUpdateStatus(
        templateName: String,
        configType: AppTemplateConfig.TypeEnum,
        configInstanceName: String,
        appName: String,
        lastInstanceUpdateSeqNo: Long,
        instanceUpdateStatus: AppTemplateConfigSyncStatus.InstanceUpdateStatus
    ) {
        appTemplateConfigSyncStatusMapper.updateInstanceUpdateStatus(
            templateName,
            configType.name,
            configInstanceName,
            appName,
            lastInstanceUpdateSeqNo,
            jacksonObjectMapper().writeValueAsString(instanceUpdateStatus)
        )
    }

    override fun findPresetPaginated(search: String, current: Long, pageSize: Long): Pagination<AppTemplate> {
        val presetTemplates = loadPresetTemplates(
            PRESET_TEMPLATE_NAMES.map {
                javaClass.getResource(String.format(PRESET_TEMPLATE_PATH_FORMAT, it, LocaleUtil.getLanguage()))
            }
        ).filter {
            if (StringUtils.isNotBlank(search)) {
                it.name.contains(search) || it.displayName.contains(search)
            } else {
                true
            }
        }

        return Pagination(
            total = presetTemplates.size.toLong(),
            current = current,
            pageSize = pageSize,
            pages = Math.ceil(presetTemplates.size / pageSize.toDouble()).toLong(),
            records = presetTemplates.subList(
                (current - 1).toInt() * pageSize.toInt(),
                if (current * pageSize > presetTemplates.size) presetTemplates.size else current.toInt() * pageSize.toInt()
            )
        )
    }

    override fun findPresetConfig(templateName: String, type: AppTemplateConfig.TypeEnum): AppTemplateConfig? {
        if (PRESET_TEMPLATE_NAMES.contains(templateName)) {
            return loadPresetTemplateConfig(
                javaClass.getResource(
                    String.format(
                        PRESET_TEMPLATE_PATH_FORMAT,
                        templateName,
                        LocaleUtil.getLanguage()
                    )
                ),
                type
            )
        }
        return null
    }

    private fun loadPresetTemplates(presetTemplateResources: List<java.net.URL>): List<AppTemplate> {
        return presetTemplateResources.map {
            val root = jacksonObjectMapper().readTree(it.readText())
            val appTemplate = jacksonObjectMapper().readValue(root["template"].toString(), AppTemplate::class.java)
            appTemplate.type = AppTemplate.Type.PRESET
            appTemplate
        }
    }

    private fun loadPresetTemplateConfig(resource: java.net.URL, type: AppTemplateConfig.TypeEnum): AppTemplateConfig? {
        val root = jacksonObjectMapper().readTree(resource.readText())
        val configs = (root["configs"] as ArrayNode).map { config ->
            jacksonObjectMapper().readValue(config.toString(), AppTemplateConfig::class.java)
        }
        return configs.firstOrNull { it.type == type}
    }
}