package com.alibabacloud.devops.appstack.apps.biz.service.facade

import com.alibabacloud.devops.appstack.apps.biz.service.client.api.ChangeOrderApi
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.checkBizWithCode
import com.alibabacloud.devops.appstack.libs.model.`do`.env.EnvTaskDetail
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
@Slf4k
class ChangeOrderFacade {
    @Autowired
    lateinit var changeOrderApi: ChangeOrderApi

    fun deleteOrderAndVersionByAppName(appName: String) {
        logger.info("delete change orders and version of app[${appName}]")
        val res = changeOrderApi.deleteOrderAndVersion(appName = appName)
        checkBizWithCode(res.success) {
            logger.error("[ChangeOrderFacade] Failed to delete change orders: app=$appName")
            ErrorCode.AS_UNKNOWN
        }
    }

    fun batchDeleteRelate(appName: String) {
        logger.info("delete all change orders relate with app[${appName}]")
        val res = changeOrderApi.batchDeleteRelate(appName = appName)
        checkBizWithCode(res.success) {
            logger.error("[ChangeOrderFacade] Failed to perform batch-delete for change orders: app=$appName")
            ErrorCode.AS_UNKNOWN
        }
    }

    fun queryLatestEnvTaskByAppName(appName: String): List<EnvTaskDetail> {
        logger.info("queryLatestEnvTaskByAppName with app[$appName]")
        var res = changeOrderApi.queryLatestEnvTaskByAppName(appName = appName)
        checkBizWithCode(res.success) { ErrorCode.AS_CHANGE_ORDER_TASK_NOT_EXISTED }
        res.data?.let {
            return it
        }
        return emptyList()
    }

}