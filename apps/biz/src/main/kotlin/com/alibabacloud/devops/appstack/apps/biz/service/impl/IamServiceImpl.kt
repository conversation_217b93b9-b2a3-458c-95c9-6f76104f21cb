package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.IamClient
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.config.IamContext
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.constant.Constant.getOwnerRoleName
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.service.AuthorizeService
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.iam.OrgRole
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.MemberRole
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.RolePolicy
import com.alibabacloud.devops.appstack.libs.org.spring.boot.starter.model.Group
import com.alibabacloud.devops.appstack.libs.org.spring.boot.starter.model.OrganizationMember
import com.alibabacloud.devops.appstack.libs.org.spring.boot.starter.model.Team
import com.alibabacloud.devops.appstack.libs.org.spring.boot.starter.service.OrgFacades
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.exception.IamNotFoundException
import com.alibabacloud.devops.iam.model.Resource
import com.alibabacloud.devops.iam.model.ResourcePlayer
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.RoleWithPlayer
import com.alibabacloud.devops.iam.model.request.CanRequest
import com.alibabacloud.devops.iam.model.request.CreateResourceRequest
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service

/**
 * <AUTHOR> <EMAIL>
 * @version : IamServiceImpl, v0.1
 * @date : 2021-11-29 16:19
 **/
@Slf4k
@Service
@ConditionalOnProperty(name = ["devops.iam.environment"], havingValue = "aliyun", matchIfMissing = true)
open class IamServiceImpl : IamService {

    @Autowired
    lateinit var iamClient: IamClient

    @Autowired
    lateinit var orgFacades: OrgFacades

    @Autowired
    lateinit var authorizeService: AuthorizeService

    override fun rebuildIamConfig() {
        iamClient.rebuildConfig()
    }

    override fun registerResource(protocolType: ProtocolType, resourceName: String, orgId: String, ownerId: String) {
        if (!iamClient.whiteList.isEmpty() && !iamClient.whiteList.contains(orgId)) {
            return
        }
        val resourcePlayerRequest = ResourcePlayerRequest.builder()
            .id(ownerId)
            .type(SubjectType.User)
            .build()
        val roles = mapOf(
            getOwnerRoleName(protocolType) to listOf<ResourcePlayerRequest>(resourcePlayerRequest)
        )
        val createResourceRequest = CreateResourceRequest.builder()
            .name(resourceName)
            .displayName(resourceName)
            .description("register by biz")
            .attributes(emptyMap())
            .roles(roles)
            .build()
        try {
            // check权限初始化
            iamClient.modelApi.find(protocolType)
        } catch (e: IamNotFoundException) {
            iamClient.rebuildConfig()
        }
        iamClient.resourceApi.create(protocolType, createResourceRequest)
        logger.info("register resource [${resourceName}], protocol [${protocolType}], ownerId [${ownerId}], orgId [${orgId}]")
    }

    override fun unregisterResource(
        protocolType: ProtocolType,
        resourceName: String,
        orgId: String,
        operatorId: String,
    ) {
        if (!iamClient.whiteList.isEmpty() && !iamClient.whiteList.contains(orgId)) {
            return
        }
        try {
            iamClient.resourceApi.delete(protocolType, resourceName)
            logger.info("unregister resource [${resourceName}], protocol [${protocolType}]")
        } catch (e: IamNotFoundException) {
            //ignore
        }
    }

    override fun findRole(protocolType: ProtocolType, roleName: String): Role? {
        return try {
            iamClient.roleApi.find(protocolType, roleName)
        } catch (e: IamNotFoundException) {
            logger.warn("app role [{}] not found", roleName)
            null
        }
    }

    override fun findPlayerRoles(
        protocolType: ProtocolType,
        resourceName: String,
    ): Map<ResourcePlayer, MutableList<Role>> {
        return try {
            val resource = iamClient.resourceApi.find(protocolType, resourceName)
            val playerRoles = resource.convertToPlayerRolesMap()
//            val userIdList = playerRoles.keys.mapNotNull { if (it.type == SubjectType.User) it.id else null }
            val groupIdList = playerRoles.keys.mapNotNull { if (it.type == SubjectType.Group) it.id else null }
            val teamIdList = playerRoles.keys.mapNotNull { if (it.type == SubjectType.Team) it.id else null }
//            val memberMap = batchGetOrganizationMembers(userIdList).associateBy { it.userId }
            val groupMap = batchGetGroups(groupIdList).associateBy { it.id }
            val teamMap = batchGetTeams(teamIdList).associateBy { it.id }
            playerRoles.keys.forEach {
                when (it.type) {
//                    SubjectType.User -> {
//                        val member = memberMap[it.id]
//                        it.displayName = member?.name ?: ""
//                        it.description = member?.userInfo?.name ?: ""
//                        it.avatar = member?.userInfo?.avatarUrl ?: ""
//                    }
                    SubjectType.Group -> {
                        val group = groupMap[it.id]
                        it.displayName = group?.name ?: ""
                    }

                    SubjectType.Team -> {
                        val team = teamMap[it.id]
                        it.displayName = team?.name ?: ""
                    }

                    else -> {}
                }
            }
            return playerRoles
        } catch (e: IamNotFoundException) {
            logger.warn("resource [${resourceName}] members not found")
            emptyMap()
        }
    }

    override fun findRolePlayers(
        protocolType: ProtocolType,
        resourceNameList: List<String>,
        roleNameList: List<String>,
    ): Map<String, List<RoleWithPlayer>> {
        val resourceList = iamClient.resourceApi.findList(protocolType, resourceNameList)
        return resourceList.associate { resource ->
            resource.name to resource.rolePlayerList.filter {
                roleNameList.contains(it.name)
            }
        }
    }

    override fun findRolePlayers(
        protocolType: ProtocolType,
        resourceNameList: List<String>,
        roleName: String,
    ): Map<String, List<String>> {
        return iamClient.resourceApi.findRolePlayers(protocolType, resourceNameList, roleName)
    }

    override fun addRoleMember(
        protocolType: ProtocolType,
        resourceName: String,
        roleName: String,
        playerList: List<ResourcePlayerRequest>,
    ) {
        iamClient.resourceApi.addRolePlayer(protocolType, resourceName, roleName, playerList)
    }

    override fun updateRole(
        protocolType: ProtocolType,
        resourceName: String,
        roleName: String,
        playerList: List<ResourcePlayerRequest>,
    ) {
        iamClient.resourceApi.updateRole(protocolType, resourceName, roleName, playerList)
    }

    override fun updatePlayer(
        protocolType: ProtocolType,
        resourceName: String,
        subjectId: String,
        subjectType: SubjectType,
        roleNameList: List<String>,
    ) {
        iamClient.resourceApi.updatePlayer(protocolType, resourceName, subjectType, subjectId, roleNameList)
    }

    override fun countResource(
        protocolType: ProtocolType,
        subjectId: String,
        subjectType: SubjectType,
        search: String?,
    ): Int {
        return if (subjectType == SubjectType.User) {
            val players = allPlayersByUser(userId = subjectId)
            iamClient.resourceApi.countBySubjects(protocolType, search, players)
        } else {
            iamClient.resourceApi.countBySubject(protocolType, subjectType, subjectId, search)
        }
    }

    override fun searchResourceName(
        protocolType: ProtocolType,
        subjectId: String,
        subjectType: SubjectType,
        search: String?,
        page: Int,
        size: Int,
    ): List<String> {
        return if (subjectType == SubjectType.User) {
            val players = allPlayersByUser(userId = subjectId)
            iamClient.resourceApi.searchPaginated(protocolType, search, page, size, players)
        } else {
            iamClient.resourceApi.searchPaginatedBySubject(protocolType, subjectType, subjectId, search, page, size)
        }
    }

    override fun searchResource(
        protocolType: ProtocolType,
        subjectId: String,
        subjectType: SubjectType,
        search: String?,
        page: Int,
        size: Int,
        containPlayers: Boolean,
    ): List<Resource> {
        return iamClient.resourceApi.searchPaginatedBySubject(
            protocolType,
            subjectType,
            subjectId,
            search,
            page,
            size,
            containPlayers
        )
    }

    override fun countResourceWithPolicy(
        protocolType: ProtocolType,
        subjectId: String,
        subjectType: SubjectType,
        policyName: String,
        search: String?,
    ): Int {
        return iamClient.resourceApi.countBySubjectAndPolicy(protocolType, subjectType, subjectId, policyName, search)
    }

    override fun searchResourceWithPolicy(
        protocolType: ProtocolType,
        subjectId: String,
        subjectType: SubjectType,
        policyName: String,
        search: String?,
        page: Int,
        size: Int,
    ): List<String> {
        return iamClient.resourceApi.searchPaginatedBySubjectAndPolicy(
            protocolType,
            subjectType,
            subjectId,
            policyName,
            search,
            page,
            size
        )
    }

    override fun searchResourceWithPolicy(
        protocolType: ProtocolType,
        playersList: List<ResourcePlayerRequest>,
        policyName: String,
        search: String?,
        page: Int,
        size: Int,
    ): List<String> {
        return iamClient.resourceApi.searchPaginatedByPlayersAndPolicy(
            protocolType,
            playersList,
            policyName,
            search,
            page,
            size
        )
    }

    override fun can(protocolType: ProtocolType, resourceName: String, action: Action): Boolean {
        return authorizeService.can(protocolType, resourceName, action)
    }

    override fun can(
        protocolType: ProtocolType,
        resourceName: String,
        action: Action,
        abacProtocolType: ProtocolType,
        canRequest: CanRequest,
    ): Boolean {
        return authorizeService.can(protocolType, resourceName, action, abacProtocolType, canRequest)
    }

    override fun fetchRolePermission(protocolType: ProtocolType, filter: List<Action>): List<RolePolicy> {
        val codes = filter.map { it.code }
        val roles = iamClient.roleApi.findAll(protocolType)
        return roles.associateWith {
            iamClient.permissionApi.find(protocolType, it.name).policyOptionList.filter { policyOption ->
                codes.contains(policyOption.name)
            }
        }.map { RolePolicy(it.key, it.value) }
    }

    override fun fetchOrgMembers(): List<MemberRole> {
        return orgFacades.organizationFacade.getAllOrgMember(orgId = AuthUtil.getTenant())
            .filter { !it.isDisabled }
            .map {
                val roleList = when (it.roleLevel) {
                    2 -> listOf(Role().apply {
                        this.protocol = ProtocolType.AppStack.toString(); this.name =
                        OrgRole.owner.name; this.displayName =
                        OrgRole.owner.displayName
                    })

                    1 -> listOf(Role().apply {
                        this.protocol = ProtocolType.AppStack.toString(); this.name =
                        OrgRole.admin.name; this.displayName =
                        OrgRole.admin.displayName
                    })

                    0 -> listOf(Role().apply {
                        this.protocol = ProtocolType.AppStack.toString(); this.name =
                        OrgRole.user.name; this.displayName =
                        OrgRole.user.displayName
                    })

                    -1 -> listOf(Role().apply {
                        this.protocol = ProtocolType.AppStack.toString(); this.name =
                        OrgRole.guest.name; this.displayName =
                        OrgRole.guest.displayName
                    })

                    else -> listOf()
                }
                MemberRole(id = it.userId, type = SubjectType.User, displayName = it.name, roleList = roleList)
            }
    }

    override fun checkPlayersExist(players: List<ResourcePlayerRequest>): Boolean {
        val userIdList = players.mapNotNull { if (it.type == SubjectType.User) it.id else null }
        val groupIdList = players.mapNotNull { if (it.type == SubjectType.Group) it.id else null }
        val teamIdList = players.mapNotNull { if (it.type == SubjectType.Team) it.id else null }
        return try {
            batchGetOrganizationMembers(userIdList).associateBy { it.userId }
            batchGetGroups(groupIdList).associateBy { it.id }
            batchGetTeams(teamIdList).associateBy { it.id }
            true
        } catch (e: Throwable) {
            logger.error("failed to checkPlayersExist: players: $players", e)
            false
        }
    }

    override fun deletePlayer(subjectType: SubjectType, subjectId: String) {
        iamClient.resourceApi.deletePlayer(subjectType, subjectId)
    }

    override fun getTeamPlayer(orgId: String, userId: String, containSuperior: Boolean): List<ResourcePlayerRequest> {
        val teams = getJoinedTeams(orgId, userId).map { ResourcePlayerRequest(it.id, SubjectType.Team) }
        return if (containSuperior) {
            teams.flatMap { getTeamPath(it.id) }.map { team ->
                ResourcePlayerRequest(team.id, SubjectType.Team)
            }.distinctBy { it.id }
        } else {
            teams
        }
    }

    override fun getGroupPlayer(orgId: String, userId: String): List<ResourcePlayerRequest> {
        return getJoinedGroups(orgId, userId).map { ResourcePlayerRequest(it.id, SubjectType.Group) }
    }

    override fun checkUserExist(userId: String) {
        orgFacades.organizationFacade.getMember(orgId = AuthUtil.getTenant(), userId = userId)?: throw BizException(
            ErrorCode.AS_BASE_USER_NOT_FOUND)
    }

    override fun listUserIdsByTeam(orgId: String, teamId: String): List<String> {
        return orgFacades.organizationFacade.listMembersOfTeam(orgId, teamId).map { it.userId }
    }

    override fun listUserIdsByGroup(orgId: String, groupId: String): List<String> {
        return orgFacades.organizationFacade.listMembersOfGroup(orgId, groupId).map { it.userId }
    }

    private fun batchGetOrganizationMembers(userIdList: List<String>): List<OrganizationMember> {
        return orgFacades.organizationFacade.getMemberList(orgId = AuthUtil.getTenant(), userIds = userIdList)
    }

    private fun batchGetTeams(teamsIdList: List<String>): List<Team> {
        return orgFacades.organizationFacade.listOrgTeams(orgId = AuthUtil.getTenant(), teamIds = teamsIdList)
    }

    protected open fun getJoinedTeams(orgId: String, userId: String): List<Team> {
        return orgFacades.organizationFacade.getJoinedTeams(orgId, userId)
    }

    protected open fun getTeamPath(teamId: String): List<Team> {
        return orgFacades.organizationFacade.getTeamPath(orgId = AuthUtil.getTenant(), teamId = teamId)
    }

    private fun batchGetGroups(groupsIdList: List<String>): List<Group> {
        return orgFacades.organizationFacade.listOrgGroups(orgId = AuthUtil.getTenant(), groupIds = groupsIdList)
    }

    protected open fun getJoinedGroups(orgId: String, userId: String): List<Group> {
        // 公有云暂未启用群组权限，此处做短路操作，避免查询底座
        // return orgFacades.organizationFacade.getJoinedGroups(orgId, userId)
        return emptyList()
    }

    private fun allPlayersByUser(userId: String): List<ResourcePlayerRequest> {
        return listOf(ResourcePlayerRequest(userId, SubjectType.User)) +
                getTeamPlayer(AuthUtil.getTenant(), userId) +
                getGroupPlayer(AuthUtil.getTenant(), userId)
    }

}