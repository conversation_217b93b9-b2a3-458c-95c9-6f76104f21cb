package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.apps.biz.adapter.EnvLogChangeOrderAdapter
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeableEnv
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum


/**
 * <AUTHOR>
 * @date 2022-03-31
 */
interface EnvLogChangeOrderAdapterFactory {

    fun build(engineType: ChangeableEnv.EngineType, resourceType: ResourceTypeEnum): EnvLogChangeOrderAdapter
}