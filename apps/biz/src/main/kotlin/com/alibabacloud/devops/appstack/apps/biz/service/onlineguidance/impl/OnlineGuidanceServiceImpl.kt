package com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.impl

import com.alibabacloud.devops.appstack.apps.biz.model.OnlineGuidance
import com.alibabacloud.devops.appstack.apps.biz.model.State
import com.alibabacloud.devops.appstack.apps.biz.model.StepProgress
import com.alibabacloud.devops.appstack.apps.biz.model.ext.toModel
import com.alibabacloud.devops.appstack.apps.biz.model.ext.toPO
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.OnlineGuidanceMapper
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.find
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.update
import com.alibabacloud.devops.appstack.apps.biz.model.po.OnlineGuidancePO
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.OnlineGuidanceService
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.OnlineGuideStepFactory
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import javax.annotation.PostConstruct

/**
 * @author: <EMAIL>
 * @date: 2022-08-25 22:08
 * @version: OnlineGuidanceServiceImpl, v0.1
 **/
@Service
@Slf4k
open class OnlineGuidanceServiceImpl : OnlineGuidanceService {

    @Autowired
    lateinit var onlineGuideStepFactory: OnlineGuideStepFactory

    @Autowired
    lateinit var onlineGuidanceMapper: OnlineGuidanceMapper

    @Value("\${devops.appstack.environment}")
    lateinit var environment: String
    private var onlineGuidanceList: List<OnlineGuidance> = emptyList()

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0)
        ]
    )
    override fun find(appName: String): OnlineGuidance {
        var onlineGuidancePO = onlineGuidanceMapper.find(appName)
        if (onlineGuidancePO == null) {
            onlineGuidancePO = generate(appName = appName)
        }
        val onlineGuidance = onlineGuidancePO.toModel()
        val stepProcessList = onlineGuidance.stepList.map {
            if(it.state == State.FINISH){
                StepProgress(step = it, needUpdate = false)
            }else {
                onlineGuideStepFactory.build(it.name).handle(appName, it)
            }
        }
        logger.info("onlineGuidance newStepList after handle is : [{}] ", stepProcessList.toString())
        onlineGuidance.stepList = stepProcessList.filterNotNull().map { it.step}
        if(stepProcessList.filterNotNull().any { it.needUpdate }){
            onlineGuidanceMapper.update(onlineGuidance.toPO())
        }
        return onlineGuidance
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_DELETE, resourceArgIndex = 0)
        ]
    )
    override fun delete(appName: String) {
        val onlineGuidancePO = onlineGuidanceMapper.find(appName)
        onlineGuidancePO?.let {
            onlineGuidanceMapper.deleteById(it.id)
        }
    }

    private fun generate(appName: String, name: String = ""): OnlineGuidancePO {
        val guidanceName = "${environment}${name}"
        val template = onlineGuidanceList.first { it.name == guidanceName }
        val onlineGuidancePO = OnlineGuidance(
                appName = appName,
                name = template.name,
                stepList = template.stepList
        ).toPO()
        onlineGuidanceMapper.insert(onlineGuidancePO)
        return onlineGuidancePO
    }

    @PostConstruct
    fun init() {
        val onlineGuidanceTemplateStr = javaClass.getResource("/template/onlineguidance/online-guidance-flow-v1.json").readText()
        onlineGuidanceList = jacksonObjectMapper().readValue(onlineGuidanceTemplateStr)
    }
}