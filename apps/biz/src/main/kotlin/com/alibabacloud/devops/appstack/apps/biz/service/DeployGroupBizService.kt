package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.DeployGroup
import com.alibabacloud.devops.appstack.libs.model.request.UpsertDeployGroupRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.DeployGroupQuery
import com.alibabacloud.devops.appstack.libs.model.vo.DeployGroupVO

/**
 * @author: <EMAIL>
 * @date: 2022-08-02 11:06
 * @version: DeployGroupBizService, v0.1
 **/
interface DeployGroupBizService {

    /**
     * 查询资源池下的指定实例下的部署组
     */
    fun findAllByInstance(poolName: String, instanceName: String): List<DeployGroupVO>

    /**
     * 查询指定部署类型的可用部署组
     */
    fun findAllByType(poolName: String, type: String): List<DeployGroup>

    /**
     * 按名称查询部署组
     */
    fun find(poolName: String, groupName: String): DeployGroup

    /**
     * 创建部署组
     */
    fun create(poolName: String, upsertDeployGroupRequest: UpsertDeployGroupRequest): DeployGroup

    /**
     * 更新部署组信息
     */
    fun update(poolName: String, groupName: String, upsertDeployGroupRequest: UpsertDeployGroupRequest): DeployGroup

    /**
     * 删除部署组
     */
    fun delete(poolName: String, groupName: String)
}