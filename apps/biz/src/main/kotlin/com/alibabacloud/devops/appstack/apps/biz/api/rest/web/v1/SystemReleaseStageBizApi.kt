package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.system.ReleaseStageBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ExecutePipelineResult
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStage
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.*
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.ReleaseIntegratedMetadata
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @date 2022-06-28
 */
@Tag(name = "SystemReleaseStage", description = "研发阶段相关 API")
@RestController
@RequestMapping("/api/v1")
open class SystemReleaseStageBizApi {

    @Autowired
    lateinit var releaseStageBizService: ReleaseStageBizService

    @Operation(summary = "创建发布阶段")
    @PostMapping("/systems/{systemName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages")
    fun createSystemReleaseStage(
        @PathVariable systemName: String,
        @PathVariable releaseWorkflowSn: String,
        @RequestBody request: CreateReleaseStageBizRequest
    ): Response<ReleaseStage> {
        return Response.success(releaseStageBizService.create(systemName, releaseWorkflowSn, request))
    }

    @Operation(summary = "查找发布流程下所有阶段")
    @GetMapping("/systems/{systemName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages")
    fun findAllSystemReleaseStage(@PathVariable systemName: String, @PathVariable releaseWorkflowSn: String): Response<List<ReleaseStage>> {
        return Response.success(releaseStageBizService.findAll(systemName, releaseWorkflowSn))
    }


    @Operation(summary = "查询阶段详情")
    @GetMapping("/systems/{systemName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}")
    fun findSystemReleaseStage(
        @PathVariable systemName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String
    ): Response<ReleaseStage> {
        return Response.success(releaseStageBizService.find(systemName, releaseWorkflowSn, releaseStageSn))
    }

    @Operation(summary = "编辑阶段")
    @PutMapping("/systems/{systemName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}")
    fun updateSystemReleaseStage(
        @PathVariable systemName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @RequestBody request: UpdateReleaseStageRequest
    ): Response<ReleaseStage> {
        return Response.success(releaseStageBizService.update(systemName, releaseWorkflowSn, releaseStageSn, request))
    }

    @Operation(summary = "删除阶段")
    @DeleteMapping("/systems/{systemName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}")
    fun deleteSystemReleaseStage(
        @PathVariable systemName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String
    ): Response<Unit> {
        releaseStageBizService.delete(systemName, releaseWorkflowSn, releaseStageSn)
        return Response.success()
    }

    @Operation(summary = "更新阶段的流水线")
    @PutMapping("/systems/{systemName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}:pipeline")
    fun updateSystemPipeline(
        @PathVariable systemName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @RequestBody request: UpdateReleaseStagePipelineRequest
    ): Response<ReleaseStage> {
        return Response.success(
            releaseStageBizService.updatePipeline(
                systemName,
                releaseWorkflowSn,
                releaseStageSn,
                request
            )
        )
    }

    @Operation(summary = "运行阶段的流水线")
    @PostMapping("/systems/{systemName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}:execute_pipeline")
    fun executeSystemPipeline(
        @PathVariable systemName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @RequestBody request: ExecuteReleaseStagePipelineRequest
    ): Response<ExecutePipelineResult> {
        return Response.success(
            releaseStageBizService.executePipeline(
                systemName,
                releaseWorkflowSn,
                releaseStageSn,
                request
            )
        )
    }

    @Operation(summary = "阶段发布集成信息")
    @PostMapping("/systems/{systemName}/releaseWorkflow/{releaseWorkflowSn}/releaseStage/{releaseStageSn}:find_release_integrated_metadata")
    fun findSystemReleaseIntegratedMetadata(
        @PathVariable systemName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @RequestBody request: QueryReleaseStageCrMetadataRequest
    ): Response<ReleaseIntegratedMetadata> {
        return Response.success(
            releaseStageBizService.findReleaseStageCrMetadata(
                systemName,
                releaseWorkflowSn,
                releaseStageSn,
                request
            )
        )
    }

    @Operation(summary = "前序阶段发布集成信息")
    @PostMapping("/systems/{systemName}/releaseWorkflow/{releaseWorkflowSn}/releaseStage/{releaseStageSn}/findPreviousMetadata")
    fun findSystemPreviousMetadata(
        @PathVariable systemName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String
    ): Response<ReleaseIntegratedMetadata> {
        return Response.success(
            releaseStageBizService.findPreviousMetadata(
                systemName,
                releaseWorkflowSn,
                releaseStageSn
            )
        )
    }

    @Operation(summary = "查询阶段部署环境详情")
    @GetMapping("/systems/{systemName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}:findStageEnvList")
    fun findSystemStageEnvList(
        @PathVariable systemName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String
    ): Response<List<String>> {
        return Response.success(releaseStageBizService.findStageEnvList(releaseStageSn))
    }

    @Operation(summary = "查询阶段使用的变量组")
    @GetMapping("/systems/{systemName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}:variableGroups")
    fun findVariableGroups(
        @PathVariable systemName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String
    ): Response<List<VariableGroup>> {
        return Response.success(releaseStageBizService.findVariableGroups(systemName, releaseWorkflowSn, releaseWorkflowSn))
    }
}