package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.apps.biz.service.system.ReleaseBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.ChangeRequest
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.WorkflowMetadata
import com.alibabacloud.devops.appstack.libs.model.`do`.release.AppRelease
import com.alibabacloud.devops.appstack.libs.model.`do`.release.AppReleaseInst
import com.alibabacloud.devops.appstack.libs.model.iam.ReleaseRole
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.CreateAppReleaseRequest
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.SearchAppReleasableRequest
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.SearchAppReleaseRequest
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.SearchOrgAppReleaseRequest
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.TransferOwnerRequest
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.UpdateAppReleaseRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.AppReleaseRecordWithOwner
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2023-03-02 18:33
 * @version: AppReleaseBizApi, v0.1
 **/
@Tag(name = "Release", description = "应用稳态发布 API")
@RestController
@RequestMapping("/api/v1")
open class ReleaseBizApi {
    @Autowired
    lateinit var releaseBizService: ReleaseBizService

    @Operation(summary = "创建应用发布")
    @PostMapping("/systems/{systemName}/releases")
    fun createRelease(
        @PathVariable systemName: String,
        @RequestBody createAppReleaseRequest: CreateAppReleaseRequest
    ): Response<AppRelease> {
        return Response.success(releaseBizService.create(systemName, createAppReleaseRequest))
    }

    @Operation(summary = "删除应用发布")
    @DeleteMapping("/systems/{systemName}/releases/{sn}")
    fun deleteRelease(
        @PathVariable systemName: String,
        @PathVariable sn: String
    ): Response<Boolean> {
        return Response.success(releaseBizService.delete(systemName, sn))
    }

    @Operation(summary = "废弃应用发布")
    @PutMapping("/systems/{systemName}/releases/{sn}:close")
    fun closeRelease(
        @PathVariable systemName: String,
        @PathVariable sn: String,
    ): Response<Boolean> {
        return Response.success(releaseBizService.close(systemName, sn))
    }

    @Operation(summary = "手动置为已发布")
    @PutMapping("/systems/{systemName}/releases/{sn}:release")
    fun release(
        @PathVariable systemName: String,
        @PathVariable sn: String,
        @RequestParam instSn: String,
    ): Response<Boolean> {
        return Response.success(releaseBizService.release(systemName, sn, instSn))
    }

    @Operation(summary = "修改应用发布")
    @PutMapping("/systems/{systemName}/releases/{sn}")
    fun updateRelease(
        @PathVariable systemName: String,
        @PathVariable sn: String,
        @RequestBody updateAppReleaseRequest: UpdateAppReleaseRequest,
    ): Response<AppRelease> {
        return Response.success(releaseBizService.update(systemName = systemName, sn = sn, updateAppReleaseRequest))
    }

    @Operation(summary = "查找指定发布")
    @GetMapping("/systems/{systemName}/releases/{sn}")
    fun findRelease(@PathVariable systemName: String, @PathVariable sn: String): Response<AppRelease> {
        return Response.success(releaseBizService.find(systemName, sn))
    }

    @Operation(summary = "分页查找应用发布, 有发布权限的")
    @PostMapping("/systems/{systemName}/releases:releasable")
    fun findReleasablePaginated(
        @PathVariable systemName: String,
        @RequestBody request: SearchAppReleasableRequest,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<AppReleaseRecordWithOwner>> {
        return Response.success(releaseBizService.searchReleasablePaginated(systemName, current, pageSize, request))
    }

    @Operation(summary = "分页查找应用发布")
    @PostMapping("/systems/{systemName}/releases:search")
    fun searchReleasePaginatedUnderApp(
        @PathVariable systemName: String,
        @RequestBody searchAppReleaseRequest: SearchAppReleaseRequest,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<AppReleaseRecordWithOwner>> {
        return Response.success(releaseBizService.searchPaginatedUnderApp(systemName, current, pageSize, searchAppReleaseRequest))
    }

    @Operation(summary = "查找企业下应用发布")
    @PostMapping("/appReleases:search")
    fun searchAppReleasePaginated(
        @RequestBody searchOrgAppReleaseRequest: SearchOrgAppReleaseRequest,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<AppReleaseRecordWithOwner>> {
        return Response.success(releaseBizService.searchPaginated(searchOrgAppReleaseRequest, current, pageSize))
    }

    @Operation(summary = "查找应用发布实例")
    @GetMapping("/systems/{systemName}/releases/{appReleaseSn}/instances/{instanceSn}")
    fun findAppReleaseInstance(
        @PathVariable systemName: String,
        @PathVariable appReleaseSn: String,
        @PathVariable instanceSn: String,
    ): Response<AppReleaseInst?> {
        return Response.success(
            releaseBizService.findInstance(
                systemName = systemName,
                releaseSn = appReleaseSn,
                instanceSn = instanceSn,
            )
        )
    }

    @Operation(summary = "查找应用发布最新一次实例")
    @GetMapping("/systems/{systemName}/releases/{appReleaseSn}/instances:latest")
    fun findInstanceLatest(
        @PathVariable systemName: String,
        @PathVariable appReleaseSn: String,
    ): Response<AppReleaseInst?> {
        return Response.success(
            releaseBizService.findInstanceLatest(
                systemName = systemName,
                releaseSn = appReleaseSn,
            )
        )
    }

    @Operation(summary = "发布项的比较, from/to为instanceSn, to为空时即为发布项的最新内容")
    @GetMapping("/systems/{systemName}/releases/{appReleaseSn}/instance:compare")
    fun findAppReleaseItemsCompared(
        @PathVariable systemName: String,
        @PathVariable appReleaseSn: String,
        @RequestParam("from") from: String,
        @RequestParam("to", required = false, defaultValue = "") to: String?,
    ): Response<AppReleaseInst.ComparedResult> {
        return Response.success(
            releaseBizService.compareAppReleaseInst(
                systemName = systemName,
                releaseSn = appReleaseSn,
                from = from,
                to = to,
            )
        )
    }

    @Operation(summary = "分页查找应用发布实例")
    @PostMapping("/systems/{systemName}/releases/{appReleaseSn}/instances")
    fun findInstancePaginated(
        @PathVariable systemName: String,
        @PathVariable appReleaseSn: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<AppReleaseInst>> {
        return Response.success(
            releaseBizService.findInstancePaginated(
                systemName = systemName,
                releaseSn = appReleaseSn,
                current = current,
                pageSize = pageSize,
            )
        )
    }

    @Operation(summary = "获取发布项的版本")
    @GetMapping("/systems/{systemName}/releases/{appReleaseSn}/items/{itemSn}")
    fun findItemVersion(
        @PathVariable systemName: String,
        @PathVariable appReleaseSn: String,
        @RequestParam("instanceSn", required = false) instanceSn: String?,
        @PathVariable itemSn: String,
    ): Response<AppReleaseInst.Version> {
        return Response.success(
            releaseBizService.findItemVersion(
                systemName = systemName,
                releaseSn = appReleaseSn,
                instanceSn = instanceSn,
                itemSn = itemSn,
            )
        )
    }

    @Operation(summary = "查找发布元数据")
    @PostMapping("/systems/{systemName}/releases/{sn}/releaseMetadata")
    fun findMetadata(
        @PathVariable systemName: String,
        @PathVariable sn: String,
        @RequestParam workflowSn: String,
    ): Response<List<WorkflowMetadata>> {
        return Response.success(
            releaseBizService.findMetadata(
                systemName = systemName,
                releaseSn = sn,
                workflowSn = workflowSn,
            )
        )
    }

    @Operation(summary = "阶段发布集成信息")
    @PostMapping("/systems/{systemName}/releases/findInstanceByTrigger")
    fun findInstanceByTrigger(
        @PathVariable systemName: String,
        @RequestBody trigger: AppReleaseInst.Trigger
    ): Response<AppReleaseInst?> {
        return Response.success(
            releaseBizService.findInstanceByTrigger(
                systemName = systemName,
                trigger = trigger,
            )
        )
    }
    @Operation(summary = "查找成员")
    @GetMapping("/systems/{systemName}/releases/{sn}/members")
    fun findMember(@PathVariable systemName: String, @PathVariable sn: String): Response<List<MemberVO>> {
        val res = releaseBizService.listMembers(systemName, sn, 1L, 100L)
        return Response.success(res.records)
    }

    @Operation(summary = "更新成员的角色, 删除成员=角色列表为空")
    @PutMapping("/systems/{systemName}/releases/{sn}/members/{userId}")
    fun updateAppReleaseMemberRole(
        @PathVariable systemName: String,
        @PathVariable("sn") sn: String,
        @PathVariable("userId") userId: String,
        @RequestBody roleNameList: List<String>,
    ): Response<Boolean> {
        releaseBizService.updateMemberRole(
            systemName = systemName,
            releaseSn = sn,
            userId = userId,
            roleNameList = roleNameList,
        )
        return Response.success(true)
    }

    @Operation(summary = "更新角色的成员")
    @PutMapping("/systems/{systemName}/releases/{sn}/roles/{roleName}")
    fun updateRoleMember(
        @PathVariable systemName: String,
        @PathVariable("sn") sn: String,
        @PathVariable("roleName") roleName: String,
        @RequestBody playerList: List<ResourcePlayerRequest>,
    ): Response<Boolean> {
        releaseBizService.updateRoleMember(
            systemName = systemName,
            releaseSn = sn,
            roleName = roleName,
            playerList = playerList,
        )
        return Response.success(true)
    }

    @Operation(summary = "移交负责人")
    @PutMapping("/systems/{systemName}/releases/{sn}:owner")
    fun transferOwner(
        @PathVariable systemName: String,
        @PathVariable("sn") sn: String,
        @RequestBody request: TransferOwnerRequest,
    ): Response<Boolean> {
        releaseBizService.transferOwner(systemName, sn, request.owner)
        return Response.success(true)
    }

    @Operation(summary = "查找与发布关联的变更列表")
    @GetMapping("/systems/{systemName}/releases/{releaseSn}/changeRequests")
    fun listAttachedChangeRequests(
        @PathVariable systemName: String,
        @PathVariable releaseSn: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long
    ): Response<Pagination<ChangeRequest>> {
        return Response.success(
            releaseBizService.listAttachedChangeRequests(
                appName = systemName,
                releaseSn = releaseSn,
                current = current,
                pageSize = pageSize
            )
        )
    }

    @Operation(summary = "将变更关联到发布")
    @PostMapping("/systems/{systemName}/releases/{releaseSn}/changeRequests")
    fun attachChangeRequests(
        @PathVariable systemName: String,
        @PathVariable releaseSn: String,
        @RequestBody changeRequestSnList: List<String>
    ): Response<Boolean> {
        return Response.success(
            releaseBizService.attachChangeRequests(
                appName = systemName,
                releaseSn = releaseSn,
                changeRequestSnList = changeRequestSnList
            )
        )
    }

    @Operation(summary = "解除变更与发布的关联")
    @DeleteMapping("/systems/{systemName}/releases/{releaseSn}/changeRequests")
    fun detachChangeRequests(
        @PathVariable systemName: String,
        @PathVariable releaseSn: String,
        @RequestBody changeRequestSnList: List<String>
    ): Response<Boolean> {
        return Response.success(
            releaseBizService.detachChangeRequests(
                appName = systemName,
                releaseSn = releaseSn,
                changeRequestSnList = changeRequestSnList
            )
        )
    }

}