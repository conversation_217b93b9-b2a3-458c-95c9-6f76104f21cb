package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.cr.StageRuleConfig
import com.alibabacloud.devops.appstack.libs.model.request.temp.StageRuleConfigRequest

/**
 * @author: <EMAIL>
 * @date: 2022-08-01 19:08
 * @version: StageRuleConfigBizService, v0.1
 **/
interface StageRuleConfigBaseService {

    fun find(appName: String, releaseWorkflowSn: String, releaseStageSn: String): StageRuleConfig?

    fun upsert(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        stageRuleConfigRequest: StageRuleConfigRequest
    ): StageRuleConfig?
}