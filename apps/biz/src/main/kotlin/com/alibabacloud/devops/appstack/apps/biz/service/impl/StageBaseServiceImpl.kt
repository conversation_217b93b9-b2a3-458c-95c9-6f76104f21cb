package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.annotation.ApiMetricCounter
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.StageBaseService
import com.alibabacloud.devops.appstack.apps.biz.service.WorkflowBaseService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.model.ErrorEntry
import com.alibabacloud.devops.appstack.libs.common.util.checkBizWithEntry
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ExecutePipelineResult
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStage
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageBriefVO
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageInstance
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.org.event.ReleaseStageAudit
import com.alibabacloud.devops.appstack.libs.model.request.PaginationQuery
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.*
import com.alibabacloud.devops.appstack.libs.model.vo.ReleaseIntegratedMetadata
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2022-06-28
 */
@Service
open class StageBaseServiceImpl : StageBaseService {


    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var releaseWorkflowBaseService: WorkflowBaseService

    @Autowired
    lateinit var coreFacade: CoreFacades

    @Autowired
    lateinit var auditLogService: AuditLogService

    @ApiMetricCounter(metricName = "appReleaseStage_operation", methodTag = "create")
    override fun create(
        appName: String,
        releaseWorkflowSn: String,
        request: CreateReleaseStageBizRequest
    ): ReleaseStage {
        val releaseStage = workflowControllerFacade.releaseStageFacade.create(
            CreateReleaseStageRequest(
                name = request.name,
                releaseWorkflowSn = releaseWorkflowSn,
                labelList = request.labelList,
                order = request.order,
                appName = appName,
                variableGroups = request.variableGroups
            )
        )
        return releaseStage
    }

    override fun findAll(appName: String, releaseWorkflowSn: String): List<ReleaseStage> {
        releaseWorkflowBaseService.find(appName = appName, sn = releaseWorkflowSn)
        return workflowControllerFacade.releaseStageFacade.findAll(releaseWorkflowSn)
    }


    override fun find(appName: String, releaseWorkflowSn: String, releaseStageSn: String): ReleaseStage {
        releaseWorkflowBaseService.find(appName = appName, sn = releaseWorkflowSn)
        val releaseStage = workflowControllerFacade.releaseStageFacade.find(releaseStageSn)
        checkBizWithEntry(releaseWorkflowSn == releaseStage.releaseWorkflowSn) {
            ErrorEntry(code = ErrorCode.AS_RELEASE_STAGE_NOT_FOUND)
        }
        return releaseStage
    }

    override fun findBrief(appName: String, releaseWorkflowSn: String, releaseStageSn: String): ReleaseStageBriefVO {
        releaseWorkflowBaseService.findBrief(appName = appName, sn = releaseWorkflowSn)
        val releaseStage = workflowControllerFacade.releaseStageFacade.findBrief(releaseStageSn)
        checkBizWithEntry(releaseWorkflowSn == releaseStage.releaseWorkflowSn) {
            ErrorEntry(code = ErrorCode.AS_RELEASE_STAGE_NOT_FOUND)
        }
        return releaseStage
    }

    @ApiMetricCounter(metricName = "appReleaseStage_operation", methodTag = "update")
    override fun update(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        request: UpdateReleaseStageRequest
    ): ReleaseStage {
        val releaseWorkflow = releaseWorkflowBaseService.find(appName = appName, sn = releaseWorkflowSn)
        val previous = find(appName, releaseWorkflowSn, releaseStageSn)
        val after = workflowControllerFacade.releaseStageFacade.update(sn = releaseStageSn, request = request)
        val audit = ReleaseStageAudit(
            target = AuditTarget(id = releaseWorkflowSn, name = releaseWorkflow.name ?: releaseWorkflowSn),
            appName = appName,
            releaseStage = previous.name
        )
        auditLogService.log(OrgEventType.RELEASE_WORKFLOW_STAGE_MODIFY, audit)
        return after
    }

    @ApiMetricCounter(metricName = "appReleaseStage_operation", methodTag = "delete")
    override fun delete(appName: String, releaseWorkflowSn: String, releaseStageSn: String, commitMessage: String?): ReleaseStage {
        val releaseStage = find(appName, releaseWorkflowSn, releaseStageSn)
        workflowControllerFacade.releaseStageFacade.delete(releaseStageSn, commitMessage)
        return releaseStage
    }

    @ApiMetricCounter(metricName = "appReleaseStage_operation", methodTag = "updatePipeline")
    override fun updatePipeline(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        request: UpdateReleaseStagePipelineRequest
    ): ReleaseStage {
        val releaseWorkflow = releaseWorkflowBaseService.find(appName = appName, sn = releaseWorkflowSn)
        val before = find(appName, releaseWorkflowSn, releaseStageSn)
        val after = workflowControllerFacade.releaseStageFacade.updatePipeline(releaseStageSn, request)
        val audit = ReleaseStageAudit(
            target = AuditTarget(id = releaseWorkflowSn, name = releaseWorkflow.name ?: releaseWorkflowSn),
            appName = appName,
            releaseStage = before.name
        )
        auditLogService.log(OrgEventType.RELEASE_WORKFLOW_STAGE_PIPELINE_MODIFY, audit)
        return after
    }

    @ApiMetricCounter(metricName = "release_workflow_pipeline_execution")
    override fun executePipeline(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        request: ExecuteReleaseStagePipelineRequest
    ): ExecutePipelineResult {
        return workflowControllerFacade.releaseStageFacade.executePipeline(releaseStageSn, request)
    }

    override fun findReleaseStageCrMetadata(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        number: Long
    ): ReleaseIntegratedMetadata? {
        return workflowControllerFacade.releaseStageFacade.findReleaseStageCrMetadata(releaseStageSn, number)
    }


    override fun findPreviousMetadata(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String
    ): ReleaseIntegratedMetadata? {
        return workflowControllerFacade.releaseStageFacade.findPreviousMetadata(releaseStageSn)
    }

    override fun findStageEnvList(
        releaseStageSn: String,
    ): List<String> {
        return workflowControllerFacade.releaseStageFacade.findStageEnvList(releaseStageSn)
    }

    override fun findVariableGroups(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String
    ): List<VariableGroup> {
        return workflowControllerFacade.releaseStageFacade.findVariableGroups(appName, releaseWorkflowSn, releaseStageSn)
    }

    override fun listReleaseStageInstance(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        paginationQuery: PaginationQuery
    ): PageList<ReleaseStageInstance> {
        return workflowControllerFacade.releaseStageFacade.listReleaseStageInstance(releaseStageSn, paginationQuery)
    }
}