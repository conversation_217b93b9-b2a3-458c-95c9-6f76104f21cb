package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderInputPatchValueHandler
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderInput
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.RevisionVariableGroup
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2023-04-20
 */
@Service
class ChangeOrderInputPatchValueHandlerChain {

    @Autowired
    lateinit var handlers: List<ChangeOrderInputPatchValueHandler>

    fun handle(
        env: Env,
        type: ChangeOrder.Type,
        orchestration: AppOrchestration?,
        profiles: List<RevisionVariableGroup>,
        stableChangeOrderInput: ChangeOrderInput?,
        customPatchValues: Map<String, String>
    ): Map<String, String> {
        val currentHandlers = handlers.filter {
            it.suitableDeployType().contains(type)
        }.sortedBy { it.order() }

        val result = mutableMapOf<String, String>()
        currentHandlers.forEach {
            it.handle(env, orchestration, profiles, stableChangeOrderInput, customPatchValues, result)
        }
        return result
    }
}