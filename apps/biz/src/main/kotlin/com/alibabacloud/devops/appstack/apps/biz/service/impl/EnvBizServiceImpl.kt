package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.annotation.ApiMetricCounter
import com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1.EnvStrategyForm
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.EnvBizService
import com.alibabacloud.devops.appstack.apps.biz.service.EnvLogBizService
import com.alibabacloud.devops.appstack.apps.biz.service.KruiseEnforceService
import com.alibabacloud.devops.appstack.apps.biz.service.facade.EnvActionLogFacade
import com.alibabacloud.devops.appstack.libs.change.controller.spring.boot.starter.service.ChangeControllerFacades
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.constant.CommonCode
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.model.ErrorEntry
import com.alibabacloud.devops.appstack.libs.common.util.LocaleUtil
import com.alibabacloud.devops.appstack.libs.common.util.PageUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkBizWithEntry
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.constant.Default
import com.alibabacloud.devops.appstack.libs.model.`do`.Label
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeableEnv
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.Job
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.K8sIaC
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeDeploymentStrategyV2
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.env.EnvDTO
import com.alibabacloud.devops.appstack.libs.model.`do`.env.RolloutStrategy
import com.alibabacloud.devops.appstack.libs.model.`do`.env.toEnvAtBestEfforts
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.RevisionVariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.event.ActionLog
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.org.event.EnvAudit
import com.alibabacloud.devops.appstack.libs.model.request.CreateEnvRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateEnvRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.EnvQuery
import com.alibabacloud.devops.appstack.libs.model.vo.*
import com.alibabacloud.devops.appstack.libs.model.vo.EnvAction.Action.Delete
import com.alibabacloud.devops.appstack.libs.model.vo.EnvAction.Action.Deploy
import com.alibabacloud.devops.appstack.libs.model.vo.EnvAction.Action.Rollback
import com.alibabacloud.devops.appstack.libs.model.vo.EnvAction.Action.Scale
import com.alibabacloud.devops.appstack.libs.model.vo.EnvAction.Action.values
import com.alibabacloud.devops.appstack.resource.manager.spring.boot.starter.ResourceManagerFacades
import org.springframework.beans.factory.annotation.Autowired

import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2021-12-31
 */
@Service
@Slf4k
open class EnvBizServiceImpl : EnvBizService {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var envActionLogFacade: EnvActionLogFacade

    @Autowired
    lateinit var changeControllerFacades: ChangeControllerFacades

    @Autowired
    lateinit var envLogBizService: EnvLogBizService

    @Autowired
    lateinit var resourceManagerFacades: ResourceManagerFacades

    @Autowired
    lateinit var i18nMessageService: I18nMessageService

    @Autowired
    lateinit var auditLogService: AuditLogService

    @Autowired
    lateinit var kruiseEnforceService: KruiseEnforceService

    @ApiMetricCounter(metricName = "appEnv_operation", methodTag = "create")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_ENV_CREATE, resourceArgIndex = 0),
        ]
    )
    override fun create(appName: String, createEnvRequest: CreateEnvRequest): Env {
        // 环境级别标签校验
        checkEnvLabel(createEnvRequest.labelList)
        checkEnvProfiles(appName, createEnvRequest.profiles)
        if(createEnvRequest.spec == null){
            createEnvRequest.spec = Env.Spec()
        }
        createEnvRequest.spec!!.withoutOam = Env.Spec.WithoutOamState.ON

        val env = coreFacades.envFacade.create(appName, createEnvRequest)
        val engineType = ChangeableEnv.EngineType.Ade2
        changeControllerFacades.changeableEnvFacade.create(
            ChangeableEnv(
                appName = appName,
                envName = env.name,
                engineType = engineType,
                resourceType = createEnvRequest.resourceType
            )
        )
        envLogBizService.recordCreateEnvAction(env)
        envAudit(OrgEventType.ENV_CREATE, env)
        return env
    }

    @ApiMetricCounter(metricName = "appEnv_operation", methodTag = "update")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ENV_SET, resourceArgIndex = 0),
        ]
    )
    override fun update(
        env: Env,
        updateEnvRequest: UpdateEnvRequest
    ): Env {
        val changeableEnv = changeControllerFacades.changeableEnvFacade.find(env.appName, env.name)
        val latestChangeOrder = changeControllerFacades.changeableEnvFacade.findLatestChangeOrder(env.appName, env.name)
        checkExists(changeableEnv) { ErrorCode.AS_ENV_NOT_FOUND }
        // 环境校验
        checkEnvLabel(updateEnvRequest.labelList ?: env.labelList)
        checkEnvProfiles(env.appName, updateEnvRequest.profiles)
        checkDeployGroup(env, updateEnvRequest, latestChangeOrder)
        checkResourceType(changeableEnv, updateEnvRequest)
        checkSpec(env, updateEnvRequest.spec)
        // 此标万不可丢 -_-!
        if(updateEnvRequest.spec != null){
            updateEnvRequest.spec!!.withoutOam = env.spec.withoutOam
        }

        val updatedEnv = coreFacades.envFacade.update(env.appName, env.name, updateEnvRequest)
        changeControllerFacades.changeableEnvFacade.update(
            ChangeableEnv(
                sn = changeableEnv.sn,
                appName = env.appName,
                envName = env.name,
                engineType = changeableEnv.engineType,
                resourceType = updateEnvRequest.resourceType ?: changeableEnv.resourceType
            )
        )
        envLogBizService.recordUpdateEnvAction(updatedEnv, env)
        envAudit(OrgEventType.ENV_MODIFY, env)
        return updatedEnv
    }

    @ApiMetricCounter(metricName = "appEnv_operation", methodTag = "delete")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ENV_DELETE, resourceArgIndex = 0),
        ]
    )
    override fun delete(env: Env, deleteDeployGroup: Boolean): Env {
        val deletedEnv = coreFacades.envFacade.delete(env.appName, env.name)
        changeControllerFacades.changeableEnvFacade.delete(env.appName, env.name)
        if (!env.resourcePoolName.isNullOrBlank() && !env.deployGroupName.isNullOrBlank() && deleteDeployGroup) {
            resourceManagerFacades.resourceDeployGroupFacade.delete(env.resourcePoolName!!, env.deployGroupName!!)
        }
        envLogBizService.recordDeleteEnvAction(deletedEnv)
        envAudit(OrgEventType.ENV_DELETE, env)
        return deletedEnv
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ENV_LOCK_OR_UNLOCK, resourceArgIndex = 0),
        ]
    )
    override fun lock(env: Env, isLock: Boolean): Env {
        val updatedEnv = coreFacades.envFacade.lock(env.appName, env.name, isLock)
        envLogBizService.recordLockEnvAction(updatedEnv, isLock)
        envAudit(if (isLock) OrgEventType.ENV_LOCK else OrgEventType.ENV_UNLOCK, env)
        return updatedEnv
    }

    override fun findAll(appName: String): List<Env> {
        return coreFacades.envFacade.findAll(appName)
    }

    override fun list(appName: String, query: EnvQuery): PageList<Env> {
        if (!query.checkPassed()) {
            throw BizException(ErrorCode.AS_PAGINATION_PARAM_INVALID)
        }
        return coreFacades.envFacadeV2.list(appName, query)
    }

    override fun findPaginatedActionLog(
        appName: String,
        envName: String,
        pageSize: Long,
        current: Long
    ): Pagination<ActionLog> {
        return envActionLogFacade.findPaginatedEnvActionLog(appName, envName, pageSize, current)
    }

    override fun find(appName: String, envName: String): Env {
        val env = coreFacades.envFacade.find(appName, envName)
        checkExists(env) { ErrorCode.AS_ENV_NOT_FOUND }
        return env
    }

    override fun findChangeableEnv(appName: String, envName: String): ChangeableEnvVO? {
        val env = coreFacades.envFacade.find(appName, envName)
        checkExists(env) { ErrorCode.AS_ENV_NOT_FOUND }
        // 初始化rolloutStrategy
        changeControllerFacades.changeableEnvFacade.initEnvSpecRolloutStrategy(appName,envName)
        return fetchChangeableEnvVO(env)
    }

    override fun findAllChangeableEnvs(appName: String): List<ChangeableEnvRecordVO> {
        return decorateToChangeableEnvRecordVO(coreFacades.envFacade.findAllBrief(appName))
    }

    // TODO: 待确认，是不是具有查看应用/环境权限就应该具有查看应用环境下变量组的权限
    @Can(
        accessList = [
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.ORG_APP_MANAGE)
        ]
    )
    override fun listEnvRevisionVariableGroups(appName: String, envName: String): List<RevisionVariableGroup> {
        return coreFacades.envFacade.listEnvRevisionVariableGroups(appName, envName)
    }

    override fun findEnvStrategyForm(appName: String, envName: String): EnvStrategyForm? {
        val changeableEnvVO = fetchChangeableEnvVO(find(appName, envName))
        if (changeableEnvVO.resourceType == ResourceTypeEnum.HOST) {
            return EnvStrategyForm(
                replicasManagement = listOf(Env.Spec.ReplicasManagement.SYSTEM),
                locator = listOf(
                    EnvStrategyForm.LocatorVO(
                        targetReplicas = changeableEnvVO.deployGroup?.claimList?.size,
                        targetReplicasEditable = !(null != changeableEnvVO.deployGroup?.claimList && changeableEnvVO.deployGroup!!.claimList.isNotEmpty()),
                        batchType = listOf(AdeDeploymentStrategyV2.BatchStep.Type.REPLICAS),
                        locator = "*"
                    )
                )
            )
        }

        val locatorList = coreFacades.appOrchestrationFacade.getLocatorList(appName, envName)
        val env = coreFacades.envFacade.find(appName, envName)
//        val ade2WithoutOamEnabled = env?.spec?.withoutOam != null && env.spec.withoutOam == Env.Spec.WithoutOamState.ON
        val ade2WithoutOamEnabled = kruiseEnforceService.shouldEnableKruise(env)
        val locatorStrategyMap = changeableEnvVO.iac?.let {
            it as K8sIaC
            it.locatorStrategyMap
        }
        val agentAlive = envRelatedAgentAlive(env)

        return EnvStrategyForm(
            replicasManagement = listOfNotNull(
                Env.Spec.ReplicasManagement.SYSTEM,
                if (ade2WithoutOamEnabled) Env.Spec.ReplicasManagement.USER else null
            ),
            locator = locatorList.mapNotNull {
                if (null == it.locator || it.kind != "Deployment") {
                    null
                } else {
                    val targetReplicas = locatorStrategyMap?.get(it.locator!!)?.targetReplicas
                    EnvStrategyForm.LocatorVO(
                        targetReplicas = targetReplicas,
                        targetReplicasEditable = null == targetReplicas,
                        batchType = listOfNotNull(
                            AdeDeploymentStrategyV2.BatchStep.Type.REPLICAS,
                            if (ade2WithoutOamEnabled) AdeDeploymentStrategyV2.BatchStep.Type.WEIGHT else null
                        ),
                        locator = it.locator
                    )
                }
            },
            featureGates = EnvStrategyForm.FeatureGates(
                agentAvailable = agentAlive
            )
        )
    }

    private fun envRelatedAgentAlive(env: Env?): Boolean {
        if (env?.deployGroupName == null) {
            return false
        }

        val deployGroup = resourceManagerFacades.resourceDeployGroupFacade.find("default", env.deployGroupName!!)
        val kubernetesClaims = deployGroup.claimList.filter { it.belong2K8s() }
        if (kubernetesClaims.isEmpty()) {
            throw BizException(errorCode = ErrorCode.AS_ENV_NO_K8S_BINDING)
        } else if (kubernetesClaims.size > 1) {
            throw BizException(errorCode = ErrorCode.AS_ENV_MULTIPLE_K8S_BINDING)
        }
        val instanceName = kubernetesClaims.first().instanceName

//        val agentWorkload = if (env.spec.withoutOam == Env.Spec.WithoutOamState.ON) {
        val agentWorkload = if (kruiseEnforceService.shouldEnableKruise(env)) {
            resourceManagerFacades.kruiseRolloutFacade.find(getResourcePath(deployGroup.poolName, instanceName))
        } else {
            resourceManagerFacades.kubeVelaFacade.find(getResourcePath(deployGroup.poolName, instanceName))
        }

        val agentWorkloadExists = agentWorkload != null
        val agentPodReady = (agentWorkload?.status?.readyReplicas ?: 0) > 0
        val agentPodAvailable = (agentWorkload?.status?.availableReplicas ?: 0) > 0

        return agentWorkloadExists && agentPodReady && agentPodAvailable
    }

    private fun findEnvActions(env: Env, latestChangeOrder: ChangeOrder?): List<EnvAction> {
        // 检查环境锁
        if (!env.lockBy.isNullOrEmpty()) {
            return values().map { action ->
                EnvAction(
                    action = action,
                    disabled = true,
                    reason = i18nMessageService.commonResource.getMessage(
                        CommonCode.REFUSE_ENV_ACTION_DUE_TO_LOCKED.value,
                        null,
                        LocaleUtil.getLocale()
                    )
                )
            }
        }

        // 检查部署组
        if (env.deployGroupName.isNullOrEmpty()) {
            return values().map { action ->
                when (action) {
                    Delete -> EnvAction(action = action, disabled = false)
                    else -> EnvAction(
                        action = action,
                        disabled = true,
                        reason = i18nMessageService.commonResource.getMessage(
                            CommonCode.REFUSE_ENV_ACTION_DUE_TO_NO_RESOURCE.value,
                            null,
                            LocaleUtil.getLocale()
                        )
                    )
                }
            }
        }
        // 检查最近一次工单
        if (latestChangeOrder == null) {
            return values().map { action ->
                when (action) {
                    Deploy, Delete -> EnvAction(action = action, disabled = false)
                    Scale, Rollback ->
                        EnvAction(
                            action = action,
                            disabled = true,
                            reason = i18nMessageService.commonResource.getMessage(
                                CommonCode.REFUSE_ENV_ACTION_DUE_TO_NO_BASELINE.value,
                                null,
                                LocaleUtil.getLocale()
                            )
                        )
                }
            }
        } else if (!latestChangeOrder.state.isFinish()) {
            return values().map { action ->
                when (action) {
                    Delete -> EnvAction(action = action, disabled = false)
                    else -> EnvAction(
                        action = action,
                        disabled = true,
                        reason = i18nMessageService.commonResource.getMessage(
                            CommonCode.REFUSE_ENV_ACTION_DUE_TO_HAS_RUNNING_ORDER.value,
                            null,
                            LocaleUtil.getLocale()
                        )
                    )
                }
            }
        }
        // 检查环境迁移标
        if (env.spec.migrateState != null) {
            return values().map { action ->
                when (action) {
                    Scale, Rollback -> EnvAction(
                        action = action,
                        disabled = true,
                        reason = i18nMessageService.commonResource.getMessage(
                            CommonCode.REFUSE_OPERATE_ENV_DUE_TO_MIGRATE.value,
                            null,
                            LocaleUtil.getLocale()
                        )
                    )
                    else -> EnvAction(action = action, disabled = false)
                }
            }
        }

        // 判断replicas是否为用户管理，禁用扩缩
        if (env.spec.replicasManagement == Env.Spec.ReplicasManagement.USER) {
            return values().map { action ->
                when (action) {
                    Scale -> EnvAction(
                        action = action,
                        disabled = true,
                        reason = i18nMessageService.commonResource.getMessage(
                            CommonCode.REFUSE_SCALE_ENV_DUE_TO_REPLICAS_MANAGED_BY_USER.value,
                            null,
                            LocaleUtil.getLocale()
                        )
                    )
                    else -> EnvAction(action = action, disabled = false)
                }
            }
        }

        return values()
            .map { action ->
                when (action) {
                    Scale -> EnvAction(action = action, disabled = false)
                    Rollback -> EnvAction(action = action, disabled = false)
                    Deploy -> EnvAction(action = action, disabled = false)
                    Delete -> EnvAction(action = action, disabled = false)
                }
            }
    }

    @Deprecated("该函数依赖的查询过于繁琐，会导致 sql 压力重，不要再用")
    private fun calculateEnvPseudoState(changeOrder: ChangeOrder?, envName: String): EnvState {
        return changeOrder?.let {
            when (changeOrder.jobs.last { job -> job.envName == envName }.state) {
                Job.State.INIT,
                Job.State.PREPARING,
                Job.State.RUNNING,
                Job.State.SUSPENDED,
                Job.State.SUSPENDING,
                Job.State.STOPPING -> EnvState.DEPLOYING
                Job.State.SUCCESS -> EnvState.RUNNING
                Job.State.FAILED, Job.State.CANCELED -> EnvState.ERROR
            }
        } ?: EnvState.NEW
    }

    private fun calculateEnvPseudoState(job: JobBriefRecordVO?): EnvState {
        if (job == null) {
            return EnvState.NEW
        }
        return when (job.state) {
            Job.State.SUCCESS -> EnvState.RUNNING
            Job.State.FAILED, Job.State.CANCELED -> EnvState.ERROR
            else -> EnvState.DEPLOYING
        }
    }

    private fun decorateToChangeableEnvRecordVO(envList: List<EnvDTO>): List<ChangeableEnvRecordVO> {
        // 这里仍然是循环查询，但只限于每个 env 下钻一个 job 及其对应的 changeOrder（changeOrder 主要用于追溯发布版本）
        // 环境少的情况下也就查十几次，尚可接受
        // TODO 可能的话以后做数据库冗余吧，为了一个部署单版本号从 job 上溯 changeOrder 不合算，没必要严格给 mysql 这种 join 稀烂的 rds 遵循 3nf/bcnf
        return envList.map { env ->
            val latestJob =
                changeControllerFacades.changeableEnvFacade.findLatestJobBrief(appName = env.appName, envName = env.name)
            val envPseudoState = calculateEnvPseudoState(latestJob)
            val deployGroupDTO = if (env.deployGroupName != null) {
                coreFacades.deployGroupFacade.findDTO("default", env.deployGroupName!!)
            } else {
                null
            }
            val latestMinimalChangeOrderInfo = if (latestJob != null) {
                ChangeOrder(
                    creator = latestJob.creator,
                    gmtCreate = latestJob.gmtCreate,
                    name = latestJob.changeOrderName,
                    appName = env.appName,
                    type = latestJob.changeOrderType,
                    version = latestJob.changeOrderVersion,
                    tag = "",
                    sn = latestJob.changeOrderSn
                )
            } else {
                null
            }
            ChangeableEnvRecordVO(
                env = env.toEnvAtBestEfforts(),
                envPseudoState = envPseudoState,
                version = latestJob?.changeOrderVersion,
                deployGroup = deployGroupDTO,
                latestChangeOrder = latestMinimalChangeOrderInfo
            )
        }
    }

    private fun fetchChangeableEnvVO(env: Env): ChangeableEnvVO {
        val changeableEnv = changeControllerFacades.changeableEnvFacade.find(env.appName, env.name)
        val latestChangeOrder = changeControllerFacades.changeableEnvFacade.findLatestChangeOrder(env.appName, env.name)
        val stableChangeOrder = changeControllerFacades.changeableEnvFacade.findStableChangeOrder(env.appName, env.name)
        checkExists(changeableEnv) { ErrorCode.AS_ENV_NOT_FOUND }
        latestChangeOrder?.let {
            val filter = it.jobs.filter { job: Job -> job.nextSn == null }
            it.jobs = filter
        }
        val revisionVariableGroup = listEnvRevisionVariableGroups(env.appName, env.name)
        // find orchestration by latest changeOrder tag
        val appOrchestration = latestChangeOrder?.let {
            coreFacades.appOrchestrationFacade.find(
                appName = env.appName,
                sn = "${env.appName}@${changeableEnv.resourceType.name}",
                tagName = it.tag,
                sha = null,
            )
        }
        val envActions = findEnvActions(env, latestChangeOrder)
        val envPseudoState = calculateEnvPseudoState(latestChangeOrder, env.name)
        val deployGroupVO = if (!env.resourcePoolName.isNullOrBlank() && !env.deployGroupName.isNullOrBlank()) {
            val deployGroup =
                resourceManagerFacades.resourceDeployGroupFacade.find(env.resourcePoolName!!, env.deployGroupName!!)
            val claimList = deployGroup.claimList.map {
                val resourceInstanceVO =
                    resourceManagerFacades.resourceInstanceFacade.findInstance(deployGroup.poolName, it.instanceName)
                it.toVO(resourceInstanceVO)
            }
            deployGroup.toVO(emptyList(), claimList)
        } else {
            null
        }
        return ChangeableEnvVO(
            env = env,
            changeableEnv = changeableEnv,
            profiles = revisionVariableGroup,
            appOrchestration = appOrchestration,
            envActions = envActions,
            envPseudoState = envPseudoState,
            deployGroup = deployGroupVO,
            latestChangeOrder = latestChangeOrder,
            stableChangeOrder = stableChangeOrder,
        )
    }

    private fun envAudit(type: OrgEventType, env: Env) {
        val audit = EnvAudit(
            target = AuditTarget(id = env.appName, name = env.appName),
            envName = env.name
        )
        auditLogService.log(type, audit)
    }

    private fun checkEnvLabel(labelList: List<Label>?) {
        if (labelList.isNullOrEmpty()) {
            throw BizException(errorCode = ErrorCode.AS_ENV_MISS_ENV_TYPE_LABEL)
        }
        if (labelList.size > 1) {
            throw BizException(errorCode = ErrorCode.AS_ENV_CONTAINS_MUTIPLE_ENV_TYPE_LABELS)
        }
        val orgLabels = coreFacades.labelFacade.findAll(Default.NAMESPACE, null, null)
        if ((labelList - orgLabels).size > 0) {
            throw BizException(errorCode = ErrorCode.AS_LABEL_NOT_FOUND)
        }
    }

    private fun checkEnvProfiles(appName: String, profiles: List<VariableGroup>?) {
        if (!profiles.isNullOrEmpty()) {
            val distinctSize = profiles.map { Pair(it.type, it.name) }.toSet().size
            checkBizWithEntry(profiles.size == distinctSize) { ErrorEntry(code = ErrorCode.AS_ENV_CONTAINS_DUPLICATED_VARIABLE_GROUPS) }

            profiles.forEach {
                when (it.type) {
                    VariableGroup.Type.APP -> {
                        val profile = coreFacades.variableFacade.findProfileVO(appName, it.name)
                        checkExists(profile) { ErrorCode.AS_VAR_PROFILE_NOT_FOUND }
                    }
                    VariableGroup.Type.GLOBAL -> {
                        val profile = coreFacades.globalVarFacade.find(it.name)
                        checkExists(profile) { ErrorCode.AS_GLOBAL_VAR_NOT_FOUND }
                    }
                }
            }
        }
    }

    // 有进行中的部署单，不允许调整
    private fun checkDeployGroup(env: Env, updateEnvRequest: UpdateEnvRequest, latestChangeOrder: ChangeOrder?) {
        if (updateEnvRequest.deployGroupName.isNullOrBlank() || env.deployGroupName.isNullOrBlank()) {
            return
        }
        if (env.deployGroupName != updateEnvRequest.deployGroupName && latestChangeOrder != null && !latestChangeOrder.state.isFinish()) {
            throw BizException(errorCode = ErrorCode.AS_ENV_DEPLOY_GROUP_CANNOT_CHANGE_DUE_TO_RUNNING_CHANGE_ORDER)
        }
        updateEnvRequest.deployGroupName?.let {
            coreFacades.deployGroupFacade.find(Default.POOLNAME, it)
        }
    }

    private fun checkResourceType(changeableEnv: ChangeableEnv, updateEnvRequest: UpdateEnvRequest) {
        if (changeableEnv.resourceType == ResourceTypeEnum.BePending || updateEnvRequest.resourceType == null) {
            return
        }

        if (updateEnvRequest.resourceType != changeableEnv.resourceType) {
            throw BizException(errorCode = ErrorCode.AS_ENV_RESOURCE_TYPE_CANNOT_CHANGE)
        }
    }

    private fun checkSpec(env: Env, spec: Env.Spec?) {
        val withoutOamOff = !kruiseEnforceService.shouldEnableKruise(env)
        if (spec?.rolloutStrategy != null) {
            if (spec.rolloutStrategy!!.any { it.deployType == RolloutStrategy.DeployType.Batch && null != it.batchSteps && it.batchSteps!!.any { it.type == RolloutStrategy.BatchStep.Type.WEIGHT } }) {
                if (withoutOamOff) {
                    throw BizException(ErrorCode.AS_STRATEGY_BATCH_STEP_TYPE_NOT_SUPPORTED)
                }
            }
            if (spec.rolloutStrategy!!.any { it.deployType == RolloutStrategy.DeployType.Batch && (it.batchSteps.isNullOrEmpty() || it.batches == null) }) {
                throw BizException(ErrorCode.AS_ENV_STRATEGY_BATCH_INVALID)
            }
        }
        // OAM环境不允许非管控
        if (withoutOamOff && spec?.replicasManagement == Env.Spec.ReplicasManagement.USER) {
            throw BizException(ErrorCode.AS_ENV_OAM_STRATEGY_CHANGE_NOT_ALLOWED)
        }
    }

    private fun getResourcePath(poolName: String, instanceName: String): String {
        return "RI:$poolName:$instanceName"
    }

}