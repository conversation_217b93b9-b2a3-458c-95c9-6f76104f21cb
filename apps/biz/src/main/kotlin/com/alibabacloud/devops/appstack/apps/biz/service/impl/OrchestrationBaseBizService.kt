package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.libs.common.util.LocaleUtil
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.InvalidChangeItem
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Component
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.KubernetesCheckOutputItem


/**
 * <AUTHOR> <EMAIL>
 * @version : OrchestrationBaseBizService, v0.1
 * @date : 2023-07-25 23:21
 **/
open class OrchestrationBaseBizService(
    open val i18nMessageService: I18nMessageService,
) {

    fun checkInvalid(
        checkOutputItems: List<KubernetesCheckOutputItem>,
        componentList: List<Component>,
    ): List<InvalidChangeItem> {
        val ret = mutableListOf<InvalidChangeItem>()
        ret.addAll(checkOutputItems.map { it.invalidItems }.flatten())

        // 修正kind，前端无法判断出实际的kind类型。
        componentList.forEach { component ->
            checkOutputItems.find { it.componentName == component.name }?.resources?.firstOrNull()?.kind?.let {
                component.kind = it
            }
        }

        // 校验同个kind下面资源是不是重名（仅限已经校验合格的资源）
        val componentNameToKubernetesNameList =
            checkOutputItems.filter { it.invalidItems.isEmpty() && it.resources.isNotEmpty() }
                .groupBy { it.componentName }
                .mapValues { it.value.map { it.metadataName } }
        ret.addAll(
            getSameKindMetaNameErrors(
                componentList,
                componentNameToKubernetesNameList
            )
        )

        // component同名校验，前端已存在校验，冗余设计
        val duplicateNameMap =
            componentList.groupBy { it.name }.filter { it.value.size > 1 }
        if (duplicateNameMap.isNotEmpty()) {
            ret.addAll(
                duplicateNameMap.entries.map { (name, list) ->
                    InvalidChangeItem(
                        itemKey = name,
                        itemType = list.first().kind,
                        message = i18nMessageService.commonResource.getMessage(
                            "orchestration.verify.duplicate.component.name",
                            arrayOf(name),
                            LocaleUtil.getLocale()
                        )
                    )
                }
            )
        }

        // 多deployment并行检查，暂且不支持在同一个stage内有多个deployment
        val multiDeploymentInSameStage = componentList.groupBy { it.priority }
            .mapValues { it.value.filter { it.kind == "Deployment" } }.filter { it.value.size > 1 }
        if (multiDeploymentInSameStage.isNotEmpty()) {
            ret.addAll(
                multiDeploymentInSameStage.entries.map { (_, list) ->
                    InvalidChangeItem(
                        itemKey = list.last().name,
                        itemType = list.last().kind,
                        message = i18nMessageService.commonResource.getMessage(
                            "orchestration.verify.multi.deployment.in.same.stage",
                            null,
                            LocaleUtil.getLocale()
                        )
                    )
                }
            )
        }
        return ret
    }

    private fun getSameKindMetaNameErrors(
        componentList: List<Component>,
        componentNameToKubernetesNameList: Map<String, List<String?>>,
    ): List<InvalidChangeItem> {
        val triples = componentList.flatMap { component ->
            componentNameToKubernetesNameList[component.name]?.map {
                Triple(component.kind, component.name, it)
            } ?: emptyList()
        }

        val mapValues = triples.groupBy {
            it.first
        }.mapValues { entry ->
            entry.value.fold(emptyList<Pair<String, String?>>()) { acc, triple ->
                acc + Pair(triple.second, triple.third)
            }
        }
        return mapValues.flatMap { entry ->
            val kind = entry.key
            val componentToMetadataName = entry.value
            val nullMetadataName =
                componentToMetadataName.filter { it.second == null }.map { pair: Pair<String, String?> ->
                    InvalidChangeItem(
                        itemKey = pair.first,
                        itemType = kind,
                        message = i18nMessageService.commonResource.getMessage(
                            "orchestration.verfiy.empty.metadata.name",
                            arrayOf(kind),
                            LocaleUtil.getLocale()
                        )
                    )
                }
            val pairs = componentToMetadataName.filter { it.second != null } as List<Pair<String, String>>
            val metaDataNames = pairs.groupBy { it.second }.mapValues {
                it.value.map { pair -> pair.first }
            }
            val duplicateMetadataName = metaDataNames.filter { it.value.size > 1 }.map {
                InvalidChangeItem(
                    itemKey = it.value.last(),
                    itemType = kind,
                    message = i18nMessageService.commonResource.getMessage(
                        "orchestration.verify.duplicate.name.for.same.kind",
                        arrayOf(kind, it.value),
                        LocaleUtil.getLocale()
                    )
                )
            }
            return@flatMap nullMetadataName + duplicateMetadataName
        }
    }


    /*
        旧代码保留，占位符取值范围限制（旧功能移除）

        private fun checkPlaceholderValue(placeholderList: List<Placeholder>) {
            val errorList = placeholderList.filter {
                !it.rule.isNullOrBlank()
                        && it.valueSource == Placeholder.ValueSource.CONSTANT
                        && listOf("number", "float").contains(it.type)
            }.map {
                val schema = JSONSchema.parse(it.rule!!)
                val errors = schema.validateBasic(it.value).errors ?: return@map null
                "placeholder ${it.name}: ${errors.joinToString()}."
            }.filterNotNull()

            if (errorList.isNotEmpty()) {
                throw BizException(
                    ErrorEntry(
                        code = ErrorCode.AS_ORC_PLACEHOLDER_INVALID,
                        advice = errorList.joinToString(),
                    )
                )
            }
        }

        private fun generateJsonSchema(placeholderList: List<Placeholder>) {
            //todo 此处前端占位符数据要转成jsonSchema的String,等超过3个开工厂
            placeholderList.forEach {
                val rule = it.rule
                val type = it.type
                if (!rule.isNullOrBlank() && listOf("number", "float").contains(type)) {
                    val ruleMap: Map<String, Any> = jacksonObjectMapper().readValue(rule)
                    val filterRuleMap = ruleMap.filter {
                        listOf("minimum", "maximum").contains(it.key)
                    }.map {
                        it.key to (castToFloat(it.value) ?: 0.0F)
                    }.toMap().toMutableMap<String, Any>()
                    if (filterRuleMap.isNotEmpty()) {
                        filterRuleMap["\$schema"] = "http://json-schema.org/draft/2019-09/schema"
                        filterRuleMap["type"] = "number"
                        it.rule = jacksonObjectMapper().writeValueAsString(filterRuleMap)
                        return
                    }
                }
                it.rule = null
            }
        }
        */
}