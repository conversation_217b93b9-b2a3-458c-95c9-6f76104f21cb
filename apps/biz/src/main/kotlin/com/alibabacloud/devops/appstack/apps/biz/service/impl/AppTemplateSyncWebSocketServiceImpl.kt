package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.config.WebSocketConfig
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateSyncWebSocketService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.model.`do`.apptemplate.AppTemplateSyncProgress
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.springframework.stereotype.Service
import org.springframework.web.socket.CloseStatus
import org.springframework.web.socket.TextMessage
import org.springframework.web.socket.WebSocketSession
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentSkipListSet

/**
 * <AUTHOR>
 * @date 2024-02-17
 */
class AppTemplateSyncWebSocketServiceImpl : AppTemplateSyncWebSocketService() {

    companion object {
        val transactionId2SessionIdsMap: ConcurrentHashMap<String, ConcurrentSkipListSet<String>> =
            ConcurrentHashMap()
        val sessionMap: ConcurrentHashMap<String, WebSocketSession> = ConcurrentHashMap()
    }

    override fun syncProgress(transactionId: String, progress: AppTemplateSyncProgress) {
        val sessionIdSet = transactionId2SessionIdsMap[transactionId]
        if (CollectionUtils.isNotEmpty(sessionIdSet)) {
            sessionIdSet!!.forEach {
                try {
                    sessionMap[it]?.sendMessage(
                        TextMessage(
                            jacksonObjectMapper().writeValueAsString(progress)
                        )
                    )
                    logger.info("Notify Sync Finished for transactionId[$transactionId] sessionId[$it]")
                } catch (exception: Exception) {
                    val session = sessionMap.remove(it)
                    logger.info("Session[${session?.id}] has been closed, Remove it")
                    transactionId2SessionIdsMap.get(transactionId)?.remove(it)
                    try {
                        session?.close()
                    } catch (exception: Exception) {

                    }
                }
            }
        }
    }


    override fun afterConnectionEstablished(session: WebSocketSession) {
        val transactionId = getTransactionId(session)
        transactionId?.let {
            sessionMap[session.id] = session
            transactionId2SessionIdsMap.putIfAbsent(it, ConcurrentSkipListSet(setOf(session.id)))?.add(session.id)
            logger.info("WebSocket Client Connected for TransactionId[$transactionId] Session[${session.id}]")
        }
    }

    override fun afterConnectionClosed(session: WebSocketSession, status: CloseStatus) {
        val transactionId = getTransactionId(session)
        sessionMap.remove(session.id)
        transactionId?.let {
            transactionId2SessionIdsMap.get(it)?.remove(session.id)
            logger.info("WebSocket Client DisConnected for TransactionId[$transactionId] Session[${session.id}] StatusCode[${status.code}]")
        }
    }

    private fun getTransactionId(session: WebSocketSession): String? {
        val transactionId = session.attributes.get(WebSocketConfig.TRANSACTION_ID)
        transactionId?.let {
            if (it is String) {
                return it
            }
        }
        return null
    }
}

