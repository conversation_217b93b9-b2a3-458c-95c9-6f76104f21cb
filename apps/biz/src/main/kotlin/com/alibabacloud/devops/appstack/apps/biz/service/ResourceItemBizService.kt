package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.vo.ResourceItemRecord

/**
 * @author: su<PERSON>@alibaba-inc.com
 * @date: 2023-04-17 14:18
 * @version: ResourceItembizService, v0.1
 **/
interface ResourceItemBizService {
    fun findPaginated(
        poolName: String,
        instanceName: String,
        search: String? = null,
        current: Long,
        pageSize: Long
    ): Pagination<ResourceItemRecord>

    fun updateHostSn(): <PERSON><PERSON><PERSON>
}