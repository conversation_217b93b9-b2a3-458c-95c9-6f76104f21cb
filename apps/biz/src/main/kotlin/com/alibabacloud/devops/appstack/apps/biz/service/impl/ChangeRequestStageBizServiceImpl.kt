package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.model.vo.ReleaseStageInstanceIntegratedMetadataVO
import com.alibabacloud.devops.appstack.apps.biz.model.vo.ReleaseStageInstanceVO
import com.alibabacloud.devops.appstack.apps.biz.model.vo.convert2VO
import com.alibabacloud.devops.appstack.apps.biz.service.AppArtifactRepoBizService
import com.alibabacloud.devops.appstack.apps.biz.service.AppCodeRepoBizService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestStageBizService
import com.alibabacloud.devops.appstack.apps.biz.service.StageBaseService
import com.alibabacloud.devops.appstack.apps.biz.service.*
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil.jacksonObjectMapper
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStage
import com.alibabacloud.devops.appstack.libs.model.constant.SystemVariable
import com.alibabacloud.devops.appstack.libs.model.constant.WorkflowEnvVariable
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ExecutePipelineResult
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageInstance
import com.alibabacloud.devops.appstack.libs.model.constant.FlowPermission
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.PaginationQuery
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AppReleaseStagePipelineAudit
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.org.event.ReleaseStageAudit
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.*
import com.alibabacloud.devops.appstack.libs.model.vo.ReleaseIntegratedMetadata
import com.alibabacloud.devops.iam.exception.IamForbiddenException
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.readValue
import org.jetbrains.annotations.TestOnly
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2023-11-08
 */
@Service
open class ChangeRequestStageBizServiceImpl : ChangeRequestStageBizService {

    @Autowired
    lateinit var stageBaseService: StageBaseService

    @Autowired
    lateinit var appCodeRepoBizService: AppCodeRepoBizService

    @Autowired
    lateinit var appArtifactRepoBizService: AppArtifactRepoBizService

    @Autowired
    lateinit var serviceConnectionBizService: ServiceConnectionBizService

    @Autowired
    lateinit var auditLogService: AuditLogService

    @Autowired
    lateinit var releaseWorkflowBaseService: WorkflowBaseService

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var flowPermissionHandlerChain: FlowPermissionHandlerChain

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun create(
        appName: String, releaseWorkflowSn: String, request: CreateReleaseStageBizRequest
    ): ReleaseStage {
        val releaseWorkflow = releaseWorkflowBaseService.find(appName = appName, sn = releaseWorkflowSn)
        val releaseStage= stageBaseService.create(appName, releaseWorkflowSn, request)
        val audit = ReleaseStageAudit(
            target = AuditTarget(id = releaseWorkflowSn, name = releaseWorkflow.name ?: releaseWorkflowSn),
            appName = appName,
            releaseStage = releaseStage.name
        )
        auditLogService.log(OrgEventType.RELEASE_WORKFLOW_STAGE_CREATE, audit)
        return releaseStage
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun findAll(appName: String, releaseWorkflowSn: String): List<ReleaseStage> {
        return stageBaseService.findAll(appName, releaseWorkflowSn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun find(appName: String, releaseWorkflowSn: String, releaseStageSn: String): ReleaseStage {
        return stageBaseService.find(appName, releaseWorkflowSn, releaseStageSn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun update(
        appName: String, releaseWorkflowSn: String, releaseStageSn: String, request: UpdateReleaseStageRequest
    ): ReleaseStage {
        val releaseWorkflow = releaseWorkflowBaseService.find(appName = appName, sn = releaseWorkflowSn)
        val previous = find(appName, releaseWorkflowSn, releaseStageSn)
        val releaseStage = stageBaseService.update(appName, releaseWorkflowSn, releaseStageSn, request)
        val audit = ReleaseStageAudit(
            target = AuditTarget(id = releaseWorkflowSn, name = releaseWorkflow.name ?: releaseWorkflowSn),
            appName = appName,
            releaseStage = previous.name
        )
        auditLogService.log(OrgEventType.RELEASE_WORKFLOW_STAGE_MODIFY, audit)
        return releaseStage
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun delete(appName: String, releaseWorkflowSn: String, releaseStageSn: String) {
        val releaseWorkflow = releaseWorkflowBaseService.find(appName = appName, sn = releaseWorkflowSn)
        val releaseStage = stageBaseService.delete(appName, releaseWorkflowSn, releaseStageSn)
        val audit = ReleaseStageAudit(
            target = AuditTarget(id = releaseWorkflowSn, name = releaseWorkflow.name ?: releaseWorkflowSn),
            appName = appName,
            releaseStage = releaseStage.name
        )
        auditLogService.log(OrgEventType.RELEASE_WORKFLOW_STAGE_DELETE, audit)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun updatePipeline(
        appName: String, releaseWorkflowSn: String, releaseStageSn: String, request: UpdateReleaseStagePipelineRequest
    ): ReleaseStage {
        val releaseWorkflow = releaseWorkflowBaseService.find(appName = appName, sn = releaseWorkflowSn)
        val releaseStage = stageBaseService.updatePipeline(appName, releaseWorkflowSn, releaseStageSn, request)
        val audit = ReleaseStageAudit(
            target = AuditTarget(id = releaseWorkflowSn, name = releaseWorkflow.name ?: releaseWorkflowSn),
            appName = appName,
            releaseStage = releaseStage.name
        )
        auditLogService.log(OrgEventType.RELEASE_WORKFLOW_STAGE_PIPELINE_MODIFY, audit)
        return releaseStage
    }

    override fun getPipelineParams(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String
    ): MutableMap<String, Any> {
        val actualParams: MutableMap<String, Any> = mutableMapOf()
        actualParams.put(SystemVariable.APPSTACK_APP_NAME, appName)
        // sources related
        val appCodeRepos = appCodeRepoBizService.findAll(appName)
        val codeIdentityMap =
            appCodeRepos.associateBy({ "${WorkflowEnvVariable.APPSTACK_APP_SOURCE_PREFIX}${it.identifier}" },
                { handlePipelineEnv(jacksonObjectMapper().writeValueAsString(it.asPipelineEnvMap())) })
        actualParams.putAll(codeIdentityMap)

        val appSourceMap = appCodeRepos.associateBy({ it.identifier },
            {
                // 从之前的map中获取，不重复计算
                val value = codeIdentityMap["${WorkflowEnvVariable.APPSTACK_APP_SOURCE_PREFIX}${it.identifier}"]
                jacksonObjectMapper().readValue(value, Map::class.java)
            }).toMutableMap()
        val appArtifactsMap = appArtifactRepoBizService.findAll(appName).associateBy({ it.identifier }, {
            it.repoContext.asPipelineEnvMap()
        })
        appSourceMap.putAll(appArtifactsMap)
        actualParams[WorkflowEnvVariable.APPSTACK_APP_SOURCES] = appSourceMap.toMap()
        return actualParams
    }


    override fun executePipeline(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        request: ExecuteReleaseStagePipelineRequest
    ): ExecutePipelineResult {
        val app = coreFacades.appFacade.find(appName)
        checkExists(app) { ErrorCode.AS_APP_NOT_FOUND }
        val releaseWorkflow = releaseWorkflowBaseService.findBrief(appName = appName, sn = releaseWorkflowSn)
        val releaseStage = stageBaseService.findBrief(appName, releaseWorkflowSn, releaseStageSn)
        // permission check
        if(!flowPermissionHandlerChain.handle(FlowPermission.PERMISSION_PIPELINE_RUN, app, releaseWorkflow, releaseStage)){
            throw IamForbiddenException("权限检查失败 appName: $appName, workflowSn: ${releaseWorkflow.sn} stageSn: ${releaseStage.sn}")
        }
        val actualParams = request.params
        val systemParams = getPipelineParams(appName, releaseWorkflowSn, releaseStageSn)
        actualParams.putAll(systemParams)
        val executeResult = stageBaseService.executePipeline(appName, releaseWorkflowSn, releaseStageSn, request)
        val audit = ReleaseStageAudit(
            target = AuditTarget(id = releaseWorkflowSn, name = releaseWorkflow.name ?: releaseWorkflowSn),
            appName = appName,
            releaseStage = releaseStage.name
        )
        auditLogService.log(OrgEventType.RELEASE_WORKFLOW_STAGE_PIPELINE_RUN, audit)
        val pipelineAudit = AppReleaseStagePipelineAudit(
            target = AuditTarget(id = releaseWorkflowSn, name = releaseWorkflowSn),
            appName = appName,
            releaseWorkflowSn = releaseWorkflowSn,
            releaseStageSn = releaseStage.sn!!,
            pipelineId = executeResult.pipelineId.toString(),
            runNumber = executeResult.pipelineRunId.toString(),
            state = ReleaseStageInstance.State.RUNNING.name,
        )
        auditLogService.log(OrgEventType.RELEASE_WORKFLOW_STAGE_PIPELINE_HOOK, pipelineAudit)
        return executeResult
    }

    @TestOnly
    fun exposeHandlePipelineEnv(value: String): String {
        return handlePipelineEnv(value)
    }

    // 处理环境变量
    private fun handlePipelineEnv(value: String): String {
        val obj: JsonNode = jacksonObjectMapper().readValue(value)
        val transferServiceConnectionID = { id: String ->
            // 空字符串不处理
            if (id.isEmpty()) {
                ""
            } else {
                val connections = serviceConnectionBizService.findByIds(listOf(id))
                val uuid = connections.firstOrNull { it.id == id.toLongOrNull() }?.uuid ?: ""
                uuid
            }
        }
        JacksonUtil.replace(obj, "serviceConnectionID", transferServiceConnectionID)
        return jacksonObjectMapper().writeValueAsString(obj)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun findReleaseStageCrMetadata(
        appName: String, releaseWorkflowSn: String, releaseStageSn: String, request: QueryReleaseStageCrMetadataRequest
    ): ReleaseIntegratedMetadata? {
        return stageBaseService.findReleaseStageCrMetadata(appName, releaseWorkflowSn, releaseStageSn, request.buildNum)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun findPreviousMetadata(
        appName: String, releaseWorkflowSn: String, releaseStageSn: String
    ): ReleaseIntegratedMetadata? {
        return stageBaseService.findPreviousMetadata(appName, releaseWorkflowSn, releaseStageSn)
    }

    override fun findStageEnvList(releaseStageSn: String): List<String> {
        return stageBaseService.findStageEnvList(releaseStageSn)
    }

    @Can(
        accessList = [Access(action = Action.ORG_APP_MANAGE), Access(action = Action.ORG_APP_VIEW), Access(
            action = Action.APP_VIEW,
            resourceArgIndex = 0
        )]
    )
    override fun findVariableGroups(
        appName: String, releaseWorkflowSn: String, releaseStageSn: String
    ): List<VariableGroup> {
        return stageBaseService.findVariableGroups(appName, releaseWorkflowSn, releaseStageSn)
    }

    @Can(
        accessList = [Access(action = Action.ORG_APP_MANAGE), Access(action = Action.ORG_APP_VIEW), Access(
            action = Action.APP_VIEW,
            resourceArgIndex = 0
        )]
    )
    override fun listReleaseStageInstances(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        paginationQuery: PaginationQuery
    ): PageList<ReleaseStageInstanceVO> {
        if(!paginationQuery.checkPassed()){
            throw BizException(ErrorCode.AS_PAGINATION_PARAM_INVALID)
        }
        val pagination = stageBaseService.listReleaseStageInstance(
            appName,
            releaseWorkflowSn,
            releaseStageSn,
            paginationQuery
        )
        return PageList(
            nextToken = pagination.nextToken,
            total = pagination.total,
            current = pagination.current,
            perPage = pagination.perPage,
            pages = pagination.pages,
            data = pagination.data.map { it.convert2VO() }
        )
    }

    @Can(
        accessList = [Access(action = Action.ORG_APP_MANAGE), Access(action = Action.ORG_APP_VIEW), Access(
            action = Action.APP_VIEW,
            resourceArgIndex = 0
        )]
    )
    override fun listReleaseStageInstanceIntegratedMetadata(
        appName: String, releaseWorkflowSn: String, releaseStageSn: String, number: Long
    ): List<ReleaseStageInstanceIntegratedMetadataVO> {
        val metadata = stageBaseService.findReleaseStageCrMetadata(appName, releaseWorkflowSn, releaseStageSn, number)
        val resultList = mutableListOf<ReleaseStageInstanceIntegratedMetadataVO>()
        metadata?.let {
            resultList.add(it.convert2VO())
        }
        return resultList
    }
}