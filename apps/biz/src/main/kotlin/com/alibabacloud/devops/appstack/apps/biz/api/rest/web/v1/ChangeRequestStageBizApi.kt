package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestStageBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ExecutePipelineResult
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStage
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.*
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.ReleaseIntegratedMetadata
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @date 2022-06-28
 */
@Tag(name = "ReleaseStage", description = "研发阶段相关 API")
@RestController
@RequestMapping("/api/v1")
open class ChangeRequestStageBizApi {

    @Autowired
    lateinit var changeRequestStageBizService: ChangeRequestStageBizService

    @Operation(summary = "创建研发阶段")
    @PostMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages")
    fun createReleaseStage(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @RequestBody request: CreateReleaseStageBizRequest
    ): Response<ReleaseStage> {
        return Response.success(changeRequestStageBizService.create(appName, releaseWorkflowSn, request))
    }

    @Operation(summary = "查找研发流程下所有阶段")
    @GetMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages")
    fun findAllReleaseStage(@PathVariable appName: String, @PathVariable releaseWorkflowSn: String): Response<List<ReleaseStage>> {
        return Response.success(changeRequestStageBizService.findAll(appName, releaseWorkflowSn))
    }


    @Operation(summary = "查询阶段详情")
    @GetMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}")
    fun findReleaseStage(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String
    ): Response<ReleaseStage> {
        return Response.success(changeRequestStageBizService.find(appName, releaseWorkflowSn, releaseStageSn))
    }

    @Operation(summary = "编辑阶段")
    @PutMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}")
    fun updateReleaseStage(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @RequestBody request: UpdateReleaseStageRequest
    ): Response<ReleaseStage> {
        return Response.success(changeRequestStageBizService.update(appName, releaseWorkflowSn, releaseStageSn, request))
    }

    @Operation(summary = "删除阶段")
    @DeleteMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}")
    fun deleteReleaseStage(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String
    ): Response<Unit> {
        changeRequestStageBizService.delete(appName, releaseWorkflowSn, releaseStageSn)
        return Response.success()
    }

    @Operation(summary = "更新阶段的流水线")
    @PutMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}:pipeline")
    fun updatePipeline(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @RequestBody request: UpdateReleaseStagePipelineRequest
    ): Response<ReleaseStage> {
        return Response.success(
            changeRequestStageBizService.updatePipeline(
                appName,
                releaseWorkflowSn,
                releaseStageSn,
                request
            )
        )
    }

    @Operation(summary = "运行阶段的流水线")
    @PostMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}:execute_pipeline")
    fun executePipeline(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @RequestBody request: ExecuteReleaseStagePipelineRequest
    ): Response<ExecutePipelineResult> {
        return Response.success(
            changeRequestStageBizService.executePipeline(
                appName,
                releaseWorkflowSn,
                releaseStageSn,
                request
            )
        )
    }

    @Operation(summary = "阶段发布集成信息")
    @PostMapping("/apps/{appName}/releaseWorkflow/{releaseWorkflowSn}/releaseStage/{releaseStageSn}:find_release_integrated_metadata")
    fun findReleaseIntegratedMetadata(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @RequestBody request: QueryReleaseStageCrMetadataRequest
    ): Response<ReleaseIntegratedMetadata> {
        return Response.success(
            changeRequestStageBizService.findReleaseStageCrMetadata(
                appName,
                releaseWorkflowSn,
                releaseStageSn,
                request
            )
        )
    }

    @Operation(summary = "前序阶段发布集成信息")
    @PostMapping("/apps/{appName}/releaseWorkflow/{releaseWorkflowSn}/releaseStage/{releaseStageSn}/findPreviousMetadata")
    fun findPreviousMetadata(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String
    ): Response<ReleaseIntegratedMetadata> {
        return Response.success(
            changeRequestStageBizService.findPreviousMetadata(
                appName,
                releaseWorkflowSn,
                releaseStageSn
            )
        )
    }

    @Operation(summary = "查询阶段部署环境详情")
    @GetMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}:findStageEnvList")
    fun findStageEnvList(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String
    ): Response<List<String>> {
        return Response.success(changeRequestStageBizService.findStageEnvList(releaseStageSn))
    }

    @Operation(summary = "查询阶段使用的变量组")
    @GetMapping("/apps/{appName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}:variableGroups")
    fun findVariableGroups(
        @PathVariable appName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String
    ): Response<List<VariableGroup>> {
        return Response.success(changeRequestStageBizService.findVariableGroups(appName, releaseWorkflowSn, releaseStageSn))
    }
}