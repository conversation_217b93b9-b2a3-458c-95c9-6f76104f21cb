package com.alibabacloud.devops.appstack.apps.biz.config

import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Configuration

@Configuration
open class FlowExecutionConfiguration {

    @Value("\${service.flow-execution.url}")
    lateinit var executionComponentUrl: String

    @Value("\${service.flow-execution.callback-url}")
    lateinit var callbackURL: String

    @Value("\${service.flow-execution.secret}")
    lateinit var secret: String

    @Value("\${service.flow-execution.execStep:exec-shell}")
    lateinit var execStep: String
}