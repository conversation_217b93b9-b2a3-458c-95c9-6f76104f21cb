package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.ProjexChangeRequestBizService
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.response.cr.ProjexWorkItem
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-08-20 11:09
 * @version: ProjexBizApi, v0.1
 **/
@Tag(name = "Projex", description = "项目工作项相关 API")
@RestController
@RequestMapping("/api/v1/projex")
open class ProjexBizApi {

    @Autowired
    lateinit var projexChangeRequestBizService: ProjexChangeRequestBizService

    @Operation(summary = "推荐")
    @GetMapping("/workItems:recommend")
    fun recommend(): Response<List<ProjexWorkItem>> {
        return Response.success(projexChangeRequestBizService.recommend())
    }

    @Operation(summary = "查询")
    @GetMapping("/workItems:search")
    fun searchByNameOrId(
        @RequestParam("q", required = false, defaultValue = "") q: String,
    ): Response<List<ProjexWorkItem>> {
        return Response.success(projexChangeRequestBizService.searchByNameOrId(query = q))
    }

    @Operation(summary = "查找关联工作项")
    @GetMapping("/{appName}/changeRequests/{crSn}/workItems")
    fun findWorkItems(@PathVariable appName: String, @PathVariable crSn: String): Response<List<ProjexWorkItem>> {
        return Response.success(projexChangeRequestBizService.searchByCrSn(appName, crSn))
    }

    @Operation(summary = "关联工作项")
    @PostMapping("/{appName}/changeRequests/{crSn}/workItems:bind")
    fun bind(
        @PathVariable appName: String,
        @PathVariable crSn: String,
        @RequestParam workItemId: String
    ): Response<Boolean> {
        return Response.success(projexChangeRequestBizService.bindCrToWorkItem(workItemId, crSn))

    }

    @Operation(summary = "解绑工作项")
    @PostMapping("/{appName}/changeRequests/{crSn}/workItems:unbind")
    fun unbind(
        @PathVariable appName: String,
        @PathVariable crSn: String,
        @RequestParam workItemId: String
    ): Response<Boolean> {
        return Response.success(projexChangeRequestBizService.unbindCrToWorkItem(workItemId, crSn))
    }
}