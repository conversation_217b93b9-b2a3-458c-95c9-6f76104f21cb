package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.adapter.EnvLogChangeOrderAdapter

import com.alibabacloud.devops.appstack.apps.biz.service.EnvLogChangeOrderAdapterFactory
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeableEnv
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ext.getChangeOrderEngineType
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import org.slf4j.LoggerFactory

import org.springframework.beans.factory.BeanFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.*

/**
 * <AUTHOR>
 * @date 2022-03-31
 */
@Service
class EnvLogChangeOrderAdapterFactoryImpl: EnvLogChangeOrderAdapterFactory {

    private val logger = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var beanFactory: BeanFactory

    override fun build(engineType: ChangeableEnv.EngineType, resourceType: ResourceTypeEnum): EnvLogChangeOrderAdapter {
        val engineTypeStr = getChangeOrderEngineType(engineType, resourceType)
        val beanName = "${engineTypeStr}EnvLogChangeOrderAdapter".replaceFirstChar {
            it.lowercase(Locale.getDefault())
        }
        val bean = if (beanFactory.containsBean(beanName)) {
            beanFactory.getBean(beanName) as EnvLogChangeOrderAdapter
        } else {
            null
        }

        checkExists(bean, mapOf("engineType" to engineTypeStr)) {
            logger.error("[EnvLogChangeOrderAdapterFactoryImpl] Change order log service not recoginzed: type=$engineTypeStr")
            ErrorCode.AS_UNKNOWN
        }
        return bean
    }
}