package com.alibabacloud.devops.appstack.apps.biz.service.impl.handler.deploy

import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderInputPatchValueHandler
import com.alibabacloud.devops.appstack.apps.biz.service.DeployFlowBizService.Companion.COMMON_ARTIFACT_PREFIX
import com.alibabacloud.devops.appstack.apps.biz.service.DeployFlowBizService.Companion.IMAGE_PREFIX
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrderInput
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppBuiltInOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.RevisionVariableGroup
import org.springframework.stereotype.Service

/**
 * 提取编排中的预置镜像占位符，使用环境基线中的制品替代
 *
 * <AUTHOR>
 * @date 2023-04-20
 */
@Service
class StableChangeOrderChangeOrderInputPatchValueHandlerForDeploy : ChangeOrderInputPatchValueHandler {
    override fun handle(
        env: Env,
        orchestration: AppOrchestration?,
        profiles: List<RevisionVariableGroup>,
        stableChangeOrderInput: ChangeOrderInput?,
        customPatchValues: Map<String, String>,
        result: MutableMap<String, String>
    ) {
        orchestration?.let {
            orchestration as AppBuiltInOrchestration
            orchestration.placeholderList.filter {
                it.predefined == true
                        && (it.name.startsWith(IMAGE_PREFIX) || it.name.startsWith(COMMON_ARTIFACT_PREFIX))
                        && (it.value.isBlank() || it.value == "NULL")
            }.forEach {
                val value = stableChangeOrderInput?.envValues?.get(env.name)?.get(it.name)
                if(value == null){
                    result[it.name] = ""
                }else {
                    result[it.name] = value.toString()
                }
            }
        }
    }

    override fun suitableDeployType() = listOf(ChangeOrder.Type.Deploy)

    override fun order() = 2
}