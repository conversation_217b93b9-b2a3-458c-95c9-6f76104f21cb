package com.alibabacloud.devops.appstack.apps.biz.api.rest.inner

import com.alibabacloud.devops.appstack.apps.biz.service.FlowPipelineBizService
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.ExecuteReleaseStagePipelineRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Hidden
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @create 2023/11/9 8:07 PM
 **/
@Hidden
@Tag(name = "flowPipeline", description = "流水线相关 API")
@RestController
@RequestMapping("/inner/api/flow/pipeline")
class FlowPipelineBizApi {

    @Autowired
    lateinit var flowPipelineBizService: FlowPipelineBizService

    @Operation(summary = "执行研发阶段的流水线")
    @PostMapping("/executeStagePipeline")
    fun findPipeline(
        @RequestBody request: ExecuteReleaseStagePipelineInnerRequest
    ): Response<Map<String, Any>> {
        return Response.success(flowPipelineBizService.executeStagePipeline(request.params))
    }

    @Operation(summary = "获取流水线运行信息")
    @GetMapping("/run")
    fun getRunInfo(
        @RequestParam("pipelineId") pipelineId: String,
        @RequestParam("pipelineRunId") pipelineRunId: String
    ): Response<Map<String, Any>> {
        return Response.success(flowPipelineBizService.getRunInfo(pipelineId, pipelineRunId))
    }

}

data class ExecuteReleaseStagePipelineInnerRequest(
    val params: ExecuteReleaseStagePipelineRequest
)