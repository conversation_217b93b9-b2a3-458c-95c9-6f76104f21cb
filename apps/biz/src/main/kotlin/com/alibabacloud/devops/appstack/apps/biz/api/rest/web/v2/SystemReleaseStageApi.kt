package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import com.alibabacloud.devops.appstack.apps.biz.service.system.ReleaseStageBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ExecutePipelineResult
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStage
import com.alibabacloud.devops.appstack.libs.model.request.ng.ExecuteReleaseStagePipelineNgRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.ExecuteReleaseStagePipelineRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.UpdateReleaseStageRequest
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 * @create 2023/12/26 4:11 PM
 **/
@Tag(name = "发布阶段", description = "发布阶段相关API")
@RestController
@RequestMapping("/api/v2")
open class SystemReleaseStageApi {

    @Autowired
    lateinit var releaseStageBizService: ReleaseStageBizService

    @VpcApi
    @Operation(summary = "更新发布阶段", operationId = "UpdateSystemReleaseStage")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "更新发布阶段成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PutMapping("/systems/{systemName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}")
    fun updateSystemReleaseStage(
        @PathVariable systemName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @RequestBody request: UpdateReleaseStageRequest
    ): ReleaseStage {
        return releaseStageBizService.update(systemName, releaseWorkflowSn, releaseStageSn, request)
    }

    @VpcApi
    @Operation(summary = "执行发布阶段流水线", operationId = "ExecuteReleaseStage")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "执行发布阶段成功"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/systems/{systemName}/releaseWorkflows/{releaseWorkflowSn}/releaseStages/{releaseStageSn}:execute")
    fun executeChangeRequestStagePipeline(
        @PathVariable systemName: String,
        @PathVariable releaseWorkflowSn: String,
        @PathVariable releaseStageSn: String,
        @RequestBody request: ExecuteReleaseStagePipelineNgRequest
    ): ExecutePipelineResult {
        return releaseStageBizService.executePipeline(
            systemName = systemName,
            releaseWorkflowSn = releaseWorkflowSn,
            releaseStageSn = releaseStageSn,
            request = ExecuteReleaseStagePipelineRequest(
                params = request.params.toMutableMap(),
                appReleaseSn = request.appReleaseSn
            )
        )
    }
}