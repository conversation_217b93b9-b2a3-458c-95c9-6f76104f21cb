package com.alibabacloud.devops.appstack.apps.biz.model

import com.alibabacloud.devops.appstack.apps.biz.model.po.DeployFlowPO
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil
import com.alibabacloud.devops.appstack.libs.model.vo.Source
import com.fasterxml.jackson.module.kotlin.readValue

/**
 * @author: <EMAIL>
 * @date: 2022-03-07 19:23
 * @version: DeployFlow, v0.1
 **/

data class DeployFlow(
    val appName: String,
    val envName: String,
    val objectType: ObjectType,
    val objectId: String,
    val changeOrderSn: String,
    var source: Source? = null,
){
    enum class ObjectType{
        FLOW_APP_STACK
    }

}
fun DeployFlow.toPO(): DeployFlowPO {
    val deployFlowPO =  DeployFlowPO()
    deployFlowPO.appName = appName
    deployFlowPO.envName = envName
    deployFlowPO.objectId = objectId
    deployFlowPO.changeOrderSn = changeOrderSn
    deployFlowPO.objectType = objectType.name
    deployFlowPO.source = JacksonUtil.jacksonObjectMapper().writeValueAsString(source)
    return deployFlowPO
}

fun DeployFlowPO.toModel(): DeployFlow {
    return DeployFlow(
        appName = appName,
        envName = envName,
        objectType = DeployFlow.ObjectType.valueOf(objectType),
        objectId = objectId,
        changeOrderSn = changeOrderSn,
        source = source?.let { JacksonUtil.jacksonObjectMapper().readValue(it) }
    )
}