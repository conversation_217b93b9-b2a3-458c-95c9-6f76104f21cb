package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.model.AppTemplateConfigReference
import com.alibabacloud.devops.appstack.apps.biz.model.AppTemplateNameAndConfig
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.AppTemplateConfigReferenceMapper
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.deleteByTemplate
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.listByReference
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.listByTemplate
import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateConfigReferencePO
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateConfigReferenceService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2023-11-09
 */
@Service
class AppTemplateConfigReferenceServiceImpl : AppTemplateConfigReferenceService {

    @Autowired
    lateinit var appTemplateConfigReferenceMapper: AppTemplateConfigReferenceMapper

    override fun reBind(
        appTemplateName: String,
        appTemplateConfigSn: String,
        references: List<AppTemplateConfigReference>
    ) {
        val currentPOs = appTemplateConfigReferenceMapper.listByTemplate(appTemplateName, appTemplateConfigSn)
        val currentReferences = currentPOs.map {
            AppTemplateConfigReference(
                AppTemplateConfigReference.TypeEnum.valueOf(it.referenceType!!),
                it.referenceId!!
            )
        }
        (currentReferences - references.toSet()).distinct().forEach { ref ->
            val po = currentPOs.first { it.referenceType == ref.type.name && it.referenceId == ref.id }
            appTemplateConfigReferenceMapper.deleteById(po)
        }

        (references - currentReferences.toSet()).distinct().forEach { ref ->
            val po = AppTemplateConfigReferencePO()
            po.appTemplateName = appTemplateName
            po.appTemplateConfigSn = appTemplateConfigSn
            po.referenceType = ref.type.name
            po.referenceId = ref.id
            appTemplateConfigReferenceMapper.insert(po)
        }

    }

    override fun deleteAll(appTemplateName: String, appTemplateConfigSn: String):Int {
        return appTemplateConfigReferenceMapper.deleteByTemplate(appTemplateName, appTemplateConfigSn)
    }

    override fun findAppTemplateNameAndConfigByReference(reference: AppTemplateConfigReference): List<AppTemplateNameAndConfig> {
        return appTemplateConfigReferenceMapper.listByReference(reference.type.name, reference.id).map {
            AppTemplateNameAndConfig(it.appTemplateName, it.appTemplateConfigSn)
        }
    }
}