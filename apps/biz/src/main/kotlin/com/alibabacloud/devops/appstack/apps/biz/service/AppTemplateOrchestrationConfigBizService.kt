package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.Label
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.InvalidChangeItem
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.OrchestrationSyncRevision
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.k8s.LocatorInfo
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.template.AppTemplateOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateCheckRequest
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateSyncRequest
import com.alibabacloud.devops.appstack.libs.model.response.orchestration.OrchestrationTemplateSyncStatusResponse
import com.alibabacloud.devops.appstack.libs.model.vo.AppOrchestrationDiffVO
import com.alibabacloud.devops.appstack.libs.model.vo.OrchestrationDiffVO

/**
 * <AUTHOR>
 * @date 2023-10-26
 */
interface AppTemplateOrchestrationConfigBizService {

    fun findPaginatedRevisionAndLabel(
        name: String,
        current: Long,
        pageSize: Long,
    ): Pagination<OrchestrationSyncRevision>

    fun findOrchestrationBySha(name: String, sn: String, sha: String): AppTemplateOrchestration

    fun compare(
        name: String,
        sn: String,
        beforeRevisionSha: String,
        afterRevisionSha: String,
    ): Triple<OrchestrationDiffVO, OrchestrationDiffVO, List<DiffItem<String>>>

    fun check(name: String, checkRequest: OrchestrationTemplateCheckRequest): List<InvalidChangeItem>

    fun bindLabels(name: String, sha: String, labels: List<Label>)

    fun sync2Apps(name: String, request: OrchestrationTemplateSyncRequest)
    fun syncStatus(name: String, sn: String): OrchestrationTemplateSyncStatusResponse
    fun compareToAppOrchestration(templateName: String, sn: String, appName: String): Triple<AppOrchestrationDiffVO, OrchestrationDiffVO, List<DiffItem<String>>>
    fun getLocatorList(name: String, sn: String, envName: String, profileName: List<Map<String, String>>): List<LocatorInfo>
}