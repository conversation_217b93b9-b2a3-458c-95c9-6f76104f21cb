package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.apps.biz.config.KruiseEnforceConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.env.shouldEnableKruise
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2024-08-06 16:32
 * @version: KruiseEnforceService, v0.1
 **/
@Service
class KruiseEnforceService {

    @Autowired
    lateinit var kruiseEnforceConfig: KruiseEnforceConfig

    fun shouldEnableKruise(env: Env?): Bo<PERSON>an {
        if (env == null) {
            return false
        }
        return env.shouldEnableKruise(
            kruiseEnforceConfig.ratioLimitRaw.toInt(),
            kruiseEnforceConfig.blackOrgIds.toList()
        )
    }

}