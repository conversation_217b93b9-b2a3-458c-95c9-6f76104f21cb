package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.libs.hook.spring.boot.starter.service.HookService
import com.alibabacloud.devops.appstack.apps.biz.model.ext.toModel
import com.alibabacloud.devops.appstack.apps.biz.model.ext.toPO
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.AppTemplateMapper
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.AppTemplateUsageRelationMapper
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.deleteByName
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.findByName
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.findPaginated
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.findPaginatedByAppTemplateName
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.searchByNames
import com.alibabacloud.devops.appstack.apps.biz.service.*
import com.alibabacloud.devops.appstack.apps.biz.service.impl.AppTemplateServiceImpl.Companion.PRESET_TEMPLATE_NAMES
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.model.ErrorEntry
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil
import com.alibabacloud.devops.appstack.libs.common.util.PageUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkBizWithCode
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.common.util.checkNonExist
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppType
import com.alibabacloud.devops.appstack.libs.model.`do`.app.toStandardVo
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.ActionType
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.ResourceType
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.iam.AppTemplateRole
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.org.event.Member
import com.alibabacloud.devops.appstack.libs.model.org.event.MemberAudit
import com.alibabacloud.devops.appstack.libs.model.request.AppTemplateQuery
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateConfigRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateRequest
import com.alibabacloud.devops.appstack.libs.model.vo.ng.Application
import com.alibabacloud.devops.appstack.libs.org.spring.boot.starter.service.OrgFacades
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.ResourcePlayer
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.fasterxml.jackson.databind.node.ArrayNode
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.aop.framework.AopContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.text.SimpleDateFormat
import kotlin.math.ceil

@Service
open class AppTemplateBizServiceImpl : AppTemplateBizService {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var auditLogService: AuditLogService

    @Autowired
    lateinit var appTemplateMapper: AppTemplateMapper

    @Autowired
    lateinit var appTemplateUsageRelationMapper: AppTemplateUsageRelationMapper

    @Autowired
    lateinit var appTemplateConfigBizService: AppTemplateConfigBizService

    @Autowired
    lateinit var i18nMessageService: I18nMessageService

    @Autowired
    lateinit var iamService: IamService

    @Autowired
    lateinit var permissionService: PermissionService

    @Autowired
    lateinit var orgFacades: OrgFacades

    @Autowired
    lateinit var appTemplateService: AppTemplateService

    @Autowired
    lateinit var hookService: HookService

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_CREATE),
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
        ]
    )
    override fun create(request: UpsertAppTemplateRequest): AppTemplate {
        if (request.name.isNullOrBlank() || request.displayName.isNullOrBlank()) {
            throw BizException(ErrorCode.AS_APP_TEMPLATE_PARAM_INVALID)
        }
        val appTemplate = request.toModel()
        val exists = appTemplateMapper.findByName(appTemplate.name)
        checkNonExist(exists, appTemplate.name) { ErrorCode.AS_APP_TEMPLATE_DUPLICATED }

        val appTemplatePO = appTemplate.toPO()

        val res = appTemplateMapper.insert(appTemplatePO)

        iamService.registerResource(
            ProtocolType.AppStackAppTemplate,
            appTemplatePO.name,
            AuthUtil.getTenant(),
            AuthUtil.getUserId()
        )

        checkBizWithCode(res > 0) { ErrorCode.AS_APP_TEMPLATE_CREATE_FAILED }
        hookService.invoke(ResourceType.AppTemplate, ActionType.Create, null, appTemplate, appTemplate.name)
        return appTemplatePO.toModel()
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_VIEW),
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
        ]
    )
    override fun list(query: AppTemplateQuery): PageList<AppTemplate> {
        val nextToken: String? = PageUtil.getToken(query.nextToken, query.orderBy)
        val appTemplatePage = appTemplateMapper.list(query.search, query.perPage, query.orderBy, query.sort, nextToken)
        val records = appTemplatePage.map { it.toModel() }
        var token: String? = null
        val data: MutableMap<String, String> = mutableMapOf()
        if (appTemplatePage.size == query.perPage) {
            val item = appTemplatePage.last()
            if (query.orderBy == "id") {
                data["id"] = item.id.toString()
            } else if (query.orderBy == "gmtCreate") {
                val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                data["gmtCreate"] = format.format(item.gmtCreate)
            }
            if (data.isNotEmpty()) {
                token = PageUtil.generateToken("v2", data)
            }
        }
        return PageList(nextToken = token, data = records)
    }

    override fun findPaginated(search: String, current: Long, pageSize: Long, type: AppTemplate.Type): Pagination<AppTemplate> {
        return if (type == AppTemplate.Type.PRESET) {
            appTemplateService.findPresetPaginated(search, current, pageSize)
        } else {
            (AopContext.currentProxy() as AppTemplateBizServiceImpl).pagination(search, current, pageSize)
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_VIEW),
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
        ]
    )
    private fun pagination(
        search: String,
        current: Long,
        pageSize: Long,
    ): Pagination<AppTemplate> {
        val queryPage = appTemplateMapper.findPaginated(search, emptyList(), Page(current, pageSize))
        val records = queryPage.records.map { it.toModel() }
        val page = Pagination<AppTemplate>()
        page.records = records
        page.total = queryPage.total
        page.current = queryPage.current
        page.pageSize = queryPage.pages
        page.pages = queryPage.pages
        return page
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.APP_TEMPLATE_DELETE, resourceArgIndex = 0),
        ]
    )
    override fun delete(name: String) {
        val appTemplatePO = appTemplateMapper.findByName(name)
        checkExists(appTemplatePO, name) { ErrorCode.AS_APP_TEMPLATE_NOT_FOUND }
        val apps = findPaginatedApps(name, 1, 10)
        checkBizWithCode(apps.total == 0L) { ErrorCode.AS_APP_TEMPLATE_DELETE_CHECK_FAILED }
        // 删除配置
        AppTemplateConfig.TypeEnum.values().forEach {
            val find = appTemplateConfigBizService.find(name, it)
            var sn = find?.sn ?: ""
            // 是否存在配置项都需要删除，避免某些配置项创建失败导致的异常数据
            try {
                appTemplateConfigBizService.delete(name, it, sn)
            } catch (e: Throwable) {
                logger.error("delete AppTemplateConfig[${name}.${it.name}] failed sn:$sn", e)
            }
        }
        val res = appTemplateMapper.deleteByName(name)
        checkBizWithCode(res > 0) {
            logger.error("[AppTemplateBizServiceImpl] Failed to delete app template: " +
                "orgId=${AuthUtil.getTenant()}, userId=${AuthUtil.getUserId()}, name=$name"
            )
            ErrorCode.AS_UNKNOWN
        }
        iamService.unregisterResource(
            ProtocolType.AppStackAppTemplate,
            appTemplatePO.name,
            AuthUtil.getTenant(),
            AuthUtil.getUserId()
        )
        val appTemplate = appTemplatePO.toModel()
        hookService.invoke(ResourceType.AppTemplate, ActionType.Delete, null, appTemplate, appTemplate.name)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.ORG_APP_TEMPLATE_VIEW),
            Access(action = Action.ORG_APP_TEMPLATE_CREATE),
            Access(action = Action.APP_TEMPLATE_USE, resourceArgIndex = 0),
        ]
    )
    override fun copy(name: String, request: UpsertAppTemplateRequest): AppTemplate {
        val fromTemplate = find(name)
        val appTemplate = create(
            UpsertAppTemplateRequest(
                name = request.name,
                displayName = request.displayName ?: fromTemplate.displayName,
                description = request.description ?: fromTemplate.description,
                cover = request.cover ?: fromTemplate.cover
            )
        )

        AppTemplateConfig.TypeEnum.values().forEach {
            val sourceConfig = appTemplateConfigBizService.find(name, it)
            if (sourceConfig != null) {
                try {
                    appTemplateConfigBizService.upsert(
                        appTemplate.name,
                        it,
                        UpsertAppTemplateConfigRequest(
                            modeSetting = sourceConfig.modeSetting,
                            configuration = sourceConfig.configuration.toRequest()
                        )
                    )
                } catch (e: Throwable) {
                    logger.error("copy AppTemplateConfig[${name}.${it.name}] failed", e)
                    try {
                        delete(appTemplate.name)
                    } catch (e2: Throwable) {
                        logger.error("clear AppTemplateConfig[${appTemplate.name}] failed", e2)
                    }
                    // 完善异常信息
                    val bizException = e as? BizException
                    var errorMessage = ""
                    var errorAdvice: String? = null
                    bizException?.let {
                        errorMessage = ": " + i18nMessageService.getErrorMessage(it)
                        errorAdvice = i18nMessageService.getErrorAdvice(it)
                    }
                    throw BizException(
                        errorEntry = ErrorEntry(
                            code = ErrorCode.AS_APP_TEMPLATE_COPY_FAILED,
                            advice = errorAdvice
                        ),
                        context = mapOf(
                            "originTemplateName" to name,
                            "targetTemplateName" to appTemplate.name,
                            "msg" to errorMessage
                        )
                    )
                }
            }
        }
        return appTemplate
    }

    override fun findPaginatedApps(name: String, current: Long, pageSize: Long): Pagination<Application> {
        return if(PRESET_TEMPLATE_NAMES.contains(name)){
            findPaginatedAppsInternal(name, current, pageSize)
        }else {
            (AopContext.currentProxy() as AppTemplateBizServiceImpl).findPaginatedAppsInternal(name, current, pageSize)
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_VIEW),
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.APP_TEMPLATE_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_EDIT, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_USE, resourceArgIndex = 0),
        ]
    )
    private fun findPaginatedAppsInternal(name: String, current: Long, pageSize: Long): Pagination<Application> {
        val appTemplate = find(name)
        val queryPage = appTemplateUsageRelationMapper.findPaginatedByAppTemplateName(Page(current, pageSize), name)
        // TODO: 批量获取应用
        // 过滤无效应用，records.size可能与pageSize不一致，但总页数是一致的，暂时忽略该问题
        val records = queryPage.records.map { coreFacades.appFacade.find(it.appName) }
            .filter { it?.type == null || it.type == AppType.APP }
            .mapNotNull { it?.toStandardVo(appTemplate) }
        val page = Pagination<Application>()
        page.records = records
        page.total = queryPage.total
        page.current = queryPage.current
        page.pageSize = queryPage.pages
        page.pages = queryPage.pages
        return page
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.APP_TEMPLATE_EDIT, resourceArgIndex = 0),
        ]
    )
    override fun update(name: String, request: UpsertAppTemplateRequest): AppTemplate {
        val appTemplatePO = appTemplateMapper.findByName(name)
        checkExists(appTemplatePO, name) { ErrorCode.AS_APP_TEMPLATE_NOT_FOUND }
        appTemplatePO.displayName = request.displayName ?: appTemplatePO.displayName
        appTemplatePO.description = request.description ?: appTemplatePO.description
        appTemplatePO.cover = request.cover ?: appTemplatePO.cover
        appTemplateMapper.updateById(appTemplatePO)
        val appTemplate = appTemplatePO.toModel()
        hookService.invoke(ResourceType.AppTemplate, ActionType.Update, null, appTemplate, appTemplate.name)
        return appTemplate
    }


    override fun find(name: String): AppTemplate {
        return if(PRESET_TEMPLATE_NAMES.contains(name)){
            findInternal(name)
        }else {
            (AopContext.currentProxy() as AppTemplateBizServiceImpl).findInternal(name)
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_VIEW),
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.APP_TEMPLATE_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_EDIT, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_USE, resourceArgIndex = 0),
        ]
    )
    private fun findInternal(name: String): AppTemplate{
        return appTemplateService.find(name)
    }

    override fun preset() {
        listOf(
            javaClass.getResource("/preset/apptemplate/appTemplateK8s.json"),
            javaClass.getResource("/preset/apptemplate/appTemplateHost.json")
        ).forEach { it ->
            try {
                presetAppTemplate(jsonString = it!!.readText())
            } catch (e: Exception) {
                logger.error("preset app template failed, file: ${it?.path}")
            }
        }
    }

    override fun searchByNames(names: List<String>): Map<String, AppTemplate> {
        if (names.isEmpty()) {
            return emptyMap()
        }
        return appTemplateMapper.searchByNames(names).map { it.toModel() }.associateBy { it.name }
    }

    private fun presetAppTemplate(jsonString: String) {
        val jsonMapper = JacksonUtil.jacksonObjectMapper()
        val root = jsonMapper.readTree(jsonString)
        val request: UpsertAppTemplateRequest =
            jsonMapper.readValue(root["template"].toString())
        val appTemplate = create(request)

        val arrayNode = root["configs"] as ArrayNode
        val configs = arrayNode.map { config ->
            try {
                jsonMapper.readValue(config.toString(), AppTemplateConfig::class.java)
            } catch (e: Exception) {
                throw RuntimeException("Json cast failed")
            }
        }
        configs.forEach { config ->
            appTemplateConfigBizService.upsert(
                appTemplate.name,
                config.type,
                UpsertAppTemplateConfigRequest(
                    modeSetting = config.modeSetting,
                    configuration = config.configuration.toRequest()
                )
            )
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.APP_TEMPLATE_ADMIN_TRANSFER, resourceArgIndex = 0)
        ]
    )
    override fun transferOwner(name: String, player: ResourcePlayerRequest) {
        // 校验用户是否存在
        try {
            orgFacades.userFacade.getUserId(player.id) ?: throw BizException(ErrorCode.AS_BASE_USER_NOT_FOUND)
        } catch (e: Throwable) {
            logger.warn("get userId failed", e)
            BizException(ErrorCode.AS_BASE_USER_NOT_FOUND)
        }

        findAppTemplateOwner(listOf(name))[name]?.let { owner ->
            iamService.updatePlayer(
                protocolType = ProtocolType.AppStackAppTemplate,
                resourceName = name,
                subjectType = SubjectType.User,
                subjectId = owner,
                roleNameList = listOf(AppTemplateRole.admin.name)
            )
        }
        iamService.updatePlayer(
            protocolType = ProtocolType.AppStackAppTemplate,
            resourceName = name,
            subjectType = SubjectType.User,
            subjectId = player.id,
            roleNameList = listOf(AppTemplateRole.owner.name)
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_VIEW),
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.APP_TEMPLATE_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_EDIT, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_USE, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_MEMBER_SET, resourceArgIndex = 0),
        ]
    )
    override fun findAllMember(name: String): Map<ResourcePlayer, List<Role>> {
        val playerRolesMap = iamService.findPlayerRoles(ProtocolType.AppStackAppTemplate, name)
        playerRolesMap.values.forEach { roleList ->
            roleList.sortBy { AppTemplateRole.valueOf(it.name).ordinal }
        }
        return playerRolesMap
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.APP_TEMPLATE_MEMBER_SET, resourceArgIndex = 0),
        ]
    )
    override fun addRoleMember(name: String, roleName: String, playerList: List<ResourcePlayerRequest>) {
        val role = permissionService.findAppTemplateRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        checkBizWithCode(role.name != AppTemplateRole.owner.name, null) {
            ErrorCode.AS_ROLE_OWNER_ONLY_CAN_BE_MODIFIED_BY_TRANSFER
        }
        if (!iamService.checkPlayersExist(playerList)) {
            throw BizException(ErrorCode.AS_BASE_USER_NOT_FOUND)
        }
        iamService.addRoleMember(
            protocolType = ProtocolType.AppStackAppTemplate,
            resourceName = name,
            roleName = role.name,
            playerList = playerList
        )
        val audit = MemberAudit(
            target = AuditTarget(id = name, name = name),
            member = Member(roleName = role.name, userIds = playerList.map { it.id })
        )
        auditLogService.log(OrgEventType.APP_MEMBER_ADD, audit)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.APP_TEMPLATE_MEMBER_SET, resourceArgIndex = 0),
        ]
    )
    override fun updateMemberRole(
        name: String,
        subjectId: String,
        subjectType: SubjectType,
        roleNameList: List<String>,
    ) {
        val allRoleNames = permissionService.findAllAppTemplateRoles().map { it.name }
        // 如果有不存在的应用模板角色
        val unknownRole = roleNameList.firstOrNull { !allRoleNames.contains(it) }
        if (unknownRole != null) {
            throw BizException(ErrorCode.AS_ROLE_NOT_FOUND)
        }
        // 如果有不存在的用户
        if (!iamService.checkPlayersExist(listOf(ResourcePlayerRequest(subjectId, subjectType)))) {
            throw BizException(ErrorCode.AS_BASE_USER_NOT_FOUND)
        }
        val roleNameExceptOwnerList = roleNameList.filter { it != AppTemplateRole.owner.name }
        val previousRoles = iamService.findPlayerRoles(
            protocolType = ProtocolType.AppStackAppTemplate,
            resourceName = name,
        ).mapKeys { it.key.id }[subjectId] ?: emptyList()
        iamService.updatePlayer(
            protocolType = ProtocolType.AppStackAppTemplate,
            resourceName = name,
            subjectType = subjectType,
            subjectId = subjectId,
            roleNameList = roleNameExceptOwnerList
        )
    }

    private fun findAppTemplateOwner(names: List<String>): Map<String, String?> {
        if (names.isEmpty()) return emptyMap()
        return iamService.findRolePlayers(ProtocolType.AppStackAppTemplate, names, AppTemplateRole.owner.name)
            .mapValues { it.value[0] }
    }

    override fun listMemberPaginated(search: String, current: Long, pageSize: Long): Pagination<AppTemplate> {
        if (iamService.can(
                ProtocolType.AppStack,
                "any",
                Action.ORG_APP_TEMPLATE_MANAGE
            ) || iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_TEMPLATE_VIEW)
        ) {
            // 具有【管理应用模板】权限点，则返回所有应用模板
            return pagination(search, current, pageSize)
        }
        val total = iamService.countResource(
            protocolType = ProtocolType.AppStackAppTemplate,
            subjectType = SubjectType.User,
            subjectId = AuthUtil.getUserId(),
            search = search
        ).toLong()
        val names = iamService.searchResourceName(
            protocolType = ProtocolType.AppStackAppTemplate,
            subjectType = SubjectType.User,
            subjectId = AuthUtil.getUserId(),
            search = search,
            page = current.toInt(),
            size = pageSize.toInt(),
        ).toMutableList()

        if (names.isEmpty()) {
            val page = Pagination<AppTemplate>()
            page.records = emptyList()
            page.total = total
            page.current = current
            page.pageSize = pageSize
            page.pages = ceil(total.toDouble() / pageSize).toLong()
            return page
        }

        val paginated = appTemplateMapper.findPaginated("", names, Page(current, pageSize))
        if (paginated.records.size != names.size) {
            val diff = names - paginated.records.map { po -> po.name }.toSet()
            logger.warn("app in iam but not in core, name list: $diff")
        }
        // names 是iam查询结果，按照授权时间排序；core查询是按应用创建时间排序，这里顺序保持一致
        paginated.records = paginated.records.sortedWith { o1, o2 ->
            names.indexOf(o1.name) - names.indexOf(o2.name)
        }

        val records = paginated.records.map { it.toModel() }
        val page = Pagination<AppTemplate>()
        page.records = records
        page.total = total
        page.current = current
        page.pageSize = pageSize
        page.pages = ceil(total.toDouble() / pageSize).toLong()
        return page
    }
}