package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.EnterpriseSettingsBizService
import com.alibabacloud.devops.appstack.apps.biz.service.PermissionService
import com.alibabacloud.devops.appstack.apps.biz.service.facade.EnterpriseSettingsFacade
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.KubeConfigVO
import com.alibabacloud.devops.appstack.libs.model.request.UpsertAliyunAuthAccountRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpsertKubeConfigRequest
import com.alibabacloud.devops.appstack.libs.model.vo.AliyunAuthAccountVO
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2022-01-27 12:22
 * @version: EnterpriseSettingsBizServiceImpl, v0.1
 **/
@Slf4k
@Service
open class EnterpriseSettingsBizServiceImpl : EnterpriseSettingsBizService {

    @Autowired
    private lateinit var enterpriseSettingsFacade: EnterpriseSettingsFacade

    @Autowired
    private lateinit var permissionService: PermissionService

    override fun findAllAliyunAuthAccounts(): List<AliyunAuthAccountVO> {
        return enterpriseSettingsFacade.findAllAliyunAuthAccounts()
    }

    override fun createAliyunAuthAccount(request: UpsertAliyunAuthAccountRequest): AliyunAuthAccountVO? {
        permissionService.checkServiceConnectionPermission(
            Action.ORG_SERVICE_CONNECTION_CREATE,
            Action.ORG_SERVICE_CONNECTION_MANAGE
        )
        return enterpriseSettingsFacade.createAliyunAuthAccount(request)
    }

    override fun updateAliyunAuthAccount(request: UpsertAliyunAuthAccountRequest): AliyunAuthAccountVO? {
        if (!isAliyunPkAvailableForOrgAndUser(request.aliyunPK)) {
            throw BizException(ErrorCode.AS_AUTH_FAILED)
        }
        permissionService.checkServiceConnectionPermission(
            Action.ORG_SERVICE_CONNECTION_MANAGE
        )
        return enterpriseSettingsFacade.upsertAliyunAuthAccount(request)
    }

    override fun deleteAliyunAuthAccount(id: Long) {
        permissionService.checkServiceConnectionPermission(
            Action.ORG_SERVICE_CONNECTION_MANAGE
        )
        enterpriseSettingsFacade.deleteAliyunAuthAccount(id)
    }

    private fun isAliyunPkAvailableForOrgAndUser(aliyunPk: String): Boolean {
        val accountResult = enterpriseSettingsFacade.findAllAliyunAuthAccounts()
        if (accountResult.isNullOrEmpty()) {
            return false
        }
        return accountResult.map { it.aliyunPK }.contains(aliyunPk)
    }

    override fun findAllKubeConfig(): List<KubeConfigVO> {
        return enterpriseSettingsFacade.findAllKubeConfig()
    }

    override fun createKubeConfig(upsertKubeConfigRequest: UpsertKubeConfigRequest): KubeConfigVO {
        permissionService.checkServiceConnectionPermission(
            Action.ORG_SERVICE_CONNECTION_CREATE,
            Action.ORG_SERVICE_CONNECTION_MANAGE
        )
        return enterpriseSettingsFacade.createKubeConfig(upsertKubeConfigRequest)
    }

    override fun updateKubeConfig(id: Long, upsertKubeConfigRequest: UpsertKubeConfigRequest): KubeConfigVO? {
        permissionService.checkServiceConnectionPermission(
            Action.ORG_SERVICE_CONNECTION_MANAGE
        )
        return enterpriseSettingsFacade.updateKubeConfig(id, upsertKubeConfigRequest)
    }

    override fun deleteKubeConfig(id: Long) {
        permissionService.checkServiceConnectionPermission(
            Action.ORG_SERVICE_CONNECTION_MANAGE
        )
        enterpriseSettingsFacade.deleteKubeConfig(id)
    }

}