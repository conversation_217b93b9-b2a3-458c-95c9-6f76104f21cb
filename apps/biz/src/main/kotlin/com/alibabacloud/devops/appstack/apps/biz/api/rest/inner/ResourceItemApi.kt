package com.alibabacloud.devops.appstack.apps.biz.api.rest.inner

import com.alibabacloud.devops.appstack.apps.biz.service.ResourceItemBizService
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Hidden
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @create 2023/12/24 6:41 PM
 **/
@Hidden
@Tag(name = "Appstack Resource Item", description = "资源项相关 API")
@RestController
@RequestMapping("/inner/api/resources")
class ResourceItemApi {

    @Autowired
    lateinit var resourceItemBizService: ResourceItemBizService

    /**
     * Flow主机更新后，更新主机的sn
     */
    @PostMapping("/items:updateHostSn")
    fun updateHostSn(): Response<Boolean> {
        return Response.success(resourceItemBizService.updateHostSn())
    }
}