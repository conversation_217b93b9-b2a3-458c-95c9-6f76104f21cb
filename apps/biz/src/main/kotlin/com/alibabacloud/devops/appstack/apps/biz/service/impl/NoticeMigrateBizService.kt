package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.model.NoticeConfig
import com.alibabacloud.devops.appstack.apps.biz.model.vo.AppStackNotice
import com.alibabacloud.devops.appstack.apps.biz.service.NoticeBizService
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import javax.annotation.PostConstruct

/**
 * @author: <EMAIL>
 * @date: 2023-02-21 18:23
 * @version: NoticeMigrateBizService, v0.1
 **/
@Service("noticeMigrateBizService")
class NoticeMigrateBizService : NoticeBizService {

    @Value("\${devops.appstack.notice}")
    lateinit var noticeConfigStr: String

    companion object {
        var noticeConfigList: List<NoticeConfig>  = emptyList()
    }

    override fun fetch(): AppStackNotice? {
        return noticeConfigList.mapNotNull {
            if (it.orgIdList.contains(AuthUtil.getTenant())) {
                AppStackNotice(
                    type = AppStackNotice.Type.Migrate,
                    htmlContent = it.content ?: ""
                )
            } else {
                null
            }
        }.firstOrNull()
    }


    @PostConstruct
    fun initConfig(){
        noticeConfigList = jacksonObjectMapper().readValue(noticeConfigStr)
    }
}