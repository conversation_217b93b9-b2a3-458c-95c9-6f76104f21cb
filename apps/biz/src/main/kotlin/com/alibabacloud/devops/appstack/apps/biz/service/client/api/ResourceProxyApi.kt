package com.alibabacloud.devops.appstack.apps.biz.service.client.api

import com.alibabacloud.devops.appstack.libs.model.request.resource.AckAddonInstallRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.HeaderMap
import retrofit2.http.POST
import retrofit2.http.Query

@RetrofitClient(baseUrl = "http://appstack-resource-manager-ng")
interface ResourceProxyApi {

    @GET("/api/internal/ack/addons/describeClusterAddonsUpgradeStatus")
    fun describeClusterAddonsUpgradeStatus(
        @HeaderMap headers: Map<String, String>,
        @Query("aliyunPK") aliyunPK: String,
        @Query("clusterId") clusterId: String,
    ): Response<Map<String, String>>

    @POST("/api/internal/ack/addons/installClusterAddons")
    fun installClusterAddons(
        @HeaderMap headers: Map<String, String>,
        @Body ackAddonInstallRequest: AckAddonInstallRequest,
    ): Response<Map<String, String>>

    @GET("/api/internal/ack/prometheus/listDashboards")
    fun listDashboards(
        @HeaderMap headers: Map<String, String>,
        @Query("aliyunPK") aliyunPK: String,
        @Query("clusterId") clusterId: String,
        @Query("regionId") regionId: String,
    ): Response<Map<String, String>>
}