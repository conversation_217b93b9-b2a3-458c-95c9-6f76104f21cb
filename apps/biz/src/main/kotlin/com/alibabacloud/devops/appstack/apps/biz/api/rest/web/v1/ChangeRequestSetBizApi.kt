package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestSetBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.ChangeRequestSet
import com.alibabacloud.devops.appstack.libs.model.request.cr.TransferChangeSetOwnerRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.UpdateChangeSetParticipatorsRequest
import com.alibabacloud.devops.appstack.libs.model.request.crset.QueryChangeRequestSet
import com.alibabacloud.devops.appstack.libs.model.request.crset.UpsertChangeRequestSetRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeRequestSetOutlineVO
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-08-22 20:26
 * @version: ChangeRequestSetBizApi, v0.1
 **/
@Tag(name = "ChangeRequestSet", description = "变更集相关 API")
@RestController
@RequestMapping("/api/v1/")
open class ChangeRequestSetBizApi {

    @Autowired
    lateinit var changeRequestSetBizService: ChangeRequestSetBizService

    @Operation(summary = "创建或更新变更集")
    @PostMapping("/changeRequestSets")
    fun upsertChangeRequestSet(@RequestBody upsertChangeRequestSetRequest: UpsertChangeRequestSetRequest): Response<ChangeRequestSet> {
        return Response.success(changeRequestSetBizService.upsert(upsertChangeRequestSetRequest = upsertChangeRequestSetRequest))
    }

    @Operation(summary = "查看变更集详情")
    @GetMapping("/changeRequestSets/{sn}")
    fun findChangeRequestSet(@PathVariable sn: String): Response<ChangeRequestSetOutlineVO> {
        return Response.success(changeRequestSetBizService.findDetail(sn = sn))
    }

    @Operation(summary = "查看变更集列表")
    @PostMapping("/changeRequestSets:search")
    fun findPaginated(
        @RequestBody request: QueryChangeRequestSet,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
        @RequestParam("orderBy", required = false, defaultValue = "gmt_create") orderBy: String,
        @RequestParam("sort", required = false, defaultValue = "desc") sort: String,
    ): Response<Pagination<ChangeRequestSet>> {
        return Response.success(
            changeRequestSetBizService.findPaginated(
                request = request,
                current = current,
                pageSize = pageSize,
                orderBy = orderBy,
                sort = sort
            )
        )
    }

    @Operation(summary = "修改变更集负责人")
    @PutMapping("/changeRequestSets/{sn}:owner")
    fun transferOwner(
        @PathVariable sn: String,
        @RequestBody request: TransferChangeSetOwnerRequest
    ): Response<ChangeRequestSet> {
        return Response.success(changeRequestSetBizService.transferOwner(sn, request))
    }

    @Operation(summary = "修改变更集参与人")
    @PutMapping("/changeRequestSets/{sn}:participators")
    fun updateParticipators(
        @PathVariable sn: String,
        @RequestBody request: UpdateChangeSetParticipatorsRequest
    ): Response<ChangeRequestSet> {
        return Response.success(changeRequestSetBizService.updateParticipators(sn, request))
    }

    @Operation(summary = "关闭变更集")
    @PutMapping("/changeRequestSets/{sn}:close")
    fun close(
        @PathVariable sn: String
    ): Response<ChangeRequestSet> {
        return Response.success(changeRequestSetBizService.close(sn))
    }
}