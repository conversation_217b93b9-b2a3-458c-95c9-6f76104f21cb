package com.alibabacloud.devops.appstack.apps.biz.mq.broadcast

import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderWebSocketService
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.BroadcastListener
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.BroadcastTagEnum
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.OrderStatusChangedBody
import com.alibabacloud.devops.appstack.libs.change.controller.spring.boot.starter.service.ChangeControllerFacades
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2022-04-06
 */
@Slf4k
@Service
class OrderStatusChangedListener : BroadcastListener {


    @Autowired
    lateinit var changeControllerFacades: ChangeControllerFacades


    @Autowired
    lateinit var changeOrderWebSocketService: ChangeOrderWebSocketService


    override fun expectedTag(): BroadcastTagEnum {
        return BroadcastTagEnum.ORDER_STATUS_CHANGED
    }

    override fun handle(body: ByteArray) {
        logger.info("Received broadcast content: ${String(body)}")
        val content = jacksonObjectMapper().readValue(body, OrderStatusChangedBody::class.java)
        AuthThreadContext.setTenant(content.tenant)
        AuthThreadContext.setUserId(content.operator)
        content.changeOrderSn?.let {
            notify(it)
            return
        }

        content.jobSn?.let { jobSn ->
            val jobVO = changeControllerFacades.jobFacade.find(jobSn)
            jobVO?.let {
                notify(it.changeOrderSn)
            }
        }
    }

    private fun notify(changeOrderSn: String) {
        val changeOrderVO = changeControllerFacades.changeOrderFacade.find(changeOrderSn)
        changeOrderVO?.let {
            changeOrderWebSocketService.notifyStateChanged(it.sn, jacksonObjectMapper().writeValueAsString(it))
        }
    }
}