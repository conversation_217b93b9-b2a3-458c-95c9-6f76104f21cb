package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.apps.biz.model.vo.AppActionVO
import com.alibabacloud.devops.appstack.apps.biz.model.vo.SystemActionVO
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.iam.model.Permission
import com.alibabacloud.devops.iam.model.PolicyOption
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.request.CreateRoleRequest
import com.alibabacloud.devops.iam.model.request.UpdateRoleRequest

/**
 * @author: <EMAIL>
 * @date: 2021-11-02 15:17
 * @version: PermissionService, v0.1
 **/
interface PermissionService {
    fun findOrgRole(name: String): Role?
    fun findAllOrgRoles(): List<Pair<Role, Boolean>>
    fun createOrgRole(createRoleRequest: CreateRoleRequest): Role
    fun updateOrgRole(role: Role, updateRoleRequest: UpdateRoleRequest): Role
    fun deleteOrgRole(role: Role): Boolean
    fun findOrgPermission(role: Role): Permission
    fun saveOrgPermission(role: Role, policyOptionList: List<PolicyOption>): Permission

    fun findAppRole(name: String): Role?
    fun findAllAppRoles(): List<Role>
    fun createAppRole(createRoleRequest: CreateRoleRequest): Role
    fun updateAppRole(role: Role, updateRoleRequest: UpdateRoleRequest): Role
    fun deleteAppRole(role: Role): Boolean
    fun findAppPermission(role: Role): Permission
    fun saveAppPermission(role: Role, policyOptionList: List<PolicyOption>): Permission

    fun findSystemRole(name: String): Role?
    fun findAllSystemRoles(): List<Role>
    fun createSystemRole(createRoleRequest: CreateRoleRequest): Role
    fun updateSystemRole(role: Role, updateRoleRequest: UpdateRoleRequest): Role
    fun deleteSystemRole(role: Role): Boolean
    fun findSystemPermission(role: Role): Permission
    fun saveSystemPermission(role: Role, policyOptionList: List<PolicyOption>): Permission

    fun permissionVisible(): Boolean
    fun findOrgActionAllowed(): List<String>
    fun findAppActionAllowed(appName: String): AppActionVO
    fun findResActionAllowed(poolName: String, instanceName: String): List<String>
    fun findSystemActionAllowed(systemName: String): SystemActionVO

    fun checkServiceConnectionPermission(vararg actions: Action)

    fun findVarRole(name: String): Role?

    fun findVarActionAllowed(name: String): List<String>

    fun findAppTemplateRole(name: String): Role?

    fun findAppTemplateActionAllowed(name: String): List<String>

    fun findAllAppTemplateRoles(): List<Role>
}