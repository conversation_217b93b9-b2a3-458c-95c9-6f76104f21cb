package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowBriefVO
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowRevision
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.CreateReleaseWorkflowBizRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.SortReleaseWorkflowRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.UpdateReleaseWorkflowRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.ga.AppTemplateWorkflowRequest

/**
 * <AUTHOR>
 * @date 2022-06-26
 */
interface WorkflowBaseService {
    fun create(appName: String, request: CreateReleaseWorkflowBizRequest): ReleaseWorkflow
    fun create(appName: String, request: AppTemplateWorkflowRequest): ReleaseWorkflowRevision
    fun findAll(appName: String, type: ReleaseWorkflow.TypeEnum?): List<ReleaseWorkflow>
    fun findAllBrief(appName: String, type: ReleaseWorkflow.TypeEnum?): List<ReleaseWorkflowBriefVO>
    fun find(appName: String, sn: String): ReleaseWorkflow
    fun findBrief(appName: String, sn: String): ReleaseWorkflowBriefVO
    fun findWithRevision(appName: String, sn: String): ReleaseWorkflowRevision?
    fun findYamlBySha(appName: String, sn: String, sha: String): String
    fun listRevisionPaginated(systemName: String, sn: String, current: Long, pageSize: Long): Pagination<Revision>
    fun compare(
        appName: String,
        sn: String,
        beforeRevisionSha: String,
        afterRevisionSha: String,
    ): Triple<String, String, List<DiffItem<String>>>

    fun update(appName: String, sn: String, request: AppTemplateWorkflowRequest): ReleaseWorkflowRevision
    fun update(appName: String, sn: String, updateReleaseWorkflowRequest: UpdateReleaseWorkflowRequest): ReleaseWorkflow
    fun delete(appName: String, sn: String): ReleaseWorkflow
    fun sort(appName: String, request: SortReleaseWorkflowRequest): List<ReleaseWorkflow>
}