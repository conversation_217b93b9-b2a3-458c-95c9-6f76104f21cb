package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateConfigBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateConfigRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR> <EMAIL>
 * @version : AppTemplateConfigBizApi, v0.1
 * @date : 2023-10-19 20:03
 **/
@Tag(name = "AppTemplateConfig", description = "应用模板配置相关 API")
@RestController
@RequestMapping("/api/v1/appTemplates/{name}/configs/{type}")
open class AppTemplateConfigBizApi {

    @Autowired
    lateinit var appTemplateConfigBizService: AppTemplateConfigBizService

    @Operation(summary = "查询应用模板配置")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询应用模板配置成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常")
        ]
    )
    @GetMapping
    fun findAppTemplateConfig(
        @PathVariable name: String,
        @PathVariable type: AppTemplateConfig.TypeEnum,
    ): Response<AppTemplateConfig> {
        return Response.success(appTemplateConfigBizService.find(appTemplateName = name, type = type))
    }

    @Operation(summary = "更新应用模板配置")
    @PutMapping
    fun upsertAppTemplateConfig(
        @PathVariable name: String,
        @PathVariable type: AppTemplateConfig.TypeEnum,
        @RequestBody request: UpsertAppTemplateConfigRequest
    ): Response<AppTemplateConfig> {
        return Response.success(appTemplateConfigBizService.upsert(name, type, request))
    }
}