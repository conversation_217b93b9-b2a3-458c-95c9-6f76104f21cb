package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.model.OnlineGuidance
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.OnlineGuidanceService
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-08-25 22:13
 * @version: OnlineGuidanceBizApi, v0.1
 **/
@Tag(name = "OnlineGuidance", description = "应用上线引导 API")
@RestController
@RequestMapping("/api/v1")
open class OnlineGuidanceBizApi {

    @Autowired
    lateinit var onlineGuidanceService: OnlineGuidanceService

    @Operation(summary = "应用上线引导")
    @GetMapping("/onlineGuidance")
    fun findOnlineGuidance(
        @RequestParam appName: String
    ): Response<OnlineGuidance> {
        return Response.success(onlineGuidanceService.find(appName))
    }

}
