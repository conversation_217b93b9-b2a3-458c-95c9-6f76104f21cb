package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.HookSubscribeBizService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.integration.spring.boot.starter.service.IntegrationFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.*
import com.alibabacloud.devops.appstack.libs.model.event.EventResult
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.PaginationQuery
import com.alibabacloud.devops.iam.constant.ProtocolType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @create 2024/6/26 11:47 AM
 **/
@Service
open class HookSubscribeBizServiceImpl: HookSubscribeBizService {

    @Autowired
    lateinit var integrationFacades: IntegrationFacades

    @Autowired
    lateinit var iamService: IamService

    override fun create(hookChannelSubscribe: HookChannelSubscribe): HookChannelSubscribe {
        checkAuth(hookChannelSubscribe)
        // 目前只开放 WebHook 类型
        if (hookChannelSubscribe.type != HookChannelType.WEBHOOK) {
            throw BizException(ErrorCode.AS_HOOK_CHANNEL_TYPE_UNSUPPORTED)
        }
        return integrationFacades.hookChannelSubscribeFacade.create(hookChannelSubscribe)
    }

    override fun delete(sn: String): Boolean {
        checkAuth(get(sn))
        return integrationFacades.hookChannelSubscribeFacade.delete(sn)
    }

    override fun update(
        sn: String,
        hookChannelSubscribe: HookChannelSubscribe
    ): HookChannelSubscribe {
        checkAuth(get(sn))
        return integrationFacades.hookChannelSubscribeFacade.update(sn, hookChannelSubscribe)
    }

    override fun get(sn: String): HookChannelSubscribe {
        return integrationFacades.hookChannelSubscribeFacade.get(sn)
    }

    override fun findByScopeAndType(
        scopeType: String,
        scopeId: String,
        channelType: String
    ): List<HookChannelSubscribe> {
        checkAuth(
            HookChannelSubscribe(
                scopeType = HookSubscribeScopeType.valueOf(scopeType),
                scopeId = scopeId,
                type = HookChannelType.valueOf(channelType),
                spec = YunxiaoChannelSpec()
            ),
            true
        )
        var scopeTypeEnum: HookSubscribeScopeType? = null
        var channelTypeEnum: HookChannelType? = null
        try {
            scopeTypeEnum = HookSubscribeScopeType.valueOf(scopeType)
        } catch (e: Exception) {
            throw BizException(ErrorCode.AS_HOOK_SCOPE_TYPE_UNSUPPORTED)
        }
        try {
            channelTypeEnum = HookChannelType.valueOf(channelType)
        } catch (e: Exception) {
            throw BizException(ErrorCode.AS_HOOK_CHANNEL_TYPE_UNSUPPORTED)
        }
        return integrationFacades.hookChannelSubscribeFacade.findByScopeAndType(scopeTypeEnum, scopeId, channelTypeEnum)
    }

    override fun invokeEvent(sn: String, event: HookEvent): EventResult {
        checkAuth(get(sn))
        return integrationFacades.hookChannelSubscribeFacade.invokeEvent(sn, event)
    }

    override fun findInvokeLogsPaginated(sn: String, query: PaginationQuery, event: String?, action: String?): PageList<HookSendLog> {
        // TODO: 支持 event、action 过滤
        checkAuth(get(sn))
        return integrationFacades.hookChannelSubscribeFacade.listSendLogs(sn, query)
    }

    override fun getSupportEventTypes(channelType: String, scopeType: String): List<HookEventTypeVO> {
        return integrationFacades.hookChannelSubscribeFacade.getSupportEventTypes(channelType, scopeType)
    }

    private fun checkAuth(hookChannelSubscribe: HookChannelSubscribe, viewOnly: Boolean = false) {
        val type = hookChannelSubscribe.scopeType
        val resourceName = hookChannelSubscribe.scopeId
        var orgAction = Action.ORG_HOOK_MANAGE
        var appAction = Action.APP_HOOK_MANAGE
        if (viewOnly) {
            orgAction = Action.ORG_APP_VIEW
            appAction = Action.APP_VIEW
        }
        when (type) {
            HookSubscribeScopeType.ORGANIZATION -> {
                if (!iamService.can(ProtocolType.AppStack, "any", orgAction)) {
                    throw BizException(ErrorCode.AS_PERMISSION_DENIED)
                }
            }

            HookSubscribeScopeType.APP -> {
                if (iamService.can(ProtocolType.AppStack, "any", Action.ORG_APP_MANAGE)) {
                    return
                }
                if (!iamService.can(ProtocolType.AppStackApp, resourceName, appAction)) {
                    throw BizException(ErrorCode.AS_PERMISSION_DENIED)
                }
            }
        }
    }
}