package com.alibabacloud.devops.appstack.apps.biz.aspect

import com.alibaba.csp.sentinel.Entry
import com.alibaba.csp.sentinel.EntryType
import com.alibaba.csp.sentinel.SphU
import com.alibaba.csp.sentinel.slots.block.BlockException
import com.alibabacloud.devops.appstack.apps.biz.annotation.OrgBasedRateLimitSpot
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.reflect.MethodSignature
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import javax.annotation.PostConstruct

/**
 * 约束同步请求的限流切面，使用 AHAS Sentinel 实现。
 *
 * @author: <EMAIL>
 * @date: 2023-04-10 15:18
 * @version: OrgBasedRateLimitAhasAspect, v0.1
 **/
@Aspect
@Slf4k
@Component
class OrgBasedRateLimitAhasAspect {

    @Value("\${ratelimit.forced}")
    private lateinit var forced: String

    @Value("\${ratelimit.selective.targets}")
    private lateinit var selectiveTargetsRaw: Array<String>

    private lateinit var selectiveTargets: Set<String>

    @Around("@annotation(com.alibabacloud.devops.appstack.apps.biz.annotation.OrgBasedRateLimitSpot)")
    fun onRateLimitedInvocation(joinPoint: ProceedingJoinPoint): Any? {
        val signature = joinPoint.signature as MethodSignature
        val method = signature.method
        val rateLimitHint: OrgBasedRateLimitSpot = method.getAnnotation(OrgBasedRateLimitSpot::class.java)

        var entry: Entry? = null
        val org = AuthUtil.getTenant()

        // 在【没有强制开启全量限流】且【选择性名单不包含当前企业】的情况下，跳过限流逻辑
        if (forced != "on" && !selectiveTargets.contains(org)) {
            return joinPoint.proceed()
        }

        try {
            entry = SphU.entry(rateLimitHint.restraintName, EntryType.IN, rateLimitHint.qpsWeight, org)
            return joinPoint.proceed()
        } catch (e: BlockException) {
            // 被限流
            logger.info("[RATE_LIMITED] org=$org, restraint=${rateLimitHint.restraintName}", e)
            throw BizException(ErrorCode.AS_RATE_LIMITED)
        } catch (e: Throwable) {
            // 其他情况
            throw e
        } finally {
            // 按 AHAS 要求完成限流收尾
            entry?.exit(rateLimitHint.qpsWeight, org)
        }
    }

    @PostConstruct
    fun init() {
        logger.info("[RATE_LIMIT_INIT] forced=${forced}, selectiveTargets=${selectiveTargetsRaw}")
        selectiveTargets = selectiveTargetsRaw.toSet()
    }

}