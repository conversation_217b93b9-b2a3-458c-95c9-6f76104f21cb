package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.apps.biz.model.FlowRole
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ConnectionType
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ServiceConnection
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ServiceConnectionRegion

/**
 * @author: <EMAIL>
 * @date: 2022-07-08 14:23
 * @version: ServiceConnectionBizService, v0.1
 **/
interface ServiceConnectionBizService {
    fun queryRolePermission(roleId: String): FlowRole
    fun updateRolePermission(roleId: String, roleName: String, permissions: String): Boolean
    fun findAll(connectionType: ConnectionType): List<ServiceConnection>
    fun findRegionsById(id: Long): List<ServiceConnectionRegion>

    fun findByIds(ids: List<String>): List<ServiceConnection>
}