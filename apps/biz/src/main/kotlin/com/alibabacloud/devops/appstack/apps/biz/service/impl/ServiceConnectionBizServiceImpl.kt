package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.config.FlowAuthConfiguration
import com.alibabacloud.devops.appstack.apps.biz.model.FlowRole
import com.alibabacloud.devops.appstack.apps.biz.service.ServiceConnectionBizService
import com.alibabacloud.devops.appstack.apps.biz.service.client.api.FlowServiceConnectionApi
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.model.ErrorEntry
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.TokenUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkBiz
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ConnectionType
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ServiceConnection
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ServiceConnectionRegion
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2022-07-08 14:31
 * @version: ServiceConnectionBizServiceImpl, v0.1
 **/
@Service
open class ServiceConnectionBizServiceImpl : ServiceConnectionBizService {

    @Autowired
    lateinit var flowAuthConfig: FlowAuthConfiguration

    @Autowired
    lateinit var flowServiceConnectionApi: FlowServiceConnectionApi

    override fun queryRolePermission(roleId: String): FlowRole {
        val response = flowServiceConnectionApi.queryRolePermission(getHeaders(), roleId)
        checkExists(response) {
            ErrorCode.AS_HTTP_CALL_ERROR
        }
        checkBiz(response.successful) {
            ErrorEntry(code = ErrorCode.AS_RES_CALL_FLOW_SERVICE_CONNECTION_FAILED, message = response.errorMessage)
        }
        return response.data!!
    }

    override fun updateRolePermission(roleId: String, roleName: String, permissions: String): Boolean {
        val response = flowServiceConnectionApi.updateRolePermission(getHeaders(), roleId, roleName, permissions)
        checkExists(response) {
            ErrorCode.AS_HTTP_CALL_ERROR
        }
        checkBiz(response.successful) {
            ErrorEntry(code = ErrorCode.AS_RES_CALL_FLOW_SERVICE_CONNECTION_FAILED, message = response.errorMessage)
        }
        return response.data!!
    }

    override fun findAll(connectionType: ConnectionType): List<ServiceConnection> {
        val response = flowServiceConnectionApi.find(getHeaders(), connectionType.name, AuthUtil.getUserId())
        checkExists(response) {
            ErrorCode.AS_HTTP_CALL_ERROR
        }
        checkBiz(response.successful) {
            ErrorEntry(code = ErrorCode.AS_RES_CALL_FLOW_SERVICE_CONNECTION_FAILED, message = response.errorMessage)
        }
        return response.data!!
    }

    override fun findRegionsById(id: Long): List<ServiceConnectionRegion> {
        val response = flowServiceConnectionApi.findRegionsByServiceConnectionId(getHeaders(), id)
        checkExists(response) {
            ErrorCode.AS_HTTP_CALL_ERROR
        }
        checkBiz(response.successful) {
            ErrorEntry(code = ErrorCode.AS_RES_CALL_FLOW_SERVICE_CONNECTION_FAILED, message = response.errorMessage)
        }
        return response.data!!
    }

    override fun findByIds(ids: List<String>): List<ServiceConnection> {
        val response = flowServiceConnectionApi.findByIds(getHeaders(), ids.joinToString(","))
        checkExists(response) {
            ErrorCode.AS_HTTP_CALL_ERROR
        }
        checkBiz(response.successful) {
            ErrorEntry(code = ErrorCode.AS_RES_CALL_FLOW_SERVICE_CONNECTION_FAILED, message = response.errorMessage)
        }
        return response.data!!
    }

    private fun getHeaders(): Map<String, String> {
        return TokenUtil.generateAuthHeaders(flowAuthConfig.appId, flowAuthConfig.appSecret)
    }

}