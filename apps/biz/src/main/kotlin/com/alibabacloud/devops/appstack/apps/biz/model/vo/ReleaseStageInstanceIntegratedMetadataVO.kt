package com.alibabacloud.devops.appstack.apps.biz.model.vo

import com.alibabacloud.devops.appstack.libs.model.vo.ReleaseIntegratedMetadata
import com.alibabacloud.devops.appstack.libs.model.vo.VO
import com.aliyun.amp.plugin.annotation.AmpIgnore
import com.aliyun.amp.plugin.annotation.AmpParam

/**
 * <AUTHOR>
 * @date 2024-05-27
 */
data class ReleaseStageInstanceIntegratedMetadataVO(
    @field:AmpParam(description = "发布分支")
    var releaseBranch: String? = null,
    @field:AmpParam(description = "发布分支git版本")
    var releaseRevision: String? = null,
    @field:AmpParam(description = "代码仓库地址")
    var repoUrl: String? = null,
    @field:AmpParam(description = "代码仓库类型")
    val repoType: String? = null,
    @field:AmpParam(description = "变更请求")
    val changeRequests: List<ChangeRequestIntegratedMetadataVO> = emptyList()
): VO() {

    override fun convert2AliyunPopObject(convertUserId2AliyunPK: (String) -> String) {
        changeRequests.forEach {
            it.convert2AliyunPopObject(convertUserId2AliyunPK)
        }
    }

    data class ChangeRequestIntegratedMetadataVO(
        @field:AmpParam(description = "变更请求编号")
        var sn: String? = null,
        @field:AmpParam(description = "变更请求名称")
        var name: String? = null,
        @field:AmpParam(description = "变更请求分支")
        var branchName: String? = null,
        @field:AmpParam(description = "变更请求commitId")
        var commitId: String? = null,
        @field:AmpIgnore
        var ownerId: String? = null,
        @field:AmpParam(description = "变更请求拥有者的阿里云pk")
        var ownerAccountId: String? = null
    ): VO(){
        override fun convert2AliyunPopObject(convertUserId2AliyunPK: (String) -> String) {
            ownerId?.let{
                ownerAccountId = convertUserId2AliyunPK(it)
            }
            ownerId = null
        }
    }
}

fun ReleaseIntegratedMetadata.convert2VO() = ReleaseStageInstanceIntegratedMetadataVO(
    releaseBranch = this.releaseBranch,
    releaseRevision = this.releaseRevision,
    repoUrl = this.repoUrl,
    repoType = this.repoType,
    changeRequests = this.crDetailList.map { it.convert2VO() }
)

fun ReleaseIntegratedMetadata.CrDetail.convert2VO() = ReleaseStageInstanceIntegratedMetadataVO.ChangeRequestIntegratedMetadataVO(
    sn = this.sn,
    name = this.name,
    branchName = this.branchName,
    commitId = this.commitId,
    ownerId = this.ownerId,
)