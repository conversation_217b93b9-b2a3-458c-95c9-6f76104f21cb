package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.ResourceItemBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.common.filter.TraceFilter
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.FlowSelfHostItemSpec
import com.alibabacloud.devops.appstack.libs.model.vo.ResourceItemRecord
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2023-04-17 14:30
 * @version: ResourceItemBizServiceImpl, v0.1
 **/
@Service
class ResourceItemBizServiceImpl : ResourceItemBizService {

    @Autowired
    lateinit var coreFacades: CoreFacades

    override fun findPaginated(
        poolName: String,
        instanceName: String,
        search: String?,
        current: Long,
        pageSize: Long
    ): Pagination<ResourceItemRecord> {
        return coreFacades.resourceItemFacade.findPaginated(poolName, instanceName, search, current, pageSize)
    }

    override fun updateHostSn(): Boolean {
        logger.info("updateHostSn orgId: ${AuthThreadContext.getTenant()} userId: ${AuthThreadContext.getUserId()}")
        val ctx = AuthThreadContext.export()
        GlobalScope.launch {
            // prepare context
            AuthThreadContext.replaceAll(ctx)
            MDC.put(TraceFilter.TRACE_CONTEXT_KEY, AuthThreadContext.getTraceId())

            val resourceItems = coreFacades.resourceItemFacade.findAll()
            resourceItems.forEach { resourceItem ->
                // Flow主机更新后，sn更新为 s-{sn}
                val spec = resourceItem.itemSpec as? FlowSelfHostItemSpec
                spec?.let {
                    if (!spec.sn.startsWith("s-")) {
                        spec.sn = "s-" + spec.sn
                    }
                    // 后续根据性能考虑是否改为批量更新
                    try {
                        logger.info("updateHostSn begin orgId: ${AuthThreadContext.getTenant()} userId: ${AuthThreadContext.getUserId()} resourceSn: ${resourceItem.sn} specSn: ${spec.sn}")
                        coreFacades.resourceItemFacade.updateBySn(resourceItem)
                    } catch (e: Throwable) {
                        logger.error("updateHostSn error orgId: ${AuthThreadContext.getTenant()} userId: ${AuthThreadContext.getUserId()} resourceSn: ${resourceItem.sn} specSn: ${spec.sn}", e)
                    } finally {
                        logger.info("updateHostSn end orgId: ${AuthThreadContext.getTenant()} userId: ${AuthThreadContext.getUserId()} resourceSn: ${resourceItem.sn} specSn: ${spec.sn}")
                    }
                }
            }
        }
        return true
    }
}