package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.ServiceConnectionBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ConnectionType
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ServiceConnection
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ServiceConnectionRegion
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-07-08 14:15
 * @version: ServiceConnectionBizApi, v0.1
 **/
@Tag(name = "ServiceConnection", description = "服务连接相关API")
@RestController
@RequestMapping("api/v1")
open class ServiceConnectionBizApi {

    @Autowired
    lateinit var serviceConnectionBizService: ServiceConnectionBizService

    @Operation(summary = "查找可用的服务连接")
    @GetMapping("/serviceConnection:search")
    fun findAllServiceConnection(@RequestParam connectionType: ConnectionType): Response<List<ServiceConnection>> {
        return Response.success(serviceConnectionBizService.findAll(connectionType))
    }

    @Operation(summary = "查找服务连接下的可用区域")
    @GetMapping("/serviceConnection/{id}:regions")
    fun findRegionsById(@PathVariable id: Long): Response<List<ServiceConnectionRegion>> {
        return Response.success(serviceConnectionBizService.findRegionsById(id))
    }

}