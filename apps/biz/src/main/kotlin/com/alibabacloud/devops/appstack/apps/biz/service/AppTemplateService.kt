package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfigSyncStatus

/**
 * <AUTHOR>
 * @date 2024-02-15
 */
interface AppTemplateService {

    fun find(templateName: String): AppTemplate

    fun findPresetPaginated(search: String, current: Long, pageSize: Long): Pagination<AppTemplate>

    fun findPresetConfig(templateName: String, type: AppTemplateConfig.TypeEnum): AppTemplateConfig?

    fun bindAppTemplateToApp(templateName: String, appName: String)

    fun findAppTemplateNameBindedByApp(appName: String): String?

    fun findAppTemplateBindedByAppList(appNames: List<String>): Map<String, AppTemplate>

    fun unbindAppTemplateFromApp(templateName: String, appName: String)

    fun findConfigSyncStatus(
        templateName: String,
        configType: AppTemplateConfig.TypeEnum,
        configInstanceName: String,
        appName: String
    ): AppTemplateConfigSyncStatus?

    fun initSyncStatus(
        appTemplateName: String,
        appTemplateConfigType: AppTemplateConfig.TypeEnum,
        appTemplateConfigInstanceName: String,
        appName: String
    )

    fun updateTemplateSyncStatus(
        templateName: String,
        configType: AppTemplateConfig.TypeEnum,
        configInstanceName: String,
        appName: String,
        templateSyncStatus: AppTemplateConfigSyncStatus.TemplateSyncStatus
    )

    fun updateInstanceUpdateStatus(
        templateName: String,
        configType: AppTemplateConfig.TypeEnum,
        configInstanceName: String,
        appName: String,
        lastInstanceUpdateSeqNo: Long,
        instanceUpdateStatus: AppTemplateConfigSyncStatus.InstanceUpdateStatus
    )
}