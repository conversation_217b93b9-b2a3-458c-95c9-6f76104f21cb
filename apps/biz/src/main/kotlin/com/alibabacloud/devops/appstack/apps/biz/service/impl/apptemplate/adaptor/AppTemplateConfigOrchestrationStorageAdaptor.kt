package com.alibabacloud.devops.appstack.apps.biz.service.impl.apptemplate.adaptor

import com.alibabacloud.devops.appstack.apps.biz.model.ext.toModel
import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateConfigPO
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateConfigStorageAdaptor
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateOrchestrationConfigBizService
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil
import com.alibabacloud.devops.appstack.libs.common.util.LocaleUtil
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.OrchestrationConfiguration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Component
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Placeholder
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.template.AppTemplateBuildInOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.template.PresetOrgOrchestrationTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.BranchInfo
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.request.AppTemplateOrchestrationCreateRequest
import com.alibabacloud.devops.appstack.libs.model.request.AppTemplateOrchestrationUpdateRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.AbstractConfigurationUpsertRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.OrchestrationConfigurationUpsertRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateConfigRequest
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR> <EMAIL>
 * @version : AppTemplateConfigOrchestrationStorageAdaptor, v0.1
 * @date : 2023-10-18 20:22
 **/
@Service
class AppTemplateConfigOrchestrationStorageAdaptor : AppTemplateConfigStorageAdaptor<OrchestrationConfiguration>() {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var appTemplateOrchestrationConfigBizService: AppTemplateOrchestrationConfigBizService

    @Autowired
    lateinit var auditLogService: AuditLogService

    override fun insert(appTemplate: AppTemplate, request: UpsertAppTemplateConfigRequest): AppTemplateConfig {
        val configuration = request.configuration as OrchestrationConfigurationUpsertRequest
        if (configuration.orchestrations.isNotEmpty()) {
            val pair = coreFacades.appTemplateOrchestrationFacade.init(
                appTemplate.name, AppTemplateOrchestrationCreateRequest(
                    name = appTemplate.name,
                    description = null,
                    suitableResourceTypes = configuration.orchestrations.flatMap { it.suitableResourceTypes }.distinct()
                )
            )
            val orcList = pair.first
            processOrchestrationFields(
                configuration,
                OrchestrationConfiguration(orchestrations = orcList.toMutableList())
            )
            val placeholderList = mutableListOf<Placeholder>()
            val componentList = mutableListOf<Component>()
            val groupNameMap = mutableMapOf<Long, String>()

            configuration.orchestrations.forEach {
                it as AppTemplateBuildInOrchestration
                placeholderList.addAll(it.placeholderList)
                componentList.addAll(it.componentList)
                groupNameMap.putAll(it.groupNameMap)
            }

            if (componentList.isNotEmpty() || placeholderList.isNotEmpty()) {
                val (_, revision) = coreFacades.appTemplateOrchestrationFacade.update(
                    appTemplate.name, AppTemplateOrchestrationUpdateRequest(
                        name = appTemplate.name,
                        componentList = componentList,
                        placeholderList = placeholderList,
                        groupNameMap = groupNameMap,
                    ).apply {
                        // 初始化时，使用初始化后的第一个版本号
                        this.fromRevisionSha = pair.second.sha
                        this.branchName = configuration.branchName
                        this.message = configuration.message
                    }
                )

                appTemplateOrchestrationConfigBizService.bindLabels(appTemplate.name, revision.sha, configuration.labels)
            }
        }
        return super.insert(appTemplate, request)
    }

    private fun processOrchestrationFields(
        targetConfigRequest: OrchestrationConfigurationUpsertRequest,
        previousConfig: OrchestrationConfiguration
    ) {
        val previousResourceTypes = previousConfig.orchestrations.flatMap {
            it as AppTemplateBuildInOrchestration
            it.componentList.map { it.type } + it.placeholderList.map { it.rsType }
        }.toSet()

        // 应对编排库中没有内容时，用户添加了一种编排类型，此时需要初始化一些占位的内容
        targetConfigRequest.orchestrations.forEach { orc ->
            orc as AppTemplateBuildInOrchestration
            orc.suitableResourceTypes.firstOrNull()?.let {
                if ((orc.componentList.isEmpty() && orc.placeholderList.isEmpty()) && !previousResourceTypes.contains(it)) {
                    when (it) {
                        ResourceTypeEnum.KUBERNETES -> {
                            orc.placeholderList.add(
                                Placeholder(
                                    name = "appName",
                                    description = "appName",
                                    type = Placeholder.Type.STRING,
                                    value = "APPSTACK_APP_NAME",
                                    overridable = false,
                                    valueSource = Placeholder.ValueSource.VARIABLE,
                                    predefined = true,
                                    rsType = it
                                )
                            )
                        }
                        ResourceTypeEnum.HOST -> {
                            val template: PresetOrgOrchestrationTemplate = jacksonObjectMapper().readValue(
                                javaClass.getResource("/preset/apptemplate/org_orchestration_host_template_${LocaleUtil.getLanguage()}.json").readText()
                            )
                            orc.placeholderList.addAll(template.placeholderList)
                            orc.componentList = template.componentList
                        }
                        else -> {}
                    }
                }
            }
        }
    }

    override fun update(
        appTemplate: AppTemplate,
        previous: AppTemplateConfig,
        request: UpsertAppTemplateConfigRequest
    ): AppTemplateConfig {
        request.configuration?.let {
            val appTemplateName = previous.appTemplateName
            val configuration = request.configuration as OrchestrationConfigurationUpsertRequest
            processOrchestrationFields(
                configuration,
                previous.configuration as OrchestrationConfiguration
            )
            val placeholderList = mutableListOf<Placeholder>()
            val componentList = mutableListOf<Component>()
            val groupNameMap: MutableMap<Long, String> = mutableMapOf()

            configuration.orchestrations.forEach {
                it as AppTemplateBuildInOrchestration
                placeholderList.addAll(it.placeholderList)
                componentList.addAll(it.componentList)
                groupNameMap.putAll(it.groupNameMap)
            }

            val (_, revision) = coreFacades.appTemplateOrchestrationFacade.update(appTemplateName,
                AppTemplateOrchestrationUpdateRequest(
                    name = appTemplateName,
                    componentList = componentList,
                    placeholderList = placeholderList,
                    groupNameMap = groupNameMap,
                ).apply {
                    this.fromRevisionSha = configuration.fromRevisionSha
                    this.branchName = configuration.branchName
                    this.message = configuration.message
                }
            )
            appTemplateOrchestrationConfigBizService.bindLabels(appTemplateName, revision.sha, configuration.labels)
        }
        return super.update(appTemplate, previous, request)
    }

    override fun delete(appTemplate: AppTemplate, appTemplateConfigSn: String) {
        coreFacades.appTemplateOrchestrationFacade.deleteAll(appTemplate.name)
        super.delete(appTemplate, appTemplateConfigSn)
    }

    override fun convertToModel(appTemplateConfigPO: AppTemplateConfigPO): AppTemplateConfig {
        val dto =
            JacksonUtil.jacksonObjectMapper().readValue<OrchestrationConfigDTO>(appTemplateConfigPO.configuration!!)
        val configuration = when (dto) {
            is OrchestrationConfigV1DTO -> {
                var result: Triple<List<AppTemplateBuildInOrchestration>, Revision?, BranchInfo?> =
                    Triple(listOf(), null, null)
                try {
                    result = coreFacades.appTemplateOrchestrationFacade.findAll(appTemplateConfigPO.appTemplateName!!)
                } catch (e: Throwable) {
                    // 没找到编排库
                }
                OrchestrationConfiguration(
                    orchestrations = result.first.toMutableList(),
                    revision = result.second,
                    branchInfo = result.third,
                )
            }
            else -> TODO()
        }
        return appTemplateConfigPO.toModel(configuration)
    }

    override fun convertConfiguration(appTemplateName: String, configuration: AbstractConfigurationUpsertRequest): Any {
        val config = configuration as OrchestrationConfigurationUpsertRequest
        return OrchestrationConfigV1DTO(
            templateSnList = config.orchestrations.map { it.sn }
        )
    }

    override fun configType(): AppTemplateConfig.TypeEnum = AppTemplateConfig.TypeEnum.Orchestration

    override fun logAudit(appTemplate: AppTemplate) {
        auditLogService.commonLog(OrgEventType.APP_TEMPLATE_MODIFY_ORCHESTRATION_AUDIT, appTemplate.name, appTemplate.displayName)
    }

    @JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "version",
        visible = true
    )
    @JsonSubTypes(
        JsonSubTypes.Type(value = OrchestrationConfigV1DTO::class, name = "v1"),
    )
    interface OrchestrationConfigDTO {
        val version: String
    }

    data class OrchestrationConfigV1DTO(
        override val version: String = "v1",
        val templateSnList: List<String>,
    ) : OrchestrationConfigDTO
}