package com.alibabacloud.devops.appstack.apps.biz.api.rest.hook.aonecode

import com.alibabacloud.devops.appstack.apps.biz.service.AuditItemBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.model.`do`.audit.AuditItem
import com.alibabacloud.devops.appstack.libs.model.`do`.codereview.AoneCodeReviewCallBack
import com.alibabacloud.devops.appstack.libs.model.request.audit.splc.AuditCallbackRequest
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-11-28 14:22
 * @version: AoneCodeReviewCallbackApi, v0.1
 **/
@Tag(name = "AoneCodeReviewCallbackBizApi", description = "Aone CodeReview 回调 API")
@RestController
@RequestMapping("/callbackapi/aone/code/review")
@Slf4k
class AoneCodeReviewCallbackBizApi {

    @Autowired
    lateinit var auditItemBizService: AuditItemBizService


    @PostMapping("/callback")
    fun callback(
        @RequestParam("orgId") orgId: String,
        @RequestParam("userId") userId: String,
        @RequestParam("refSn") refSn: String,
        @RequestBody aoneCodeReviewCallBack: AoneCodeReviewCallBack
    ) {
        logger.info("aone code-review callback orgId:{}, userId:{}, refSn: {}, requestBody:{}", orgId, userId, refSn, jacksonObjectMapper().writeValueAsString(aoneCodeReviewCallBack))
        AuthThreadContext.setTenant(orgId)
        AuthThreadContext.setUserId(userId)
        auditItemBizService.callback(
            AuditCallbackRequest(
                type = AuditItem.Type.CODE_REVIEW,
                refType = AuditItem.RefType.CR,
                refSn = refSn,
                context = aoneCodeReviewCallBack
            )
        )
    }
}