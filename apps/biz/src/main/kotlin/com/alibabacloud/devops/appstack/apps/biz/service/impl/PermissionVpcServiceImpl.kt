package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibaba.aone.framework.tbs.app.sdk.org.OrgService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.constant.Constant
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.exception.IamNotFoundException
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import com.alibaba.aone.framework.tbs.app.sdk.permission.PermissionService as V5PermissionService

/**
 * @author: <EMAIL>
 * @date: 2021-11-02 15:37
 * @version: PermissionServiceImpl, v0.1
 **/
@Slf4k
@Service
@ConditionalOnProperty(name = ["devops.iam.environment"], havingValue = "vpc", matchIfMissing = false)
open class PermissionVpcServiceImpl : PermissionServiceImpl() {

    companion object {
        val SERVICE_CONNECTION_LIST = listOf(Action.ORG_SERVICE_CONNECTION_CREATE, Action.ORG_SERVICE_CONNECTION_MANAGE)
    }

    override fun findOrgActionAllowed(): List<String> {
        checkPermissionEnable()
        checkIamVersion()

        val v5permissions = OrgService.listMemberPermissions(AuthUtil.getTenant(), AuthUtil.getUserId(), null).toSet()
        val actionSet = Action.filter(ProtocolType.AppStack).map { it.v5code }.intersect(v5permissions)
            .map { Action.v5CodeOf(it!!) }.toMutableSet()
        Action.ORG_MANAGE_MAP.forEach { manageAction, set ->
            if (actionSet.contains(manageAction)) {
                actionSet.addAll(set)
            }
        }
        return actionSet.map { it.code }
    }

    private fun checkIamVersion() {
        try {
            val model = iamClient.modelApi.find(ProtocolType.AppStack)
            if (Constant.VERSION != model.version) {
                iamClient.rebuildConfig()
            }
        } catch (e: IamNotFoundException) {
            logger.warn("org [{}] does not init permission data", AuthUtil.getTenant())
            // 开启权限
            iamClient.initConfig()
        }
    }

    override fun checkServiceConnectionPermission(vararg actions: Action) {
        actions.forEach {
            if (!SERVICE_CONNECTION_LIST.contains(it))
                throw BizException(ErrorCode.AS_PERMISSION_DENIED)
        }
        actions.forEach { action ->
            val permission = V5PermissionService.checkOrganizationPermission(
                AuthUtil.getTenant(),
                AuthUtil.getUserId(),
                action.v5code,
            )
            if (permission.success) {
                return
            }
        }
        throw BizException(ErrorCode.AS_PERMISSION_DENIED)
    }
}