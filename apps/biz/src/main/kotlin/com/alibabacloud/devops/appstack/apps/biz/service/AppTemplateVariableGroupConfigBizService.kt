package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.VariableGroupConfiguration
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.request.VariableTemplateBatchUpdateRequest
import com.alibabacloud.devops.appstack.libs.model.vo.AppTemplateVariableGroupDiffDTO


/**
 * <AUTHOR>
 * @date 2023-10-26
 */
interface AppTemplateVariableGroupConfigBizService {

    fun findByRevison(appTemplateName: String, sha: String): VariableGroupConfiguration

    fun listRevisionPaginated(appTemplateName: String, current: Long, pageSize: Long): Pagination<Revision>

    fun compareRevision(appTemplateName: String, beforeRevisionSha: String, afterRevisionSha: String): AppTemplateVariableGroupDiffDTO

    fun batchUpdate(appTemplateName: String, request: VariableTemplateBatchUpdateRequest)
}

