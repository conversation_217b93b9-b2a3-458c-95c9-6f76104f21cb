package com.alibabacloud.devops.appstack.apps.biz.config

import org.springdoc.core.SpringDocProviders
import org.springdoc.core.providers.*
import org.springdoc.webmvc.core.SpringWebMvcProvider
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Lazy
import java.util.*

/**
 * <AUTHOR>
 * @date 2024-05-21
 */
@Configuration
@ConditionalOnProperty(name = ["devops.nezha-doc"], havingValue = "true")
open class NezhaDocConfiguration {

    @Bean
    open fun nezhaDocSpringWebMvcProvider(): SpringWebMvcProvider {
        return NezhaDocSpringWebMvcProvider()
    }

    @Bean
    @ConditionalOnMissingBean
    @Lazy(false)
    open fun springDocProviders(
        actuatorProvider: Optional<ActuatorProvider?>?,
        springCloudFunctionProvider: Optional<CloudFunctionProvider?>?,
        springSecurityOAuth2Provider: Optional<SecurityOAuth2Provider?>?,
        repositoryRestResourceProvider: Optional<RepositoryRestResourceProvider?>?,
        routerFunctionProvider: Optional<RouterFunctionProvider?>?,
        springWebProvider: Optional<SpringWebProvider?>?,
        webConversionServiceProvider: Optional<WebConversionServiceProvider?>?,
        objectMapperProvider: ObjectMapperProvider?
    ): SpringDocProviders? {
        return SpringDocProviders(
            actuatorProvider,
            springCloudFunctionProvider,
            springSecurityOAuth2Provider,
            repositoryRestResourceProvider,
            routerFunctionProvider,
            springWebProvider,
            webConversionServiceProvider,
            objectMapperProvider
        )
    }
}