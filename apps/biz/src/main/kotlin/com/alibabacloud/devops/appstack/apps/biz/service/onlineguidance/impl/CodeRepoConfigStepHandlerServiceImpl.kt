package com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.impl

import com.alibabacloud.devops.appstack.apps.biz.model.State
import com.alibabacloud.devops.appstack.apps.biz.model.Step
import com.alibabacloud.devops.appstack.apps.biz.model.StepProgress
import com.alibabacloud.devops.appstack.apps.biz.service.AppBizService
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.StepHandlerService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.app.FunctionPoint
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR> liyebin.lyb
 * @date : 2022/8/26 2:44 PM
 */
@Service
@Slf4k
class CodeRepoConfigStepHandlerServiceImpl : StepHandlerService {

    @Autowired
    lateinit var coreFacades: CoreFacades

    override fun handle(appName: String, step: Step): StepProgress {
        val appCodeRepos = coreFacades.appCodeRepoFacade.findAll(appName)
        val state = if (appCodeRepos.isNotEmpty()) {
            State.FINISH
        } else {
            State.INIT
        }
        return StepProgress(
            needUpdate = step.state != state,
            step = Step(
                name = step.name,
                state = state
            )
        )
    }
}