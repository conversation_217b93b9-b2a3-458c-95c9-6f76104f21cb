package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.CodeProxyBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ShowType
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.model.`do`.app.ConnectionConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.RepoContext
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.response.cr.GitBranchInfo
import com.alibabacloud.devops.appstack.libs.model.response.cr.GitProject
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @date 2022-11-26
 */
@Tag(name = "ProxyCodeApi", description = "代理代码仓库相关API")
@RestController
@RequestMapping("/api/v1/proxy/code")
open class ProxyCodeApi {

    @Autowired
    lateinit var codeProxyBizService: CodeProxyBizService

    @Operation(summary = "查询代码库列表")
    @PostMapping("/projects:search")
    fun searchProjects(
        @RequestBody request: SearchRequest
    ): Response<List<GitProject>> {
        return Response.success(
            codeProxyBizService.searchProjects(
                request.repoContext,
                request.connectionConfig,
                request.search
            )
        )
    }

    @Operation(summary = "查询代码库分支")
    @PostMapping("/project/branches")
    fun searchProjectBranches(
        @RequestBody request: SearchRequest
    ): Response<List<GitBranchInfo>> {
        return Response.success(
            codeProxyBizService.searchBranches(
                request.repoContext,
                request.connectionConfig,
                request.search
            )
        )
    }

    @Operation(summary = "获取代码库分支")
    @PostMapping("/project/branches:find")
    fun findBranch(
        @RequestBody request: FindBranchRequest,
    ): Response<GitBranchInfo> {
        try {
            return Response.success(
                codeProxyBizService.findBranch(
                    repoContext = request.repoContext,
                    connectionConfig = request.connectionConfig,
                    branchName = request.branchName,
                )
            )
        } catch (e: BizException) {
            e.errorEntry.showType = ShowType.SILENT
            throw e
        }
    }

}


data class SearchRequest(
    val repoContext: RepoContext,
    val connectionConfig: ConnectionConfig,
    val search: String = ""
)

data class FindBranchRequest(
    val repoContext: RepoContext,
    val connectionConfig: ConnectionConfig,
    val branchName: String,
)