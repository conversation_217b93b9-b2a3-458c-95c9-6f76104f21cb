package com.alibabacloud.devops.appstack.apps.biz.aspect


import com.alibabacloud.devops.appstack.apps.biz.annotation.ApiMetricCounter
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.reflect.MethodSignature
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * @author: <EMAIL>
 * @date: 2023-03-08 14:39
 * @version: ApiMetricCounterAspect, v0.1
 **/
@Aspect
@Slf4k
@Component
open class ApiMetricCounterAspect {

    @Autowired
    private lateinit var meterRegistry: MeterRegistry

    @Around("@annotation(com.alibabacloud.devops.appstack.apps.biz.annotation.ApiMetricCounter)")
    fun onSuccessfulInvocation(joinPoint: ProceedingJoinPoint): Any? {
        val signature = joinPoint.signature as MethodSignature
        val method = signature.method
        val metricAnnotation: ApiMetricCounter = method.getAnnotation(ApiMetricCounter::class.java)
        val methodValue = if (metricAnnotation.methodTag.isNotBlank()) {
            "NONE"
        } else {
            metricAnnotation.methodTag
        }
        val counterBuilder = Counter.builder("${metricAnnotation.metricPrefix}_${metricAnnotation.metricName}")
            .tag("method", methodValue)
        var counterResult = "success"

        try {
            return joinPoint.proceed()
        } catch (e: Throwable) {
            counterResult = "failed"
            throw e
        } finally {
            counterBuilder.tag("result", counterResult).register(meterRegistry).increment()
        }
    }

}