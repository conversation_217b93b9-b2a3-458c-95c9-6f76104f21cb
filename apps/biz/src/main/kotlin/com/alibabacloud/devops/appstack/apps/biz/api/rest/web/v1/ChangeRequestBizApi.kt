package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.OrderEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.SortEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.ChangeRequest
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.WorkflowMetadata
import com.alibabacloud.devops.appstack.libs.model.request.cr.CreateChangeRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.QueryAppChangeRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.QueryChangeRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.TransferChangeOwnerRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.UpdateChangeRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeRequestDetailVO
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController


@Tag(name = "ChangeRequest", description = "变更相关 API")
@RestController
@RequestMapping("/api/v1")
open class ChangeRequestBizApi {

    @Autowired
    lateinit var changeRequestBizService: ChangeRequestBizService

    @Operation(summary = "创建变更")
    @PostMapping("/apps/{appName}/changeRequests")
    fun createChangeRequest(
        @PathVariable appName: String,
        @RequestBody request: CreateChangeRequest
    ): Response<ChangeRequest> {
        return Response.success(changeRequestBizService.create(appName, request))
    }

    @Operation(summary = "查看变更详情")
    @GetMapping("/apps/{appName}/changeRequests/{sn}")
    fun findChangeRequest(
        @PathVariable appName: String,
        @PathVariable sn: String
    ): Response<ChangeRequestDetailVO> {
        return Response.success(changeRequestBizService.find(appName, sn))
    }

    @Operation(summary = "修改变更")
    @PutMapping("/apps/{appName}/changeRequests/{sn}")
    fun updateChangeRequest(
        @PathVariable appName: String,
        @PathVariable sn: String,
        @RequestBody request: UpdateChangeRequest
    ): Response<ChangeRequest> {
        return Response.success(changeRequestBizService.update(appName, sn, request))
    }

    @Operation(summary = "修改变更负责人")
    @PutMapping("/apps/{appName}/changeRequests/{sn}:owner")
    fun transferOwner(
        @PathVariable appName: String,
        @PathVariable sn: String,
        @RequestBody request: TransferChangeOwnerRequest
    ): Response<ChangeRequest> {
        return Response.success(changeRequestBizService.transferOwner(appName, sn, request))
    }

    @Operation(summary = "关闭变更")
    @PutMapping("/apps/{appName}/changeRequests/{sn}:close")
    fun close(
        @PathVariable appName: String,
        @PathVariable sn: String
    ): Response<Unit> {
        return Response.success(changeRequestBizService.close(appName, sn))
    }

    @Operation(summary = "查询应用下的变更列表")
    @PostMapping("/apps/{appName}/changeRequests:search")
    fun findByAppPaginated(
        @PathVariable appName: String,
        @RequestBody request: QueryAppChangeRequest,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
        @RequestParam("orderBy", required = false, defaultValue = "gmt_create") orderBy: String,
        @RequestParam("sort", required = false, defaultValue = "desc") sort: String
    ): Response<Pagination<ChangeRequest>> {
        return Response.success(
            changeRequestBizService.findByAppPaginated(
                appName,
                request,
                current,
                pageSize,
                OrderEnum.valueOf(orderBy),
                SortEnum.valueOf(sort)
            )
        )
    }

    @Operation(summary = "查询变更发布详情")
    @GetMapping("/apps/{appName}/changeRequests/{crSn}:release_metadata")
    fun findReleaseMetadata(
        @PathVariable appName: String,
        @PathVariable crSn: String,
        @RequestParam releaseWorkflowSn: String,
    ): Response<List<WorkflowMetadata>> {
        return Response.success(changeRequestBizService.findReleaseMetadata(appName, releaseWorkflowSn, crSn))
    }

    @Operation(summary = "企业下变更列表")
    @PostMapping("/changeRequests:search")
    fun findByOrgPaginated(
        @RequestBody request: QueryChangeRequest,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
        @RequestParam("orderBy", required = false, defaultValue = "gmt_create") orderBy: String,
        @RequestParam("sort", required = false, defaultValue = "desc") sort: String
    ): Response<Pagination<ChangeRequest>> {
        return Response.success(
            changeRequestBizService.findByOrgPaginated(
                request,
                current,
                pageSize,
                OrderEnum.valueOf(orderBy),
                SortEnum.valueOf(sort)
            )
        )
    }

    @Operation(summary = "查看多个变更详情")
    @GetMapping("/apps/{appName}/changeRequests:findBySns")
    fun findBySns(
        @PathVariable appName: String,
        @RequestParam sns: String
    ): Response<List<ChangeRequestDetailVO>> {
        return Response.success(changeRequestBizService.findBySns(appName, sns))
    }
}