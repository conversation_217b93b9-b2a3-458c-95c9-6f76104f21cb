package com.alibabacloud.devops.appstack.apps.biz.model.mapper

import com.alibabacloud.devops.appstack.apps.biz.model.po.DeployFlowPO
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.core.mapper.BaseMapper
import org.apache.ibatis.annotations.Mapper

@Mapper
interface DeployFlowMapper : BaseMapper<DeployFlowPO>

fun DeployFlowMapper.find(objectType: String, objectId: String): DeployFlowPO? {
    val list = selectList(QueryWrapper<DeployFlowPO>().eq("object_type", objectType).eq("object_Id", objectId))
    if (list.isEmpty()) {
        return null
    }
    return list.first()
}

fun DeployFlowMapper.find(objectType: String, objectId: String, appName: String, envName: String): DeployFlowPO? {
    val list = selectList(
        QueryWrapper<DeployFlowPO>()
            .eq("object_type", objectType)
            .eq("object_Id", objectId)
            .eq("app_name", appName)
            .eq("env_name", envName)
    )
    if (list.isEmpty()) {
        return null
    }
    return list.first()
}

fun DeployFlowMapper.findList(changeOrderSnList: List<String>): List<DeployFlowPO> {
    return if (changeOrderSnList.isEmpty()) {
        emptyList()
    } else {
        selectList(QueryWrapper<DeployFlowPO>().`in`("change_order_sn", changeOrderSnList))
    }
}

fun DeployFlowMapper.deleteByObjectTypeAndObjectId(objectType: String, objectId: String): Int =
    delete(QueryWrapper<DeployFlowPO>().eq("object_id", objectId).eq("object_type", objectType))