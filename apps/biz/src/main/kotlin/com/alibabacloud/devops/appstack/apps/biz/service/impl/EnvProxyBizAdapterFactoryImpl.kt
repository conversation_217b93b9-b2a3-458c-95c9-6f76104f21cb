package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.EnvProxyBizAdapterFactory
import com.alibabacloud.devops.appstack.apps.biz.service.HasMetadataVO
import com.alibabacloud.devops.appstack.apps.biz.service.KindResourceService
import com.alibabacloud.devops.appstack.apps.biz.service.KindResourceService.Companion.DEFAULT_VERSION_LABEL
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.constant.K8sResourceLabelConstants
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeableEnv
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.Task
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.TaskContext
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeTaskContext
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.AdeTaskStrategy
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ade.TaskBehavior
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.Orchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppBuiltInOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import com.alibabacloud.devops.appstack.resource.manager.spring.boot.starter.ResourceManagerFacades
import com.fasterxml.jackson.module.kotlin.convertValue
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import io.fabric8.kubernetes.api.model.ConfigMap
import io.fabric8.kubernetes.api.model.GenericKubernetesResource
import io.fabric8.kubernetes.api.model.HasMetadata
import io.fabric8.kubernetes.api.model.LabelSelector
import io.fabric8.kubernetes.api.model.LabelSelectorRequirement
import io.fabric8.kubernetes.api.model.ObjectMeta
import io.fabric8.kubernetes.api.model.PersistentVolumeClaim
import io.fabric8.kubernetes.api.model.Pod
import io.fabric8.kubernetes.api.model.Secret
import io.fabric8.kubernetes.api.model.apps.DaemonSet
import io.fabric8.kubernetes.api.model.apps.Deployment
import io.fabric8.kubernetes.api.model.apps.ReplicaSet
import io.fabric8.kubernetes.api.model.apps.StatefulSet
import io.fabric8.kubernetes.api.model.batch.v1.CronJob
import io.fabric8.kubernetes.api.model.batch.v1.Job
import io.fabric8.kubernetes.api.model.networking.v1.Ingress
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
@Slf4k
class EnvProxyBizAdapterFactoryImpl : EnvProxyBizAdapterFactory {

    @Autowired
    lateinit var ade1KindResourceService: Ade1KindResourceService

    @Autowired
    lateinit var ade1DeploymentResourceService: Ade1DeploymentResourceService

    @Autowired
    lateinit var ade2KindResourceService: Ade2KindResourceService

    @Autowired
    lateinit var ade2WorkloadResourceService: Ade2WorkloadResourceService

    @Autowired
    lateinit var ade2DeploymentResourceService: Ade2DeploymentResourceService

    @Autowired
    lateinit var commonResourceService: CommonResourceService

    override fun get(engineType: ChangeableEnv.EngineType, kind: String): KindResourceService {
        return when (engineType) {
            ChangeableEnv.EngineType.Ade1 -> if (kind == TaskContext.Kind.Deployment.name) {
                ade1DeploymentResourceService
            } else {
                ade1KindResourceService
            }

            ChangeableEnv.EngineType.Ade2 -> when (kind) {
                TaskContext.Kind.Deployment.name -> ade2DeploymentResourceService
                TaskContext.Kind.ReplicaSet.name, TaskContext.Kind.StatefulSet.name, TaskContext.Kind.DaemonSet.name, TaskContext.Kind.Job.name, TaskContext.Kind.CronJob.name -> ade2WorkloadResourceService
                else -> ade2KindResourceService
            }
            else -> commonResourceService
        }
    }
}

@Service
open class Ade1KindResourceService : KindResourceService {
    override fun countGroupByKind(changeOrder: ChangeOrder): Map<String, Int> {
        return changeOrder.getAllTask().groupBy {
            val taskContext = jacksonObjectMapper().readValue<AdeTaskContext>(it.contextContent)
            taskContext.kind
        }.mapValues { entry ->
            entry.value.size
        }
    }

    override fun convertHasMetadataVOList(
        changeOrder: ChangeOrder,
        kind: String,
        envName: String,
        resourceList: List<HasMetadata>,
    ): List<HasMetadataVO> {
        val list = mutableListOf<HasMetadataVO>()
        val tasks = changeOrder.jobs.flatMap { it.stages }.flatMap { it.tasks }
        val appName = changeOrder.appName
        resourceList.forEach { resource ->
            val exist = tasks.filter {
                val taskContext = jacksonObjectMapper().readValue<AdeTaskContext>(it.contextContent)
                taskContext.kind == kind
            }.firstOrNull {
                isMatched(
                    task = it,
                    hasMetadata = resource,
                    appName = appName,
                    envName = envName,
                    kind = kind
                )
            }
            list.add(
                HasMetadataVO(
                    data = resource,
                    state = if (null != exist) HasMetadataVO.HasMetadataVOState.MATCHED else HasMetadataVO.HasMetadataVOState.EXTRA,
                    deliveries = if (null != exist) exist.deliveries ?: "" else "",
                    relatedResource = findRelatedResource(
                        appName = appName,
                        envName = envName,
                        resource = resource
                    ),
                    displayName = exist?.name ?: resource.metadata.name
                )
            )
        }

        tasks.filter {
            val taskContext = jacksonObjectMapper().readValue<AdeTaskContext>(it.contextContent)
            taskContext.kind == kind
        }.filter {
            resourceList.none { resource ->
                isMatched(
                    task = it,
                    hasMetadata = resource,
                    appName = appName,
                    envName = envName,
                    kind = kind
                )
            }
        }.forEach {
            val taskContext = jacksonObjectMapper().readValue<AdeTaskContext>(it.contextContent)
            val objectMeta = ObjectMeta()
            objectMeta.name = taskContext.name
            val resource: HasMetadata? = when (kind) {
                TaskContext.Kind.Deployment.name -> Deployment().apply { metadata = objectMeta }
                TaskContext.Kind.ConfigMap.name -> ConfigMap().apply { metadata = objectMeta }
                TaskContext.Kind.Secret.name -> Secret().apply { metadata = objectMeta }
                TaskContext.Kind.Service.name -> io.fabric8.kubernetes.api.model.Service()
                    .apply { metadata = objectMeta }

                TaskContext.Kind.Ingress.name -> Ingress().apply { metadata = objectMeta }
                TaskContext.Kind.PersistentVolumeClaim.name -> PersistentVolumeClaim().apply { metadata = objectMeta }
                else -> null
            }
            if (null != resource) {
                list.add(
                    HasMetadataVO(
                        data = resource,
                        state = HasMetadataVO.HasMetadataVOState.LOST,
                        deliveries = it.deliveries ?: "",
                        relatedResource = findRelatedResource(
                            appName = appName,
                            envName = envName,
                            resource = resource,
                        ),
                        displayName = resource.metadata.name
                    )
                )
            }
        }
        return list
    }

    override fun isMatched(
        task: Task,
        hasMetadata: HasMetadata,
        appName: String,
        envName: String,
        kind: String,
    ): Boolean {
        val taskContext = jacksonObjectMapper().readValue<AdeTaskContext>(task.contextContent)
        return hasMetadata.metadata.name == taskContext.name
                && taskContext.kind == kind
                && hasMetadata.metadata.namespace == taskContext.namespace
    }

}

@Service
open class Ade1DeploymentResourceService : Ade1KindResourceService() {

    @Autowired
    lateinit var resourceManagerFacades: ResourceManagerFacades

    override fun isMatched(
        task: Task,
        hasMetadata: HasMetadata,
        appName: String,
        envName: String,
        kind: String,
    ): Boolean {
        val taskContext = jacksonObjectMapper().readValue<AdeTaskContext>(task.contextContent)
        return hasMetadata.metadata.labels["devops.aliyun.com/app-name"] == appName && hasMetadata.metadata.labels["devops.aliyun.com/env-name"] == envName && hasMetadata.metadata.namespace == taskContext.namespace
    }

    override fun findRelatedResource(
        appName: String,
        envName: String,
        resource: HasMetadata,
    ): List<HasMetadata> {
        val metadata = resource.metadata
        return if (null != metadata.namespace && null != metadata.name) {
            resourceManagerFacades.envProxyFacade.findAllPodByDeployment(
                appName,
                envName,
                metadata.namespace,
                metadata.name
            ).map {
                val versionValue = it.metadata.labels.getValue(K8sResourceLabelConstants.POD_REVISION)
                if (StringUtils.isNotBlank(versionValue)) {
                    it.metadata.labels[DEFAULT_VERSION_LABEL] = versionValue
                }
                it
            }
        } else {
            emptyList()
        }
    }
}

@Service
open class Ade2KindResourceService : KindResourceService {
    override fun countGroupByKind(changeOrder: ChangeOrder): Map<String, Int> {
        return changeOrder.getAllTask().filter {
            val strategy = jacksonObjectMapper().readValue<AdeTaskStrategy>(it.strategyContent)
            strategy.behavior == TaskBehavior.Upsert
        }.groupBy {
            val taskContext = jacksonObjectMapper().readValue<AdeTaskContext>(it.contextContent)
            taskContext.kind
        }.mapValues { entry ->
            entry.value.size
        }
    }

    override fun convertHasMetadataVOList(
        changeOrder: ChangeOrder,
        kind: String,
        envName: String,
        resourceList: List<HasMetadata>,
    ): List<HasMetadataVO> {
        val resourceMap = resourceList.filter { it.kind == kind }.associateBy {
            // name 情况比较复杂（滚动/分批，oam applicationName 修复等多种因素，比较难计算），用 version 替代
            val version = it.metadata.labels?.get(K8sResourceLabelConstants.VERSION) ?: ""
            val name = it.metadata.labels?.get(K8sResourceLabelConstants.OAM_COMPONENT) ?: it.metadata.name
            Triple(it.kind, name, version)
        }
        val taskMap = changeOrder.getAllTask().filter {
            val strategy = jacksonObjectMapper().readValue<AdeTaskStrategy>(it.strategyContent)
            strategy.behavior == TaskBehavior.Upsert
        }.mapNotNull {
            val taskContext = jacksonObjectMapper().readValue<AdeTaskContext>(it.contextContent)
            if (taskContext.kind == kind) {
                Triple(taskContext.kind, taskContext.name, taskContext.changeOrderVersion) to it
            } else {
                return@mapNotNull null
            }
        }.toMap()

        val extra = (resourceMap.keys - taskMap.keys).map { key ->
            val resource = resourceMap[key]!!
            HasMetadataVO(
                data = resource,
                state = HasMetadataVO.HasMetadataVOState.EXTRA,
                deliveries = "",
                relatedResource = findRelatedResource(
                    appName = changeOrder.appName,
                    envName = envName,
                    resource = resource,
                ),
                displayName = resource.metadata.name
            )
        }

        val lost = (taskMap.keys - resourceMap.keys).mapNotNull { key ->
            val task = taskMap[key]!!
            val taskContext = jacksonObjectMapper().readValue<AdeTaskContext>(task.contextContent)
            val objectMeta = ObjectMeta().apply { name = taskContext.name }
            when (kind) {
                TaskContext.Kind.Deployment.name -> Deployment().apply { metadata = objectMeta }
                TaskContext.Kind.ConfigMap.name -> ConfigMap().apply { metadata = objectMeta }
                TaskContext.Kind.Secret.name -> Secret().apply { metadata = objectMeta }
                TaskContext.Kind.Service.name -> io.fabric8.kubernetes.api.model.Service()
                    .apply { metadata = objectMeta }

                TaskContext.Kind.Ingress.name -> Ingress().apply { metadata = objectMeta }
                TaskContext.Kind.PersistentVolumeClaim.name -> PersistentVolumeClaim().apply { metadata = objectMeta }
                else -> return@mapNotNull null
            }.let {
                HasMetadataVO(
                    data = it,
                    state = HasMetadataVO.HasMetadataVOState.LOST,
                    deliveries = task.deliveries ?: "",
                    relatedResource = emptyList(),
                    displayName = it.metadata.name
                )
            }
        }

        val matched = resourceMap.keys.intersect(taskMap.keys).map { key ->
            val resource = resourceMap[key]!!
            val task = taskMap[key]!!
            HasMetadataVO(
                data = resource,
                state = HasMetadataVO.HasMetadataVOState.MATCHED,
                deliveries = task.deliveries ?: "",
                relatedResource = findRelatedResource(
                    appName = changeOrder.appName,
                    envName = envName,
                    resource = resource,
                ),
                displayName = resource.metadata.name
            )
        }
        return matched + lost + extra
    }

    override fun isMatched(
        task: Task,
        hasMetadata: HasMetadata,
        appName: String,
        envName: String,
        kind: String,
    ): Boolean {
        val taskContext = jacksonObjectMapper().readValue<AdeTaskContext>(task.contextContent)
        return hasMetadata.metadata.name == taskContext.name
                && taskContext.kind == kind
                && hasMetadata.metadata.namespace == taskContext.namespace
    }

}

@Service
open class Ade2DeploymentResourceService : Ade2KindResourceService() {
    @Autowired
    lateinit var resourceManagerFacades: ResourceManagerFacades

    override fun findRelatedResource(appName: String, envName: String, resource: HasMetadata): List<HasMetadata> {
        return resourceManagerFacades.envProxyFacade.findAllPodByDeployment(
            appName,
            envName,
            resource.metadata.namespace,
            resource.metadata.name
        )
    }
}

@Deprecated(message = "实现有缺陷，通过label查找工作负载不准确，未来做观测的时候要能精准匹配，现在先作为一个兜底实现吧")
@Service
open class Ade2WorkloadResourceService : Ade2KindResourceService() {

    @Autowired
    lateinit var resourceManagerFacades: ResourceManagerFacades

    override fun findRelatedResource(appName: String, envName: String, resource: HasMetadata): List<HasMetadata> {
        val metadata = resource.metadata
        return if (null != metadata.namespace && null != metadata.name) {
            val labelSelector = when (resource) {
                is ReplicaSet -> resource.spec.selector
                is StatefulSet -> resource.spec.selector
                is DaemonSet -> resource.spec.selector
                is Job -> resource.spec.selector
                is CronJob -> {
                    val jobList = resourceManagerFacades.envProxyFacade.findJobListByOwnerUid(
                        appName,
                        envName,
                        metadata.namespace,
                        metadata.uid
                    )
                    LabelSelector(listOf(LabelSelectorRequirement("controller-uid", "In", jobList.map { it.metadata.uid })), emptyMap())
                }
                is GenericKubernetesResource -> {
                    try {
                        (resource.additionalProperties["spec"] as Map<String, Any>)["selector"]?.let {
                            jacksonObjectMapper().convertValue<LabelSelector>(it)
                        }
                    } catch (e: Exception) {
                        null
                    }
                }

                else -> null
            }
            labelSelector?.let {
                getPodByLabelSelector(
                    appName,
                    envName,
                    metadata.namespace,
                    it,
                )
            } ?: emptyList()
        } else {
            emptyList()
        }
    }

    private fun getPodByLabelSelector(
        appName: String,
        envName: String,
        namespace: String,
        labelSelector: LabelSelector,
    ): List<Pod> {
        return resourceManagerFacades.envProxyFacade.findPodListByLabelSelector(
            appName,
            envName,
            namespace,
            labelSelector
        )?.items ?: emptyList()
    }
}

@Service
class CommonResourceService : Ade1KindResourceService() {

    @Autowired
    lateinit var coreFacades: CoreFacades

    override fun countGroupByKind(changeOrder: ChangeOrder): Map<String, Int> {
        val orchestration = coreFacades.appOrchestrationFacade.findAll(changeOrder.appName)
            .find { it.format == Orchestration.FormatEnum.MANIFEST && it.suitableResourceTypes.contains(ResourceTypeEnum.KUBERNETES) }
        return if (orchestration != null) {
            orchestration as AppBuiltInOrchestration
            orchestration.componentList.groupBy {
                it.kind
            }.mapValues {
                it.value.size
            }
        } else emptyMap()

    }

    override fun convertHasMetadataVOList(
        changeOrder: ChangeOrder,
        kind: String,
        envName: String,
        resourceList: List<HasMetadata>,
    ): List<HasMetadataVO> {
        return resourceList.map { resource ->
            HasMetadataVO(
                data = resource,
                state = HasMetadataVO.HasMetadataVOState.MATCHED,
                deliveries = "",
                displayName = resource.metadata.name
            )
        }
    }

    override fun isMatched(
        task: Task,
        hasMetadata: HasMetadata,
        appName: String,
        envName: String,
        kind: String,
    ): Boolean {
        return true
    }
}
