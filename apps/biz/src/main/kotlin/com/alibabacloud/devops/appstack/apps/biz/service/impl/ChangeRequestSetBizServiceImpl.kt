package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.annotation.PremiumVersionCheck
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestSetBizService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.apps.biz.service.OrgConfigItemBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.IamClient
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.OnlyViewAccessableAppV1OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.ChangeRequestSet
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.iam.CrSetRole
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.request.cr.TransferChangeSetOwnerRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.UpdateChangeSetParticipatorsRequest
import com.alibabacloud.devops.appstack.libs.model.request.crset.QueryChangeRequestSet
import com.alibabacloud.devops.appstack.libs.model.request.crset.UpsertChangeRequestSetRequest
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeRequestSetOutlineVO
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import org.springframework.aop.framework.AopContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Slf4k
@Service
open class ChangeRequestSetBizServiceImpl : ChangeRequestSetBizService {

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var iamService: IamService

    @Autowired
    lateinit var iamClient: IamClient

    @Autowired
    lateinit var auditLogService: AuditLogService

    @Autowired
    lateinit var orgConfigItemBizService: OrgConfigItemBizService

    @PremiumVersionCheck
    override fun upsert(upsertChangeRequestSetRequest: UpsertChangeRequestSetRequest): ChangeRequestSet? {
        val dynamicProxy = AopContext.currentProxy() as ChangeRequestSetBizServiceImpl
        return if (null == upsertChangeRequestSetRequest.sn) {
            dynamicProxy.create(upsertChangeRequestSetRequest)
        } else {
            dynamicProxy.update(upsertChangeRequestSetRequest.sn!!, upsertChangeRequestSetRequest)
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_CRSET_MANAGE),
            Access(action = Action.ORG_CRSET_CREATE),
        ]
    )
    @PremiumVersionCheck
    open fun create(upsertChangeRequestSetRequest: UpsertChangeRequestSetRequest): ChangeRequestSet {
        val changeRequestSet = workflowControllerFacade.changeRequestSetFacade.upsert(upsertChangeRequestSetRequest)!!
        iamService.registerResource(
            ProtocolType.AppstackCrSet,
            changeRequestSet.sn,
            AuthUtil.getTenant(),
            changeRequestSet.owner
        )
        iamService.updateRole(
            ProtocolType.AppstackCrSet,
            changeRequestSet.sn,
            CrSetRole.user.name,
            changeRequestSet.participators.map {
                ResourcePlayerRequest().apply { this.id = it; this.type = SubjectType.User }
            }
        )
        auditLogService.commonLog(OrgEventType.CRSET_CREATE, id = changeRequestSet.sn, name = changeRequestSet.name)
        return changeRequestSet
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_CRSET_MANAGE),
            Access(action = Action.CRSET_EDIT, resourceArgIndex = 0),
        ]
    )
    @PremiumVersionCheck
    open fun update(sn: String, upsertChangeRequestSetRequest: UpsertChangeRequestSetRequest): ChangeRequestSet? {
        val changeRequestSet = workflowControllerFacade.changeRequestSetFacade.upsert(upsertChangeRequestSetRequest)!!
        iamService.updateRole(
            ProtocolType.AppstackCrSet,
            changeRequestSet.sn,
            CrSetRole.admin.name,
            listOf(ResourcePlayerRequest().apply { this.id = changeRequestSet.owner; this.type = SubjectType.User })

        )
        iamService.updateRole(
            ProtocolType.AppstackCrSet,
            changeRequestSet.sn,
            CrSetRole.user.name,
            changeRequestSet.participators.map {
                ResourcePlayerRequest().apply { this.id = it; this.type = SubjectType.User }
            }
        )
        return changeRequestSet
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_CRSET_VIEW),
            Access(action = Action.ORG_CRSET_MANAGE),
            Access(action = Action.CRSET_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun find(sn: String): ChangeRequestSet? {
        val changeRequestSet = workflowControllerFacade.changeRequestSetFacade.find(sn) ?: return null
        val (owner, participators) = findChangeRequestSetRoles(sn)
        changeRequestSet.owner = owner
        changeRequestSet.participators = participators
        return changeRequestSet
    }

    private fun findChangeRequestSetRoles(sn: String): Pair<String, List<String>> {
        val adminPlayers = iamService.findRolePlayers(
            ProtocolType.AppstackCrSet,
            listOf(sn),
            CrSetRole.admin.name
        )[sn]
        val admin = adminPlayers?.firstOrNull() ?: ""


        val userPlayers = iamService.findRolePlayers(
            ProtocolType.AppstackCrSet,
            listOf(sn),
            CrSetRole.user.name
        )[sn]
        val participatorsRequest = userPlayers?.distinct() ?: emptyList()

        return Pair(admin, participatorsRequest)
    }

    override fun findPaginated(
        request: QueryChangeRequestSet,
        current: Long,
        pageSize: Long,
        orderBy: String,
        sort: String,
    ): Pagination<ChangeRequestSet> {
        val orgConfigItem = orgConfigItemBizService.find(OrgConfigItem.ONLY_VIEW_ACCESSABLE_APP)
        // 如果勾选了【仅支持查看我有权限的应用列表】
        if ((orgConfigItem as? OnlyViewAccessableAppV1OrgConfigItem)?.enable == true) {
            // 如果查询全部但没有查看全部应用权限
            if (request.owners.isEmpty()
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_CRSET_VIEW)
                && !iamService.can(ProtocolType.AppStack, "any", Action.ORG_CRSET_VIEW)
            ) {
                throw BizException(ErrorCode.AS_PERMISSION_DENIED)
            }
        }
        return workflowControllerFacade.changeRequestSetFacade.findPaginated(request, current, pageSize, orderBy, sort)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_CRSET_VIEW),
            Access(action = Action.ORG_CRSET_MANAGE),
            Access(action = Action.CRSET_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun findDetail(sn: String): ChangeRequestSetOutlineVO? {
        val detail = workflowControllerFacade.changeRequestSetFacade.findDetail(sn) ?: return null
        val (owner, participators) = findChangeRequestSetRoles(sn)
        detail.changeRequestSet.owner = owner
        detail.changeRequestSet.participators = participators
        return detail
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_CRSET_MANAGE),
            Access(action = Action.CRSET_ADMIN_TRANSFER, resourceArgIndex = 0)
        ]
    )
    @PremiumVersionCheck
    override fun transferOwner(
        sn: String,
        request: TransferChangeSetOwnerRequest,
    ): ChangeRequestSet? {
        val changeRequestSet = find(sn = sn)
        checkExists(changeRequestSet) { ErrorCode.AS_CR_SET_NOT_FOUND }
        // 校验用户是否存在
        iamService.checkUserExist(request.owner)
        val (originOwner, participators) = findChangeRequestSetRoles(sn)
        if (originOwner.isNotBlank()) {
            iamService.updatePlayer(
                protocolType = ProtocolType.AppstackCrSet,
                resourceName = sn,
                subjectType = SubjectType.User,
                subjectId = originOwner,
                roleNameList = listOf(CrSetRole.user.name)
            )
            workflowControllerFacade.changeRequestSetFacade.updateParticipators(
                sn,
                UpdateChangeSetParticipatorsRequest(participators = (participators + originOwner - request.owner).distinct())
            )
        }
        iamService.updatePlayer(
            protocolType = ProtocolType.AppstackCrSet,
            resourceName = sn,
            subjectType = SubjectType.User,
            subjectId = request.owner,
            roleNameList = listOf(CrSetRole.admin.name)
        )

        workflowControllerFacade.changeRequestSetFacade.updateOwner(sn, request)
        return workflowControllerFacade.changeRequestSetFacade.find(sn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_CRSET_MANAGE),
            Access(action = Action.CRSET_MEMBER_SET, resourceArgIndex = 0)
        ]
    )
    @PremiumVersionCheck
    override fun updateParticipators(sn: String, request: UpdateChangeSetParticipatorsRequest): ChangeRequestSet? {
        val changeRequestSet = find(sn = sn)
        checkExists(changeRequestSet) { ErrorCode.AS_CR_SET_NOT_FOUND }

        val (_, originParticipators) = findChangeRequestSetRoles(sn)

        (request.participators - originParticipators).forEach { newParticipator ->
            iamService.updatePlayer(
                protocolType = ProtocolType.AppstackCrSet,
                resourceName = sn,
                subjectType = SubjectType.User,
                subjectId = newParticipator,
                roleNameList = listOf(CrSetRole.user.name)
            )
        }

        (originParticipators - request.participators).forEach { deleteParticipator ->
            iamService.updatePlayer(
                protocolType = ProtocolType.AppstackCrSet,
                resourceName = sn,
                subjectType = SubjectType.User,
                subjectId = deleteParticipator,
                roleNameList = listOf()
            )
        }
        workflowControllerFacade.changeRequestSetFacade.updateParticipators(sn, request)
        return workflowControllerFacade.changeRequestSetFacade.find(sn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_CRSET_MANAGE),
            Access(action = Action.CRSET_CLOSE, resourceArgIndex = 0)
        ]
    )
    @PremiumVersionCheck
    override fun close(sn: String): ChangeRequestSet? {
        workflowControllerFacade.changeRequestSetFacade.close(sn = sn)
        val changeRequestSet = find(sn)
        if (changeRequestSet != null) {
            auditLogService.commonLog(OrgEventType.CRSET_CLOSE, id = changeRequestSet.sn, name = changeRequestSet.name)
        }
        return changeRequestSet
    }

}