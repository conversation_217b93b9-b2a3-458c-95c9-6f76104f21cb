package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.AppArtifactRepoBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppArtifactRepo
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ObjectType
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.ServiceConnectionBindDTO
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.CreateAppArtifactRepoRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppArtifactRepoRequest
import com.alibabacloud.devops.appstack.resource.manager.spring.boot.starter.ResourceManagerFacades
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2023-11-02
 */
@Service
open class AppArtifactRepoBizServiceImpl : AppArtifactRepoBizService {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var resourceManagerFacades: ResourceManagerFacades

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_BASIC_SET, resourceArgIndex = 0),
        ]
    )
    override fun create(appName: String, request: CreateAppArtifactRepoRequest): AppArtifactRepo {
        validateIdentifier(appName, request.identifier)
        val artifactRepo = coreFacades.appArtifactRepoFacade.create(appName, request)
        bindServiceConnection(appName, artifactRepo)
        return artifactRepo
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_BASIC_SET, resourceArgIndex = 0),
        ]
    )
    override fun delete(appName: String, sn: String) {
        val find = find(appName, sn)
        checkExists(find) {
            ErrorCode.AS_APP_ARTIFACT_REPO_NOT_FOUND
        }
        coreFacades.appArtifactRepoFacade.delete(appName, sn)
        unbindServiceConnection(appName, find)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_BASIC_SET, resourceArgIndex = 0),
        ]
    )
    override fun update(appName: String, sn: String, request: UpdateAppArtifactRepoRequest): AppArtifactRepo {
        val previous = find(appName, sn)
        checkExists(previous) {
            ErrorCode.AS_APP_ARTIFACT_REPO_NOT_FOUND
        }
        var after = AppArtifactRepo(
            appName = appName,
            sn = previous.sn,
            identifier = previous.identifier,
            repoContext = request.repoContext,
            connectionConfig = request.connectionConfig
        )
        after = coreFacades.appArtifactRepoFacade.update(appName, sn, after)
        if (previous.connectionConfig != after.connectionConfig) {
            unbindServiceConnection(appName, previous)
            bindServiceConnection(appName, after)
        }
        return after
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun findAll(appName: String): List<AppArtifactRepo> {
        return coreFacades.appArtifactRepoFacade.findAll(appName)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun find(appName: String, sn: String): AppArtifactRepo {
        val repo = coreFacades.appArtifactRepoFacade.find(appName, sn)
        checkExists(repo) {
            ErrorCode.AS_APP_ARTIFACT_REPO_NOT_FOUND
        }
        return repo
    }

    private fun unbindServiceConnection(appName: String, artifactRepo: AppArtifactRepo) {
        val result = resourceManagerFacades.serviceConnectionFacade.unbind(
            ServiceConnectionBindDTO(
                connectionConfigType = artifactRepo.connectionConfig.connectionType,
                connectionId = artifactRepo.connectionConfig.connectionId,
                objectType = ObjectType.appstackAppArtifactRepo,
                objectId = getAppArtifactRepoServiceConnectionObjectId(appName, artifactRepo),
            )
        )
        logger.info("Unbind AppArtifactRepo[${artifactRepo.sn}] ServiceConnection[${artifactRepo.connectionConfig}]: $result")
    }

    private fun bindServiceConnection(appName: String, artifactRepo: AppArtifactRepo) {
        val result = resourceManagerFacades.serviceConnectionFacade.bind(
            ServiceConnectionBindDTO(
                connectionConfigType = artifactRepo.connectionConfig.connectionType,
                connectionId = artifactRepo.connectionConfig.connectionId,
                objectType = ObjectType.appstackAppArtifactRepo,
                objectId = getAppArtifactRepoServiceConnectionObjectId(appName, artifactRepo),
            )
        )
        logger.info("Bind AppArtifactRepo[${artifactRepo.sn}] ServiceConnection[${artifactRepo.connectionConfig}]: $result")
    }

    fun validateIdentifier(appName: String, newIdentifier: String){
        val codeRepoIds = coreFacades.appCodeRepoFacade.findAll(appName).map { it.identifier }
        val artifactRepoIds = coreFacades.appArtifactRepoFacade.findAll(appName).map { it.identifier }
        if(artifactRepoIds.isNotEmpty()){
            throw BizException(errorCode = ErrorCode.AS_APP_ARTIFACT_REPO_NUMBER_LIMIT)
        }
        if(codeRepoIds.contains(newIdentifier) || artifactRepoIds.contains(newIdentifier)){
            throw BizException(errorCode = ErrorCode.AS_APP_ARTIFACT_REPO_IDENTIFIER_DUPLICATED)
        }
    }

    private fun getAppArtifactRepoServiceConnectionObjectId(appName: String, appArtifactRepo: AppArtifactRepo) =
        "$appName::${appArtifactRepo.identifier}"
}