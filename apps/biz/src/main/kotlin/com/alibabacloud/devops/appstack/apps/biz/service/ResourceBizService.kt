package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceInstance
import com.alibabacloud.devops.appstack.libs.model.request.ng.ResourceInstanceQuery
import com.alibabacloud.devops.appstack.libs.model.request.resource.ResourceInstanceRequest
import com.alibabacloud.devops.appstack.libs.model.vo.ResourceInstanceRecord
import com.alibabacloud.devops.appstack.libs.model.vo.ResourceInstanceVO
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.ResourcePlayer
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest

/**
 * <AUTHOR>
 * @date 2022-03-17
 */
interface ResourceBizService {

    fun importInstance(
        poolName: String,
        resourceInstanceRequest: ResourceInstanceRequest
    ): ResourceInstance

    fun findAllInstance(poolName: String, type: String?): List<ResourceInstanceRecord>
    fun listInstances(poolName: String, query: ResourceInstanceQuery): PageList<ResourceInstanceVO>
    fun findInstance(poolName: String, instanceName: String): ResourceInstanceVO?
    fun deleteResourceInstance(poolName: String, instanceName: String)
    fun updateResourceInstance(poolName: String, name: String, resourceInstance: ResourceInstance): ResourceInstance

    fun findAllMember(poolName: String, instanceName: String): Map<ResourcePlayer, List<Role>>
    fun addRoleMember(poolName: String, instanceName: String, role: Role, playerList: List<ResourcePlayerRequest>)
    fun updateRoleMember(poolName: String, instanceName: String, role: Role, playerList: List<ResourcePlayerRequest>)
    fun transferOwner(poolName: String, instanceName: String, playerList: List<ResourcePlayerRequest>)
    fun updateMemberRole(
        poolName: String,
        instanceName: String,
        subjectId: String,
        subjectType: SubjectType,
        roleNameList: List<String>
    )
    fun findAllInstanceCanUse(poolName: String, type: String?): List<ResourceInstanceVO>
}