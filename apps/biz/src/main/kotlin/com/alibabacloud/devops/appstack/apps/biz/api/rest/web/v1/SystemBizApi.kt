package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.apps.biz.service.system.SystemBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppCodeRepo
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import com.alibabacloud.devops.appstack.libs.model.request.ng.*
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import com.alibabacloud.devops.appstack.libs.model.response.ng.AppWithSourcesVO
import com.alibabacloud.devops.appstack.libs.model.response.ng.SystemCountVO
import com.alibabacloud.devops.appstack.libs.model.response.ng.SystemWithPersonalFavouritesVO
import com.alibabacloud.devops.appstack.libs.model.vo.ng.System
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import com.aliyun.amp.plugin.annotation.AmpApi
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.api.annotations.ParameterObject
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * @author: <EMAIL>
 * @date: 2023-11-02 16:08
 * @version: SystemApi, v0.1
 **/
@Tag(name = "System", description = "系统领域相关 api")
@RestController
@RequestMapping("/api/v1/systems")
class SystemBizApi {

    @Autowired
    lateinit var systemBizService: SystemBizService

    @AmpApi
    @GetMapping("/{systemName}")
    fun findSystem(
        @PathVariable("systemName") systemName: String
    ): Response<SystemWithPersonalFavouritesVO> {
        return Response.success(systemBizService.find(systemName))
    }

    @GetMapping("/:count")
    fun getSystemGroupCount(
        @ParameterObject filter: SearchSystemFilter
    ): Response<SystemCountVO> {
        return Response.success(systemBizService.getGroupCount(filter))
    }

    @GetMapping("/:search")
    fun searchSystem(
        @ParameterObject request: SearchSystemFilter,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long
    ): Response<Pagination<SystemWithPersonalFavouritesVO>> {
        return Response.success(systemBizService.search(request, current = current, pageSize = pageSize))
    }

    @GetMapping("/")
    fun listSystems(
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long
    ): Response<Pagination<SystemWithPersonalFavouritesVO>> {
        return Response.success(systemBizService.list(current = current, pageSize = pageSize))
    }

    @PostMapping("/")
    fun createSystem(
        @RequestBody request: CreateSystemRequest
    ): Response<System> {
        return Response.success(systemBizService.create(request))
    }

    @PutMapping("/{systemName}")
    fun updateSystem(
        @PathVariable("systemName") systemName: String,
        @RequestBody request: UpdateSystemRequest
    ): Response<System> {
        return Response.success(systemBizService.update(systemName, request))
    }

    @DeleteMapping("/{systemName}")
    fun deleteSystem(
        @PathVariable("systemName") systemName: String
    ): Response<Boolean> {
        return Response.success(systemBizService.delete(systemName))
    }

    @GetMapping("/{systemName}/members")
    fun listSystemMembers(
        @PathVariable("systemName") systemName: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long
    ): Response<Pagination<MemberVO>> {
        return Response.success(systemBizService.listMembers(systemName, current = current, pageSize = pageSize))
    }

    @Operation(summary = "添加系统成员", operationId = "CreateSystemMembers")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "添加系统成员成功"),
            ApiResponse(responseCode = "403", description = "没权限设置拥有者"),
            ApiResponse(responseCode = "404", description = "角色未找到"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PostMapping("/{systemName}/members")
    fun createSystemMembers(
        @Schema(description = "系统名", example = "my-demo-system", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("systemName") systemName: String,
        @RequestBody addMembersRequest: AddMembersRequest
    ): Boolean {
        var flag = true
        addMembersRequest.roleNames.forEach {
            if (!systemBizService.addRoleMember(systemName, it, addMembersRequest.playerList)) {
                flag = false
            }
        }
        return flag
    }

    @Operation(summary = "删除系统成员", operationId = "DeleteSystemMember")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "删除系统成员成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @DeleteMapping("/{systemName}/members")
    fun deleteSystemMember(
        @Schema(description = "系统", example = "my-demo-system", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("systemName") systemName: String,
        @Schema(description = "成员类型", example = "User", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @RequestParam subjectType: SubjectType,
        @Schema(description = "成员id", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @RequestParam subjectId: String,
    ): Boolean {
        systemBizService.updateMemberRole(
            systemName = systemName,
            subjectType = subjectType,
            subjectId = subjectId,
            roleNameList = emptyList()
        )
        return true
    }


    @Operation(summary = "更新系统成员角色", operationId = "UpdateSystemMember")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "更新系统成员角色成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PutMapping("/{systemName}/members")
    fun updateSystemMember(
        @Schema(description = "系统名", example = "my-demo-system", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("systemName") systemName: String,
        @RequestBody updateMemberRequest: UpdateMemberRequest
    ): Boolean {
        systemBizService.updateMemberRole(
            systemName = systemName,
            subjectType = updateMemberRequest.player.type,
            subjectId = updateMemberRequest.player.id,
            roleNameList = updateMemberRequest.roleNames,
        )
        return true
    }

    @PostMapping("/{systemName}:transferOwner")
    fun transferOwner(
        @PathVariable("systemName") systemName: String,
        @RequestBody owner: ResourcePlayerRequest
    ): Response<Boolean> {
        return Response.success(systemBizService.transferOwner(systemName, owner))
    }

    @PostMapping("/{systemName}:favour")
    fun favourSystem(
        @PathVariable("systemName") systemName: String,
    ): Response<Boolean> {
        return Response.success(systemBizService.favourSystem(systemName))
    }

    @PostMapping("/{systemName}:disfavour")
    fun disfavourSystem(
        @PathVariable("systemName") systemName: String,
    ): Response<Boolean> {
        return Response.success(systemBizService.disfavourSystem(systemName))
    }

    @PostMapping("/{systemName}:attachApps")
    fun attachAppsToSystem(
        @PathVariable("systemName") systemName: String,
        @RequestBody targetApps: Set<String>
    ): Response<Boolean> {
        return Response.success(systemBizService.attachAppsToSystem(systemName, targetApps))
    }

    @PostMapping("/{systemName}:detachApps")
    fun detachAppsFromSystem(
        @PathVariable("systemName") systemName: String,
        @RequestBody appNames: Set<String>
    ): Response<Boolean> {
        return Response.success(systemBizService.detachAppsFromSystem(systemName, appNames))
    }

    @GetMapping("/{systemName}/apps")
    fun listAttachedApps(
        @PathVariable("systemName") systemName: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<AppWithSourcesVO>> {
        return Response.success(systemBizService.listAttachedApps(systemName, current = current, pageSize = pageSize))
    }

    @Operation(summary = "查找系统下的代码仓库列表")
    @GetMapping("/{systemName}/codeRepos")
    fun findAllSystemCodeRepo(@PathVariable systemName: String): Response<List<AppCodeRepo>> {
        return Response.success(systemBizService.findAllCodeRepos(systemName))
    }

    @Operation(summary = "查找系统下的代码仓库列表")
    @GetMapping("/{systemName}/orchestrations:all")
    fun findAllSystemOrchestration(
        @PathVariable("systemName") systemName: String,
    ): Response<List<AppOrchestration>> {
        return Response.success(systemBizService.findAllOrchestrations(systemName))
    }

}