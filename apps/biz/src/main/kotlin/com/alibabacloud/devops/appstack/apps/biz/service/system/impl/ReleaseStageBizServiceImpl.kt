package com.alibabacloud.devops.appstack.apps.biz.service.system.impl

import com.alibabacloud.devops.appstack.apps.biz.annotation.IsSystem
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.ServiceConnectionBizService
import com.alibabacloud.devops.appstack.apps.biz.service.system.ReleaseStageBizService
import com.alibabacloud.devops.appstack.apps.biz.service.StageBaseService
import com.alibabacloud.devops.appstack.apps.biz.service.WorkflowBaseService
import com.alibabacloud.devops.appstack.apps.biz.service.impl.FlowPermissionHandlerChain
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.release.AppRelease
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ExecutePipelineResult
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStage
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageInstance
import com.alibabacloud.devops.appstack.libs.model.constant.FlowPermission
import com.alibabacloud.devops.appstack.libs.model.constant.WorkflowEnvVariable
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.org.event.SystemReleaseStagePipelineAudit
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.*
import com.alibabacloud.devops.appstack.libs.model.vo.ReleaseIntegratedMetadata
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.alibabacloud.devops.iam.exception.IamForbiddenException
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.jetbrains.annotations.TestOnly
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.text.SimpleDateFormat

/**
 * <AUTHOR>
 * @date 2023-11-08
 */
@Service
open class ReleaseStageBizServiceImpl : ReleaseStageBizService {

    private lateinit var coreFacades: CoreFacades

    private lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var stageBaseService: StageBaseService

    @Autowired
    lateinit var serviceConnectionBizService: ServiceConnectionBizService

    @Autowired
    lateinit var auditLogService: AuditLogService

    @Autowired
    lateinit var releaseWorkflowBaseService: WorkflowBaseService

    @Autowired
    lateinit var flowPermissionHandlerChain: FlowPermissionHandlerChain

    @Autowired
    fun setCoreFacades(coreFacades: CoreFacades) {
        this.coreFacades = coreFacades
    }

    @Autowired
    fun setWorkflowControllerFacade(workflowControllerFacade: WorkflowControllerFacade) {
        this.workflowControllerFacade = workflowControllerFacade
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    @IsSystem
    override fun create(
        systemName: String,
        releaseWorkflowSn: String,
        request: CreateReleaseStageBizRequest
    ): ReleaseStage {
        return stageBaseService.create(systemName, releaseWorkflowSn, request)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    @IsSystem
    override fun findAll(systemName: String, releaseWorkflowSn: String): List<ReleaseStage> {
        return stageBaseService.findAll(systemName, releaseWorkflowSn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    @IsSystem
    override fun find(systemName: String, releaseWorkflowSn: String, releaseStageSn: String): ReleaseStage {
        return stageBaseService.find(systemName, releaseWorkflowSn, releaseStageSn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    @IsSystem
    override fun update(
        systemName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        request: UpdateReleaseStageRequest
    ): ReleaseStage {
        return stageBaseService.update(systemName, releaseWorkflowSn, releaseStageSn, request)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    @IsSystem
    override fun delete(systemName: String, releaseWorkflowSn: String, releaseStageSn: String) {
        stageBaseService.delete(systemName, releaseWorkflowSn, releaseStageSn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    @IsSystem
    override fun updatePipeline(
        systemName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        request: UpdateReleaseStagePipelineRequest
    ): ReleaseStage {
        return stageBaseService.updatePipeline(systemName, releaseWorkflowSn, releaseStageSn, request)
    }

    override fun getPipelineParams(
        systemName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        appReleaseSn: String?
    ): MutableMap<String, Any> {
        val actualParams: MutableMap<String, Any> = mutableMapOf()
        actualParams.put(WorkflowEnvVariable.APPSTACK_SYS_NAME, systemName)
        actualParams.put(WorkflowEnvVariable.APPSTACK_SYS_WORKFLOW_SN, releaseWorkflowSn)
        actualParams.put(WorkflowEnvVariable.APPSTACK_SYS_WORKFLOW_STAGE_SN, releaseStageSn)
        // sources related
        val appSources = coreFacades.appFacade.listAllRelatedApps(systemName)
        val appNames = appSources.map { it.name }
        actualParams.put(WorkflowEnvVariable.APPSTACK_SYS_SOURCE_APP_NAMES, appNames)

        // release related
        if (!appReleaseSn.isNullOrBlank()) {
            val appRelease = workflowControllerFacade.appReleaseFacade.find(systemName, appReleaseSn!!)
            checkExists(appRelease) { ErrorCode.AS_APP_RELEASE_NOT_FOUND }
            // 注入发布的变量
            actualParams.putAll(
                mapOf(
                    WorkflowEnvVariable.APPSTACK_RELEASE_ID to appReleaseSn,
                    WorkflowEnvVariable.APPSTACK_RELEASE_SN to appReleaseSn,
                    WorkflowEnvVariable.APPSTACK_RELEASE_SCHEDULED_RELEASE_TIME to
                            if (appRelease.scheduledReleaseTime != null)
                                SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(appRelease.scheduledReleaseTime)
                            else "",
                    WorkflowEnvVariable.APPSTACK_RELEASE_VERSION to appRelease.version,
                    WorkflowEnvVariable.APPSTACK_RELEASE_NAME to appRelease.name,
                    WorkflowEnvVariable.APPSTACK_RELEASE_BASELINE_VERSION to (appRelease.baseAppRelease?.version ?: ""),
                    WorkflowEnvVariable.APPSTACK_RELEASE_DESCRIPTION to appRelease.description,
                ).filterValues { it != null } as Map<String, Any>
            )

            val releaseAppNames =
                appRelease.releaseItems.filter { it.type == AppRelease.AppReleaseItem.TypeEnum.APP }.map {
                    (it.spec as AppRelease.AppReleaseItem.AppSpec).appName
                }
            actualParams.put(WorkflowEnvVariable.APPSTACK_RELEASE_ITEM_APP_NAMES, releaseAppNames)

            val releaseGenericNames =
                appRelease.releaseItems.filter { it.type == AppRelease.AppReleaseItem.TypeEnum.GENERIC }.map {
                    (it.spec as AppRelease.AppReleaseItem.AppGenericSpec).name
                }
            actualParams.put(WorkflowEnvVariable.APPSTACK_RELEASE_ITEM_GENERIC_NAMES, releaseGenericNames)

            appRelease.releaseItems.forEach { item ->
                item.spec.asPipelineEnv()?.let { (key, value) ->
                    val afterValue = handlePipelineEnv(item.type.name, value)
                    actualParams.put(WorkflowEnvVariable.APPSTACK_RELEASE_ITEM_PREFIX + key, afterValue)
                }
            }
            val appReleaseMap = mutableMapOf<String, Map<String, Any>>()
            // 过滤，仅返回应用发布项
            appRelease.releaseItems.filter { it.type == AppRelease.AppReleaseItem.TypeEnum.APP }.forEach { item ->
                item.spec.asPipelineEnv()?.let { (key, value) ->
                    // 从之前的map中获取，不重复计算
                    val afterValue = actualParams[WorkflowEnvVariable.APPSTACK_RELEASE_ITEM_PREFIX + key].toString()
                    appReleaseMap[key] = jacksonObjectMapper().readValue(afterValue)
                }
            }
            actualParams[WorkflowEnvVariable.APPSTACK_RELEASE_ITEM_APPS] = appReleaseMap.toMap()
        }
        return actualParams
    }

    @IsSystem
    override fun executePipeline(
        systemName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        request: ExecuteReleaseStagePipelineRequest
    ): ExecutePipelineResult {
        val app = coreFacades.appFacade.find(systemName)
        checkExists(app) { ErrorCode.AS_APP_NOT_FOUND }
        val releaseWorkflow = releaseWorkflowBaseService.findBrief(appName = systemName, sn = releaseWorkflowSn)
        val releaseStage = stageBaseService.findBrief(systemName, releaseWorkflowSn, releaseStageSn)
        // permission check
        if(!flowPermissionHandlerChain.handle(FlowPermission.PERMISSION_PIPELINE_RUN, app, releaseWorkflow, releaseStage)){
            throw IamForbiddenException("权限检查失败 systemName: $systemName, workflowSn: ${releaseWorkflow.sn} stageSn: ${releaseStage.sn}")
        }
        val actualParams = request.params
        val systemParams = getPipelineParams(systemName, releaseWorkflowSn, releaseStageSn, request.appReleaseSn)
        actualParams.putAll(systemParams)
        val executeResult = stageBaseService.executePipeline(systemName, releaseWorkflowSn, releaseStageSn, request)
        val pipelineAudit = SystemReleaseStagePipelineAudit(
            target = AuditTarget(id = releaseWorkflowSn, name = releaseWorkflowSn),
            systemName = systemName,
            releaseWorkflowSn = releaseWorkflowSn,
            releaseStageSn = releaseStage.sn!!,
            pipelineId = executeResult.pipelineId.toString(),
            runNumber = executeResult.pipelineRunId.toString(),
            state = ReleaseStageInstance.State.RUNNING.name,
        )
        auditLogService.log(OrgEventType.SYSTEM_RELEASE_WORKFLOW_STAGE_PIPELINE_HOOK, pipelineAudit)
        return executeResult
    }

    @TestOnly
    fun exposeHandlePipelineEnv(type: String, value: String): String {
        return handlePipelineEnv(type, value)
    }

    // 处理环境变量
    private fun handlePipelineEnv(type: String, value: String): String {
        // 目前仅需要处理应用发布项的变量
        return when (type) {
            AppRelease.AppReleaseItem.TypeEnum.APP.name -> {
                val obj: JsonNode = jacksonObjectMapper().readValue(value)
                val transferServiceConnectionID = { id: String ->
                    // 空字符串不处理
                    if (id.isEmpty()) {
                        ""
                    } else {
                        val connections = serviceConnectionBizService.findByIds(listOf(id))
                        val uuid = connections.firstOrNull { it.id == id.toLongOrNull() }?.uuid ?: ""
                        uuid
                    }
                }
                JacksonUtil.replace(obj, "serviceConnectionID", transferServiceConnectionID)
                jacksonObjectMapper().writeValueAsString(obj)
            }
            else -> value
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    @IsSystem
    override fun findReleaseStageCrMetadata(
        systemName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        request: QueryReleaseStageCrMetadataRequest
    ): ReleaseIntegratedMetadata? {
        return stageBaseService.findReleaseStageCrMetadata(systemName, releaseWorkflowSn, releaseStageSn, request.buildNum)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0),
            Access(action = Action.SYSTEM_RELEASE_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    @IsSystem
    override fun findPreviousMetadata(
        systemName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String
    ): ReleaseIntegratedMetadata? {
        return stageBaseService.findPreviousMetadata(systemName, releaseWorkflowSn, releaseStageSn)
    }

    override fun findStageEnvList(releaseStageSn: String): List<String> {
        return stageBaseService.findStageEnvList(releaseStageSn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_MANAGE),
            Access(action = Action.ORG_SYSTEM_VIEW),
            Access(action = Action.SYSTEM_VIEW, resourceArgIndex = 0)
        ]
    )
    override fun findVariableGroups(
        systemName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String
    ): List<VariableGroup> {
        return stageBaseService.findVariableGroups(systemName, releaseWorkflowSn, releaseStageSn)
    }

}