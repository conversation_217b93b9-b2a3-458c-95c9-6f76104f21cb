package com.alibabacloud.devops.appstack.apps.biz.model.po

import com.alibabacloud.devops.appstack.libs.common.annotation.Comment
import com.alibabacloud.devops.appstack.libs.common.model.BasePO
import com.baomidou.mybatisplus.annotation.TableName
import lombok.Data
import javax.persistence.Column
import javax.persistence.Index
import javax.persistence.Table

/**
 * @author: <EMAIL>
 * @date: 2022-03-07 19:15
 * @version: DeployFlowPO, v0.1
 **/
@TableName("deploy_flows")
@Comment("DeployFlows")
@Table(
    indexes = [
        Index(columnList = "org_id(100),object_type(20),object_id(20)"),
    ]
)
@Data
open class DeployFlowPO: BasePO() {

    @Comment("应用名")
    @Column(columnDefinition = "varchar(100)", nullable = false)
    lateinit var appName: String

    @Comment("环境名")
    @Column(columnDefinition = "varchar(100)", nullable = false)
    lateinit var envName: String

    @Comment("objectType")
    @Column(columnDefinition = "varchar(20)", nullable = false)
    lateinit var objectType: String

    @Comment("objectId")
    @Column(columnDefinition = "varchar(20)", nullable = false)
    lateinit var objectId: String

    @Comment("changeOrderSn")
    @Column(columnDefinition = "varchar(128)", nullable = false)
    lateinit var changeOrderSn: String

    @Comment("触发来源")
    @Column(columnDefinition = "text", nullable = true)
    var source: String? = null
}