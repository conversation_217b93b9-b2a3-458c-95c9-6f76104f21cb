package com.alibabacloud.devops.appstack.apps.biz.model.po

import com.alibabacloud.devops.appstack.libs.common.annotation.Comment
import com.alibabacloud.devops.appstack.libs.common.model.BasePO
import com.baomidou.mybatisplus.annotation.TableName
import lombok.Data
import javax.persistence.Column
import javax.persistence.Index
import javax.persistence.Table

/**
 * <AUTHOR>
 * @create 2023/10/16 5:56 PM
 **/
@TableName("app_templates")
@Comment("AppTemplates")
@Table(
    indexes = [
        Index(unique = true, columnList = "org_id(64),is_deleted(32),name(64)"),
    ]
)
@Data
class AppTemplatePO : BasePO() {

    @Comment("名称，唯一标识")
    @Column(columnDefinition = "varchar(255)", nullable = false)
    lateinit var name: String

    @Comment("显示名")
    @Column(columnDefinition = "varchar(255)", nullable = false)
    lateinit var displayName: String

    @Comment("描述")
    @Column(columnDefinition = "text", nullable = true)
    var description: String? = null

    @Comment("封面地址")
    @Column(columnDefinition = "varchar(512)", nullable = true)
    var cover: String? = null

}