package com.alibabacloud.devops.appstack.apps.biz.aspect

import com.alibabacloud.devops.appstack.apps.biz.service.client.api.ConfigCenterApi
import com.alibabacloud.devops.appstack.apps.biz.service.impl.AgentServiceVpcImpl
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import org.aspectj.lang.JoinPoint
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.annotation.Before
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

/**
 * @author: <EMAIL>
 * @date: 2023-06-08 10:41
 * @version: PremiumVersionCheck, v0.1
 **/
@Aspect
@Slf4k
@Component
open class PremiumVersionCheck {

    @Value("\${tb.sso.appId}")
    lateinit var appId: String

    @Value("\${tb.sso.appSecret}")
    lateinit var appSecret: String

    @Value("\${service.org.environment}")
    lateinit var environment: String

    @Autowired
    lateinit var configCenterApi: ConfigCenterApi

    @Before("@annotation(com.alibabacloud.devops.appstack.apps.biz.annotation.PremiumVersionCheck)")
    fun preCheck(joinPoint: JoinPoint) {
        if (environment != "aliyun") return
        val isPremiumVersion = configCenterApi.findOrgPlan(
            mapOf(
                "Authorization" to "Bearer " + AgentServiceVpcImpl.genAppToken(appId, appSecret)
            ),
            AuthUtil.getTenant(), appId
        )

        if ("false" == isPremiumVersion) {
            throw BizException(ErrorCode.AS_PREMIUM_VERSION_CHECK_FAILED)
        }
    }
}