package com.alibabacloud.devops.appstack.apps.biz.model.mapper

import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateConfigReferencePO
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.core.mapper.BaseMapper
import org.apache.ibatis.annotations.Mapper

/**
 * <AUTHOR>
 * @date 2023-11-09
 */
@Mapper
interface AppTemplateConfigReferenceMapper : BaseMapper<AppTemplateConfigReferencePO>

fun AppTemplateConfigReferenceMapper.deleteByTemplate(appTemplateName: String, appTemplateConfigSn: String) =
    delete(
        QueryWrapper<AppTemplateConfigReferencePO>()
            .eq("app_template_name", appTemplateName)
            .eq("app_template_config_sn", appTemplateConfigSn)
    )

fun AppTemplateConfigReferenceMapper.listByTemplate(appTemplateName: String, appTemplateConfigSn: String) =
    selectList(
        QueryWrapper<AppTemplateConfigReferencePO>()
            .eq("app_template_name", appTemplateName)
            .eq("app_template_config_sn", appTemplateConfigSn)
    )

fun AppTemplateConfigReferenceMapper.listByReference(referenceType: String, referenceId: String) =
    selectList(
        QueryWrapper<AppTemplateConfigReferencePO>()
            .eq("reference_type", referenceType)
            .eq("reference_id", referenceId)
    )