package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.config.FlowAuthConfiguration
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestStageBizService
import com.alibabacloud.devops.appstack.apps.biz.service.FlowForwardBizService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.apps.biz.service.system.ReleaseStageBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.constant.Header
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.TokenUtil
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.constant.FlowPermission
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageBriefVO
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowBriefVO
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import com.alibabacloud.devops.iam.exception.IamForbiddenException
import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.apache.http.client.config.RequestConfig
import org.apache.http.config.RegistryBuilder
import org.apache.http.conn.socket.ConnectionSocketFactory
import org.apache.http.conn.socket.PlainConnectionSocketFactory
import org.apache.http.conn.ssl.NoopHostnameVerifier
import org.apache.http.conn.ssl.SSLConnectionSocketFactory
import org.apache.http.impl.client.HttpClientBuilder
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager
import org.apache.http.ssl.SSLContexts
import org.apache.http.ssl.TrustStrategy
import org.jetbrains.annotations.TestOnly
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory
import org.springframework.http.converter.StringHttpMessageConverter
import org.springframework.mock.web.MockHttpServletRequest
import org.springframework.mock.web.MockHttpServletResponse
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.URLDecoder
import java.nio.charset.StandardCharsets
import java.security.cert.X509Certificate
import java.util.*
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 * <AUTHOR>
 * @create 2023/10/17 7:41 AM
 **/
@Service
open class FlowForwardBizServiceImpl : FlowForwardBizService {

    private val PREFIX = "/forward/flow"

    private val PERMISSION_PIPELINE_VIEW = "tb.flow.pipeline.view"

    private val PERMISSION_PIPELINE_RUN = "tb.flow.pipeline.run"

    private val PERMISSION_PIPELINE_EDIT = "tb.flow.pipeline.edit"

    lateinit var restTemplate : RestTemplate

    lateinit private var jacksonMapper: ObjectMapper

    lateinit private var flowForwardMap: MutableMap<String, ObjectNode>

    lateinit private var operationMap: MutableMap<String, ObjectNode>

    @Value("\${service.flow.url}")
    lateinit var flowUrl: String

    @Value("\${service.flow.forward-map}")
    var flowForwardMapStr: String = ""

    @Value("\${service.flow.operation-map}")
    var operationMapStr: String = ""

    @Autowired
    lateinit var flowAuthConfig: FlowAuthConfiguration

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var flowPermissionHandlerChain: FlowPermissionHandlerChain

    @Autowired
    lateinit var changeRequestStageBizService: ChangeRequestStageBizService

    @Autowired
    lateinit var releaseStageBizService: ReleaseStageBizService

    @Autowired
    lateinit var iamService: IamService

    companion object {
        val NO_PERMISSION = "NO_PERMISSION"
        val FLOW_GRAY_COOKIE_VALUE = "grayTraffic=true"
        // 临时使用，后续Flow提供一个查询api后替换
        val FLOW_GRAY_ORGS = setOf(
            "5e706eb315eccd0001c1fe96",
            "5e73763515eccd0001c1ff66",
            "5e97f942f89c9700014a4f00",
            "5e9d8080f89c9700014a5393",
            "5ea7f76ef89c9700014a6664",
            "5ead2dfe3fd198000181a670",
            "5ea940d4e17c0e0001fda520",
            "5eb5068038076f00011bcf0b",
            "5eb6d70f38076f00011bd213",
            "5eb77c7538076f00011bd238",
            "5eb90bdb8123212b59d56afc",
            "5eb24d5b3fd198000181a968",
            "5ebb57de8123212b59d58171",
            "5ebbc0408123212b59d58347",
            "5ebbcdb1053c10a2a6002a23",
            "5eb53cca38076f00011bcfd8",
            "5ec7cbda405cdab50f3fd5f1",
            "5ec7de0a405cdab50f3fd8e0",
            "5ecccc8c405cdab50f3fe06c",
            "5ed1ace9405cdab50f3fe993",
            "5ed47a9d405cdab50f3fedc3",
            "5ee20896360ec3ecf8980adf",
            "5ee8399bb88b559999eced1f",
            "5ee8790bc0c980661fa054c5",
            "5eeb12c6892c58bb7c394ace",
            "5ef1887c87f8da7d5e937200",
            "5ef3003b0e73ad738218a393",
            "5efb2d7587f8da7d5e937816",
            "5efbebd8c1c8059ce3769a86",
            "5efd4fe00e73ad738218aa7a",
            "5f02ba8062b03dd98afb1b75",
            "5f03eeed99f4b36b17846378",
            "5f068818db0493ecef909242",
            "5f070913d2481460393369f3",
            "5f0e70165fd102f22f6b78b0",
            "5f145dd9df9df74e36afab3c",
            "5f14f8de5fd102f22f6b7e6f",
            "5f157800d248146039337a37",
            "5f16348c769820a3e817e632",
            "5f1c506e6207a1a8b17f64e1",
            "5edefa8d405cdab50f4003e2",
            "5f218eb3769820a3e817f483",
            "5f27b530df9df74e36afc17f",
            "5f2921656207a1a8b17f73ba",
            "5f2bac7bd248146039339819",
            "5f2d02b0df9df74e36afcc72",
            "5f30dccc5fd102f22f6ba1c4",
            "5f3cd23b5fd102f22f6bb397",
            "5f3f3da7769820a3e8181cb2",
            "5f48a2246207a1a8b17fa203",
            "5f48cb99d24814603933c1a5",
            "5f168da2d248146039337b0f",
            "5f619ff5df9df74e36b019c2",
            "5f62c9f1df9df74e36b01d2c",
            "5f630825d24814603933ec72",
            "5f65c2cedb0493ecef911bed",
            "5f7051259cffa29cfdd39e47",
            "5f714def93de782518721ce5",
            "5f85b6d930352652858489e0",
            "5f8fb41393de7825187238b5",
            "5f97f3921858a172104688ac",
            "5f9bc1a893de782518725ada",
            "5faa3678e6f9d07f148517ac",
            "5faa6d6ee6f9d07f1485185b",
            "5fb0db80feb86b450c198bd4",
            "5fbccfc5e89148238ce817c0",
            "5fc3459e2f8cc15c287b61af",
            "5fc60f2422a8c7ff39352e31",
            "5fc998c790004265b52c5547",
            "5fcdbff6f9b3ccf7cd2cc5a6",
            "5fdb40b1c9b6991721d6992c",
            "5fe2aa02c9b6991721d6a2fb",
            "5fea8ebec9b6991721d6af24",
            "5feaa374c9b6991721d6af81",
            "5feef320578739320804fdcc",
            "5fef3d1b578739320804fde0",
            "5ff17f9bde850cee539d8d39",
            "5ff3c1a113a14c70d22a8d31",
            "5ff931db531109df66f12598",
            "5ffffa7f1e45db3c1cc27185",
            "60065e0f8deaa14d9e02b7a1",
            "60069db88deaa14d9e02b875",
            "600789132a8cae58be1e5eaf",
            "600928eb57a5c7b95a42ac89",
            "600e8ff1640593207be3204b",
            "601124c0b4f3e0ef1adb552d",
            "60128cba2a8cae58be1e6ff8",
            "6017e6092a8cae58be1e75b3",
            "6017ee8db4f3e0ef1adb5d7e",
            "60191ab4841cc46b7c49a6f4",
            "601a588fb4f3e0ef1adb61aa",
            "601a7413841cc46b7c49a99a",
            "601122e8b4f3e0ef1adb5520",
            "601d18628deaa14d9e02dc98",
            "60375e3b007680b0f8911547",
            "6042f4aeff5718e91a649408",
            "5f7ecb4593de7825187224eb",
            "604ab85824e1e6fbecd1e730",
            "604b1bc2b5bb3a7e65860a4a",
            "60507c8a24e1e6fbecd22869",
            "60516325b5bb3a7e658649e6",
            "6054481fb5bb3a7e658650c7",
            "6055ecc86802dbc46efe56a5",
            "60595eafb5bb3a7e65865c69",
            "60111588841cc46b7c499c39",
            "607539aab209386279b75bf5",
            "6075443a6cc98419b95d7ed7",
            "6076bb1b069125defa1646c6",
            "6076ddc461a945067837be78",
            "60770be8afdac3288fbb5410",
            "60794551afdac3288fbb5c53",
            "6079837d069125defa1650eb",
            "607984eade1fa9b291076f63",
            "60798676069125defa1650f4",
            "607987b81bcc8f5015dfd19a",
            "6079882b1bcc8f5015dfd19d",
            "607988d2dfc740d9194b3d2b",
            "607989a5de1fa9b291076f70",
            "60798d6161a945067837c7e4",
            "60798ca1069125defa165104",
            "607996b5069125defa16510e",
            "607e49a035f5934d3af8d82b",
            "6080caa1c06031c0ebd329ba",
            "60810cde35f5934d3af8e181",
            "60852817d1435a57e45e406b",
            "608807226bb8c3dff7e8dd8b",
            "6089083c53a7917d93dcae6e",
            "608a60115e3405caab8ef442",
            "6094a424d40eb063bac81013",
            "609cb9a94b10fdb6d086204c",
            "609ce6f8a04d2af7aa3da8cd",
            "609f37a4b8df5d7f4449842a",
            "60a20ee55aa36e7b10f6cf3b",
            "60a33036b03f7fc0e76133ed",
            "60a46c99b8301d20d58b0fd2",
            "60a607d37db6c7317ae82383",
            "60a8c1e344816b8ed23330ce",
            "60b479d32c5969c370c5d251",
            "60b71d6d66bba1c04b443900",
            "60bdc94644816b8ed2339efd",
            "60c1d27adbbed34fe0f0c6e7",
            "60c30966c033f37cf9f3e0ca",
            "60c864115c06757d198e5fdd",
            "60cc1dacc033f37cf9f3f2f5",
            "60d0948fa9aa40b865a0acc6",
            "60d095c8a9aa40b865a0acd1",
            "60d1e8ad077c732675e5fbb8",
            "60d2ee52f944551d08b56bb7",
            "60d54f3daccf2bbd6659f3ad",
            "60dd8c5352743a5162b5f5e8",
            "60de7a6852743a5162b5f957",
            "60e414562f222be2d940125a",
            "60e420254690c27532d3ed77",
            "60ed2ca72f222be2d9403276",
            "60ee8a814690c27532d412f8",
            "60f042fd342aa6e0763aba69",
            "60f7b7a82d299f8f0cbc5766",
            "60fe2b92a4490fe41328adc5",
            "610753df22d5077081df57ee",
            "610a5d5ae47471cc44fdbef8",
            "610b3c9d86508f8da8b08436",
            "610e392357e7cd986df9ce59",
            "610f4b4f0fc7bf0dbe1d7d09",
            "6110da571bd96aa110f19d72",
            "61148370adb2703b38cc8158",
            "611cc671adb2703b38cd1f05",
            "611ce9f20fc7bf0dbe1e4674",
            "6124c24c03f23adfbed125e4",
            "612cb27957e7cd986dfaf21f",
            "61376648109ccab1ac590130",
            "613864cdce3034e52627e029",
            "613866212bf07942fbc24266",
            "6139b4362bf07942fbc2469e",
            "613abbfe57c27abcb6467070",
            "614150b6f6fabb78efa41132",
            "614777666e3a9afb7eb0a187",
            "6050d92b24e1e6fbecd22912",
            "6160015009edb81ca824f556",
            "615fe354c6536d077d967f0b",
            "616cd4a341e07dc1d478aa04",
            "61714ab34024c8d3ec69deeb",
            "6173d1454024c8d3ec69e513",
            "6177bdfb3962cc4bc2b6d25a",
            "617b513ad39c439da2ee909d",
            "617f46c76746bc7c6cc8dc05",
            "618358364d2b371c479a50e2",
            "6183840038b0ff04d2a3e68f",
            "618e6e5b041d450d2c25531d",
            "6195b618cfe94d7ade4bfc38",
            "619a62b564fa260377313806",
            "619c46efcc6e31f53d1804bf",
            "61a6e0aedb20fb96553e71ea",
            "61b2f0aba9dfa18f9b46a3ad",
            "61b2fb48b06145a70d0dbe5b",
            "61bd4197ac40125af0f5fbe3",
            "61c2e700fa282c88e103cec2",
            "61c492de4c6f7d217121b93e",
            "61c94baa1ccc3a1faae0aca7",
            "61c968ec4c6f7d217121cd5b",
            "61cbad1103ecfc3a1a252f8f",
            "61cd6b54f9a895d4b3a5d8a2",
            "61cd731c5356b19beeb1aa44",
            "61d45cf95356b19beeb1bfbc",
            "61d4f3ab6112fe9819daa474",
            "61d698216112fe9819daaf73",
            "61d7f47b6112fe9819dab60a",
            "61db9af2148974246be631fd",
            "61dbc5166112fe9819dabdaa",
            "61dbcd725356b19beeb1dc03",
            "61dd0012797bacb26a992fbf",
            "61e2b730a6063d9abc33f4e0",
            "61e2b72b4b66e5c57455cd98",
            "61e3b1890bb300d827e1a669",
            "61e3b18062ff79a3acfda4bb",
            "61e3dab3a6063d9abc33f60d",
            "61e57dff62ff79a3acfdac47",
            "61e57dfa62ff79a3acfdac3e",
            "61e68bf5a6063d9abc3401c8",
            "61e6c6010bb300d827e1b5c7",
            "61e6c677a6063d9abc3402a9",
            "61e6c6a85377858e3d947a45",
            "61e768b762ff79a3acfdb2a3",
            "61e8fee55377858e3d9486f2",
            "61ed84e7c5006adb19fe2ade",
            "6209afddb3ead41b374a3c81",
            "620f543fc5006adb19feabb6",
            "620608579d9cd9dd5cd958e5",
            "6214a6f1c5006adb19ff8248",
            "6218766746d4d2ca636d0497",
            "6227fd57ed03e0a3c933fb02",
            "622822feed03e0a3c933fd8b",
            "622abca2dbd8f53c068de787",
            "6239265473909d64dcfa50b6",
            "6247c5c256f85235f7ddb0b4",
            "6253d62e569a5e3edf2d6140",
            "6254dbaa569a5e3edf2d65af",
            "625697b0569a5e3edf2d7a61",
            "6258dc2fe273565207e57146",
            "625d21efa60d8a4bbe173a26",
            "6273285b64c8a06be2d4b577",
            "627625c23e81781f3ad0b09f",
            "627b9ba23e81781f3ad0c63d",
            "627bd24f0065edd3d51a760e",
            "627a65320065edd3d51a70a9",
            "62823970e4166464dc30390b",
            "62858d45487c500c27f5aab5",
            "6285c1380065edd3d51aaf65",
            "628aef43487c500c27f5bb6c",
            "629f23b8487c500c27f604ee",
            "62a83ead487c500c27f62623",
            "62b5455be4166464dc3110d4",
            "62b6daaf3e81781f3ad1bf2a",
            "62baa01a64c8a06be2d62adf",
            "62bbfcf998b10058fdb7e31a",
            "62c7dac5e553209687c4bd07",
            "62d828d5487c500c27f74155",
            "62dba8c41a358b4399af7262",
            "62de6fbf1dbb334b98baa2ce",
            "62e111291a358b4399af8ec6",
            "62ea35863c1b5281cd47aacb",
            "62f36b4c897786801545648b",
            "5eb2836e3fd198000181aad2",
            "630cb049f9861067e4e7fefb",
            "630d6254c09fc652fac6d200",
            "6315ab4dcb9d00684879a2a0",
            "63341f5e1017fdb58f608aa5",
            "63423c01b765641dc6176457",
            "634e0c6267fa83af64bd0235",
            "635100ecd02abff84af1c16d",
            "63575dac327f73640e96e501",
            "636dc60e30bc61bb57a1202b",
            "637dad3709a6ccfdd6a1cea5",
            "63802d1ab92fc713d05a7bf2",
            "63805560e06fc4c7458758cd",
            "63884ab65db8487f76c8301a",
            "6399734c8d9a873a30aac36b",
            "63998dd0b3dfc42ad5e851b6",
            "63b3de718de0fe7a6554fd13",
            "63d8acd54bc27dfe87596917",
            "63d8c3508b651374b3d4d683",
            "63fd856c360d441ff22c9c8b",
            "640e9cd4e9f200d37adc3e7f",
            "641d3c0d708c83a3fd906160",
            "644a10bf88aef0db4b8c1edc",
            "6465db85b60a10415c31fed8",
            "64771c9db583fece2f057ae5",
            "648835eaa708906d745a4802",
            "648fac53b44b3d9a1d1687f8",
            "649109e2b583fece2f05d1b0",
            "64a150d3a708906d745aabab",
            "64afa220d2963c5649bef499",
            "64b0f0ea702c0cacad9a3e91",
            "64d04c5dbebb3e7debf54b17",
            "64ed9ee94bb5eb36db2ee3f3",
            "6523788d8c5fee586761bb4f",
            "65379273ae64093fdcff1fbc",
            "656d6f743e469c2f3534a9ac",
            "65f941491febde2b6c514822",
            "6613be75702faeb063ba5919",
            "663479514977ded33af8f18a")
    }

    init {
        // 初始化 restTemplate
        val strategy =
            TrustStrategy { _: Array<X509Certificate?>?, _: String? -> true }
        val sslContext = SSLContexts.custom().loadTrustMaterial(null, strategy).build()
        val sslsf = SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE)

        val registry = RegistryBuilder.create<ConnectionSocketFactory>()
            .register("http", PlainConnectionSocketFactory.getSocketFactory())
            .register("https", sslsf)
            .build()

        val connectManager = PoolingHttpClientConnectionManager(registry)
        // 设置连接池
        connectManager.maxTotal = 200
        connectManager.defaultMaxPerRoute = 50

        // 设置超时时间
        val requestConfig = RequestConfig.custom()
            .setSocketTimeout(10000)
            .setConnectTimeout(10000)
            .setConnectionRequestTimeout(1000)
            .build()

        val httpClient = HttpClientBuilder.create()
            .setDefaultRequestConfig(requestConfig)
            .setConnectionManager(connectManager)
            .build()

        val factory = HttpComponentsClientHttpRequestFactory()
        factory.httpClient = httpClient

        restTemplate = RestTemplate(factory)
        restTemplate.messageConverters[1] = StringHttpMessageConverter(StandardCharsets.UTF_8)

        jacksonMapper = jacksonObjectMapper()
        flowForwardMap = mutableMapOf()
        operationMap = mutableMapOf()
    }

    override fun handlePipeline(id: String, request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        logger.info("forward flow: uri: ${request.requestURI} id: $id orgId: ${AuthUtil.getTenant()}")
        var appName = request.getHeader(Header.APPSTACK_APP_NAME)
        if (appName == null) {
            // 不是应用，是系统
            appName = request.getHeader(Header.APPSTACK_SYSTEM_NAME)
        }
        // TODO: 校验
        val releaseWorkflowSn = request.getHeader(Header.APPSTACK_WORKFLOW_SN)
        val releaseStageSn = request.getHeader(Header.APPSTACK_STAGE_SN)
        var releaseStage: ReleaseStageBriefVO? = workflowControllerFacade.releaseStageFacade.findBriefByProcessEngineSn(id)
        var releaseWorkflow: ReleaseWorkflowBriefVO? = null
        releaseStage?.releaseWorkflowSn?.let { releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findBrief(it) }
        return forwardWithPermission(request, response, appName, releaseWorkflow, releaseStage)
    }

    override fun handleCommon(request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        logger.info("forward flow: uri: ${request.requestURI} orgId: ${AuthUtil.getTenant()}")
        var appName = request.getHeader(Header.APPSTACK_APP_NAME)
        if (appName == null) {
            // 不是应用，是系统
            appName = request.getHeader(Header.APPSTACK_SYSTEM_NAME)
        }
        // TODO: 校验
        val releaseWorkflowSn = request.getHeader(Header.APPSTACK_WORKFLOW_SN)
        val releaseStageSn = request.getHeader(Header.APPSTACK_STAGE_SN)
        var releaseWorkflow: ReleaseWorkflowBriefVO? = null
        var releaseStage: ReleaseStageBriefVO? = null
        releaseStageSn?.let { releaseStage = workflowControllerFacade.releaseStageFacade.findBrief(it) }
        releaseStage?.releaseWorkflowSn?.let { releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findBrief(it) }
        return forwardWithPermission(request, response, appName, releaseWorkflow, releaseStage)
    }

    override fun checkPermissions(request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        logger.info("forward flow: uri: ${request.requestURI} orgId: ${AuthUtil.getTenant()}")
        val body = getBody(request)
        val map = parseFormBody(body ?: "")
        val obj = mutableMapOf<String, Boolean>()
        val permission = map["permissions"]?.split(",")
        permission?.let { permission -> permission.forEach { obj[it] = false } }
        var result = generateResult(false, obj, null, null)
        response.contentType = "application/json; charset=utf-8"
        val staffId = map["staffId"]
        val objectType = map["objectType"]
        val objectId = map["objectId"]
        // 查询用户非当前用户或者非流水线权限,直接返回失败
        if (!staffId.equals(AuthUtil.getUserId()) || !objectType.equals("pipeline")) {
            logger.error("failed to forwardFlow: checkPermissions: staffId: ${staffId}, userId: ${AuthUtil.getUserId()} objectType: $objectType body: $body")
            return jacksonMapper.readTree(jacksonMapper.writeValueAsString(result))
        }
        var appName = request.getHeader(Header.APPSTACK_APP_NAME)
        if (appName == null) {
            // 不是应用，是系统
            appName = request.getHeader(Header.APPSTACK_SYSTEM_NAME)
        }
        // TODO: 校验
        val releaseWorkflowSn = request.getHeader(Header.APPSTACK_WORKFLOW_SN)
        val releaseStageSn = request.getHeader(Header.APPSTACK_STAGE_SN)
        var releaseWorkflow: ReleaseWorkflowBriefVO? = null
        var releaseStage: ReleaseStageBriefVO? = null
        objectId?.let { releaseStage = workflowControllerFacade.releaseStageFacade.findBriefByProcessEngineSn(it) }
        releaseStage?.releaseWorkflowSn?.let { releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findBrief(it) }
        permission?.let { permission -> permission.forEach { obj[it] = checkPermission(it, appName, releaseWorkflow, releaseStage) } }
        result = generateResult(true, obj, null, null)
        return jacksonMapper.readTree(jacksonMapper.writeValueAsString(result))
    }

    override fun getPipelineWithParams(pipelineId: String, request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        logger.info("forward flow: uri: ${request.requestURI} orgId: ${AuthUtil.getTenant()} id: $pipelineId")
        var isSystem = false
        var appName: String? = request.getHeader(Header.APPSTACK_APP_NAME)
        if (appName == null) {
            // 不是应用，是系统
            appName = request.getHeader(Header.APPSTACK_SYSTEM_NAME)
            isSystem = true
        }
        // TODO: 校验
        val releaseWorkflowSn: String? = request.getHeader(Header.APPSTACK_WORKFLOW_SN)
        val releaseStageSn: String? = request.getHeader(Header.APPSTACK_STAGE_SN)
        var releaseWorkflow: ReleaseWorkflowBriefVO? = null
        var releaseStage: ReleaseStageBriefVO? = null
        pipelineId?.let { releaseStage = workflowControllerFacade.releaseStageFacade.findBriefByProcessEngineSn(it) }
        releaseStage?.releaseWorkflowSn?.let { releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findBrief(it) }
        if (!checkPermission(PERMISSION_PIPELINE_VIEW, appName, releaseWorkflow, releaseStage)) {
            logger.error("failed to forwardFlow: forward flow access deny: uri: ${request.requestURI}, appName: $appName, workflowSn: ${releaseWorkflow?.sn} stageSn: ${releaseStage?.sn}")
            throw IamForbiddenException("权限检查失败，uri:${request.requestURI} appName: $appName, workflowSn: ${releaseWorkflow?.sn} stageSn: ${releaseStage?.sn}")
        }
        // 填充的参数
        var params = mutableMapOf<String, Any>()
        if (isSystem) {
            params = releaseStageBizService.getPipelineParams(appName ?: "", releaseWorkflowSn ?: "", releaseStageSn ?: "", null)
        } else {
            params = changeRequestStageBizService.getPipelineParams(appName ?: "", releaseWorkflowSn ?: "", releaseStageSn ?: "")
        }
        val body = mapOf(
            "pipelineId" to pipelineId.toLong(),
            "employeeId" to AuthUtil.getUserId(),
            "params" to jacksonObjectMapper().writeValueAsString(params)
        )
        val headers = mapOf(
            "Content-Type" to "application/json"
        )
        // 特定的转发规则
        val path = "/inner/api/pop/v2/pipelines/${pipelineId}/getWithParams"
        return jacksonMapper.readTree(forward(request, response, path, HttpMethod.POST.name, jacksonObjectMapper().writeValueAsString(body), headers))
    }

    override fun getLatestSuccess(request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        logger.info("forward flow: uri: ${request.requestURI} orgId: ${AuthUtil.getTenant()}")
        val body = getBody(request)
        val map = parseFormBody(request.queryString ?: "")
        val pipelineId = map["pipelineId"]
        var appName = request.getHeader(Header.APPSTACK_APP_NAME)
        if (appName == null) {
            // 不是应用，是系统
            appName = request.getHeader(Header.APPSTACK_SYSTEM_NAME)
        }
        // TODO: 校验
        val releaseWorkflowSn = request.getHeader(Header.APPSTACK_WORKFLOW_SN)
        val releaseStageSn = request.getHeader(Header.APPSTACK_STAGE_SN)
        var releaseWorkflow: ReleaseWorkflowBriefVO? = null
        var releaseStage: ReleaseStageBriefVO? = null
        pipelineId?.let { releaseStage = workflowControllerFacade.releaseStageFacade.findBriefByProcessEngineSn(it) }
        releaseStage?.releaseWorkflowSn?.let { releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findBrief(it) }
        if (!checkPermission(PERMISSION_PIPELINE_VIEW, appName, releaseWorkflow, releaseStage)) {
            logger.error("failed to forwardFlow: forward flow access deny: uri: ${request.requestURI}, appName: $appName, workflowSn: ${releaseWorkflow?.sn} stageSn: ${releaseStage?.sn}")
            throw IamForbiddenException("权限检查失败，uri:${request.requestURI} appName: $appName, workflowSn: ${releaseWorkflow?.sn} stageSn: ${releaseStage?.sn}")
        }
        // 特定的转发规则
        val path = "/inner/api/pipelines/${pipelineId}/latestSuccess"
        return jacksonMapper.readTree(forward(request, response, path))
    }

    override fun retryAction(request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        logger.info("forward flow: uri: ${request.requestURI} orgId: ${AuthUtil.getTenant()}")
        val body = getBody(request)
        val map = parseFormBody(request.queryString ?: "")
        val componentInstId = map["componentInstId"]
        var appName = request.getHeader(Header.APPSTACK_APP_NAME)
        if (appName == null) {
            // 不是应用，是系统
            appName = request.getHeader(Header.APPSTACK_SYSTEM_NAME)
        }
        // TODO: 校验
        val releaseWorkflowSn = request.getHeader(Header.APPSTACK_WORKFLOW_SN)
        val releaseStageSn = request.getHeader(Header.APPSTACK_STAGE_SN)
        var releaseWorkflow: ReleaseWorkflowBriefVO? = null
        var releaseStage: ReleaseStageBriefVO? = null
        releaseStageSn?.let { releaseStage = workflowControllerFacade.releaseStageFacade.findBrief(it) }
        releaseStage?.releaseWorkflowSn?.let { releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findBrief(it) }
        if (!checkPermission(PERMISSION_PIPELINE_RUN, appName, releaseWorkflow, releaseStage)) {
            logger.error("failed to forwardFlow: forward flow access deny: uri: ${request.requestURI}, appName: $appName, workflowSn: ${releaseWorkflow?.sn} stageSn: ${releaseStage?.sn}")
            throw IamForbiddenException("权限检查失败，uri:${request.requestURI} appName: $appName, workflowSn: ${releaseWorkflow?.sn} stageSn: ${releaseStage?.sn}")
        }
        // 特定的转发规则
        val path = "/inner/api/pipelines/componentInsts/${componentInstId}/retry"
        return jacksonMapper.readTree(forward(request, response, path, HttpMethod.POST.name))
    }

    override fun componentCommand(request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        logger.info("forward flow: uri: ${request.requestURI} orgId: ${AuthUtil.getTenant()}")
        val body = getBody(request)
        val map = parseFormBody(request.queryString ?: "")
        val requestId = map["requestId"]
        var appName = request.getHeader(Header.APPSTACK_APP_NAME)
        if (appName == null) {
            // 不是应用，是系统
            appName = request.getHeader(Header.APPSTACK_SYSTEM_NAME)
        }
        // TODO: 校验
        val releaseWorkflowSn = request.getHeader(Header.APPSTACK_WORKFLOW_SN)
        val releaseStageSn = request.getHeader(Header.APPSTACK_STAGE_SN)
        var releaseWorkflow: ReleaseWorkflowBriefVO? = null
        var releaseStage: ReleaseStageBriefVO? = null
        releaseStageSn?.let { releaseStage = workflowControllerFacade.releaseStageFacade.findBrief(it) }
        releaseStage?.releaseWorkflowSn?.let { releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findBrief(it) }
        if (!checkPermission(PERMISSION_PIPELINE_RUN, appName, releaseWorkflow, releaseStage)) {
            logger.error("failed to forwardFlow: forward flow access deny: uri: ${request.requestURI}, appName: $appName, workflowSn: ${releaseWorkflow?.sn} stageSn: ${releaseStage?.sn}")
            throw IamForbiddenException("权限检查失败，uri:${request.requestURI} appName: $appName, workflowSn: ${releaseWorkflow?.sn} stageSn: ${releaseStage?.sn}")
        }
        // 特定的转发规则
        val path = "/inner/api/pipelines/componentInsts/${requestId}/cancel"
        return jacksonMapper.readTree(forward(request, response, path, HttpMethod.POST.name))
    }

    override fun skipAction(request: HttpServletRequest, response: HttpServletResponse): JsonNode {
        logger.info("forward flow: uri: ${request.requestURI} orgId: ${AuthUtil.getTenant()}")
        var appName = request.getHeader(Header.APPSTACK_APP_NAME)
        if (appName == null) {
            // 不是应用，是系统
            appName = request.getHeader(Header.APPSTACK_SYSTEM_NAME)
        }
        // TODO: 校验
        val releaseWorkflowSn = request.getHeader(Header.APPSTACK_WORKFLOW_SN)
        val releaseStageSn = request.getHeader(Header.APPSTACK_STAGE_SN)
        var releaseWorkflow: ReleaseWorkflowBriefVO? = null
        var releaseStage: ReleaseStageBriefVO? = null
        releaseStageSn?.let { releaseStage = workflowControllerFacade.releaseStageFacade.findBrief(it) }
        releaseStage?.releaseWorkflowSn?.let { releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findBrief(it) }
        if (!checkPermission(PERMISSION_PIPELINE_RUN, appName, releaseWorkflow, releaseStage)) {
            logger.error("failed to forwardFlow: forward flow access deny: uri: ${request.requestURI}, appName: $appName, workflowSn: ${releaseWorkflow?.sn} stageSn: ${releaseStage?.sn}")
            throw IamForbiddenException("权限检查失败，uri:${request.requestURI} appName: $appName, workflowSn: ${releaseWorkflow?.sn} stageSn: ${releaseStage?.sn}")
        }
        // 特定的转发规则
        val path = "/inner/api/v2/execution-component/skip"
        return jacksonMapper.readTree(forward(request, response, path, HttpMethod.POST.name))
    }

    private fun forwardWithPermission(request: HttpServletRequest, response: HttpServletResponse, appName: String?, releaseWorkflow: ReleaseWorkflowBriefVO?, releaseStage: ReleaseStageBriefVO?): JsonNode {
        updateForwardMap()
        // 路由匹配
        var path = request.servletPath.removePrefix(PREFIX)
        val permissions = mutableListOf<String>()
        val method = request.method
        flowForwardMap.keys.firstOrNull { path.matches(it.toRegex()) && flowForwardMap[it]!!.get("method").asText() == method }?.let {
            val replacement = flowForwardMap[it]!!.get("replacement").asText().replace("$\\", "$")
            path = path.replace(it.toRegex(), replacement)
            flowForwardMap[it]!!.get("permissions").forEach {
                permissions.add(it.asText())
            }
        }
        if (permissions.isEmpty()) {
            logger.error("failed to forwardFlow: load forwardPermission: path: $path")
            throw IamForbiddenException("权限检查失败，path: $path")
        }
        val pass = permissions.firstOrNull { checkPermission(it, appName, releaseWorkflow, releaseStage) }
        if (pass == null) {
            logger.error("failed to forwardFlow: forward flow access deny: path: $path, appName: $appName, workflowSn: ${releaseWorkflow?.sn} stageSn: ${releaseStage?.sn}")
            throw IamForbiddenException("权限检查失败，path:$path appName: $appName, workflowSn: ${releaseWorkflow?.sn} stageSn: ${releaseStage?.sn}")
        }
        return jacksonMapper.readTree(forward(request, response, path))
    }

    /**
     * path: 转换后的内部调用路径，如 /inner/api/xxx
     * method: 内部调用方法，如GET；null时使用请求原有的方法，非空时覆盖
     * body: 请求的body，null时使用请求原有的body,非空时覆盖
     * appendHeaders：覆盖的Headers
     */
    private fun forward(request: HttpServletRequest, response: HttpServletResponse, path: String, method: String? = null, body: String? = null, appendHeaders: Map<String, String> = emptyMap()) : String {
        val targetBody = body ?: getBody(request)
        val headers = getHeaders(request)
        val authHeaders = TokenUtil.generateAuthHeaders(flowAuthConfig.appId, flowAuthConfig.appSecret)
        for (key in authHeaders.keys) {
            headers.add(key, authHeaders[key])
        }
        if(FLOW_GRAY_ORGS.contains(AuthUtil.getTenant())){
            // Flow灰度企业，需要转发到Flow灰度环境
            logger.info("Add Flow Gray Cookie value for Org[${AuthUtil.getTenant()}]")
            logger.info("original cookie: $headers")
            headers.add("cookie", FLOW_GRAY_COOKIE_VALUE)
            logger.info("After cookie: $headers")
        }
        appendHeaders.forEach { (k, v) -> headers[k] = mutableListOf(v) }
        val entity = HttpEntity<String>(targetBody, headers)
        var urlParams = request.queryString ?: ""
        if (urlParams.isNotBlank()) {
            // 后续restTemplate.exchange会编码一次，所以先提前解码
            urlParams = URLDecoder.decode(urlParams, "UTF-8")
            urlParams = "?$urlParams";
        }
        val url = "$flowUrl$path$urlParams"
        var targetMethod = method?: request.method
        logger.info("forward flow path: $path params: $urlParams url: $url method: $targetMethod body: $body appendHeaders: $appendHeaders")
        try {
            val restResponse = restTemplate.exchange(url, HttpMethod.resolve(targetMethod) ?: HttpMethod.GET, entity, String::class.java)
            response.status = restResponse.statusCode.value()
            restResponse.headers.forEach { k, v ->
                if (k.equals(Header.SET_COOKIE_HEADER, ignoreCase = true)) {
                    logger.info("ignore Header: Set-Cookie")
                    return@forEach
                }
                v.forEach {
                    response.setHeader(k,it)
                }
            }
            return restResponse.body
        } catch (e: Throwable) {
            logger.error("failed to forwardFlow: forward flow url: $url", e)
            throw e
        }
    }

    private fun updateForwardMap() {
        // 更新映射表
        if (flowForwardMapStr.isNotBlank() && flowForwardMap.isEmpty()) {
            try {
                val jsonNode = jacksonMapper.readTree(flowForwardMapStr)
                flowForwardMap = jacksonMapper.convertValue(jsonNode, object : TypeReference<LinkedHashMap<String, ObjectNode>>() {})
                logger.info("load flowForwardMap real: $flowForwardMap")
            } catch (e: Throwable) {
                logger.error("failed to forwardFlow: load flowForwardMap: str: $flowForwardMapStr", e)
            }
        }
    }

    private fun checkPermission(permission: String, appName: String?, releaseWorkflow: ReleaseWorkflowBriefVO?, releaseStage: ReleaseStageBriefVO?): Boolean {
        // 无需权限判断
        if (permission == NO_PERMISSION) {
            return true
        }
        // 应用或研发流程不存在
        if (appName == null || releaseWorkflow == null || releaseStage == null) {
            logger.error("failed to forwardFlow: checkPermission: context not exist")
            return false
        }
        val app = coreFacades.appFacade.find(appName)
        if (app == null) {
            logger.error("failed to forwardFlow: checkPermission: app not exist: $appName")
            return false
        }
        val flowPermission = FlowPermission.getByCode(permission)
        if (flowPermission == null) {
            logger.error("failed to forwardFlow: checkPermission: unSupported permission: $permission")
            return false
        }
        return flowPermissionHandlerChain.handle(flowPermission, app, releaseWorkflow, releaseStage)
    }

    private fun generateResult(ret: Boolean, obj: Any, errorCode: Any?, errorMsg: Any?): MutableMap<String, Any?> {
        val result = mutableMapOf<String, Any?>()
        result["successful"] = ret
        result["object"] = obj
        result["errorCode"] = errorCode
        result["errorMsg"] = errorMsg
        return result
    }

    @TestOnly
    fun exposeParseFormBody(body: String): Map<String, String> {
        return parseFormBody(body)
    }

    private fun parseFormBody(body: String): Map<String, String> {
        // 解析form格式body
        return try {
            // 后续restTemplate.exchange会编码一次，所以先提前解码
            body.split("&").map { it.split("=") }.associate { it[0] to URLDecoder.decode(it[1], "UTF-8") }
        } catch (e: Throwable) {
            logger.error("failed to forwardFlow: parseFormBody: body: $body", e)
            emptyMap()
        }
    }

    private fun getBody(request: HttpServletRequest) : String? {
        return try {
            val streamReader = BufferedReader(InputStreamReader(request.inputStream, "UTF-8"));
            val responseStrBuilder = StringBuilder()
            var inputStr: String?
            while (true) {
                inputStr = streamReader.readLine()
                if (inputStr == null) {
                    break
                }
                responseStrBuilder.append(inputStr)
            };
            responseStrBuilder.toString();
        } catch (e: Exception) {
            null
        }
    }

    private fun getHeaders(request: HttpServletRequest) : HttpHeaders {
        val headerNames: Enumeration<String> = request.headerNames
        val headers = HttpHeaders()
        while (headerNames.hasMoreElements()) {
            val headerName : String = headerNames.nextElement()
            var header : String = request.getHeader(headerName)
            // 域名替换为 Flow
            if (headerName == "Host" || headerName == "host") {
                header = flowUrl.replace("http://", "").replace("https://", "")
            }
            headers.add(headerName, header)
        }
        return headers
    }

    /**
     * 所有 POP API 调用 handlePopApiForward 方法，根据操作类型生成对应的 request，再鉴权并转发到 flow 的 API
     */
    override fun handlePopApiForward(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        executionNumber: String?,
        jobId: String?,
        operationName: String,
    ): JsonNode {
        val releaseWorkflow = workflowControllerFacade.releaseWorkflowFacade.findBrief(releaseWorkflowSn)
        val releaseStage = workflowControllerFacade.releaseStageFacade.findBrief(releaseStageSn)
        val pipelineId = releaseStage.processEngineSn
        pipelineId ?: throw BizException(ErrorCode.AS_RELEASE_STAGE_PIPELINE_NOT_FOUND)
        logger.info("[FlowForwardBizServiceImpl.handlePopApiForward] Got pipelineId: $pipelineId")

        // 生成一个用于转发的 request
        val forwardRequest = genForwardRequest(appName, releaseWorkflow, releaseStage, pipelineId, executionNumber, jobId, operationName)
        val response = MockHttpServletResponse()
        val result = forwardWithPermission(forwardRequest, response, appName, releaseWorkflow, releaseStage)
        logger.info("[FlowForwardBizServiceImpl.handlePopApiForward] Got result from flow, result: $result")
        return result
    }

    @TestOnly
    fun exposeGenForwardRequest(
        appName: String,
        releaseWorkflow: ReleaseWorkflowBriefVO,
        releaseStage: ReleaseStageBriefVO,
        pipelineId: String,
        executionNumber: String,
        jobId: String?,
        operationName: String
    ): HttpServletRequest {
        return genForwardRequest(appName, releaseWorkflow, releaseStage, pipelineId, executionNumber, jobId, operationName)
    }

    private fun genForwardRequest(
        appName: String,
        releaseWorkflow: ReleaseWorkflowBriefVO,
        releaseStage: ReleaseStageBriefVO,
        pipelineId: String,
        executionNumber: String?,
        jobId: String?,
        operationName: String
    ): HttpServletRequest {
        var servletPath = ""
        var queryString = ""
        var method = "GET"

        updateOperationMap()
        // 目前只有 cancelExecutionReleaseStage 需要 instanceId
        val instanceId = if(operationName == "cancelExecutionReleaseStage") getInstanceId(appName, releaseWorkflow, releaseStage, executionNumber!!) else ""
        operationMap.keys.firstOrNull { it == operationName }?.let {
            servletPath = operationMap[it]!!.get("servletPath").asText()
                .replace("$\\{pipelineId}", pipelineId)
                .replace("$\\{instanceId}", instanceId)
                .replace("$\\{pipelineRunId}", executionNumber ?: "")
                .replace("$\\{jobId}", jobId ?: "")
            queryString = operationMap[it]!!.get("queryString").asText()
                .replace("$\\{pipelineId}", pipelineId)
                .replace("$\\{instanceId}", instanceId)
                .replace("$\\{pipelineRunId}", executionNumber ?: "")
                .replace("$\\{jobId}", jobId ?: "")
            method = operationMap[it]!!.get("method").asText()
        } ?: logger.error("No matched path with operationName: $operationName")

        val headers = mutableMapOf(
            // TODO: 目前只支持应用，后续会支持系统
            Header.APPSTACK_APP_NAME to appName,
            Header.APPSTACK_STAGE_SN to releaseStage.sn!!,
            Header.APPSTACK_WORKFLOW_SN to releaseWorkflow.sn!!
        )
        return genRequest(method, servletPath, queryString, headers)
    }

    private fun updateOperationMap() {
        if(operationMapStr.isNotBlank() && operationMap.isEmpty()) {
            try {
                val jsonNode = jacksonMapper.readTree(operationMapStr)
                operationMap = jacksonMapper.convertValue(jsonNode, object : TypeReference<LinkedHashMap<String, ObjectNode>>() {})
                logger.info("load operationMap real: $operationMap")
            } catch (e: Throwable) {
                logger.error("failed to load operationMap: $operationMapStr", e)
            }
        }
    }

    private fun genRequest(method: String, servletPath: String, queryString: String, headers: MutableMap<String, String>, body: ByteArray? = null): HttpServletRequest {
        val request = MockHttpServletRequest(
            method,
            "$flowUrl$servletPath?$queryString"
        )
        request.servletPath = servletPath
        request.queryString = queryString
        headers.forEach { (headerName, headerValue) -> request.addHeader(headerName, headerValue)}
        body?.let { request.setContent(it) }
        return request
    }

    @TestOnly
    fun exposeGetInstanceId(
        appName: String,
        releaseWorkflow: ReleaseWorkflowBriefVO,
        releaseStage: ReleaseStageBriefVO,
        executionNumber: String
    ): String {
        return getInstanceId(appName, releaseWorkflow, releaseStage, executionNumber)
    }

    /**
     * 最多发起两次请求可获取 instanceId
     * 第一次发起预查询，获取 instance 总数
     * 当要获取的 instance 在最新的 9 个 instances 中存在，则直接获取 instanceId
     * 第二次根据总数计算 instance 序号，进而获取 instanceId
     */
    private fun getInstanceId(
        appName: String,
        releaseWorkflow: ReleaseWorkflowBriefVO,
        releaseStage: ReleaseStageBriefVO,
        executionNumber: String
    ): String {
        val pipelineId = releaseStage.processEngineSn
        val servletPath = "/forward/flow/ec/ajax/pipelines/$pipelineId/instances"
        val headers = mutableMapOf(
            Header.APPSTACK_APP_NAME to appName,
            Header.APPSTACK_STAGE_SN to releaseStage.sn!!,
            Header.APPSTACK_WORKFLOW_SN to releaseWorkflow.sn!!
        )
        var response = MockHttpServletResponse()
        var queryString = "pageStart=0&pageSize=9"
        var request = genRequest("GET", servletPath, queryString, headers)
        // 预查询 instance 列表
        var responseBodyJson = forwardWithPermission(request, response, appName, releaseWorkflow, releaseStage)
        var instance = responseBodyJson.get("object")?.get("dataList")?.firstOrNull { it.get("instNumber").asText() == executionNumber}
        var instanceId: String? = null
        // 如果在预查询中找到 instance，直接返回 instanceId
        if(instance != null) {
            instanceId = instance.get("id").asText()
            logger.info("[FlowForwardBizServiceImpl.getInstanceId] Got instanceId: $instanceId in pre-query of instances")
            return instanceId
        }
        // 否则获取 instance 总数
        val totalInstanceNumber = responseBodyJson.get("object")?.get("total").toString().toInt()
        if(totalInstanceNumber < executionNumber.toInt()) {
            throw BizException(ErrorCode.AS_RELEASE_STAGE_PIPELINE_INSTANCE_NOT_FOUND)
        }
        // 计算 executionNumber 对应的 pateStart
        queryString = "pageStart=${totalInstanceNumber - executionNumber.toInt()}&pageSize=1"
        response = MockHttpServletResponse()
        request = genRequest("GET", servletPath, queryString, headers)
        // 精确查询 instance
        responseBodyJson = forwardWithPermission(request, response, appName, releaseWorkflow, releaseStage)
        instance = responseBodyJson.get("object")?.get("dataList")?.firstOrNull { it.get("instNumber").asText() == executionNumber}
        instance ?: throw BizException(ErrorCode.AS_RELEASE_STAGE_PIPELINE_INSTANCE_NOT_FOUND)
        instanceId = instance.get("id").asText()
        logger.info("[FlowForwardBizServiceImpl.getInstanceId] Got instanceId: $instanceId")
        return instanceId
    }

}