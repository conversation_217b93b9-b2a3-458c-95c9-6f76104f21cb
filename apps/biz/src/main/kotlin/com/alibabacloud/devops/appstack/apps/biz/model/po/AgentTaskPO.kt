package com.alibabacloud.devops.appstack.apps.biz.model.po

import com.alibabacloud.devops.appstack.libs.common.model.BasePO
import com.alibabacloud.devops.appstack.libs.common.annotation.Comment
import com.baomidou.mybatisplus.annotation.TableName
import lombok.Data
import javax.persistence.Index
import javax.persistence.Table

@TableName("agent_tasks")
@Comment("Agent安装任务")
@Table(
    indexes = [
        Index(columnList = "task_no(100), is_deleted(16), org_id(24)", unique = true),
    ]
)
@Data
open class AgentTaskPO : BasePO() {
    @Comment("任务编号")
    var taskNo: String? = null

    @Comment("flow任务id")
    var jobId: Long? = null

    @Comment("关联对象")
    var refObj: String? = null

    @Comment("关联对象名称")
    var refObjName: String? = null

    @Comment("flow任务状态")
    var status: String? = null
}