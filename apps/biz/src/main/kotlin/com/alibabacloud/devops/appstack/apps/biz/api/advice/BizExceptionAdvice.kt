package com.alibabacloud.devops.appstack.apps.biz.api.advice

import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.constant.Header
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.LocaleUtil
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import com.alibabacloud.devops.iam.exception.IamForbiddenException
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired

import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 * @author: <EMAIL>
 * @date: 2021-08-26 15:29
 * @version: BizExceptionAdvice, v0.1
 **/
@RestControllerAdvice
class BizExceptionAdvice {

    companion object {
        val ERROR_ATTRIBUTE = "BizException.errorCode"
    }

    private val logger = LoggerFactory.getLogger(javaClass)

    @Autowired
    lateinit var i18nMessageService: I18nMessageService

    @ExceptionHandler(BizException::class)
    fun bizException(e: BizException, request: HttpServletRequest, response: HttpServletResponse): YunxiaoErrorResponse {
        val errorMessage = i18nMessageService.getErrorMessage(e)
        val errorAdvice = i18nMessageService.getErrorAdvice(e)
        logger.error("ErrorCode: ${e.errorEntry.code}, ErrorMessage: $errorMessage, ErrorAdvice: $errorAdvice, ErrorContext: ${e.context}", e)
        request.setAttribute(ERROR_ATTRIBUTE, e.errorEntry.code)
        response.status = if (e.httpStatus == 0) e.errorEntry.code.httpStatus else e.httpStatus
        return YunxiaoErrorResponse(
            errorCode = e.errorEntry.code.value,
            errorMessage = errorAdvice,
            httpStatus = response.status.toString(),
            title = errorMessage,
            traceId = AuthUtil.getTraceId(),
            requestId = request.getHeader(Header.POP_REQUEST_ID)
        )
    }

    @ExceptionHandler(Exception::class)
    fun unknownError(e: Exception, request: HttpServletRequest, response: HttpServletResponse): YunxiaoErrorResponse {
        logger.error("unknown exception:", e)
        request.setAttribute(ERROR_ATTRIBUTE, ErrorCode.AS_UNKNOWN)
        response.status = 500
        return YunxiaoErrorResponse(
            errorCode = ErrorCode.AS_UNKNOWN.value,
            errorMessage = i18nMessageService.errorAdviceResource.getMessage(ErrorCode.AS_UNKNOWN.value, null, LocaleUtil.getLocale()),
            httpStatus = "500",
            title = i18nMessageService.errorMessageResource.getMessage(ErrorCode.AS_UNKNOWN.value, null, LocaleUtil.getLocale()),
            traceId = AuthUtil.getTraceId(),
            requestId = request.getHeader(Header.POP_REQUEST_ID)
        )
    }

    @ExceptionHandler(IamForbiddenException::class)
    fun forbiddenException(e: IamForbiddenException, request: HttpServletRequest, response: HttpServletResponse): YunxiaoErrorResponse {
        val errorCode = ErrorCode.AS_PERMISSION_DENIED
        val errorMessage = i18nMessageService.errorMessageResource.getMessage(
            errorCode.value,
            null,
            LocaleUtil.getLocale(),
        )
        val errorAdvice = i18nMessageService.errorAdviceResource.getMessage(
            errorCode.value,
            null,
            LocaleUtil.getLocale(),
        )
        request.setAttribute(ERROR_ATTRIBUTE, errorCode)
        response.status = 403
        return YunxiaoErrorResponse(
            errorCode = errorCode.value,
            errorMessage = errorAdvice,
            httpStatus = "403",
            title = errorMessage,
            traceId = AuthUtil.getTraceId(),
            requestId = request.getHeader(Header.POP_REQUEST_ID)
        )
    }
}