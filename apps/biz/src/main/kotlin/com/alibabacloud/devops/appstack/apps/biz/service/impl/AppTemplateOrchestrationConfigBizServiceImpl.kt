package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.model.ext.toModel
import com.alibabacloud.devops.appstack.apps.biz.model.ext.toPO
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.*
import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateConfigSyncStatusPO
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateOrchestrationConfigBizService
import com.alibabacloud.devops.appstack.apps.biz.service.OrchestrationTemplateBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.UuidUtils
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.Label
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfigSyncStatus
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.InvalidChangeItem
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.OrchestrationSyncRevision
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.k8s.LocatorInfo
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.template.AppTemplateOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.transfer
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateCheckRequest
import com.alibabacloud.devops.appstack.libs.model.request.OrchestrationTemplateSyncRequest
import com.alibabacloud.devops.appstack.libs.model.response.orchestration.AppOrchestrationSyncStatus
import com.alibabacloud.devops.appstack.libs.model.response.orchestration.OrchestrationTemplateSyncStatusResponse
import com.alibabacloud.devops.appstack.libs.model.vo.AppOrchestrationDiffVO
import com.alibabacloud.devops.appstack.libs.model.vo.OrchestrationDiffVO
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.UnicastSender
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.AppTemplateOrchestrationSyncBody
import com.alibabacloud.devops.appstack.libs.mq.spring.boot.starter.common.UnicastTagEnum
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2023-10-26
 */
@Service
open class AppTemplateOrchestrationConfigBizServiceImpl : AppTemplateOrchestrationConfigBizService {

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var orchestrationTemplateBizService: OrchestrationTemplateBizService

    @Autowired
    lateinit var appTemplateUsageRelationMapper: AppTemplateUsageRelationMapper

    @Autowired
    lateinit var appTemplateConfigSyncStatusMapper: AppTemplateConfigSyncStatusMapper

    @Autowired
    lateinit var unicastSender: UnicastSender

    companion object {
        val DEFAULT_APP_TEMPLATE_ORCHESTRATION_INSTANCE_NAME = "default_builtin"
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.ORG_APP_TEMPLATE_VIEW),
            Access(action = Action.APP_TEMPLATE_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_EDIT, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_USE, resourceArgIndex = 0),
        ]
    )
    override fun findPaginatedRevisionAndLabel(
        name: String,
        current: Long,
        pageSize: Long,
    ): Pagination<OrchestrationSyncRevision> {
        val page =
            coreFacades.appTemplateOrchestrationFacade.findPaginatedRevisionAndLabel(name, current, pageSize)
        val syncStatus = appTemplateConfigSyncStatusMapper.listByTemplate(
            name,
            AppTemplateConfig.TypeEnum.Orchestration.name,
            DEFAULT_APP_TEMPLATE_ORCHESTRATION_INSTANCE_NAME,
        ).map { it.toModel() }
        val map = syncStatus.filter {
            it.templateSyncStatus?.lastAppliedTemplateRevisionSha != null
                    && it.templateSyncStatus?.lastAppliedAppRevisionSha == it.instanceUpdateStatus?.actualAppRevisionSha
        }.groupBy {
            it.templateSyncStatus?.lastAppliedTemplateRevisionSha
        }.mapValues { entry ->
            entry.value.map { it.appName }
        }
        val records = page.records.map {
            OrchestrationSyncRevision(
                sha = it.sha,
                message = it.message,
                author = it.author,
                commitTime = it.commitTime,
                labels = it.labels,
                appNames = map[it.sha]
            )
        }
        return page.transfer(records)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.ORG_APP_TEMPLATE_VIEW),
            Access(action = Action.APP_TEMPLATE_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_EDIT, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_USE, resourceArgIndex = 0),
        ]
    )
    override fun findOrchestrationBySha(name: String, sn: String, sha: String): AppTemplateOrchestration {
        return coreFacades.appTemplateOrchestrationFacade.find(name, sn, sha)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.ORG_APP_TEMPLATE_VIEW),
            Access(action = Action.APP_TEMPLATE_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_EDIT, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_USE, resourceArgIndex = 0),
        ]
    )
    override fun compare(
        name: String,
        sn: String,
        beforeRevisionSha: String,
        afterRevisionSha: String,
    ): Triple<OrchestrationDiffVO, OrchestrationDiffVO, List<DiffItem<String>>> {
        return coreFacades.appTemplateOrchestrationFacade.compare(name, sn, beforeRevisionSha, afterRevisionSha)
    }

    override fun check(name: String, checkRequest: OrchestrationTemplateCheckRequest): List<InvalidChangeItem> {
        // 复用应用模板的check方法
        return orchestrationTemplateBizService.check(checkRequest)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.APP_TEMPLATE_EDIT, resourceArgIndex = 0),
        ]
    )
    override fun bindLabels(name: String, sha: String, labels: List<Label>) {
        coreFacades.appTemplateOrchestrationFacade.bindLabels(name, sha, labels)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.APP_TEMPLATE_EDIT, resourceArgIndex = 0),
        ]
    )
    override fun sync2Apps(name: String, request: OrchestrationTemplateSyncRequest) {
        // 检查版本
        findOrchestrationBySha(name, request.orchestrationSn, request.orchestrationSha)
        if (request.appNames.isEmpty()) {
            throw BizException(ErrorCode.AS_APP_NOT_FOUND)
        }
        checkAndInitSyncStatus(name, request)
        unicastSender.send(
            UnicastTagEnum.APPTEMPLATE_ORCHESTRATION_SYNC,
            jacksonObjectMapper().writeValueAsBytes(
                AppTemplateOrchestrationSyncBody(
                    appTemplateName = name,
                    orchestrationSn = request.orchestrationSn,
                    orchestrationSha = request.orchestrationSha,
                    appNames = request.appNames,
                    commitMsg = request.commitMsg,
                    transactionId = request.transactionId ?: UuidUtils.getUuid()
                )
            )
        )
    }

    override fun syncStatus(name: String, sn: String): OrchestrationTemplateSyncStatusResponse {
        // 校验并补齐同步状态数据
        checkAndInitSyncStatus(name)

        val templateOrchestration = coreFacades.appTemplateOrchestrationFacade.find(name, sn)
        val latestRevisionSha = templateOrchestration.revision!!.sha

        val syncStatus = appTemplateConfigSyncStatusMapper.listByTemplate(
            name,
            AppTemplateConfig.TypeEnum.Orchestration.name,
            DEFAULT_APP_TEMPLATE_ORCHESTRATION_INSTANCE_NAME,
        ).map { it.toModel() }

        val statusList = syncStatus.map {
            AppOrchestrationSyncStatus(
                appName = it.appName,
                appOrchestrationRevisionSha = it.instanceUpdateStatus?.actualAppRevisionSha,
                lastAppliedTemplateRevisionSha = it.templateSyncStatus?.lastAppliedTemplateRevisionSha,
                lastSyncErrorMessage = it.templateSyncStatus?.lastSyncErrorMessage,
                commitMessage = it.instanceUpdateStatus?.actualAppRevisionCommitMessage,
                commitTime = it.instanceUpdateStatus?.actualAppRevisionGmtModified,
                author = it.instanceUpdateStatus?.actualAppRevisionCommitAuthor,
                syncSuccess = it.templateSyncStatus?.lastAppliedTemplateRevisionSha == latestRevisionSha
                        && it.templateSyncStatus?.lastAppliedAppRevisionSha != null
                        && it.templateSyncStatus?.lastAppliedAppRevisionSha == it.instanceUpdateStatus?.actualAppRevisionSha
            )
        }
        return OrchestrationTemplateSyncStatusResponse(
            templateName = name,
            latestRevisionSha = latestRevisionSha,
            statusList = statusList,
            successCount = statusList.count { it.syncSuccess }
        )
    }

    override fun compareToAppOrchestration(
        templateName: String,
        sn: String,
        appName: String,
    ): Triple<AppOrchestrationDiffVO, OrchestrationDiffVO, List<DiffItem<String>>> {
        val syncStatus = appTemplateConfigSyncStatusMapper.findByTemplateAndApp(
            templateName,
            AppTemplateConfig.TypeEnum.Orchestration.name,
            DEFAULT_APP_TEMPLATE_ORCHESTRATION_INSTANCE_NAME,
            appName
        )?.toModel()
        val appOrchestrationSha = syncStatus?.instanceUpdateStatus?.actualAppRevisionSha
        return coreFacades.appTemplateOrchestrationFacade.compareToAppOrchestration(
            templateName = templateName,
            sn = sn,
            appName = appName,
            appOrchestrationSha = appOrchestrationSha
        )
    }

    private fun checkAndInitSyncStatus(templateName: String) {
        val syncStatusPOs = appTemplateConfigSyncStatusMapper.listByTemplate(
            templateName,
            AppTemplateConfig.TypeEnum.Orchestration.name,
            DEFAULT_APP_TEMPLATE_ORCHESTRATION_INSTANCE_NAME
        )
        val findPaginatedByAppTemplateName =
            appTemplateUsageRelationMapper.findAllByAppTemplateName(templateName)
        val appNames = findPaginatedByAppTemplateName.map { it.appName }
        completeSyncStatus(templateName, syncStatusPOs, appNames)
    }

    /**
     *  fixme 应用多个编排模板时，此校验不成立
     */
    private fun checkAndInitSyncStatus(templateName: String, request: OrchestrationTemplateSyncRequest) {
        val syncStatusPOs = appTemplateConfigSyncStatusMapper.listByTemplateAndApps(
            templateName,
            AppTemplateConfig.TypeEnum.Orchestration.name,
            DEFAULT_APP_TEMPLATE_ORCHESTRATION_INSTANCE_NAME,
            request.appNames
        )
        completeSyncStatus(templateName, syncStatusPOs, request.appNames)
    }

    private fun completeSyncStatus(
        templateName: String,
        syncStatusPOs: List<AppTemplateConfigSyncStatusPO>,
        appNameList: List<String>,
    ) {
        val intersect = appNameList.intersect(syncStatusPOs.map { it.appName }.toSet())
        (appNameList - intersect).forEach { appName ->
            // add syncStatus
            val po = AppTemplateConfigSyncStatus(
                appTemplateName = templateName,
                appTemplateConfigType = AppTemplateConfig.TypeEnum.Orchestration,
                appTemplateConfigInstanceName = DEFAULT_APP_TEMPLATE_ORCHESTRATION_INSTANCE_NAME,
                appName = appName,
            ).toPO()
            appTemplateConfigSyncStatusMapper.insert(po)
        }
        syncStatusPOs.filter { !intersect.contains(it.appName) }.forEach { syncStatusPO ->
            // delete syncStatus
            appTemplateConfigSyncStatusMapper.deleteById(syncStatusPO.id)
        }
    }

    override fun getLocatorList(
        name: String,
        sn: String,
        envName: String,
        profileName: List<Map<String, String>>,
    ): List<LocatorInfo> {
        return try {
            coreFacades.appTemplateOrchestrationFacade.getLocatorList(
                name = name,
                sn = sn,
                envName = envName,
                profileName = profileName
            )
        } catch (e: Exception) {
            emptyList()
        }
    }
}