package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeableEnv
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.Task
import io.fabric8.kubernetes.api.model.HasMetadata

/**
 * @author: <EMAIL>
 * @date: 2022-06-10 11:12
 * @version: EnvProxyBizAdapterFactory, v0.1
 **/
interface EnvProxyBizAdapterFactory {

    fun get(engineType: ChangeableEnv.EngineType, kind: String): KindResourceService
}

interface KindResourceService {
    fun countGroupByKind(changeOrder: ChangeOrder): Map<String, Int>

    fun convertHasMetadataVOList(
        changeOrder: ChangeOrder,
        kind: String,
        envName: String,
        resourceList: List<HasMetadata>,
    ): List<HasMetadataVO>

    fun isMatched(task: Task, hasMetadata: HasMetadata, appName: String, envName: String, kind: String): Boolean

    fun findRelatedResource(
        appName: String,
        envName: String,
        resource: HasMetadata,
    ): List<HasMetadata> {
        return emptyList()
    }

    companion object {
        const val DEFAULT_VERSION_LABEL = "version"
    }
}