package com.alibabacloud.devops.appstack.apps.biz.model.mapper

import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplatePO
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.core.mapper.BaseMapper
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Select

/**
 * <AUTHOR>
 * @create 2023/10/16 6:53 PM
 **/
@Mapper
interface AppTemplateMapper : BaseMapper<AppTemplatePO> {

    @Select(
        """
        <script>
            SELECT 
                *
            FROM app_templates
            WHERE is_deleted='N'
            <if test="search != null and search != ''">
                AND (display_name like CONCAT('%', #{search}, '%'))
            </if>
            <if test="sort == 'asc'">
                <if test="orderBy == 'id'">
                    <if test="nextToken != null">
                        AND id &gt; #{nextToken}
                    </if>
                    ORDER BY id ASC
                </if>
                <if test="orderBy == 'gmtCreate'">
                    <if test="nextToken != null">
                        AND gmt_create &gt; #{nextToken}
                    </if>
                    ORDER BY gmt_create ASC
                </if>
            </if>
            <if test="sort == 'desc'">
                <if test="orderBy == 'id'">
                    <if test="nextToken != null">
                        AND id &lt; #{nextToken}
                    </if>
                    ORDER BY id DESC
                </if>
                <if test="orderBy == 'gmtCreate'">
                    <if test="nextToken != null">
                        AND gmt_create &lt; #{nextToken}
                    </if>
                    ORDER BY gmt_create DESC
                </if>
            </if>
            LIMIT #{perPage}
        </script>
        """
    )
    fun list(
        search: String?,
        perPage: Int? = 20,
        orderBy: String?,
        sort: String?,
        nextToken: String?
    ): List<AppTemplatePO>

}

fun AppTemplateMapper.findByName(name: String): AppTemplatePO? =
    selectOne(QueryWrapper<AppTemplatePO>().eq("name", name))

fun AppTemplateMapper.deleteByName(name: String): Int =
    delete(QueryWrapper<AppTemplatePO>().eq("name", name))

fun AppTemplateMapper.findPaginated(search: String, names: List<String>, page: Page<AppTemplatePO>): Page<AppTemplatePO> {
    val queryWrapper = QueryWrapper<AppTemplatePO>()
    if (search.isNotBlank()) {
        queryWrapper.and {
            it.like("name", search).or().like("display_name", search)
        }
    }
    if (names.isNotEmpty()) {
        queryWrapper.`in`("name", names)
    }
    queryWrapper.orderByAsc("gmt_create")
    return selectPage(page, queryWrapper)
}

fun AppTemplateMapper.searchByNames(names: List<String>) =
    selectList(QueryWrapper<AppTemplatePO>().`in`("name", names))