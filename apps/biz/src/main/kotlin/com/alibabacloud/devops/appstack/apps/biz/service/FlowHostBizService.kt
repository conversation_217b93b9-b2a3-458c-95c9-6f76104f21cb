package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.*

/**
 * @author: <EMAIL>
 * @date: 2022-07-08 15:13
 * @version: HostGroupBizService, v0.1
 **/
interface FlowHostBizService {

    fun createExperience(hostGroupExperienceDTO: HostGroupExperienceDTO): Long?
    fun fetchCreateResult(instanceId: String, flowScopeType: FlowScopeType): Long?

    fun findEcs(
        serviceConnectionId: Long,
        aliyunRegionId: String,
        flowScopeType: FlowScopeType,
        checkAllEcsMachine: Boolean,
        sns: String?
    ): List<Host>

    fun findSelf(purpose: FlowPurpose, flowScopeType: FlowScopeType, sns: String?): List<Host>
    fun findAllByGroupId(groupId: Long): List<Host>
    fun fetchInstallCommand(purpose: FlowPurpose, flowScopeType: FlowScopeType, osType: OsType): String
    fun fetchMachineDeployLog(tunnelId: Long, machineSn: String): DeployMachineLog

    fun isRunnerFeature(): Boolean
}