package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.DomainTagBizService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.LocaleUtil
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.app.DomainTag
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.iam.AppRole
import com.alibabacloud.devops.appstack.libs.model.request.TagBatchUnbindingRequest
import com.alibabacloud.devops.appstack.libs.model.request.TagSearchRequest
import com.alibabacloud.devops.appstack.libs.model.response.TagBatchFailInfo
import com.alibabacloud.devops.appstack.libs.model.response.TagBatchOperationResponse
import com.alibabacloud.devops.appstack.libs.model.vo.DomainTagWithCreatorVO
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.exception.IamForbiddenException
import org.springframework.aop.framework.AopContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
open class DomainTagBizServiceImpl: DomainTagBizService {
    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var iamService: IamService

    @Autowired
    lateinit var i18nMessageService: I18nMessageService

    @Can(
        accessList = [
            Access(action = Action.ORG_TAG_MANAGE)
        ]
    )
    override fun create(domainTag: DomainTag): DomainTag {
        return coreFacades.domainTagFacade.create(domainTag)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_TAG_MANAGE)
        ]
    )
    override fun update(name: String, domainTag: DomainTag): DomainTag {
        return coreFacades.domainTagFacade.update(name, domainTag)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_TAG_MANAGE)
        ]
    )
    override fun delete(name: String) {
        return coreFacades.domainTagFacade.delete(name)
    }

    override fun list(req: TagSearchRequest, current: Long, pageSize: Long): PageList<DomainTagWithCreatorVO> {
        return coreFacades.domainTagFacade.list(req, current, pageSize)
    }

    override fun listAppNamesByTag(tagName: String): List<String> {
        return coreFacades.domainTagFacade.listAppNamesByTag(tagName)
    }

    override fun listTagsByAppName(appName: String): List<DomainTag> {
        return coreFacades.domainTagFacade.listTagsByAppName(appName)
    }

    override fun listTagsByAppNames(appNames: List<String>): List<DomainTag> {
        return coreFacades.domainTagFacade.listTagsByAppNames(appNames)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_BASIC_SET, resourceArgIndex = 1)
        ]
    )
    override fun updateTagBindings(tagNames: List<String>, appName: String) {
        return coreFacades.domainTagFacade.updateTagBindings(tagNames, appName)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_BASIC_SET, resourceArgIndex = 1)
        ]
    )
    override fun bind(tagNames: List<String>, appName: String) {
        return coreFacades.domainTagFacade.bind(tagNames, appName)
    }

    override fun batchBind(tagNames: List<String>, appNames: List<String>): TagBatchOperationResponse {
        val response = TagBatchOperationResponse()
        appNames.forEach {
            try {
                (AopContext.currentProxy() as DomainTagBizServiceImpl).bind(tagNames, it)
                response.successAppNames.add(it)
            } catch (e: IamForbiddenException) {
                response.failAppInfo[it] = TagBatchFailInfo(
                    failReason = genFailReason(ErrorCode.AS_APP_BASIC_SET_PERMISSION_DENIED)
                )
            } catch (e: Exception) {
                response.failAppInfo[it] = TagBatchFailInfo(
                    failReason = genFailReason(ErrorCode.AS_UNKNOWN)
                )
            }
        }
        fillFailInfo(response.failAppInfo)
        return response
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_BASIC_SET, resourceArgIndex = 1)
        ]
    )
    override fun unbind(tagNames: List<String>, appName: String) {
        return coreFacades.domainTagFacade.unbind(tagNames, appName)
    }

    override fun batchUnbind(tagNames: List<String>, appNames: List<String>): TagBatchOperationResponse {
        val response = TagBatchOperationResponse()
        appNames.forEach {
            try {
                (AopContext.currentProxy() as DomainTagBizServiceImpl).unbind(tagNames, it)
                response.successAppNames.add(it)
            } catch (e: IamForbiddenException) {
                response.failAppInfo[it] = TagBatchFailInfo(
                    failReason = genFailReason(ErrorCode.AS_APP_BASIC_SET_PERMISSION_DENIED)
                )
            } catch (e: Exception) {
                response.failAppInfo[it] = TagBatchFailInfo(
                    failReason = genFailReason(ErrorCode.AS_UNKNOWN)
                )
            }
        }
        fillFailInfo(response.failAppInfo)
        return response
    }

    private fun genFailReason(errorCode: ErrorCode): String {
        return i18nMessageService.errorMessageResource.getMessage(
            errorCode.value,
            null,
            LocaleUtil.getLocale()
        )
    }

    private fun fillFailInfo(failInfo: MutableMap<String, TagBatchFailInfo>) {
        if (failInfo.isEmpty()) return
        val ownerMap = iamService.findRolePlayers(ProtocolType.AppStackApp, failInfo.keys.toList(), AppRole.owner.name).mapValues { it.value[0] }
        failInfo.forEach { (appName, failInfo) -> failInfo.ownerId = ownerMap[appName]}
    }
}