package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.apps.biz.model.vo.AppTemplateWorkflowRevisionVO
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ga.AppTemplateWorkflowRevision
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.sync.AppTemplateSyncRequest
import com.alibabacloud.devops.appstack.libs.model.response.apptemplate.sync.AppTemplateSyncResponse

/**
 * @author: <EMAIL>
 * @date: 2024-03-22 16:38
 * @version: AppTemplateWorkflowConfigBizService, v0.1
 **/
interface AppTemplateWorkflowConfigBizService {

    fun findPaginatedRevision(
        appTemplateName: String,
        name: String,
        current: Long,
        pageSize: Long,
    ): Pagination<AppTemplateWorkflowRevisionVO>

    fun deleteByName(
        appTemplateName: String,
        name: String,
        appTemplateConfig: String?,
    )

    fun compare(
        appTemplateName: String,
        name: String,
        beforeRevisionSha: String,
        afterRevisionSha: String,
    ): Triple<String, String, List<DiffItem<String>>>

    fun sync2Apps(name: String, request: AppTemplateSyncRequest)

    fun findByName(
        appTemplateName: String,
        name: String,
        sha: String?,
    ): AppTemplateWorkflowRevision

    fun syncStatus(templateName: String, name: String): AppTemplateSyncResponse

    fun compareWithApp(
        appTemplateName: String,
        name: String,
        appName: String,
    ): Triple<String, String, List<DiffItem<String>>>

}