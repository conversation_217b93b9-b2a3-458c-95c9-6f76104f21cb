package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.AckAddonService
import com.alibabacloud.devops.appstack.libs.model.request.resource.AckAddonInstallRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-03-18 16:51
 * @version: AckComponentApi, v0.1
 **/
@Tag(name = "AckComponent", description = "ACK组件相关api")
@RestController
@RequestMapping("/api/v1/ack")
open class AckAddonApi {

    @Autowired
    lateinit var ackAddonService: AckAddonService

    /**
     * 查询集群当前组件状态（通过版本号来看）
     */
    @GetMapping("/addons/describeClusterAddonsUpgradeStatus")
    fun describeClusterAddonsUpgradeStatus(@RequestParam("clusterId") clusterId: String): Response<Map<String, String>> {
        return Response.success(ackAddonService.describeClusterAddonsUpgradeStatus(clusterId))
    }

    /**
     * 为集群安装组件
     */
    @PostMapping("/addons/installClusterAddons")
    fun installClusterAddons(
        @RequestBody ackAddonInstallRequest: AckAddonInstallRequest,
    ): Response<Unit> {
        return Response.success(ackAddonService.installClusterAddons(ackAddonInstallRequest))
    }

    /**
     * 获取集群的Grafana大盘的列表
     */
    @GetMapping("/prometheus/listDashboards")
    fun listDashboards(
        @RequestParam("clusterId") clusterId: String,
        @RequestParam("regionId") regionId: String,
    ): Response<Map<String, String>> {
        return Response.success(ackAddonService.listDashboards(clusterId, regionId))
    }
}