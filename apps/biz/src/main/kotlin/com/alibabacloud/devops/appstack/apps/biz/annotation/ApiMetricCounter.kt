package com.alibabacloud.devops.appstack.apps.biz.annotation

import io.micrometer.core.instrument.Tag
import io.micrometer.core.instrument.Tags


/**
 * @author: <EMAIL>
 * @date: 2023-03-08 14:38
 * @version: ApiMetricCounter, v0.1
 **/
@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class ApiMetricCounter(
    val metricPrefix: String = "appstack",
    val metricName: String,
    val methodTag: String = ""
)
