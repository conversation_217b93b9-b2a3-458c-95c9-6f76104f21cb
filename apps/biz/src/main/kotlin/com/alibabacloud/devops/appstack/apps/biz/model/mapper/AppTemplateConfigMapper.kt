package com.alibabacloud.devops.appstack.apps.biz.model.mapper

import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateConfigPO
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper
import com.baomidou.mybatisplus.core.mapper.BaseMapper
import org.apache.ibatis.annotations.Mapper

/**
 * <AUTHOR> <EMAIL>
 * @version : AppTemplateConfigMapper, v0.1
 * @date : 2023-10-18 11:51
 **/

@Mapper
interface AppTemplateConfigMapper : BaseMapper<AppTemplateConfigPO>

fun AppTemplateConfigMapper.findAll(appTemplateName: String): List<AppTemplateConfigPO> =
    selectList(
        QueryWrapper<AppTemplateConfigPO>().eq("app_template_name", appTemplateName)
    )

fun AppTemplateConfigMapper.find(appTemplateName: String, type: String): AppTemplateConfigPO? =
    selectOne(
        QueryWrapper<AppTemplateConfigPO>().eq("app_template_name", appTemplateName).eq("type", type)
    )

fun AppTemplateConfigMapper.update(po: AppTemplateConfigPO) =
    update(po, QueryWrapper<AppTemplateConfigPO>().eq("app_template_name", po.appTemplateName).eq("type", po.type))

fun AppTemplateConfigMapper.deleteByNameAndType(appTemplateName: String, type: String) =
    delete(QueryWrapper<AppTemplateConfigPO>().eq("app_template_name", appTemplateName).eq("type", type))