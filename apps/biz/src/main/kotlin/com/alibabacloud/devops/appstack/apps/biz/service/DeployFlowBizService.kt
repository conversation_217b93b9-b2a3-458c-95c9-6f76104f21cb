package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.env.Env
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Placeholder
import com.alibabacloud.devops.appstack.libs.model.request.FlowCreateChangeOrderRequest
import com.alibabacloud.devops.appstack.libs.model.request.FlowCreateCleanOrderRequest
import com.alibabacloud.devops.appstack.libs.model.vo.AppVO
import com.alibabacloud.devops.appstack.libs.model.vo.ChangeOrderOnFlowVO

/**
 * @author: <EMAIL>
 * @date: 2022-03-08 09:43
 * @version: DeployFlowBizService, v0.1
 **/
interface DeployFlowBizService {

    companion object {
        const val COMMON_ARTIFACT_PREFIX = "artifact."
        const val IMAGE_PREFIX = "image."
        const val IMAGE_TYPE = "IMAGE"
        const val V1_IMAGE_NAME = "image"
    }

    fun createChangeOrder(flowCreateChangeOrderRequest: FlowCreateChangeOrderRequest): String?
    fun createCleanOrder(flowCreateCleanOrderRequest: FlowCreateCleanOrderRequest): String?
    fun findChangeOrder(
        flowInsId: String,
        removeFlowInstId: Boolean,
        appName: String?,
        envName: String?
    ): ChangeOrderOnFlowVO

    fun findPlaceholder(appName: String, type: String, envName: String? = null): List<Placeholder>
    fun findAppList(): List<AppVO>
    fun findEnvListByAppName(appName: String?): List<Env>
}
