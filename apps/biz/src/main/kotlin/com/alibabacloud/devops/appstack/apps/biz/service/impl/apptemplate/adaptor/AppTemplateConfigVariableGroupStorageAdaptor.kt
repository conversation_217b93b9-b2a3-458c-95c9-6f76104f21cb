package com.alibabacloud.devops.appstack.apps.biz.service.impl.apptemplate.adaptor

import com.alibabacloud.devops.appstack.apps.biz.model.ext.toModel
import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateConfigPO
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateConfigStorageAdaptor
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.VariableGroupConfiguration
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.request.ProfileRequest
import com.alibabacloud.devops.appstack.libs.model.request.VariableRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.AbstractConfigurationUpsertRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateConfigRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.VariableGroupConfigurationUpsertRequest
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR> <EMAIL>
 * @version : AppTemplateConfigVariableGroupStorageAdaptor, v0.1
 * @date : 2023-10-18 22:03
 **/
@Service
open class AppTemplateConfigVariableGroupStorageAdaptor :
    AppTemplateConfigStorageAdaptor<VariableGroupConfiguration>() {

    companion object {
        val type = AppTemplateConfig.TypeEnum.VariableGroup
    }

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var auditLogService: AuditLogService

    override fun insert(appTemplate: AppTemplate, request: UpsertAppTemplateConfigRequest): AppTemplateConfig {
        coreFacades.variableTemplateFacade.create(
            appTemplate.name,
            (request.configuration as VariableGroupConfigurationUpsertRequest).profileMap.values.map {
                ProfileRequest(
                    name = it.name,
                    displayName = it.displayName,
                    vars = it.vars
                )
            })
        return super.insert(appTemplate, request)
    }

    override fun update(
        appTemplate: AppTemplate,
        previous: AppTemplateConfig,
        request: UpsertAppTemplateConfigRequest
    ): AppTemplateConfig {
        request.configuration?.let {
            val configuration = request.configuration as VariableGroupConfigurationUpsertRequest
            coreFacades.variableTemplateFacade.update(
                previous.appTemplateName,
                VariableRequest(profileMap = configuration.profileMap).apply {
                    this.fromRevisionSha = configuration.fromRevisionSha
                    this.branchName = configuration.branchName
                    this.message = configuration.message
                }
            )
        }
        return super.update(appTemplate, previous, request)
    }

    override fun delete(appTemplate: AppTemplate, appTemplateConfigSn: String) {
        coreFacades.variableTemplateFacade.delete(appTemplate.name)
        super.delete(appTemplate, appTemplateConfigSn)
    }

    override fun convertToModel(appTemplateConfigPO: AppTemplateConfigPO): AppTemplateConfig {
        val dto =
            JacksonUtil.jacksonObjectMapper()
                .readValue<VariableTemplateConfigDTO>(appTemplateConfigPO.configuration!!)
        val variableTemplateVO = when (dto) {
            is VariableTemplateConfigV1DTO -> coreFacades.variableTemplateFacade.find(dto.variableTemplateName)
            else -> TODO()
        }
        checkExists(variableTemplateVO) { ErrorCode.AS_VAR_PROFILE_NOT_FOUND }
        val configuration = VariableGroupConfiguration(
            profileMap = variableTemplateVO.profileMap,
            revision = variableTemplateVO.revision,
            branchInfo = variableTemplateVO.branchInfo
        )
        return appTemplateConfigPO.toModel(configuration)
    }

    override fun convertConfiguration(appTemplateName: String, configuration: AbstractConfigurationUpsertRequest): Any {
        return VariableTemplateConfigV1DTO(variableTemplateName = appTemplateName)
    }

    override fun configType(): AppTemplateConfig.TypeEnum = AppTemplateConfig.TypeEnum.VariableGroup

    override fun logAudit(appTemplate: AppTemplate) {
        auditLogService.commonLog(OrgEventType.APP_TEMPLATE_MODIFY_VARIABLE_GROUPS_AUDIT, appTemplate.name, appTemplate.displayName)
    }

    @JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "version",
        visible = true
    )
    @JsonSubTypes(
        JsonSubTypes.Type(value = VariableTemplateConfigV1DTO::class, name = "v1"),
    )
    interface VariableTemplateConfigDTO {
        val version: String
    }

    data class VariableTemplateConfigV1DTO(
        override val version: String = "v1",
        val variableTemplateName: String,
        // TODO 版本sha值
    ) : VariableTemplateConfigDTO

}