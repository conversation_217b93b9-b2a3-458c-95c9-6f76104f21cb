package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.annotation.ApiMetricCounter
import com.alibabacloud.devops.appstack.apps.biz.service.AppOrchestrationBizService
import com.alibabacloud.devops.appstack.libs.change.controller.spring.boot.starter.service.ChangeControllerFacades
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.constant.LockConfig
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.filter.TraceFilter
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkBizWithCode
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.Label
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.InvalidChangeItem
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.ActionType
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.ResourceType
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.Orchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppBuiltInOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppExternalOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.AppOrchestration
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.app.OrchestrationLabeledRevision
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.k8s.LocatorInfo
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Component
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.KubernetesCheckOutputItem
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.render.Placeholder
import com.alibabacloud.devops.appstack.libs.model.`do`.release.AppRelease
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceTypeEnum
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.Profile
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.Variable
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.AppOrchestrationCheckRequest
import com.alibabacloud.devops.appstack.libs.model.request.AppOrchestrationCreateRequest
import com.alibabacloud.devops.appstack.libs.model.request.AppOrchestrationVerifyRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppOrchestrationRequest
import com.alibabacloud.devops.appstack.libs.model.request.apprelease.InnerSearchAppReleaseRequest
import com.alibabacloud.devops.appstack.libs.model.vo.AppOrchestrationDiffVO
import com.alibabacloud.devops.appstack.libs.org.spring.boot.starter.service.OrgFacades
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.apache.commons.lang3.time.DateFormatUtils
import org.apache.commons.lang3.tuple.ImmutablePair
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.util.StringUtils
import java.io.BufferedInputStream
import java.io.BufferedOutputStream
import java.io.ByteArrayInputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.util.*
import java.util.function.BinaryOperator
import java.util.function.Consumer
import java.util.function.Function
import java.util.regex.Pattern
import java.util.stream.Collectors
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import javax.servlet.http.HttpServletResponse

/**
 * @author: <EMAIL>
 * @date: 2021-12-17 13:35
 * @version: OrchestrationServiceImpl, v0.1
 **/
@Service
@Slf4k
open class AppOrchestrationBizServiceImpl(
    @Autowired
    override var i18nMessageService: I18nMessageService,
) : AppOrchestrationBizService, OrchestrationBaseBizService(i18nMessageService) {

    companion object {
        val PREDEFINED_VAR_LIST = listOf(
            "DATETIME",
            "TIMESTAMP",
            "APPSTACK_APP_NAME",
            "APPSTACK_ENV_NAME",
            "APPSTACK_COMPONENT_NAME"
        )
        const val YAML_FILE_SUFFIX = ".yaml"

        const val DUMMY_IMAGE_NAME = "dummy-image"
        const val DUMMY_ENV_NAME = "dummy-env"
    }

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var orgFacades: OrgFacades

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var changeControllerFacades: ChangeControllerFacades


    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun find(appName: String, sn: String, tagName: String?, sha: String?): AppOrchestration? {
        return coreFacades.appOrchestrationFacade.find(appName, sn, tagName, sha)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun findAll(appName: String): List<AppOrchestration> {
        return coreFacades.appOrchestrationFacade.findAll(appName)
    }

    @ApiMetricCounter(metricName = "appOrchestration_operation", methodTag = "create")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_ORCHESTRATION_CREATE, resourceArgIndex = 0),
        ]
    )
    override fun create(
        appName: String,
        appOrchestrationCreateRequest: AppOrchestrationCreateRequest
    ): AppOrchestration {
        val orc = coreFacades.appOrchestrationFacade.create(appName, appOrchestrationCreateRequest)
        afterAppOrchestrationChanged(appName)
        return orc
    }

    @ApiMetricCounter(metricName = "appOrchestration_operation", methodTag = "update")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_ORCHESTRATION_EDIT, resourceArgIndex = 0),
        ]
    )
    override fun update(
        appName: String,
        sn: String,
        updateAppOrchestrationRequest: UpdateAppOrchestrationRequest
    ): AppOrchestration {
        val orc = coreFacades.appOrchestrationFacade.update(appName, sn, updateAppOrchestrationRequest)
        afterAppOrchestrationChanged(appName)
        return orc
    }

    @ApiMetricCounter(metricName = "appOrchestration_operation", methodTag = "update")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_ORCHESTRATION_EDIT, resourceArgIndex = 0),
        ]
    )
    override fun updateAllBuiltin(
        appName: String,
        updateAppOrchestrationRequest: UpdateAppOrchestrationRequest
    ): AppOrchestration {
        val orc = coreFacades.appOrchestrationFacade.updateAllBuiltin(appName, updateAppOrchestrationRequest)
        afterAppOrchestrationChanged(appName)
        return orc
    }

    @ApiMetricCounter(metricName = "appOrchestration_operation", methodTag = "delete")
    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_ORCHESTRATION_EDIT, resourceArgIndex = 0),
        ]
    )
    override fun delete(appName: String, sn: String) {
        checkAllRelatedAppReleaseEnd(appName, sn)
        val orc = coreFacades.appOrchestrationFacade.find(appName, sn, null, null)
        coreFacades.appOrchestrationFacade.delete(appName, sn)
        afterAppOrchestrationChanged(appName)
    }

    private fun checkAllRelatedAppReleaseEnd(appName: String, sn: String) {
        val orchestration = find(appName = appName, sn = sn, tagName = null, sha = null)
        if (orchestration is AppExternalOrchestration<*>) {
            var currentPage = 1L
            val pageSize = 10L
            var actualSize: Long
            do {
                val appReleaseList = workflowControllerFacade.appReleaseFacade.searchPaginated(
                    InnerSearchAppReleaseRequest(
                        appNames = listOf(appName),
                        states = AppRelease.State.values().filter { !it.isEndState() }), currentPage, pageSize
                )
                if (appReleaseList.records.any { appRelease ->
                        appRelease.releaseItems.any { item ->
                            item.type == AppRelease.AppReleaseItem.TypeEnum.APP_ORCHESTRATION && item.spec is AppRelease.AppReleaseItem.AppExternalOrchestrationSpec<*> && (item.spec as AppRelease.AppReleaseItem.AppExternalOrchestrationSpec<*>).appOrchestrationSn == sn
                        }
                    }) {
                    throw BizException(ErrorCode.AS_APP_RELEASE_NOT_ALL_END)
                }
                actualSize = appReleaseList.records.size.toLong()
                currentPage++
            } while (actualSize >= pageSize)
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun findPaginatedRevision(
        appName: String,
        sn: String,
        current: Long,
        pageSize: Long
    ): Pagination<Revision> {
        return coreFacades.appOrchestrationFacade.findPaginatedRevision(appName, sn, current, pageSize)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun findLatestForEnv(appName: String, envName: String): AppOrchestration {
        // 获取环境，并且判断资源
        val env = coreFacades.envFacade.find(appName, envName) ?: throw BizException(ErrorCode.AS_ENV_NOT_FOUND)
        if (env.resourcePoolName == null) {
            throw BizException(ErrorCode.AS_RES_POOL_NOT_FOUND)
        }
        if (env.deployGroupName == null) {
            throw BizException(ErrorCode.AS_DEPLOY_GROUP_NOT_FOUND)
        }

        val deployGroup = coreFacades.deployGroupFacade.find(env.resourcePoolName!!, env.deployGroupName!!)
        val computationResources = deployGroup.claimList.filter { it.belong2Host() || it.belong2K8s() }
        // FIXME 这里是个死结：环境试图根据自身绑定的资源类型决定自身使用的编排标准，而资源实则绑定到了部署组，部署组又不限制只包含一种计算资源
        // 目前先在部署组计算资源超过一种的前提下抛异常
        // 后续需要对环境的 paas schema 有共识，而不能捡到什么做什么
        if (computationResources.isEmpty()) {
            throw BizException(ErrorCode.AS_RES_INST_NOT_FOUND)
        } else if (computationResources.size > 1) {
            throw BizException(ErrorCode.AS_RES_INST_DUPLICATED)
        }
        val targetComputationResourceClaim = computationResources.first()
        val paasType = when {
            targetComputationResourceClaim.belong2K8s() -> "KUBERNETES"
            targetComputationResourceClaim.belong2Host() -> "HOST"
            else -> throw BizException(ErrorCode.AS_RESOURCE_INSTANCE_ADAPTER_NOT_FOUND)
        }

        // 查找最新版本
        val latestRevision =
            findPaginatedRevisionAndLabel(appName = appName, sn = "$appName@$paasType", envName = envName, 1, 1)
                .records
                .firstOrNull() ?: throw BizException(ErrorCode.AS_ORC_NOT_FOUND)

        // 根据最新版本查找应用编排
        return find(appName = appName, sn = "$appName@$paasType", tagName = null, sha = latestRevision.sha)
            ?: throw BizException(ErrorCode.AS_ORC_NOT_FOUND)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun findPaginatedRevisionAndLabel(
        appName: String,
        sn: String,
        envName: String?,
        current: Long,
        pageSize: Long,
    ): Pagination<OrchestrationLabeledRevision> {
        val pair = if (envName.isNullOrBlank()) {
            Pair(emptyList(), null)
        } else {
            Pair(
                coreFacades.envFacade.find(appName, envName)?.labelList ?: emptyList(),
                changeControllerFacades.changeableEnvFacade.find(appName, envName)?.resourceType
            )
        }
        return coreFacades.appOrchestrationFacade.findPaginatedRevisionAndLabel(
            appName,
            sn,
            current,
            pageSize,
            labels = pair.first,
            resourceType = pair.second,
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun compare(
        appName: String,
        sn: String,
        beforeRevisionSha: String,
        afterRevisionSha: String
    ): Triple<AppOrchestrationDiffVO, AppOrchestrationDiffVO, List<DiffItem<String>>> {
        return coreFacades.appOrchestrationFacade.compare(
            appName,
            sn,
            beforeRevisionSha,
            afterRevisionSha
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_ORCHESTRATION_EXPORT, resourceArgIndex = 0),
        ]
    )
    override fun exportAppOrchestration(
        appName: String,
        suitableResourceType: ResourceTypeEnum,
        format: Orchestration.FormatEnum,
        sha: String,
        renderType: Orchestration.RenderTypeEnum,
        profileName: String,
        response: HttpServletResponse,
    ) {
        if (format != Orchestration.FormatEnum.MANIFEST) {
            // 其他类型暂时不支持导出
            throw BizException(errorCode = ErrorCode.AS_UNKNOWN)
        }
        val orchestration = coreFacades.appOrchestrationFacade.findAll(appName).firstOrNull {
            it.suitableResourceTypes.contains(suitableResourceType) && it.format == format
        }
        checkExists(orchestration, appName) { ErrorCode.AS_APP_ORC_NOT_FOUND }
        orchestration as AppBuiltInOrchestration
        val placeholderList: List<Placeholder> = orchestration.placeholderList
        val componentList: List<Component> = orchestration.componentList

        var envProfile: Profile? = null
        if (StringUtils.hasText(profileName)) {
            val variableStorage = coreFacades.variableFacade.find(appName)
            checkExists(variableStorage) {
                ErrorCode.AS_VCS_REV_NOT_FOUND
            }
            val profileMap: Map<String, Profile> = variableStorage.profileMap
            checkBizWithCode(profileMap.containsKey(profileName), profileName) {
                ErrorCode.AS_VAR_PROFILE_NOT_FOUND
            }
            envProfile = profileMap[profileName]
        }

        val fileNameAndContentMap: MutableMap<String, String> =
            fileNameAndContentMap(
                appName,
                componentList,
                placeholderList,
                envProfile,
                renderType,
                sha,
                orgFacades.organizationFacade.getUserName(orgId = AuthUtil.getTenant(), userId = AuthUtil.getUserId())
            )
        zipd("$appName-$profileName-$sha.zip", fileNameAndContentMap, response)
    }


    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun check(
        appName: String,
        appOrchestrationCheckRequest: AppOrchestrationCheckRequest
    ): List<InvalidChangeItem> {
        if (appOrchestrationCheckRequest.format == Orchestration.FormatEnum.MANIFEST) {
            val variableGroups = if (appOrchestrationCheckRequest.envName.isNullOrBlank()) {
                emptyList()
            } else {
                coreFacades.envFacade.listEnvRevisionVariableGroups(appName, appOrchestrationCheckRequest.envName!!)
            }
            val artifactValuesMap = appOrchestrationCheckRequest.placeholderList.filter {
                it.name.startsWith("image.") || it.name.startsWith("artifact.")
            }.associate { it.name to DUMMY_IMAGE_NAME }


            val checkResult = coreFacades.appOrchestrationFacade.check(
                appName, AppOrchestrationVerifyRequest(
                    envName = DUMMY_ENV_NAME,
                    profile = variableGroups,
                    componentList = appOrchestrationCheckRequest.componentList,
                    placeholderList = appOrchestrationCheckRequest.placeholderList,
                    patchValues = artifactValuesMap
                )
            )
            val checkOutputItems = checkResult.items as List<KubernetesCheckOutputItem>
            return checkInvalid(checkOutputItems, appOrchestrationCheckRequest.componentList)
        } else {
            // 其他格式暂时不支持检查
            return emptyList()
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_ORCHESTRATION_EDIT, resourceArgIndex = 0),
        ]
    )
    override fun bindLabels(appName: String, sn: String, sha: String, labels: List<Label>) {
        coreFacades.appOrchestrationFacade.bindLabels(appName, sn, sha, labels)
    }

    fun fileNameAndContentMap(
        appName: String,
        componentList: List<Component>,
        placeholderList: List<Placeholder>,
        envProfile: Profile?,
        renderType: Orchestration.RenderTypeEnum,
        sha: String?,
        userName: String,
    ): MutableMap<String, String> {
        val componentNameParamList: MutableList<String> = ArrayList<String>()
        val placeholderValueMap =
            extractPlaceholderValueMap(placeholderList, envProfile, componentNameParamList, appName)

        val fileNameAndContent: MutableMap<String, String> = HashMap<String, String>()
        componentList.forEach(Consumer { component: Component ->
            if (renderType == Orchestration.RenderTypeEnum.CHARTS) {
                val fileContent = chartReplacePlaceholder(component.content, componentNameParamList)
                // NOTE jiuya.wb 这里尽量回避使用 gvk 作为文件名，组件类型可能重复，不同 group 下的 kind 也可能重复
                fileNameAndContent["templates/" + component.name.lowercase(Locale.getDefault()) + YAML_FILE_SUFFIX] =
                    fileContent
            } else {
                val fileContent = yamlReplacePlaceholder(
                    component.content, componentNameParamList,
                    placeholderValueMap
                )
                // NOTE jiuya.wb 这里尽量回避使用 gvk 作为文件名，组件类型可能重复，不同 group 下的 kind 也可能重复
                fileNameAndContent[component.name.lowercase(Locale.getDefault()) + YAML_FILE_SUFFIX] = fileContent
            }
        })
        if (renderType == Orchestration.RenderTypeEnum.CHARTS) {
            val sb = StringBuilder()
            for (oneParam in placeholderValueMap.keys) {
                sb.append(buildNextMap(oneParam, placeholderValueMap[oneParam]!!, 0) + "\n")
            }
            fileNameAndContent["values$YAML_FILE_SUFFIX"] = sb.toString()

            //不想在resource里面加模板文件，都要读出来替换的，内容多了再抽文件吧
            val chartFileContent =
                "apiVersion: v1\nname: $appName-$sha\nversion: 1.0.0\nmaintainers: \n  - name: $userName"
            fileNameAndContent["Chart$YAML_FILE_SUFFIX"] = chartFileContent
        }
        return fileNameAndContent
    }

    private fun buildNextMap(currentKey: String, value: String, prefixBlankCount: Int = 0): String {
        val sb = StringBuilder()
        for (index in 1..prefixBlankCount) {
            sb.append("  ")
        }
        if (currentKey.contains(".")) {
            val key = currentKey.split(".").first()
            val nextKey = currentKey.substring(currentKey.indexOf(".") + 1, currentKey.length)
            sb.append("$key:\n").append(buildNextMap(nextKey, value, prefixBlankCount + 1))
        } else {
            sb.append("$currentKey: $value")
        }
        return sb.toString()
    }

    private fun chartReplacePlaceholder(content: String, componentNameParamList: List<String>): String {
        var content = content
        val pattern = Pattern.compile("\\{\\{ \\.AppStack\\.(.+?) \\}\\}")
        val matcher = pattern.matcher(content)
        while (matcher.find()) {
            content = content.replace(matcher.group(0), "{{ .Values." + matcher.group(1) + " }}")
        }

        //chart全部整理成values再处理
        content = replaceComponentName(content, componentNameParamList)
        return content
    }

    private fun replaceComponentName(content: String, componentNameParamList: List<String>): String {
        var content = content
        if (!componentNameParamList.isEmpty()) {
            //拿名字
            val namePattern = Pattern.compile("\\s+kind:\\s*(.+)[\\r\\n\\t]")
            val nameMatcher = namePattern.matcher(content)
            if (nameMatcher.find()) {
                val componentName = nameMatcher.group(1)
                for (oneComponentParamName in componentNameParamList) {
                    content = content.replace("{{ .Values.$oneComponentParamName }}", componentName)
                }
            }
        }
        return content
    }

    private fun downloadZip(file: File, response: HttpServletResponse): HttpServletResponse {
        try {
            // 以流的形式下载文件。
            val fis: InputStream = FileInputStream(file)
            val buffer = ByteArray(fis.available())
            fis.read(buffer)
            fis.close()
            // 清空response
            response.reset()
            val toClient: OutputStream = BufferedOutputStream(response.outputStream)
            response.contentType = "application/octet-stream"

            // 如果输出的是中文名的文件，在此处就要用URLEncoder.encode方法进行处理
            response.setHeader(
                "Content-Disposition",
                "attachment;filename=" + String(file.name.toByteArray(charset("GB2312")), charset("ISO8859-1"))
            )
            toClient.write(buffer)
            toClient.flush()
            toClient.close()
        } catch (ex: Exception) {
            ex.printStackTrace()
        } finally {
            try {
                val f = File(file.path)
                f.delete()
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return response
    }

    private fun replacePrefix(content: String, placeholderValueMap: Map<String, String>?, appstackKey: String): String {
        var content = content
        val pattern = Pattern.compile("\\{\\{ \\.$appstackKey\\.(.+?) \\}\\}")
        val matcher = pattern.matcher(content)
        while (matcher.find()) {
            if (placeholderValueMap!!.containsKey(matcher.group(1))) {
                content = content.replace(matcher.group(0), placeholderValueMap[matcher.group(1)]!!)
            }
        }
        return content
    }

    private fun extractPlaceholderValueMap(
        placeholderList: List<Placeholder>,
        envProfile: Profile?,
        componentNameParamList: MutableList<String>,
        appName: String,
    ): MutableMap<String, String> {
        val variables: Map<String, Variable> =
            envProfile?.vars?.stream()?.collect(Collectors.toMap(Variable::key, Function.identity()))
                ?: mapOf()

        return placeholderList.stream()
            .map { placeholder: Placeholder ->
                var actualValue: String? = null
                when (placeholder.valueSource?.name) {
                    "VARIABLE" -> if (PREDEFINED_VAR_LIST.contains(placeholder.value)) {
                        if ("DATETIME" == placeholder.value) {
                            actualValue = DateFormatUtils.format(Date(), "yyyy-MM-dd HH:mm:ss")
                        } else if ("TIMESTAMP" == placeholder.value) {
                            actualValue = System.currentTimeMillis().toString()
                        } else if ("APPSTACK_APP_NAME" == placeholder.value) {
                            actualValue = appName
                        } else if ("APPSTACK_COMPONENT_NAME" == placeholder.value) {
                            componentNameParamList.add(placeholder.value)
                        }
                    } else {
                        if (variables.containsKey(placeholder.value)) {
                            actualValue = java.lang.String.valueOf(variables[placeholder.value]!!.value)
                        }
                    }

                    "CONSTANT" -> actualValue = java.lang.String.valueOf(placeholder.value)
                    else -> throw RuntimeException(
                        java.lang.String.format("Placeholder type=%s not recognized", placeholder.type)
                    )
                }
                if (actualValue != null) {
                    val key: String = placeholder.name
                    ImmutablePair.of(key, actualValue)
                } else {
                    null
                }
            }.filter { obj: ImmutablePair<String, String>? ->
                Objects.nonNull(obj)
            }.collect(
                Collectors.toMap<ImmutablePair<String, String>, String, String>(
                    { obj: ImmutablePair<String, String> -> obj.getLeft() },
                    { obj: ImmutablePair<String, String> -> obj.getRight() },
                    BinaryOperator.minBy { o1, o2 -> o1.compareTo(o2) }
                )
            )
    }

    private fun yamlReplacePlaceholder(
        content: String,
        componentNameParamList: List<String>,
        placeholderValueMap: Map<String, String>?,
    ): String {
        //yaml先替换，然后再全部渲染值
        var content = content
        content = replaceComponentName(content, componentNameParamList)
        content = replacePrefix(content, placeholderValueMap, "AppStack")
        return replacePrefix(content, placeholderValueMap, "Values")
    }

    //files打成压缩包
    private fun zipFile(fileNameAndContent: Map<String, String>, outputStream: ZipOutputStream) {
        for (oneFileName in fileNameAndContent.keys) {
            zipFile(
                oneFileName, ByteArrayInputStream(fileNameAndContent[oneFileName]!!.toByteArray()),
                outputStream
            )
        }
    }

    private fun zipFile(fileName: String?, inputStream: InputStream?, ouputStream: ZipOutputStream) {
        try {
            val bins = BufferedInputStream(inputStream, 512)
            val entry = ZipEntry(fileName)
            ouputStream.putNextEntry(entry)
            var nNumber: Int
            val buffer = ByteArray(512)
            while (bins.read(buffer).also { nNumber = it } != -1) {
                ouputStream.write(buffer, 0, nNumber)
            }
            bins.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun zipd(zipTmp: String, fileNameAndContent: Map<String, String>, response: HttpServletResponse) {
        val zipTmpFile = File(zipTmp)
        try {
            if (zipTmpFile.exists()) {
                zipTmpFile.delete()
            }
            zipTmpFile.createNewFile()
            response.reset()
            // 创建文件输出流
            val fous = FileOutputStream(zipTmpFile)
            val zipOut = ZipOutputStream(fous)
            zipFile(fileNameAndContent, zipOut)
            zipOut.close()
            fous.close()
            downloadZip(zipTmpFile, response)
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }


    private fun getProfile(
        profileName: String?,
        appName: String
    ) = if (StringUtils.hasText(profileName)) {
        val variableStorage = coreFacades.variableFacade.find(appName)
        checkExists(variableStorage) { ErrorCode.AS_VAR_PROFILE_NOT_FOUND }
        val profileMap: Map<String, Profile> = variableStorage.profileMap
        checkExists(variableStorage.profileMap.containsKey(profileName), profileName!!) {
            ErrorCode.AS_VAR_PROFILE_NOT_FOUND
        }
        profileMap[profileName]
    } else null


    private fun afterAppOrchestrationChanged(appName: String) {
        logger.info("App[$appName] AppOrchestration changed")
        val ctx = AuthThreadContext.export()
        GlobalScope.launch {
            // prepare context
            AuthThreadContext.replaceAll(ctx)
            MDC.put(TraceFilter.TRACE_CONTEXT_KEY, AuthThreadContext.getTraceId())
            // 当应用代码库更新后，刷新研发阶段流水线
            // TODO 当前只刷新变更的流水线，是否刷新发布的流水线后续产品再对
            try {
                workflowControllerFacade.releaseWorkflowFacade.findAll(appName)
                    .forEach { releaseWorkflow ->
                        val stageCount = releaseWorkflow.releaseStages.size
                        val releaseKey = workflowControllerFacade.releaseWorkflowFacade.tryLock(releaseWorkflow.sn!!,
                            LockConfig.DEFAULT_TRY_LOCK_TIME, stageCount * LockConfig.DEFAULT_REFRESH_PIPELINE_EXPIRATION_TIME
                        )
                        if (releaseKey != null) {
                            releaseWorkflow.releaseStages.forEach { releaseStage ->
                                releaseStage.pipeline?.let {
                                    logger.info("Refresh releaseStage[${releaseStage.sn}]'s pipeline start")
                                    workflowControllerFacade.releaseStageFacade.refreshPipeline(releaseStage.sn!!)
                                    logger.info("Refresh releaseStage[${releaseStage.sn}]'s pipeline finish")
                                }
                            }
                            workflowControllerFacade.releaseWorkflowFacade.unLock(releaseWorkflow.sn!!, releaseKey)
                            workflowControllerFacade.releaseWorkflowFacade.upsertReleaseWorkflowRevision(releaseWorkflow.sn!!, "更新流水线源")
                        } else {
                            logger.error("Refresh releaseWorkflow[${releaseWorkflow.sn}]'s pipeline failed: failed to get lock")
                        }
                    }
            } catch (e: Exception) {
                logger.error("App[$appName] AppCodeRepo changed exception", e)
            }
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
        ]
    )
    override fun getLocatorList(appName: String, envName: String): List<LocatorInfo> {
        return try {
            coreFacades.appOrchestrationFacade.getLocatorList(appName, envName)
        } catch (e: Exception) {
            emptyList()
        }
    }
}