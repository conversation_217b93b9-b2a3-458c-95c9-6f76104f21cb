package com.alibabacloud.devops.appstack.apps.biz.service

import com.fasterxml.jackson.databind.JsonNode
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 * <AUTHOR>
 * @create 2023/10/17 7:37 AM
 **/
interface FlowForwardBizService {

    fun checkPermissions(request: HttpServletRequest, response: HttpServletResponse): JsonNode

    fun getLatestSuccess(request: HttpServletRequest, response: HttpServletResponse): JsonNode

    fun retryAction(request: HttpServletRequest, response: HttpServletResponse): JsonNode

    fun componentCommand(request: HttpServletRequest, response: HttpServletResponse): JsonNode

    fun skipAction(request: HttpServletRequest, response: HttpServletResponse): JsonNode

    fun handlePipeline(id: String, request: HttpServletRequest, response: HttpServletResponse): JsonNode

    fun handleCommon(request: HttpServletRequest, response: HttpServletResponse): JsonNode

    /**
     * 获取流水线前注入环境变量预渲染
     */
    fun getPipelineWithParams(id: String, request: HttpServletRequest, response: HttpServletResponse): JsonNode

    fun handlePopApiForward(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        executionNumber: String? = null,
        jobId: String? = null,
        operationName: String
    ): JsonNode
}