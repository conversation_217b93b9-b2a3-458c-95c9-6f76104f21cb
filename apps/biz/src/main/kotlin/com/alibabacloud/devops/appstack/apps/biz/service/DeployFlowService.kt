package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.apps.biz.model.DeployFlow

/**
 * @author: <EMAIL>
 * @date: 2022-03-07 19:21
 * @version: DeployFlowService, v0.1
 **/
interface DeployFlowService {

    fun create(deployFlow: DeployFlow): DeployFlow

    fun find(objectType: DeployFlow.ObjectType, objectId: String, appName: String?, envName: String?): DeployFlow?

    fun listByChangeOrderSns(changeOrderSnList: List<String>) : List<DeployFlow>

    fun deleteByObjectTypeAndObjectId(objectType: DeployFlow.ObjectType, objectId: String): Int
}