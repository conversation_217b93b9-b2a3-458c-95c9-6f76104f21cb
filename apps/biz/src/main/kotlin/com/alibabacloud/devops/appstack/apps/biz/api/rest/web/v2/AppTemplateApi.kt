package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.request.AppTemplateQuery
import com.alibabacloud.devops.appstack.libs.model.request.ng.AppTemplateSearch
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.api.annotations.ParameterObject
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2023-10-21 11:01
 * @version: AppTemplateApi, v0.1
 **/
@Tag(name = "应用模板", description = "应用模板领域 OpenAPI")
@RestController
@RequestMapping("/api/v2")
open class AppTemplateApi {

    @Autowired
    private lateinit var appBizTemplateBizService: AppTemplateBizService

    @VpcApi
    @Operation(summary = "搜索应用模板", operationId = "SearchAppTemplates")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "分页搜索完成（包括未匹配到对象的情况）"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @GetMapping("/appTemplates:search")
    fun search(
        @ParameterObject query: AppTemplateSearch
    ): PageList<AppTemplate> {
        return appBizTemplateBizService.list(
            AppTemplateQuery(
                pagination = query.pagination,
                perPage = query.perPage,
                orderBy = query.orderBy,
                sort = query.sort,
                nextToken = query.nextToken,
                search = query.displayNameKeyword
            )
        )
    }

}