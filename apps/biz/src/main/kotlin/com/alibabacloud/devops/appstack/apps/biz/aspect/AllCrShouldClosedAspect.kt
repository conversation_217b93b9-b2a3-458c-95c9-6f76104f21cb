package com.alibabacloud.devops.appstack.apps.biz.aspect

import com.alibabacloud.devops.appstack.apps.biz.annotation.AllCrShouldClosed
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.checkBizWithCode
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.ChangeRequest
import com.alibabacloud.devops.appstack.libs.model.request.cr.QueryChangeRequest
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import org.aspectj.lang.JoinPoint
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.annotation.Before
import org.aspectj.lang.reflect.MethodSignature
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * <AUTHOR>
 * @date 2022-08-19
 */
@Aspect
@Slf4k
@Component
open class AllCrShouldClosedAspect {

    @Autowired
    private lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Before("@annotation(com.alibabacloud.devops.appstack.apps.biz.annotation.AllCrShouldClosed)")
    fun preCheck(joinPoint: JoinPoint) {
        val signature = joinPoint.signature as MethodSignature
        val method = signature.method
        val annotation: AllCrShouldClosed = method.getAnnotation(AllCrShouldClosed::class.java)

        val appName = joinPoint.args[annotation.appNameIndex] as String

        val findNotClosed = workflowControllerFacade.changeRequestFacade.findPaginated(
            request = QueryChangeRequest(
                appName = listOf(appName),
                state = ChangeRequest.State.values().filter { !it.isEndState() }.map { it.name }
            )
        )
        checkBizWithCode(findNotClosed.total == 0L) {
            ErrorCode.AS_CR_NOT_ALL_CLOSED
        }
    }
}