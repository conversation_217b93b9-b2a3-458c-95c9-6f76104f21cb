package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditLog
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.org.event.CommonAudit
import com.alibabacloud.devops.appstack.libs.org.spring.boot.starter.service.OrgFacades
import feign.FeignException
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR> <EMAIL>
 * @version : AuditLogServiceImpl, v0.1
 * @date : 2022-12-07 20:59
 **/
@Slf4k
@Service
class AuditLogServiceImpl : AuditLogService {

    @Autowired
    lateinit var orgFacades: OrgFacades

    override fun log(eventType: OrgEventType, auditLog: AuditLog) {
        try {
            orgFacades.eventFacade.post(eventType, AuthUtil.getTenant(), auditLog)
        } catch (e: FeignException) {
            logger.error("send audit event failed, eventType: $eventType, auditLog: $auditLog", e)
        }
    }

    override fun commonLog(eventType: OrgEventType, id: String, name: String) {
        val audit = CommonAudit(
            target = AuditTarget(id = id, name = name),
        )
        log(eventType, audit)
    }
}