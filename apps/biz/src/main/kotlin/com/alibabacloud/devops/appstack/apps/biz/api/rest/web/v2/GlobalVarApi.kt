package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import com.alibabacloud.devops.appstack.apps.biz.service.GlobalVarBizService
import com.alibabacloud.devops.appstack.apps.biz.service.GlobalVariableBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.request.CreateGlobalVariableRequest
import com.alibabacloud.devops.appstack.libs.model.request.GlobalVariable
import com.alibabacloud.devops.appstack.libs.model.request.SearchGlobalVarRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateGlobalVariableRequest
import com.alibabacloud.devops.appstack.libs.model.response.GlobalVarVO
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 * @create 2023/12/25 9:01 PM
 **/
@Tag(name = "全局变量组", description = "全局变量组相关API")
@RestController
@RequestMapping("/api/v2")
open class GlobalVarApi {

    @Autowired
    lateinit var globalVarBizService: GlobalVarBizService

    @Autowired
    lateinit var globalVariableBizService: GlobalVariableBizService

    @VpcApi
    @Operation(summary = "查询全局变量组列表", operationId = "ListGlobalVars")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询全局变量组列表成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PostMapping("/globalVars:search")
    fun findGlobalVarsPaginated(
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
        @RequestBody req: SearchGlobalVarRequest,
    ): Pagination<GlobalVarVO> {
        return globalVarBizService.listPaginated(current, pageSize, req)
    }

    @VpcApi
    @Operation(summary = "创建全局变量组", operationId = "CreateGlobalVar")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "创建全局变量组成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PostMapping("/globalVars")
    fun createGlobalVar(@RequestBody req: CreateGlobalVariableRequest): GlobalVariable {
        return globalVariableBizService.create(req)
    }

    @VpcApi
    @Operation(summary = "查询全局变量组", operationId = "GetGlobalVar")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询全局变量组成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @GetMapping("/globalVars/{name}")
    fun findGlobalVar(
        @PathVariable("name") name: String,
        @RequestParam("revisionSha", required = false) revisionSha: String? = null,
    ): GlobalVariable {
        return globalVariableBizService.find(name, revisionSha)
    }

    @VpcApi
    @Operation(summary = "更新全局变量组", operationId = "UpdateGlobalVar")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "更新全局变量组成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PutMapping("/globalVars/{name}")
    fun updateGlobalVarContent(
        @PathVariable("name") name: String,
        @RequestBody req: UpdateGlobalVariableRequest,
    ): GlobalVariable {
        return globalVariableBizService.update(name, req)
    }

    @VpcApi
    @Operation(summary = "删除全局变量组", operationId = "DeleteGlobalVar")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "删除全局变量组成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @DeleteMapping("/globalVars/{name}")
    fun deleteGlobalVar(@PathVariable("name") name: String): Boolean {
        return globalVariableBizService.delete(name)
    }

}