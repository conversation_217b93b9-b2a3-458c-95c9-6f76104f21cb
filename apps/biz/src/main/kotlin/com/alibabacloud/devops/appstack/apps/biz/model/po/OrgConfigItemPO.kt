package com.alibabacloud.devops.appstack.apps.biz.model.po

import com.alibabacloud.devops.appstack.libs.common.annotation.Comment
import com.alibabacloud.devops.appstack.libs.common.model.BasePO
import com.baomidou.mybatisplus.annotation.TableName
import lombok.Data
import javax.persistence.Index
import javax.persistence.Table

/**
 * <AUTHOR>
 * @create 2023/12/21 9:39 PM
 **/
@TableName("org_config_items")
@Comment("OrgConfigItem")
@Table(
    indexes = [
        Index(columnList = "name(128), version(128), org_id(64)", unique = true),
    ]
)
@Data
class OrgConfigItemPO: BasePO() {

    @Comment("配置名称")
    var name: String? = null


    @Comment("配置版本")
    var version: String? = null

    @Comment("配置内容")
    var content: String? = null

}