package com.alibabacloud.devops.appstack.apps.biz.model.vo

import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseStageInstance
import com.alibabacloud.devops.appstack.libs.model.vo.VO
import com.aliyun.amp.plugin.annotation.AmpParam
import java.util.*

/**
 * <AUTHOR>
 * @date 2024-05-27
 */
data class ReleaseStageInstanceVO(
    @field:AmpParam(description = "流水线执行编号")
    val number: String,
    @field:AmpParam(description = "流水线执行状态", enumValues = ["RUNNING", "SUCCESS", "FAILED", "CANCELED", "UNKNOWN"])
    val state: ReleaseStageInstance.State,
    @field:AmpParam(description = "流水线执行触发方式")
    val triggerMode: String,
    @field:AmpParam(description = "流水线执行开始时间")
    val startTime: Date? = null,
    @field:AmpParam(description = "流水线执行结束时间")
    val endTime: Date? = null,
)


fun ReleaseStageInstance.convert2VO(): ReleaseStageInstanceVO {
    return ReleaseStageInstanceVO(
        number = ReleaseStageInstance.parseFlowEngineInstanceId(engineInstanceId).buildNumber,
        state = state,
        triggerMode = context.triggerMode?:"",
        startTime = context.startTime,
        endTime = context.endTime,
    )
}