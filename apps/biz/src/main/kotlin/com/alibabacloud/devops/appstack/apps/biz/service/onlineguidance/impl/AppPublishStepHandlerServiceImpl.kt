package com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.impl

import com.alibabacloud.devops.appstack.apps.biz.model.State
import com.alibabacloud.devops.appstack.apps.biz.model.Step
import com.alibabacloud.devops.appstack.apps.biz.model.StepProgress
import com.alibabacloud.devops.appstack.apps.biz.service.EnvBizService
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.StepHandlerService
import com.alibabacloud.devops.appstack.libs.change.controller.spring.boot.starter.service.ChangeControllerFacades
import com.alibabacloud.devops.appstack.libs.change.controller.spring.boot.starter.service.facade.ChangeableEnvFacade
import com.alibabacloud.devops.appstack.libs.model.`do`.changecontroller.ChangeOrder
import com.alibabacloud.devops.appstack.libs.model.vo.EnvState
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR> liyebin.lyb
 * @date : 2022/8/26 2:47 PM
 */
@Service
class AppPublishStepHandlerServiceImpl : StepHandlerService {

    @Autowired
    lateinit var envBizService: EnvBizService

    @Autowired
    lateinit var changeControllerFacades: ChangeControllerFacades

    override fun handle(appName: String, step: Step): StepProgress {
        val state: State = if (envBizService.findAll(appName)
                .any { changeControllerFacades.changeableEnvFacade.findStableChangeOrder(appName, it.name) != null }
        ) {
            State.FINISH
        } else {
            step.state
        }
        return StepProgress(
            needUpdate = step.state != state,
            step = Step(
                name = step.name,
                state = state
            )
        )
    }
}