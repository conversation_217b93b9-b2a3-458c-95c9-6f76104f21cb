package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.UserService
import com.alibabacloud.devops.appstack.apps.biz.service.client.api.AuthApi
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.model.AliyunLoginUserInfo
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.util.*

/**
 * <AUTHOR>
 * @create 2023/12/21 5:42 AM
 **/
@Service
class UserServiceImpl : UserService {

    @Autowired
    lateinit var authApi: AuthApi

    @Value("\${service.org.appId}")
    lateinit var appId: String

    @Value("\${service.org.appSecret}")
    lateinit var appSecret: String

    companion object {
        const val ALIYUN_REFER = "aliyun"
        const val ALIYUN_RAM_REFER = "aliyun_ram"
    }


    override fun getAliyunOpenIdByUserId(userId: String): String {
        var user: AliyunLoginUserInfo? = null
        try {
            user = authApi.getLoginUserByAliyunId(generateAuthHeadersUsingUserSession(appId, appSecret), ALIYUN_REFER, null, userId)
            return user.openId!!
        } catch (e: Throwable) {
        }
        try {
            user = authApi.getLoginUserByAliyunId(generateAuthHeadersUsingUserSession(appId, appSecret), ALIYUN_RAM_REFER, null, userId)
            return user.openId!!
        } catch (e: Throwable) {
        }
        if (user == null) {
            throw BizException(ErrorCode.AS_BASE_USER_NOT_FOUND)
        }
        return ""
    }

    override fun getUserIdByOpenId(openId: String): String {
        var user: AliyunLoginUserInfo? = null
        try {
            user = authApi.getLoginUserByAliyunId(generateAuthHeadersUsingUserSession(appId, appSecret), ALIYUN_REFER, openId)
            return user.userId!!
        } catch (e: Throwable) {
        }
        try {
            user = authApi.getLoginUserByAliyunId(generateAuthHeadersUsingUserSession(appId, appSecret), ALIYUN_RAM_REFER, openId)
            return user.userId!!
        } catch (e: Throwable) {
        }
        if (user == null) {
            throw BizException(ErrorCode.AS_BASE_USER_NOT_FOUND)
        }
        return ""
    }

    private fun generateAuthHeadersUsingUserSession(appId: String, appSecret: String): Map<String, String> {
        val algorithm: Algorithm = Algorithm.HMAC256(appSecret)
        val timestamp = System.currentTimeMillis()
        val issuedAt = Date(timestamp)
        val expiresAt = Date(timestamp + 60 * 1000 * 60)
        val appToken =
            JWT.create().withClaim("_appId", appId).withIssuedAt(issuedAt).withExpiresAt(expiresAt).sign(algorithm)
        val headers = mutableMapOf<String, String>()
        headers["Authorization"] = "Bearer $appToken"
        headers["X-Operator-Id"] = AuthUtil.getUserId() ?: ""
        headers["X-Tenant-Id"] = AuthUtil.getTenant() ?: ""
        return headers
    }
}
