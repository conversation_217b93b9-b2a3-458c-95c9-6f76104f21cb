package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.model.vo.MemberVO
import com.alibabacloud.devops.appstack.apps.biz.service.AppBizService
import com.alibabacloud.devops.appstack.apps.biz.service.PermissionService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.constant.ShowType
import com.alibabacloud.devops.appstack.libs.common.model.ErrorEntry
import com.alibabacloud.devops.appstack.libs.common.util.checkExistsWithEntry
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.App
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.iam.AppRole
import com.alibabacloud.devops.appstack.libs.model.request.AppBindTemplateRequest
import com.alibabacloud.devops.appstack.libs.model.request.AppRequest
import com.alibabacloud.devops.appstack.libs.model.request.SearchAppRequest
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.AddMembersRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.DeleteMembersRequest
import com.alibabacloud.devops.appstack.libs.model.request.ng.MemberOperateResult
import com.alibabacloud.devops.appstack.libs.model.request.ng.UpdateMemberRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import com.alibabacloud.devops.appstack.libs.model.vo.*

import com.alibabacloud.devops.appstack.libs.model.vo.ng.Application
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

@Tag(name = "App", description = "应用相关 API")
@RestController
@RequestMapping("/api/v1/apps", "/openapi/v1/apps")
open class AppBizApi {

    @Autowired
    lateinit var appBizService: AppBizService

    @Autowired
    lateinit var permissionService: PermissionService

    @GetMapping("/{appName}")
    fun findApp(
        @PathVariable("appName") name: String
    ): Response<AppBaseInfoVO> {
        val app = appBizService.find(name = name)
        checkExistsWithEntry(app, name) { ErrorEntry(code = ErrorCode.AS_APP_NOT_FOUND, showType = ShowType.SILENT) }
        return Response.success(app)
    }

    @PostMapping("/:count")
    fun groupCount(
        @RequestBody searchAppRequest: SearchAppRequest,
    ): Response<AppCountVO> {
        return Response.success(appBizService.groupCount(searchAppRequest))
    }

    @PostMapping("/:search")
    fun searchAppPaginated(
        @RequestBody searchAppRequest: SearchAppRequest,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<AppSearchVO>> {
        val paginatedApps = appBizService.searchPaginated(searchAppRequest, current, pageSize)
        return Response.success(paginatedApps)
    }

    @PostMapping("/")
    fun createApp(@RequestBody req: AppRequest): Response<App> {
        val app = appBizService.create(req)
        return Response.success(app)
    }

    @PutMapping("/{appName}")
    fun updateApp(
        @PathVariable("appName") name: String,
        @RequestBody req: UpdateAppRequest,
    ): Response<Unit> {
        appBizService.update(name, req)
        return Response.success()
    }

    @DeleteMapping("/{appName}")
    fun deleteApp(@PathVariable("appName") name: String): Response<Unit> {
        appBizService.delete(name)
        return Response.success()
    }

    @GetMapping("/{appName}/members")
    fun findAllAppMember(@PathVariable("appName") appName: String): Response<List<MemberVO>> {
        val allMemberMap = appBizService.findAllMember(appName)
        val list = allMemberMap.map { MemberVO(it.key, it.value) }.sortedWith { o1, o2 ->
            if (o1.roleList.find { it.name == AppRole.owner.name } != null) -1
            else if (o2.roleList.find { it.name == AppRole.owner.name } != null) 1
            else
                o1.displayName.compareTo(o2.displayName)
        }
        return Response.success(list)
    }

    @Operation(summary = "添加应用成员", operationId = "CreateAppMembers")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "添加应用成员成功"),
            ApiResponse(responseCode = "403", description = "没权限设置拥有者"),
            ApiResponse(responseCode = "404", description = "角色未找到"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PostMapping("/{appName}/members")
    fun createAppMembers(
        @Schema(description = "应用名", example = "my-demo-app", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("appName") appName: String,
        @RequestBody addMembersRequest: AddMembersRequest
    ): Response<Boolean> {
        addMembersRequest.roleNames.forEach {
            appBizService.addRoleMember(
                appName = appName,
                roleName = it,
                playerList = addMembersRequest.playerList,
            )
        }
        return Response.success(true)
    }

    @GetMapping("/members:findByApps")
    fun findAllAppsMember(@RequestParam("appNames") appNames: String): Response<Map<String, List<MemberVO>>> {
        return Response.success(appNames.split(",").associateWith { appName -> findAllAppMember(appName).data ?: emptyList() })
    }

    @PostMapping("/members:add")
    fun batchAddAppMembers(
        @RequestBody addMembersRequest: AddMembersRequest
    ): Response<List<MemberOperateResult>> {
        return Response.success(appBizService.batchAddMembers(addMembersRequest.appNames!!, addMembersRequest))
    }

    @PostMapping("/members:delete")
    fun batchDeleteAppMembers(
        @RequestBody deleteMembersRequest: DeleteMembersRequest,
    ): Response<List<MemberOperateResult>> {
        return Response.success(
            appBizService.batchDeleteMembers(
                deleteMembersRequest.appNames,
                deleteMembersRequest.playerList
            )
        )
    }

    @Operation(summary = "删除应用成员", operationId = "DeleteAppMember")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "删除应用成员成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @DeleteMapping("/{appName}/members")
    fun deleteAppMember(
        @Schema(description = "应用名", example = "my-demo-app", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("appName") appName: String,
        @Schema(description = "成员类型", example = "User", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @RequestParam subjectType: SubjectType,
        @Schema(description = "成员id", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @RequestParam subjectId: String,
    ): Response<Boolean> {
        appBizService.updateMemberRole(
            appName = appName,
            subjectType = subjectType,
            subjectId = subjectId,
            roleNameList = emptyList()
        )
        return Response.success(true)
    }

    @Operation(summary = "更新应用成员", operationId = "UpdateAppMember")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "更新应用成员角色成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常", content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))])
        ]
    )
    @PutMapping("/{appName}/members")
    fun updateAppMember(
        @Schema(description = "应用名", example = "my-demo-app", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("appName") appName: String,
        @RequestBody updateMemberRequest: UpdateMemberRequest
    ): Response<Boolean> {
        appBizService.updateMemberRole(
            appName = appName,
            subjectType = updateMemberRequest.player.type,
            subjectId =  updateMemberRequest.player.id,
            roleNameList = updateMemberRequest.roleNames
        )
        return Response.success(true)
    }

    @PutMapping("/{appName}:owner")
    fun transferOwner(
        @PathVariable("appName") appName: String,
        @RequestBody owner: ResourcePlayerRequest,
    ): Response<Boolean> {
        appBizService.transferOwner(
            appName = appName,
            player = owner
        )
        return Response.success(true)
    }

    @PostMapping("/{name}:favourite")
    fun favour(@PathVariable("name") name: String): Response<Unit> {
        return Response.success(appBizService.favour(name))
    }

    @DeleteMapping("/{name}:favourite")
    fun disfavour(@PathVariable("name") name: String): Response<Unit> {
        return Response.success(appBizService.disfavour(name))
    }

    @Operation(summary = "查询应用关联的应用模板")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询关联应用模板成功"),
            ApiResponse(responseCode = "404", description = "应用不存在 / 应用模板不存在"),
            ApiResponse(responseCode = "500", description = "服务端内部异常")
        ]
    )
    @GetMapping("/{name}/template")
    fun findTemplate(@PathVariable name: String): Response<AppTemplate> {
        return Response.success(appBizService.findTemplate(name))
    }

    @Operation(summary = "应用关联应用模板")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "绑定模板成功"),
            ApiResponse(responseCode = "404", description = "应用不存在 / 应用模板不存在"),
            ApiResponse(responseCode = "500", description = "服务端内部异常")
        ]
    )
    @PostMapping("/{name}/template:bind")
    fun bindTemplate(@PathVariable name: String,
        @RequestBody appBindTemplateRequest: AppBindTemplateRequest
    ): Response<Boolean>{
        appBizService.bindTemplate(name, appBindTemplateRequest.appTemplateName)
        return Response.success(true)
    }

    @Operation(summary = "应用解绑应用模板")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "解除绑定模板成功"),
            ApiResponse(responseCode = "404", description = "应用不存在 / 应用模板不存在"),
            ApiResponse(responseCode = "500", description = "服务端内部异常")
        ]
    )
    @PostMapping("/{name}/template:unbind")
    fun unbindTemplate(@PathVariable name: String): Response<Boolean> {
        appBizService.unbindTemplate(name)
        return Response.success(true)
    }

    @Operation(summary = "分页查找管理的应用列表")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询管理的应用列表成功"),
            ApiResponse(responseCode = "500", description = "服务端内部异常")
        ]
    )
    @GetMapping(":admin")
    fun listAdminedAppsPaginated(
        @RequestParam("search", required = false, defaultValue = "") search: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<Application>> {
        return Response.success(appBizService.listAdminedAppsPaginated(search, current, pageSize))
    }


    @Operation(summary = "分页查找关联系统列表")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "查询关联系统列表成功"),
            ApiResponse(responseCode = "404", description = "应用未找到"),
            ApiResponse(responseCode = "500", description = "服务端内部异常")
        ]
    )
    @GetMapping("/{name}:relatedSystems")
    fun listRelatedSystemsPaginated(
        @PathVariable("name") name: String,
        @RequestParam("search", required = false, defaultValue = "") search: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<Application>> {
        return Response.success(appBizService.listRelatedSystemsPaginated(name, search, current, pageSize))
    }
}