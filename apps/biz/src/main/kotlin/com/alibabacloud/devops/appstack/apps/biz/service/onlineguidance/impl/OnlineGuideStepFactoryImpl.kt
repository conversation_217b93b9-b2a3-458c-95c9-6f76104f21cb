package com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.impl

import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.OnlineGuideStepFactory
import com.alibabacloud.devops.appstack.apps.biz.service.onlineguidance.StepHandlerService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import org.springframework.beans.factory.BeanFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.Locale

/**
 * @author: <EMAIL>
 * @date: 2022-08-25 22:16
 * @version: OnlineGuideStepFactoryImpl, v0.1
 **/
@Service
class OnlineGuideStepFactoryImpl : OnlineGuideStepFactory {

    @Autowired
    lateinit var beanFactory: BeanFactory

    override fun build(stepName: String): StepHandlerService {
        val sb = java.lang.StringBuilder()
        stepName.lowercase().split("_").forEach { it ->
            sb.append(it.replaceFirstChar { it.uppercase() })
        }
        val beanName = sb.append("StepHandlerServiceImpl").toString().replaceFirstChar { it.lowercase(Locale.getDefault()) }
        val bean = if(beanFactory.containsBean(beanName)){
            beanFactory.getBean(beanName) as StepHandlerService
        }else {
            null
        }
        checkExists(bean){ ErrorCode.AS_BIZ_ONLINE_GUIDE_STEP_HANDLER_NOT_FOUND }
        return bean
    }
}