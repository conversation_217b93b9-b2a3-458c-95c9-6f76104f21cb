package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowTemplate
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.CreateReleaseWorkflowTemplateRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.UpdateReleaseWorkflowTemplateRequest

/**
 * <AUTHOR>
 * @date 2022-08-12
 */
interface ReleaseWorkflowTemplateBizService {

    fun searchPaginated(
        workflowType: ReleaseWorkflow.TypeEnum,
        scope: ReleaseWorkflowTemplate.ScopeEnum,
        current: Long,
        pageSize: Long,
    ): Pagination<ReleaseWorkflowTemplate>

    fun find(sn: String): ReleaseWorkflowTemplate

    fun delete(sn: String)

    fun update(sn:String, request: UpdateReleaseWorkflowTemplateRequest): ReleaseWorkflowTemplate

    fun create(request: CreateReleaseWorkflowTemplateRequest): ReleaseWorkflowTemplate
}