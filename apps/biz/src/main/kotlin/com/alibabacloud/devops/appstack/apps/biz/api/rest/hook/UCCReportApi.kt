package com.alibabacloud.devops.appstack.apps.biz.api.rest.hook

import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil
import com.aliyuncs.ucc.base.useractionlog.model.UccActionLogBO
import com.google.common.hash.Hashing
import io.swagger.v3.oas.annotations.Hidden
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.text.SimpleDateFormat
import java.util.*


/**
 * <AUTHOR> <EMAIL>
 * @version : UCCReport, v0.1
 * @date : 2024-05-24 15:06
 **/
@Slf4k
@Hidden
@Tag(name = "UCCReport", description = "运营活动上报")
@RestController
@RequestMapping("/hook/api/ucc/")
class UCCReportApi {

    companion object {
        const val ACTION = "product-yunxiao-action-appstack-spring-ai"
        const val OBJECT_ID = "appstack_spring_ai"
    }

    @GetMapping("/report")
    fun report(
        @RequestParam("aliyunPk") aliyunPk: String,
        @RequestParam("userId") userId: String,
        @RequestParam("pipelineId") pipelineId: String,
    ) {

        // 东8区时间字符串格式
        val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")

        // uid留空字符串
        val uid = ""
        val actionGmtCreate = sdf.format(Date())
        val app = "cloud-product"
        val bizCategory = "product-yunxiao-action"

        /**
         * 行为日志唯一标识，一般为UUID，补数据时不同的数据必须不同，推荐使用 业务-MD5(uid+"::"+
         * aliyunPk+"::"+actionGmtCreate+"::"+action+"::"+objectId) 格式（表示用户在什么时间对什么对象做了什么事情）
         * MD5可以用其他实现替代，此处用guava作为案例
         * 参数推荐使用Map<String></String>,String>
         */
        val logId = String.format(
            "%s-%s", bizCategory,
            Hashing.md5().hashBytes(
                String.format("%s::%s::%s::%s::%s", uid, aliyunPk, actionGmtCreate, ACTION, OBJECT_ID).toByteArray()
            )
        )
        val bo = UccActionLogBO.Builder()
            .setUid(uid)
            .setAliyunPk(aliyunPk)
            .setActionGmtCreate(actionGmtCreate)
            .setAction(ACTION)
            .setObjectId(OBJECT_ID)
            .setApp(app)
            .setBizCategory(bizCategory)
            .setLogId(logId)
            .build()

        logger.info("UCC_USER_ACTION_LOG ${JacksonUtil.jacksonObjectMapper().writeValueAsString(bo)}", )
    }
}