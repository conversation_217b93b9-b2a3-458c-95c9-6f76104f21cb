package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.ReleaseWorkflowTemplateBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.transfer
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowTemplateVO
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.toVO
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.CreateReleaseWorkflowTemplateRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.UpdateReleaseWorkflowTemplateRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * <AUTHOR>
 * @date 2022-08-12
 */
@Tag(name = "ReleaseWorkflowTemplate", description = "研发流程相关 API")
@RestController
@RequestMapping("/api/v1")
open class ReleaseWorkflowTemplateBizApi {

    @Autowired
    lateinit var releaseWorkflowTemplateBizService: ReleaseWorkflowTemplateBizService

    @Operation(summary = "创建研发流程模板")
    @PostMapping("/templates/releaseWorkflows")
    fun createReleaseWorkflowTemplate(@RequestBody request: CreateReleaseWorkflowTemplateRequest): Response<ReleaseWorkflowTemplateVO> {
        return Response.success(releaseWorkflowTemplateBizService.create(request).toVO())
    }

    @Operation(summary = "查找研发流程模板")
    @GetMapping("/templates/releaseWorkflows/{sn}")
    fun findReleaseWorkflowTemplate(@PathVariable sn: String): Response<ReleaseWorkflowTemplateVO> {
        return Response.success(releaseWorkflowTemplateBizService.find(sn).toVO())
    }

    @Operation(summary = "查找研发流程模板列表")
    @GetMapping("/templates/releaseWorkflows")
    fun searchReleaseWorkflowTemplatePaginated(
        @RequestParam(value = "workflowType") workflowType: ReleaseWorkflow.TypeEnum,
        @RequestParam(value = "scope") scope: ReleaseWorkflowTemplate.ScopeEnum,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "500") pageSize: Long,
    ): Response<Pagination<ReleaseWorkflowTemplateVO>> {
        val pageResult = releaseWorkflowTemplateBizService.searchPaginated(
            workflowType,
            scope,
            current,
            pageSize
        )
        return Response.success(pageResult.transfer(pageResult.records.map { it.toVO() }))
    }

    @Operation(summary = "删除研发流程模板")
    @DeleteMapping("/templates/releaseWorkflows/{sn}")
    fun deleteReleaseWorkflowTemplate(@PathVariable sn: String): Response<Unit> {
        releaseWorkflowTemplateBizService.delete(sn)
        return Response.success()
    }

    @Operation(summary = "修改研发流程模板")
    @PutMapping("/templates/releaseWorkflows/{sn}")
    fun updateReleaseWorkflowTemplate(
        @PathVariable sn: String,
        @RequestBody request: UpdateReleaseWorkflowTemplateRequest
    ): Response<ReleaseWorkflowTemplateVO> {
        return Response.success(releaseWorkflowTemplateBizService.update(sn ,request).toVO())
    }

}
