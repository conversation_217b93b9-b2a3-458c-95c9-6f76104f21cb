package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.audit.AuditItem
import com.alibabacloud.devops.appstack.libs.model.request.audit.splc.AuditCallbackRequest
import com.alibabacloud.devops.appstack.libs.model.request.audit.splc.AuditSubmitRequest

/**
 * @author: <EMAIL>
 * @date: 2022-11-29 10:56
 * @version: AuditItemBizService, v0.1
 **/
interface AuditItemBizService {
    fun submit(req: AuditSubmitRequest): AuditItem?
    fun callback(req: AuditCallbackRequest)
    fun find(type: AuditItem.Type, refType: AuditItem.RefType, refSn: String): AuditItem?
    fun findAll(refType: AuditItem.RefType, refSn: String): List<AuditItem>
    fun findByRefs(refType: AuditItem.RefType, refSns: List<String>): Map<String, List<AuditItem>>
}