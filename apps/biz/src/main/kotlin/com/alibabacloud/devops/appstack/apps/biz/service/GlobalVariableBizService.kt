package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.request.CreateGlobalVariableRequest
import com.alibabacloud.devops.appstack.libs.model.request.GlobalVariable
import com.alibabacloud.devops.appstack.libs.model.request.UpdateGlobalVariableRequest

/**
 * <AUTHOR> <EMAIL>
 * @version : GlobalVariableBizService, v0.1
 * @date : 2024-01-29 12:28
 **/
interface GlobalVariableBizService {

    fun find(name: String, revisionSha: String?): GlobalVariable

    fun create(req: CreateGlobalVariableRequest): GlobalVariable

    fun update(name: String, req: UpdateGlobalVariableRequest): GlobalVariable

    fun delete(name: String): <PERSON><PERSON><PERSON>

}