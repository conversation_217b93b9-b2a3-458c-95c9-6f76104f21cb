package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.PermissionService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.iam.AppRole
import com.alibabacloud.devops.appstack.libs.model.iam.OrgRole
import com.alibabacloud.devops.appstack.libs.model.iam.SystemRole
import com.alibabacloud.devops.iam.model.Permission
import com.alibabacloud.devops.iam.model.PolicyOption
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.request.CreateRoleRequest
import com.alibabacloud.devops.iam.model.request.UpdateRoleRequest
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * @author: <EMAIL>
 * @date: 2021-11-02 10:10
 * @version: GlobalSettingApi, v0.1
 **/
@Tag(name = "GlobalSetting", description = "全局设置 API")
@RestController
@RequestMapping("/api/v1/globalSettings")
open class GlobalSettingApi {

    @Autowired
    lateinit var permissionService: PermissionService

    @GetMapping("/orgRoles/{roleName}/permissions")
    fun findOrgRolePermission(@PathVariable("roleName") roleName: String): Response<Permission> {
        val role = permissionService.findOrgRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        return Response.success(permissionService.findOrgPermission(role))
    }

    @GetMapping("/orgRoles")
    fun findAllOrgRoles(): Response<List<Pair<Role, Boolean>>> {
        return Response.success(permissionService.findAllOrgRoles())
    }

    @PostMapping("/orgRoles")
    fun createOrgRole(@RequestBody createRoleRequest: CreateRoleRequest): Response<Role> {
        return Response.success(permissionService.createOrgRole(createRoleRequest))
    }

    @PutMapping("/orgRoles/{roleName}")
    fun updateOrgRole(
        @PathVariable("roleName") roleName: String,
        @RequestBody updateRoleRequest: UpdateRoleRequest
    ): Response<Role> {
        val role = permissionService.findOrgRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        return Response.success(permissionService.updateOrgRole(role, updateRoleRequest))
    }

    @DeleteMapping("/orgRoles/{roleName}")
    fun deleteOrgRole(@PathVariable("roleName") roleName: String): Response<Boolean> {
        val role = permissionService.findOrgRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        return Response.success(permissionService.deleteOrgRole(role))
    }

    @PostMapping("/orgRoles/{roleName}/permissions")
    fun saveOrgRolePermission(
        @PathVariable("roleName") roleName: String,
        @RequestBody policyOptionList: List<PolicyOption>
    ): Response<Permission> {
        val role = permissionService.findOrgRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        if (OrgRole.owner.name == role.name) {
            throw BizException(ErrorCode.AS_ROLE_OWNER_PERMISSION_CANNOT_MODIFY)
        }
        return Response.success(permissionService.saveOrgPermission(role, policyOptionList))
    }

    @GetMapping("/appRoles/{roleName}/permissions")
    fun findAppRolePermission(@PathVariable("roleName") roleName: String): Response<Permission> {
        val role = permissionService.findAppRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        return Response.success(permissionService.findAppPermission(role))
    }

    @GetMapping("/appRoles")
    fun findAllAppRoles(): Response<List<Role>> {
        return Response.success(permissionService.findAllAppRoles())
    }

    @PostMapping("/appRoles")
    fun createAppRole(@RequestBody createRoleRequest: CreateRoleRequest): Response<Role> {
        return Response.success(permissionService.createAppRole(createRoleRequest))
    }

    @PutMapping("/appRoles/{roleName}")
    fun updateAppRole(
        @PathVariable("roleName") roleName: String,
        @RequestBody updateRoleRequest: UpdateRoleRequest
    ): Response<Role> {
        val role = permissionService.findAppRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        return Response.success(permissionService.updateAppRole(role, updateRoleRequest))
    }

    @DeleteMapping("/appRoles/{roleName}")
    fun deleteAppRole(@PathVariable("roleName") roleName: String): Response<Boolean> {
        val role = permissionService.findAppRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        return Response.success(permissionService.deleteAppRole(role))
    }

    @PostMapping("/appRoles/{roleName}/permissions")
    fun saveAppRolePermission(
        @PathVariable("roleName") roleName: String,
        @RequestBody policyOptionList: List<PolicyOption>
    ): Response<Permission> {
        val role = permissionService.findAppRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        if (AppRole.owner.name == role.name) {
            throw BizException(ErrorCode.AS_ROLE_OWNER_PERMISSION_CANNOT_MODIFY)
        }
        return Response.success(permissionService.saveAppPermission(role, policyOptionList))
    }

    @GetMapping("/systemRoles/{roleName}/permissions")
    fun findSystemRolePermission(@PathVariable("roleName") roleName: String): Response<Permission> {
        val role = permissionService.findSystemRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        return Response.success(permissionService.findSystemPermission(role))
    }

    @GetMapping("/systemRoles")
    fun findAllSystemRoles(): Response<List<Role>> {
        return Response.success(permissionService.findAllSystemRoles())
    }

    @PostMapping("/systemRoles")
    fun createSystemRole(@RequestBody createRoleRequest: CreateRoleRequest): Response<Role> {
        return Response.success(permissionService.createSystemRole(createRoleRequest))
    }

    @PutMapping("/systemRoles/{roleName}")
    fun updateSystemRole(
        @PathVariable("roleName") roleName: String,
        @RequestBody updateRoleRequest: UpdateRoleRequest
    ): Response<Role> {
        val role = permissionService.findSystemRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        return Response.success(permissionService.updateSystemRole(role, updateRoleRequest))
    }

    @DeleteMapping("/systemRoles/{roleName}")
    fun deleteSystemRole(@PathVariable("roleName") roleName: String): Response<Boolean> {
        val role = permissionService.findSystemRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        return Response.success(permissionService.deleteSystemRole(role))
    }

    @PostMapping("/systemRoles/{roleName}/permissions")
    fun saveSystemRolePermission(
        @PathVariable("roleName") roleName: String,
        @RequestBody policyOptionList: List<PolicyOption>
    ): Response<Permission> {
        val role = permissionService.findSystemRole(roleName)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        if (SystemRole.owner.name == role.name) {
            throw BizException(ErrorCode.AS_ROLE_OWNER_PERMISSION_CANNOT_MODIFY)
        }
        return Response.success(permissionService.saveSystemPermission(role, policyOptionList))
    }

    @Operation(summary = "判断当前企业是否开启权限（可见）")
    @GetMapping("/permissions:visible")
    fun permissionVisible(): Response<Boolean> {
        return Response.success(permissionService.permissionVisible())
    }
}