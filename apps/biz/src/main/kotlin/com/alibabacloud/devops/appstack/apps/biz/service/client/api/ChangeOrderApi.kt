package com.alibabacloud.devops.appstack.apps.biz.service.client.api

import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.env.EnvTaskDetail
import com.alibabacloud.devops.appstack.libs.model.event.ActionLog
import com.alibabacloud.devops.appstack.libs.model.request.EnvActionLogRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitClient
import retrofit2.http.*

/**
 * change order 工单相关api
 */
@RetrofitClient(baseUrl = "http://appstack-change-order")
interface ChangeOrderApi {

    @DELETE("/v2/api/app/order/deleteByAppName")
    fun deleteOrderAndVersion(
        @HeaderMap headers: Map<String, String> = AuthUtil.getHeaders(),
        @Query("appName") appName: String
    ): Response<Boolean>

    @FormUrlEncoded
    @POST("/api/flowstep/batchDeleteRelate")
    fun batchDeleteRelate(
        @HeaderMap headers: Map<String, String> = AuthUtil.getHeaders(),
        @Field("appName") appName: String
    ): Response<Boolean>


    @POST("/api/env/action/log:create")
    fun recordEnvCreateAction(
        @HeaderMap headers: Map<String, String> = AuthUtil.getHeaders(),
        @Body request: EnvActionLogRequest
    ): Response<Boolean>

    @POST("/api/env/action/log:update")
    fun recordEnvUpdateAction(
        @HeaderMap headers: Map<String, String> = AuthUtil.getHeaders(),
        @Body request: EnvActionLogRequest
    ): Response<Boolean>

    @POST("/api/env/action/log:lock")
    fun recordEnvLockAction(
        @HeaderMap headers: Map<String, String> = AuthUtil.getHeaders(),
        @Body request: EnvActionLogRequest
    ): Response<Boolean>


    @POST("/api/env/action/log:unlock")
    fun recordEnvUnlockAction(
        @HeaderMap headers: Map<String, String> = AuthUtil.getHeaders(),
        @Body request: EnvActionLogRequest
    ): Response<Boolean>

    @POST("/api/env/action/log:delete")
    fun recordEnvDeleteAction(
        @HeaderMap headers: Map<String, String> = AuthUtil.getHeaders(),
        @Body request: EnvActionLogRequest
    ): Response<Boolean>

    @GET("/api/apps/{appName}/envs/{envName}/actionLogs")
    fun findPaginatedEnvActionLog(
        @HeaderMap headers: Map<String, String> = AuthUtil.getHeaders(),
        @Path("appName") appName: String,
        @Path("envName") envName: String,
        @Query("pageSize") pageSize: Long,
        @Query("current") current: Long,
    ): Response<Pagination<ActionLog>>

    @GET("/api/apps/{appName}/latestEnvTasks")
    fun queryLatestEnvTaskByAppName(
        @HeaderMap headers: Map<String, String> = AuthUtil.getHeaders(),
        @Path("appName") appName: String,
    ): Response<List<EnvTaskDetail>>
}