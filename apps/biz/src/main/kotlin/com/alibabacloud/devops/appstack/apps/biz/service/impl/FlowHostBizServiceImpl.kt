package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.config.FlowAuthConfiguration
import com.alibabacloud.devops.appstack.apps.biz.service.FlowHostBizService
import com.alibabacloud.devops.appstack.apps.biz.service.client.api.FlowHostApi
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.CommonCode
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.model.ErrorEntry
import com.alibabacloud.devops.appstack.libs.common.util.LocaleUtil
import com.alibabacloud.devops.appstack.libs.common.util.TokenUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkBiz
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.i18n.message.spring.boot.starter.service.I18nMessageService
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.*
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.springframework.beans.factory.annotation.Autowired

import org.springframework.stereotype.Service
import kotlin.math.log

/**
 * @author: <EMAIL>
 * @date: 2022-07-08 15:13
 * @version: HostGroupBizServiceImpl, v0.1
 **/
@Slf4k
@Service
class FlowHostBizServiceImpl : FlowHostBizService {

    @Autowired
    lateinit var flowAuthConfig: FlowAuthConfiguration

    @Autowired
    lateinit var flowHostApi: FlowHostApi

    @Autowired
    lateinit var i18nMessageService: I18nMessageService

    override fun findEcs(
        serviceConnectionId: Long,
        aliyunRegionId: String,
        flowScopeType: FlowScopeType,
        checkAllEcsMachine: Boolean,
        sns: String?
    ): List<Host> {
        val response = flowHostApi.findEcs(
            getHeaders(),
            serviceConnectionId,
            aliyunRegionId,
            flowScopeType.name,
            checkAllEcsMachine,
            sns
        )
        checkExists(response) {
            ErrorCode.AS_HTTP_CALL_ERROR
        }
        checkBiz(response.successful) {
            ErrorEntry(code = ErrorCode.AS_RES_CALL_FLOW_HOST_FAILED, message = response.errorMessage)
        }
        response.data ?: return emptyList()
        return response.data!!.aliyunEcsList.map { it.toHost() }
    }

    override fun findSelf(purpose: FlowPurpose, flowScopeType: FlowScopeType, sns: String?): List<Host> {
        val response = flowHostApi.findSelfHost(getHeaders(), purpose.name, flowScopeType.name, sns)
        checkExists(response) {
            ErrorCode.AS_HTTP_CALL_ERROR
        }
        checkBiz(response.successful) {
            ErrorEntry(code = ErrorCode.AS_RES_CALL_FLOW_HOST_FAILED, message = response.errorMessage)
        }
        response.data ?: return emptyList()
        return response.data!!.map { it.toHost() }
    }

    override fun findAllByGroupId(groupId: Long): List<Host> {
        val response = flowHostApi.findAllByGroupId(getHeaders(), groupId, true)
        checkExists(response) {
            ErrorCode.AS_HTTP_CALL_ERROR
        }
        logger.info("findAllByGroupId result:{}", jacksonObjectMapper().writeValueAsString(response))
        checkBiz(response.successful) {
            ErrorEntry(code = ErrorCode.AS_RES_CALL_FLOW_HOST_FAILED, message = response.errorMessage)
        }
        //return response.data ?: ""
        response.data ?: return emptyList()
        return response.data!!.machineInfos.map { it.toHost() }
    }

    override fun fetchInstallCommand(purpose: FlowPurpose, flowScopeType: FlowScopeType, osType: OsType): String {
        val response = flowHostApi.fetchInstallCommand(getHeaders(), purpose.name, flowScopeType.name, osType.name)
        checkExists(response) {
            ErrorCode.AS_HTTP_CALL_ERROR
        }
        checkBiz(response.successful) {
            ErrorEntry(code = ErrorCode.AS_RES_CALL_FLOW_HOST_FAILED, message = response.errorMessage)
        }
        return response.data!!
    }

    override fun fetchMachineDeployLog(tunnelId: Long, machineSn: String): DeployMachineLog {
        val response = flowHostApi.fetchMachineDeployLog(getHeaders(), tunnelId, machineSn)
        checkExists(response) {
            ErrorCode.AS_HTTP_CALL_ERROR
        }
        checkBiz(response.successful) {
            ErrorEntry(code = ErrorCode.AS_RES_CALL_FLOW_HOST_FAILED, message = response.errorMessage)
        }
        val deployMachineLog = response.data!!
        return if(deployMachineLog.deployLog.isNullOrEmpty()){
            deployMachineLog.deployLog = i18nMessageService.commonResource.getMessage(
                CommonCode.HOST_DEPLOY_LOG_NULL_TIPS.value,
                null,
                LocaleUtil.getLocale()
            )
            deployMachineLog
        }else{
            deployMachineLog
        }
    }


    override fun createExperience(hostGroupExperienceDTO: HostGroupExperienceDTO): Long? {
        val response = flowHostApi.createExperience(
            getHeaders(),
            hostGroupExperienceDTO.aliyunRegion,
            hostGroupExperienceDTO.machineGroupName,
            hostGroupExperienceDTO.accessKeyId,
            hostGroupExperienceDTO.accessSecret,
            hostGroupExperienceDTO.instanceId,
                hostGroupExperienceDTO.scope.name
            )
        checkExists(response) {
            ErrorCode.AS_HTTP_CALL_ERROR
        }
        checkBiz(response.successful) {
            ErrorEntry(code = ErrorCode.AS_RES_CALL_FLOW_HOST_GROUP_FAILED, message = response.errorMessage)
        }
        return response.data
    }

    override fun fetchCreateResult(instanceId: String, flowScopeType: FlowScopeType): Long? {
        val response = flowHostApi.fetchCreateExperienceResult(
                getHeaders(),
                instanceId,
                flowScopeType.name
            )

        checkExists(response) {
            ErrorCode.AS_HTTP_CALL_ERROR
        }
        checkBiz(response.successful) {
            ErrorEntry(code = ErrorCode.AS_RES_CALL_FLOW_HOST_GROUP_FAILED, message = response.errorMessage)
        }
        return response.data
    }

    override fun isRunnerFeature(): Boolean {
        val response = flowHostApi.isRunnerFeature(
            getHeaders()
        )
        checkExists(response) {
            ErrorCode.AS_HTTP_CALL_ERROR
        }
        checkBiz(response.successful) {
            ErrorEntry(code = ErrorCode.AS_RES_CALL_FLOW_HOST_GROUP_FAILED, message = response.errorMessage)
        }
        return response.data!!
    }

    private fun getHeaders(): Map<String, String> {
        return TokenUtil.generateAuthHeaders(flowAuthConfig.appId, flowAuthConfig.appSecret)
    }
}