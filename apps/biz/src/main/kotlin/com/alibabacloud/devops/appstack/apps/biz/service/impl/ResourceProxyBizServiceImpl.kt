package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.annotation.OrgBasedRateLimitSpot
import com.alibabacloud.devops.appstack.apps.biz.service.ResourceProxyBizService
import com.alibabacloud.devops.appstack.libs.change.controller.spring.boot.starter.service.ChangeControllerFacades
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.model.`do`.orchestration.k8s.UnstructuredKubernetesResource
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.AckClusterInfo
import com.alibabacloud.devops.appstack.libs.model.request.resourcemanager.AccessKeyRequest
import com.alibabacloud.devops.appstack.libs.model.vo.KubernetesHealthyVO
import com.alibabacloud.devops.appstack.libs.model.vo.KubernetesObjectInfoVO
import com.alibabacloud.devops.appstack.libs.model.vo.PodInfoVO
import com.alibabacloud.devops.appstack.resource.manager.spring.boot.starter.ResourceManagerFacades
import io.fabric8.kubernetes.api.model.EventList
import io.fabric8.kubernetes.api.model.NamespaceList
import io.fabric8.kubernetes.api.model.NodeList
import io.fabric8.kubernetes.api.model.Pod
import io.fabric8.kubernetes.api.model.PodList
import io.fabric8.kubernetes.api.model.apps.Deployment
import io.fabric8.kubernetes.api.model.metrics.v1beta1.NodeMetricsList
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * @author: <EMAIL>
 * @date: 2022-04-11 14:46
 * @version: ResourceProxyBizServiceImpl, v0.1
 **/
@Service
@Slf4k
open class ResourceProxyBizServiceImpl : ResourceProxyBizService {

    @Autowired
    lateinit var resourceManagerFacades: ResourceManagerFacades

    @Autowired
    lateinit var changeControllerFacades: ChangeControllerFacades

    override fun findAckCluster(clusterInfo: String, accessKeyRequest: AccessKeyRequest): AckClusterInfo {
        return resourceManagerFacades.resourceProxyFacade.findAckCluster(clusterInfo, accessKeyRequest)
    }

    override fun findAllNamespace(resourcePath: String): NamespaceList {
        return resourceManagerFacades.resourceProxyFacade.findAllNamespace(resourcePath)
    }

    override fun findAllNodeMetrics(resourcePath: String): NodeMetricsList {
        return resourceManagerFacades.resourceProxyFacade.findAllNodeMetrics(resourcePath)
    }

    override fun findAllNodes(resourcePath: String, continueVal: String?, limit: Int): NodeList {
        return resourceManagerFacades.resourceProxyFacade.findAllNodes(resourcePath, continueVal, limit)
    }

    override fun findAllPod(resourcePath: String, namespace: String, limit: Int, continueVal: String?): PodList {
        return resourceManagerFacades.resourceProxyFacade.findAllPod(resourcePath, namespace, limit, continueVal)
    }

    override fun findPod(resourcePath: String, namespace: String, name: String): Pod? {
        return resourceManagerFacades.resourceProxyFacade.findPod(resourcePath, namespace, name)
    }

    override fun findAllPodEvent(resourcePath: String, namespace: String, name: String): EventList {
        return resourceManagerFacades.resourceProxyFacade.findAllPodEvent(resourcePath, namespace, name)
    }

    override fun findPodInfo(resourcePath: String, namespace: String, name: String, taskSn: String?): PodInfoVO? {
        logger.info("findPodInfo taskSn:${taskSn} start")
        if (StringUtils.isNotBlank(taskSn)) {
            val snapshot = changeControllerFacades.taskFacade.findSnapshot(taskSn!!)
            if (null != snapshot && snapshot.any { it.name == name }) {
                return snapshot.first { it.name == name }
            }
        }
        logger.info("findPodInfo taskSn:${taskSn} snapshot failed")

        val podObject = resourceManagerFacades.resourceProxyFacade.findPod(resourcePath, namespace, name)

        return PodInfoVO(
            name = name,
            pod = podObject,
            eventList = resourceManagerFacades.resourceProxyFacade.findAllPodEvent(resourcePath, namespace, name),
            objectLiveYaml = UnstructuredKubernetesResource.yamlMapper.writeValueAsString(podObject)
        )
    }

    @OrgBasedRateLimitSpot(restraintName = "fetchPodContainerLog")
    override fun fetchPodContainerLog(
        resourcePath: String,
        namespace: String,
        name: String,
        container: String,
        tailingLines: Int
    ): String? {
        return resourceManagerFacades.resourceProxyFacade.fetchPodContainerLog(
            resourcePath,
            namespace,
            name,
            container,
            tailingLines
        )
    }

    override fun find(resourcePath: String): Deployment {
        return resourceManagerFacades.kubeVelaFacade.find(resourcePath)!!
    }

    override fun describeKubernetesObjectInfo(
        resourcePath: String,
        namespace: String,
        kind: String,
        name: String,
        taskSn: String?
    ): KubernetesObjectInfoVO? {
        try {
            if (taskSn != null) {
                val task = changeControllerFacades.taskFacade.find(taskSn)
                if (task != null && task.state.isFinish()) {
                    val diagnosisSnapshotView = changeControllerFacades.taskFacade.fetchDiagnosisSnapshot(taskSn)
                    if (diagnosisSnapshotView != null) {
                        val targetSnapshot = diagnosisSnapshotView.objects.find {
                            it.name == name && it.kind.lowercase() == kind.lowercase() && it.namespace == namespace
                        }
                        if (targetSnapshot != null) {
                            targetSnapshot.snapshot = true
                            return targetSnapshot
                        }
                    }
                }
            }
        } catch (e: Throwable) {
            logger.error("[ResourceProxyBizServiceImpl] Failed to find kubernetes object snapshot: " +
                "kind=$kind, ns=$namespace, name=$name, task=${taskSn ?: "n/a"}, message=${e.message ?: "null"}", e)
        }
        return resourceManagerFacades.resourceProxyFacade.describeKubernetesObjectInfo(resourcePath, namespace, kind, name)
    }

    override fun probeKubernetesHealth(resourcePath: String): KubernetesHealthyVO {
        return resourceManagerFacades.resourceProxyFacade.probeKubernetesHealth(resourcePath)
    }
}