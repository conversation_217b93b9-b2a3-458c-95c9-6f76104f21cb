package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateConfigBizService
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateConfigStorageFactory
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateService
import com.alibabacloud.devops.appstack.libs.hook.spring.boot.starter.service.HookService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.ActionType
import com.alibabacloud.devops.appstack.libs.model.`do`.hook.ResourceType
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.UpsertAppTemplateConfigRequest
import org.springframework.aop.framework.AopContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR> <EMAIL>
 * @version : AppTemplateConfigBizServiceImpl, v0.1
 * @date : 2023-10-19 22:06
 **/
@Service
open class AppTemplateConfigBizServiceImpl : AppTemplateConfigBizService {

    @Autowired
    lateinit var appTemplateConfigStorageFactory: AppTemplateConfigStorageFactory

    @Autowired
    lateinit var appTemplateService: AppTemplateService

    @Autowired
    lateinit var hookService: HookService

    override fun find(appTemplateName: String, type: AppTemplateConfig.TypeEnum): AppTemplateConfig? {
        return if(AppTemplateServiceImpl.PRESET_TEMPLATE_NAMES.contains(appTemplateName)){
            findInternal(appTemplateName, type)
        }else {
            (AopContext.currentProxy() as AppTemplateConfigBizServiceImpl).findInternal(appTemplateName, type)
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.ORG_APP_TEMPLATE_VIEW),
            Access(action = Action.APP_TEMPLATE_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_EDIT, resourceArgIndex = 0),
            Access(action = Action.APP_TEMPLATE_USE, resourceArgIndex = 0),
        ]
    )
    private fun findInternal(appTemplateName: String, type: AppTemplateConfig.TypeEnum): AppTemplateConfig? {
        if(appTemplateName.startsWith(AppTemplateServiceImpl.PRESET_TEMPLATE_NAME_PREFIX)){
            return appTemplateService.findPresetConfig(appTemplateName, type)
        }
        val adaptor = appTemplateConfigStorageFactory.build(type)
        return adaptor.find(appTemplateName)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.APP_TEMPLATE_EDIT, resourceArgIndex = 0)
        ]
    )
    override fun upsert(
        appTemplateName: String,
        type: AppTemplateConfig.TypeEnum,
        request: UpsertAppTemplateConfigRequest,
    ): AppTemplateConfig {
        val appTemplate = appTemplateService.find(appTemplateName)
        val adaptor = appTemplateConfigStorageFactory.build(type)
        val find = adaptor.find(appTemplateName)
        val appTemplateConfig = if (find == null) {
            adaptor.insert(appTemplate, request)
        } else {
            adaptor.update(appTemplate, find, request)
        }
        hookService.invoke(ResourceType.AppTemplate, ActionType.Update, null, appTemplate, appTemplateName)
        return appTemplateConfig
    }


    @Can(
        accessList = [
            Access(action = Action.ORG_APP_TEMPLATE_MANAGE),
            Access(action = Action.APP_TEMPLATE_DELETE, resourceArgIndex = 0)
        ]
    )
    override fun delete(appTemplateName: String, type: AppTemplateConfig.TypeEnum, appTemplateConfigSn: String) {
        val appTemplate = appTemplateService.find(appTemplateName)
        val adaptor = appTemplateConfigStorageFactory.build(type)
        return adaptor.delete(appTemplate, appTemplateConfigSn)
    }
}