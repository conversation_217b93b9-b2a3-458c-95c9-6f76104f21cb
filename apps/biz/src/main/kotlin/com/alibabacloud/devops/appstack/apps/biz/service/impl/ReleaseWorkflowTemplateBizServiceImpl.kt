package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.ReleaseWorkflowTemplateBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowTemplate
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.CreateReleaseWorkflowTemplateRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.UpdateReleaseWorkflowTemplateRequest
import com.alibabacloud.devops.appstack.libs.workflow.controller.spring.boot.starter.service.WorkflowControllerFacade
import org.springframework.aop.framework.AopContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2022-08-12
 */
@Service
open class ReleaseWorkflowTemplateBizServiceImpl : ReleaseWorkflowTemplateBizService {

    @Autowired
    lateinit var workflowControllerFacade: WorkflowControllerFacade

    @Autowired
    lateinit var auditLogService: AuditLogService

    override fun searchPaginated(
        workflowType: ReleaseWorkflow.TypeEnum,
        scope: ReleaseWorkflowTemplate.ScopeEnum,
        current: Long,
        pageSize: Long,
    ): Pagination<ReleaseWorkflowTemplate> {
        return workflowControllerFacade.releaseWorkflowTemplateFacade.searchPaginated(
            workflowType,
            scope,
            current,
            pageSize
        )
    }

    override fun find(sn: String): ReleaseWorkflowTemplate {
        val template =  workflowControllerFacade.releaseWorkflowTemplateFacade.find(sn)
        checkExists(template, sn) { ErrorCode.AS_RELEASE_WORKFLOW_TEMPLATE_NOT_FOUND }
        return template
    }

    override fun delete(sn: String) {
        val template = find(sn)
        val dynamicProxy = AopContext.currentProxy() as ReleaseWorkflowTemplateBizServiceImpl
        when (template.workflowType) {
            ReleaseWorkflow.TypeEnum.CR -> dynamicProxy.deleteReleaseWorkflow(template.templateSn)
            ReleaseWorkflow.TypeEnum.APP_RELEASE -> dynamicProxy.deleteAppReleaseWorkflow(template.templateSn)

        }
        auditLogService.commonLog(OrgEventType.WORKFLOW_TEMPLATE_DELETE, id = template.templateSn, name = template.name)
    }

    override fun update(sn: String, request: UpdateReleaseWorkflowTemplateRequest): ReleaseWorkflowTemplate {
        val template = find(sn)
        if (!request.name.isNullOrBlank()) {
            template.name = request.name!!
        }
        request.releaseStageTemplate?.let {
            template.releaseStageTemplate = it
        }
        val dynamicProxy = AopContext.currentProxy() as ReleaseWorkflowTemplateBizServiceImpl
        val updated = when (template.workflowType) {
            ReleaseWorkflow.TypeEnum.CR -> dynamicProxy.updateReleaseWorkflow(template)
            ReleaseWorkflow.TypeEnum.APP_RELEASE -> dynamicProxy.updateAppReleaseWorkflow(template)

        }
        auditLogService.commonLog(OrgEventType.WORKFLOW_TEMPLATE_MODIFY, id = updated.templateSn, name = updated.name)
        return updated
    }

    override fun create(request: CreateReleaseWorkflowTemplateRequest): ReleaseWorkflowTemplate {
        val dynamicProxy = AopContext.currentProxy() as ReleaseWorkflowTemplateBizServiceImpl
        val template = when (request.workflowType) {
            ReleaseWorkflow.TypeEnum.CR -> dynamicProxy.createReleaseWorkflow(request)
            ReleaseWorkflow.TypeEnum.APP_RELEASE -> dynamicProxy.createAppReleaseWorkflow(request)

        }
        auditLogService.commonLog(OrgEventType.WORKFLOW_TEMPLATE_CREATE, id = template.templateSn, name = template.name)
        return template
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_RELEASEWORKFLOW_TEMPLATE_CREATE),
            Access(action = Action.ORG_RELEASEWORKFLOW_TEMPLATE_MANAGE)
        ]
    )
    open fun createReleaseWorkflow(request: CreateReleaseWorkflowTemplateRequest): ReleaseWorkflowTemplate {
        return workflowControllerFacade.releaseWorkflowTemplateFacade.create(request)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_RELEASE_TEMPLATE_CREATE),
            Access(action = Action.ORG_APP_RELEASE_TEMPLATE_MANAGE)
        ]
    )
    open fun createAppReleaseWorkflow(request: CreateReleaseWorkflowTemplateRequest): ReleaseWorkflowTemplate {
        return workflowControllerFacade.releaseWorkflowTemplateFacade.create(request)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_RELEASEWORKFLOW_TEMPLATE_MANAGE)
        ]
    )
    open fun updateReleaseWorkflow(template: ReleaseWorkflowTemplate): ReleaseWorkflowTemplate {
        return workflowControllerFacade.releaseWorkflowTemplateFacade.update(template)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_RELEASE_TEMPLATE_MANAGE)
        ]
    )
    open fun updateAppReleaseWorkflow(template: ReleaseWorkflowTemplate): ReleaseWorkflowTemplate {
        return workflowControllerFacade.releaseWorkflowTemplateFacade.update(template)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_RELEASEWORKFLOW_TEMPLATE_MANAGE)
        ]
    )
    open fun deleteReleaseWorkflow(templateSn: String) {
        return workflowControllerFacade.releaseWorkflowTemplateFacade.delete(templateSn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_RELEASE_TEMPLATE_MANAGE)
        ]
    )
    open fun deleteAppReleaseWorkflow(templateSn: String) {
        return workflowControllerFacade.releaseWorkflowTemplateFacade.delete(templateSn)
    }
}