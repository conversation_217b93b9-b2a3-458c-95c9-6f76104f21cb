package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.DeployGroupBizService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.DeployGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourcePool
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.ResourcePoolVO
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2021-12-15 16:55
 * @version: ResourceBizApi, v0.1
 **/
@Tag(name = "ResourcePool", description = "资源管理相关 API")
@RestController
@RequestMapping("/api/v1/")
open class ResourcePoolBizApi {

    @Autowired
    private lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var iamService: IamService

    @Autowired
    lateinit var deployGroupBizService: DeployGroupBizService

    @GetMapping("/pools")
    fun findAllPool(
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long,
    ): Response<Pagination<ResourcePoolVO>> {
        return Response.success(coreFacades.resourceFacade.findPaginatedPool(current, pageSize))
    }

    @PostMapping("/pools")
    fun createPool(@RequestBody pool: ResourcePool): Response<ResourcePool> {
        return Response.success(coreFacades.resourceFacade.createPool(pool))
    }

    @GetMapping("/pools/{name}")
    fun findPoolByName(@PathVariable("name") name: String): Response<ResourcePoolVO> {
        return if (ResourcePool.DEFAULT_NAME == name) {
            Response.success(coreFacades.resourceFacade.findDefaultPool())
        } else {
            Response.success(coreFacades.resourceFacade.findPoolByName(name))
        }
    }

    @PutMapping("/pools/{name}")
    fun updatePool(@PathVariable("name") name: String, pool: ResourcePool): Response<ResourcePool> {
        return Response.success(coreFacades.resourceFacade.updatePool(name, pool))
    }

    @DeleteMapping("/pools/{name}")
    fun deletePool(@PathVariable("name") name: String): Response<Unit> {
        return Response.success(coreFacades.resourceFacade.deletePool(name))
    }



}