package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.AckClusterInfo
import com.alibabacloud.devops.appstack.libs.model.request.resourcemanager.AccessKeyRequest
import com.alibabacloud.devops.appstack.libs.model.vo.KubernetesHealthyVO
import com.alibabacloud.devops.appstack.libs.model.vo.KubernetesObjectInfoVO
import com.alibabacloud.devops.appstack.libs.model.vo.PodInfoVO
import io.fabric8.kubernetes.api.model.EventList
import io.fabric8.kubernetes.api.model.NamespaceList
import io.fabric8.kubernetes.api.model.NodeList
import io.fabric8.kubernetes.api.model.Pod
import io.fabric8.kubernetes.api.model.PodList
import io.fabric8.kubernetes.api.model.apps.Deployment
import io.fabric8.kubernetes.api.model.metrics.v1beta1.NodeMetricsList

/**
 * @author: <EMAIL>
 * @date: 2022-04-12 15:40
 * @version: ResourceProxyBizService, v0.1
 **/
interface ResourceProxyBizService {
    fun findAckCluster(clusterInfo: String, accessKeyRequest: AccessKeyRequest): AckClusterInfo
    fun findAllNamespace(resourcePath: String): NamespaceList
    fun findAllNodeMetrics(resourcePath: String): NodeMetricsList
    fun findAllNodes(resourcePath: String, continueVal: String?, limit: Int): NodeList
    fun findAllPod(
        resourcePath: String,
        namespace: String,
        limit: Int,
        continueVal: String?
    ): PodList
    @Deprecated("新版的接口已经整合到findPodInfo了，老版还在用这个接口，干不掉啊")
    fun findPod(resourcePath: String, namespace: String, name: String): Pod?
    @Deprecated("新版的接口已经整合到findPodInfo了，老版还在用这个接口，干不掉啊")
    fun findAllPodEvent(
        resourcePath: String,
        namespace: String,
        name: String
    ): EventList
    fun findPodInfo(resourcePath: String, namespace: String, name: String, taskSn: String?): PodInfoVO?
    fun fetchPodContainerLog(
        resourcePath: String,
        namespace: String,
        name: String,
        container: String,
        tailingLines: Int
    ): String?

    fun find(resourcePath: String): Deployment

    fun describeKubernetesObjectInfo(
        resourcePath: String,
        namespace: String,
        kind: String,
        name: String,
        taskSn: String? = null
    ): KubernetesObjectInfoVO?

    fun probeKubernetesHealth(resourcePath: String): KubernetesHealthyVO
}