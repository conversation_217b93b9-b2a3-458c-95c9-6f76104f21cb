package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.ade2.sdk.ext.defaultClient
import com.alibabacloud.devops.appstack.ade2.sdk.remote.AdeClientFactory
import com.alibabacloud.devops.appstack.ade2.sdk.request.v2.concrete.DataPlaneHandleDeleteRequest
import com.alibabacloud.devops.appstack.apps.biz.annotation.ApiMetricCounter
import com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1.ResourceInstanceBizApi
import com.alibabacloud.devops.appstack.apps.biz.model.AppTemplateConfigReference
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateBizService
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateConfigReferenceService
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.apps.biz.service.ResourceBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.context.AuthThreadContext
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.common.util.PageUtil
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.config.IamContext
import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.resource.ResourceInstance
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.iam.ResRole
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.org.event.AuditTarget
import com.alibabacloud.devops.appstack.libs.model.org.event.CommonAudit
import com.alibabacloud.devops.appstack.libs.model.org.event.Member
import com.alibabacloud.devops.appstack.libs.model.org.event.MemberAudit
import com.alibabacloud.devops.appstack.libs.model.org.event.MemberRole
import com.alibabacloud.devops.appstack.libs.model.org.event.MemberRoleAudit
import com.alibabacloud.devops.appstack.libs.model.request.ng.ResourceInstanceQuery
import com.alibabacloud.devops.appstack.libs.model.request.resource.ResourceInstanceRequest
import com.alibabacloud.devops.appstack.libs.model.vo.ResourceInstanceRecord
import com.alibabacloud.devops.appstack.libs.model.vo.ResourceInstanceVO
import com.alibabacloud.devops.appstack.libs.model.vo.toRecord
import com.alibabacloud.devops.appstack.resource.manager.spring.boot.starter.ResourceManagerFacades
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.exception.IamForbiddenException
import com.alibabacloud.devops.iam.model.ResourcePlayer
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.request.ResourcePlayerRequest
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import org.springframework.aop.framework.AopContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2022-03-17
 */
@Service
open class ResourceBizServiceImpl : ResourceBizService {

    @Autowired
    lateinit var resourceManagerFacades: ResourceManagerFacades

    @Autowired
    lateinit var iamService: IamService

    @Autowired
    lateinit var auditLogService: AuditLogService

    @Autowired
    lateinit var appTemplateConfigReferenceService: AppTemplateConfigReferenceService

    @Autowired
    lateinit var appTemplateBizService: AppTemplateBizService

    private val ade2Client = AdeClientFactory.defaultClient()

    @ApiMetricCounter(metricName = "resourceInstance_operation", methodTag = "import")
    @Can(
        accessList = [
            Access(action = Action.ORG_RESOURCE_IMPORT),
            Access(action = Action.ORG_RESOURCE_MANAGE),
        ]
    )
    override fun importInstance(
        poolName: String,
        resourceInstanceRequest: ResourceInstanceRequest,
    ): ResourceInstance {
        val resourceInstance = resourceManagerFacades.resourceInstanceFacade.importInstance(
            poolName,
            resourceInstanceRequest.toResourceInstance()
        )
        val ownerId = resourceInstanceRequest.ownerId ?: AuthUtil.getUserId()
        iamService.registerResource(
            protocolType = ProtocolType.AppStackRes,
            resourceName = resourceInstance.primaryKey(),
            orgId = AuthUtil.getTenant(),
            ownerId = ownerId
        )
        val auditLog = CommonAudit(
            target = AuditTarget(id = resourceInstance.instanceId, name = resourceInstance.name),
        )
        val type =
            if (resourceInstance.isHost()) OrgEventType.CLUSTER_HOST_IMPORT else OrgEventType.CLUSTER_K8S_IMPORT
        auditLogService.log(type, auditLog)
        return resourceInstance
    }

    override fun findAllInstance(poolName: String, type: String?): List<ResourceInstanceRecord> {
        val allInstance = resourceManagerFacades.resourceInstanceFacade.findAllInstance(poolName, type)
        allInstance.forEach { hideKubeconfig(it) }
        val names = allInstance.map { it.primaryKey() }
        val resourcePlayers = iamService.findRolePlayers(ProtocolType.AppStackRes, names, ResRole.owner.name)
        val toBeRegister = mutableListOf<ResourceInstanceVO>()
        val result = allInstance.map {
            val owner = resourcePlayers[it.primaryKey()]?.get(0)
            if (owner.isNullOrEmpty()) {
                toBeRegister.add(it)
            }
            it.toRecord(owner)
        }
        if (toBeRegister.isNotEmpty()) {
            // 补齐iam数据缺失
            val ctx = AuthThreadContext.export()
            val iamCtx = IamContext.export()
            GlobalScope.launch {
                AuthThreadContext.replaceAll(ctx)
                IamContext.replaceAll(iamCtx)
                toBeRegister.forEach {
                    iamService.registerResource(
                        protocolType = ProtocolType.AppStackRes,
                        resourceName = it.primaryKey(),
                        orgId = AuthUtil.getTenant(),
                        ownerId = it.creatorId ?: AuthUtil.getUserId()
                    )
                }
            }
        }
        return result
    }

    override fun listInstances(poolName: String, query: ResourceInstanceQuery): PageList<ResourceInstanceVO> {
        if (!query.checkPassed()) {
            throw BizException(ErrorCode.AS_PAGINATION_PARAM_INVALID)
        }
        val allInstancePage = resourceManagerFacades.resourceInstanceFacadeV2.list(poolName, query)
        val allInstance = allInstancePage.data
        allInstance.forEach { hideKubeconfig(it) }
        return PageList(nextToken = allInstancePage.nextToken, data = allInstance)
    }

    override fun findInstance(poolName: String, instanceName: String): ResourceInstanceVO {
        val dynamicProxy = AopContext.currentProxy() as ResourceBizServiceImpl
        val vo = try {
            dynamicProxy.findInstanceWithEditPermission(poolName, instanceName)
        } catch (e: IamForbiddenException) {
            dynamicProxy.findInstanceWithViewPermission(poolName, instanceName)
        }

        val templateNames =
            resourceManagerFacades.resourceDeployGroupFacade.findAllByInstance(poolName, instanceName).flatMap {
                appTemplateConfigReferenceService.findAppTemplateNameAndConfigByReference(
                    AppTemplateConfigReference(
                        type = AppTemplateConfigReference.TypeEnum.RESOURCE_INSTANCE,
                        AppTemplateConfigReference.convertResourceInstanceToReferenceId(vo.poolName, it.name)
                    )
                )
            }.map { it.appTemplateName }
        val templates = appTemplateBizService.searchByNames(templateNames).map { it.value }
        vo.relatedAppTemplate = templates
        return vo;
    }

    private fun hideKubeconfig(instance: ResourceInstance) {
        if (instance.type == ResourceInstance.InstanceConfigType.SELF_KUBERNETES) {
            // 隐藏kubeconfig
            instance.contextMap.remove("content")
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_RESOURCE_MANAGE),
            Access(action = Action.RES_EDIT, resourceArgIndex = 0, resourceArgSecondIndex = 1)
        ]
    )
    open fun findInstanceWithEditPermission(poolName: String, instanceName: String): ResourceInstanceVO {
        return resourceManagerFacades.resourceInstanceFacade.findInstance(poolName, instanceName)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_RESOURCE_VIEW),
            Access(action = Action.RES_VIEW, resourceArgIndex = 0, resourceArgSecondIndex = 1)
        ]
    )
    open fun findInstanceWithViewPermission(poolName: String, instanceName: String): ResourceInstanceVO {
        val instance = resourceManagerFacades.resourceInstanceFacade.findInstance(poolName, instanceName)
        hideKubeconfig(instance)
        return instance
    }

    @ApiMetricCounter(metricName = "resourceInstance_operation", methodTag = "delete")
    @Can(
        accessList = [
            Access(action = Action.ORG_RESOURCE_MANAGE),
            Access(action = Action.RES_DELETE, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    override fun deleteResourceInstance(poolName: String, instanceName: String) {
        val instance = findInstance(poolName, instanceName)
        checkExists(instance, mapOf("name" to instanceName)) {
            ErrorCode.AS_RES_INST_NOT_FOUND
        }
        if(instance.relateAppToEnvMap != null && instance.relateAppToEnvMap!!.isNotEmpty() || instance.relatedAppTemplate.isNotEmpty()) {
            throw BizException(ErrorCode.AS_RES_INST_RELATED_APP_TEMPLATE_OR_ENV)
        }
        resourceManagerFacades.resourceInstanceFacade.deleteResourceInstance(
            instance.poolName,
            instance.name
        )
        iamService.unregisterResource(
            protocolType = ProtocolType.AppStackRes,
            resourceName = instance.primaryKey(),
            orgId = AuthUtil.getTenant(),
            operatorId = AuthUtil.getUserId(),
        )
        if (instance.type == ResourceInstance.InstanceConfigType.ACK ||
            instance.type == ResourceInstance.InstanceConfigType.SELF_KUBERNETES
        ) {
            ade2Client.invoke(
                DataPlaneHandleDeleteRequest(
                    orgId = AuthUtil.getTenant(),
                    externalId = instanceName,
                    upstream = ResourceInstanceBizApi.UPSTREAM_NAME
                )
            )
        }
        val auditLog = CommonAudit(
            target = AuditTarget(id = instance.instanceId, name = instance.name),
        )
        val type =
            if (instance.isHost()) OrgEventType.CLUSTER_HOST_REMOVE else OrgEventType.CLUSTER_K8S_REMOVE
        auditLogService.log(type, auditLog)
    }

    @ApiMetricCounter(metricName = "resourceInstance_operation", methodTag = "update")
    @Can(
        accessList = [
            Access(action = Action.ORG_RESOURCE_MANAGE),
            Access(action = Action.RES_EDIT, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    override fun updateResourceInstance(
        poolName: String,
        name: String,
        resourceInstance: ResourceInstance,
    ): ResourceInstance {
        return resourceManagerFacades.resourceInstanceFacade.updateResourceInstance(poolName, resourceInstance)
    }

    override fun findAllMember(poolName: String, instanceName: String): Map<ResourcePlayer, List<Role>> {
        val instance = findInstance(poolName, instanceName)
        return iamService.findPlayerRoles(
            protocolType = ProtocolType.AppStackRes,
            resourceName = instance.primaryKey(),
        )
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_RESOURCE_MANAGE),
            Access(action = Action.RES_MEMBER_SET, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    override fun addRoleMember(
        poolName: String,
        instanceName: String,
        role: Role,
        playerList: List<ResourcePlayerRequest>,
    ) {
        val instance = findInstance(poolName, instanceName)
        iamService.addRoleMember(
            protocolType = ProtocolType.AppStackRes,
            resourceName = instance.primaryKey(),
            roleName = role.name,
            playerList = playerList,
        )
        val auditLog = MemberAudit(
            target = AuditTarget(id = instance.instanceId, name = instance.name),
            member = Member(roleName = role.name, userIds = playerList.map { it.id }),
        )
        auditLogService.log(OrgEventType.CLUSTER_MEMBER_ADD, auditLog)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_RESOURCE_MANAGE),
            Access(action = Action.RES_MEMBER_SET, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    override fun updateRoleMember(
        poolName: String,
        instanceName: String,
        role: Role,
        playerList: List<ResourcePlayerRequest>,
    ) {
        val instance = findInstance(poolName, instanceName)
        doUpdateRoleMember(instance, role, playerList)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_RESOURCE_MANAGE),
            Access(action = Action.RES_OWNER_TRANSFER, resourceArgIndex = 0, resourceArgSecondIndex = 1),
        ]
    )
    override fun transferOwner(
        poolName: String,
        instanceName: String,
        playerList: List<ResourcePlayerRequest>,
    ) {
        val role = iamService.findRole(ProtocolType.AppStackRes, ResRole.owner.name)
        checkExists(role) { ErrorCode.AS_ROLE_NOT_FOUND }
        val instance = findInstance(poolName, instanceName)
        // 原拥有者降级为管理员
        updateOwnerToAdmin(instance)
        doUpdateRoleMember(instance, role, playerList)
        val auditLog = MemberAudit(
            target = AuditTarget(id = instance.instanceId, name = instance.name),
            member = Member(roleName = role.name, userIds = playerList.map { it.id }),
        )
        auditLogService.log(OrgEventType.CLUSTER_OWNER_TRANSFER, auditLog)
    }

    private fun updateOwnerToAdmin(instance: ResourceInstance) {
        val resourceName = instance.primaryKey()
        val rolePlayers = iamService.findRolePlayers(
            protocolType = ProtocolType.AppStackRes,
            resourceNameList = listOf(instance.primaryKey()),
            roleName = ResRole.owner.name
        )
        rolePlayers[resourceName]?.forEach { player ->
            updateMemberRole(
                poolName = instance.poolName,
                instanceName = instance.name,
                subjectId = player,
                subjectType = SubjectType.User,
                roleNameList = listOf(ResRole.admin.name)
            )
        }
    }

    override fun updateMemberRole(
        poolName: String,
        instanceName: String,
        subjectId: String,
        subjectType: SubjectType,
        roleNameList: List<String>,
    ) {
        val instance = findInstance(poolName, instanceName)
        val previousRoles = iamService.findPlayerRoles(
            protocolType = ProtocolType.AppStackRes,
            resourceName = instance.primaryKey(),
        ).mapKeys { it.key.id }[subjectId] ?: emptyList()
        iamService.updatePlayer(
            protocolType = ProtocolType.AppStackRes,
            resourceName = instance.primaryKey(),
            subjectId = subjectId,
            subjectType = subjectType,
            roleNameList = roleNameList,
        )
        val auditLog = MemberRoleAudit(
            target = AuditTarget(id = instance.instanceId, name = instance.name),
            member = MemberRole(
                roleNames = roleNameList,
                previousRoleNames = previousRoles.map { it.name },
                userId = subjectId
            )
        )
        val auditType =
            if (roleNameList.isEmpty()) OrgEventType.CLUSTER_MEMBER_DELETE else OrgEventType.CLUSTER_MEMBER_MODIFY
        auditLogService.log(auditType, auditLog)
    }

    override fun findAllInstanceCanUse(poolName: String, type: String?): List<ResourceInstanceVO> {
        val can = iamService.can(protocolType = ProtocolType.AppStack, "any", Action.ORG_RESOURCE_MANAGE)
        if (can) {
            return resourceManagerFacades.resourceInstanceFacade.findAllInstance(poolName, type)
        }

        val teams = iamService.getTeamPlayer(AuthUtil.getTenant(), AuthUtil.getUserId())
        val groups = iamService.getGroupPlayer(AuthUtil.getTenant(), AuthUtil.getUserId())
        val list = iamService.searchResourceWithPolicy(
            protocolType = ProtocolType.AppStackRes,
            playersList = listOf(ResourcePlayerRequest(AuthUtil.getUserId(), SubjectType.User)) + teams + groups,
            policyName = Action.RES_USE.code,
            search = "",
            page = 0,
            size = 100,
        )
        return resourceManagerFacades.resourceInstanceFacade.findAllInstance(list, type)
    }

    private fun doUpdateRoleMember(
        instance: ResourceInstanceVO,
        role: Role,
        playerList: List<ResourcePlayerRequest>,
    ) {
        iamService.updateRole(
            protocolType = ProtocolType.AppStackRes,
            resourceName = instance.primaryKey(),
            roleName = role.name,
            playerList = playerList,
        )
    }

}