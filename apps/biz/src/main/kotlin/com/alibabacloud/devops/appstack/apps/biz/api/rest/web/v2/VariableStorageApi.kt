package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v2

import com.alibabacloud.devops.appstack.apps.biz.annotation.VpcApi
import com.alibabacloud.devops.appstack.apps.biz.service.AppVariableGroupsBizService
import com.alibabacloud.devops.appstack.apps.biz.service.EnvBizService
import com.alibabacloud.devops.appstack.libs.change.controller.spring.boot.starter.service.ChangeControllerFacades
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.RevisionVariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.variable.VariableGroup
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.request.ProfileRequest
import com.alibabacloud.devops.appstack.libs.model.response.YunxiaoErrorResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.responses.ApiResponses
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2023-09-13 22:36
 * @version: VariableStorageApi, v0.1
 **/
@Tag(name = "变量组", description = "变量组领域 API")
@RestController
@RequestMapping("/api/v2")
@ConditionalOnProperty(name = ["devops.iam.environment"], havingValue = "vpc", matchIfMissing = false)
open class VariableStorageApi {

    @Autowired
    lateinit var appVariableGroupsBizService: AppVariableGroupsBizService

    @Autowired
    lateinit var changeControllerFacades: ChangeControllerFacades

    @Autowired
    lateinit var envBizService: EnvBizService

    @VpcApi
    @Operation(summary = "查找环境所绑定的变量组列表详情", operationId = "GetEnvVariableGroups")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已查找到变量组列表详情"),
            ApiResponse(
                responseCode = "404",
                description = "未查找到变量组列表详情",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/envs/{envName}/variableGroups")
    fun findVariableStorage(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @Schema(description = "环境名", example = "dev", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("envName") envName: String,
    ): List<RevisionVariableGroup> {
        return envBizService.listEnvRevisionVariableGroups(appName, envName)
    }

    @VpcApi
    @Operation(summary = "查询变量组详情", operationId = "GetVariableGroup")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已查找到变量组详情"),
            ApiResponse(
                responseCode = "404",
                description = "未查找到变量组详情",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/variableGroup/{variableGroupName}")
    fun findVariableGroup(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @Schema(description = "变量组名", example = "dev", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("variableGroupName") variableGroupName: String,
    ): RevisionVariableGroup {
        val varStorage = appVariableGroupsBizService.findAllProfiles(appName)
            ?: throw BizException(errorCode = ErrorCode.AS_VAR_NOT_FOUND)
        val profile = varStorage.profileMap[variableGroupName]
        checkExists(profile, variableGroupName) { ErrorCode.AS_VAR_PROFILE_NOT_FOUND }
        return RevisionVariableGroup(
            name = profile.name,
            displayName = profile.displayName,
            type = VariableGroup.Type.APP,
            revision = varStorage.revision,
            vars = profile.vars
        )
    }

    @VpcApi
    @Operation(summary = "创建变量组", operationId = "CreateVariableGroup")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已成功创建变量组"),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PostMapping("/apps/{appName}/variableGroup")
    fun createVariableGroup(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @RequestBody requestEntity: ProfileRequest,
    ): RevisionVariableGroup {
        val renewedStorage = appVariableGroupsBizService.createProfile(appName, requestEntity)
        val profile = renewedStorage.profileMap[requestEntity.name]
        checkExists(profile, requestEntity.name) { ErrorCode.AS_VAR_PROFILE_NOT_FOUND }
        return RevisionVariableGroup(
            name = profile.name,
            displayName = profile.displayName,
            type = VariableGroup.Type.APP,
            revision = renewedStorage.revision,
            vars = profile.vars
        )
    }

    @VpcApi
    @Operation(summary = "更新变量组", operationId = "UpdateVariableGroup")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已成功更新变量组"),
            ApiResponse(
                responseCode = "404",
                description = "未查找到指定的变量组",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @PutMapping("/apps/{appName}/variableGroup/{variableGroupName}")
    fun updateVariableGroup(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @Schema(description = "变量组名", example = "dev", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("variableGroupName") variableGroupName: String,
        @RequestBody requestEntity: ProfileRequest,
    ): RevisionVariableGroup {
        requestEntity.name = variableGroupName
        val renewedStorage = appVariableGroupsBizService.updateProfile(appName, requestEntity)
        val profile = renewedStorage.profileMap[requestEntity.name]
        checkExists(profile, requestEntity.name) { ErrorCode.AS_VAR_PROFILE_NOT_FOUND }
        return RevisionVariableGroup(
            name = profile.name,
            displayName = profile.displayName,
            type = VariableGroup.Type.APP,
            revision = renewedStorage.revision,
            vars = profile.vars
        )
    }

    @VpcApi
    @Operation(summary = "删除变量组", operationId = "DeleteVariableGroup")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已成功删除变量组"),
            ApiResponse(
                responseCode = "404",
                description = "未查找到指定的变量组",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @DeleteMapping("/apps/{appName}/variableGroup/{variableGroupName}")
    fun deleteVariableGroup(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
        @Schema(description = "变量组名", example = "dev", type = "string", requiredMode = Schema.RequiredMode.REQUIRED)
        @PathVariable("variableGroupName") variableGroupName: String,
    ): Boolean {
        appVariableGroupsBizService.deleteProfile(appName, variableGroupName)
        return true
    }

    @VpcApi
    @Operation(summary = "查找应用的变量组列表详情", operationId = "GetAppVariableGroups")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已查找到变量组列表详情"),
            ApiResponse(
                responseCode = "404",
                description = "未查找到变量组列表详情",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/variableGroups")
    fun findVariableStorage(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
    ): List<RevisionVariableGroup> {
        val findAllProfiles = appVariableGroupsBizService.findAllProfiles(appName) ?: return emptyList()
        return findAllProfiles.profileMap.values.map { profile ->
            RevisionVariableGroup(
                name = profile.name,
                displayName = profile.displayName,
                type = VariableGroup.Type.APP,
                revision = findAllProfiles.revision,
                vars = profile.vars
            )
        }
    }

    @Operation(summary = "查找应用的变量组版本", operationId = "GetAppVariableGroupsRevision")
    @ApiResponses(
        value = [
            ApiResponse(responseCode = "200", description = "已查找到变量组版本"),
            ApiResponse(
                responseCode = "404",
                description = "未查找到应用变量组版本",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            ),
            ApiResponse(
                responseCode = "500",
                description = "服务端内部异常",
                content = [Content(schema = Schema(implementation = YunxiaoErrorResponse::class))]
            )
        ]
    )
    @GetMapping("/apps/{appName}/variableGroups:revision")
    fun getAppVariableGroupsRevision(
        @Schema(
            description = "应用名",
            example = "my-web-service",
            type = "string",
            requiredMode = Schema.RequiredMode.REQUIRED
        )
        @PathVariable("appName") appName: String,
    ): Revision {
        val findAllProfiles = appVariableGroupsBizService.findAllProfiles(appName) ?: throw BizException(ErrorCode.AS_VCS_REPO_NOT_FOUND)
        return findAllProfiles.revision
    }

}