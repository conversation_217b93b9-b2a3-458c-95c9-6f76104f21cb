package com.alibabacloud.devops.appstack.apps.biz.service

import com.alibabacloud.devops.appstack.libs.model.`do`.PageList
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppCodeRepo
import com.alibabacloud.devops.appstack.libs.model.request.CreateAppCodeRepoRequest
import com.alibabacloud.devops.appstack.libs.model.request.PaginationQuery
import com.alibabacloud.devops.appstack.libs.model.request.UpdateAppCodeRepoRequest

/**
 * @author: <EMAIL>
 * @date: 2022-06-23 15:15
 * @version: AppCodeRepoBiz, v0.1
 **/
interface AppCodeRepoBizService {
    fun create(appName: String, createAppCodeRepoRequest: CreateAppCodeRepoRequest): AppCodeRepo
    fun delete(appName: String, sn: String)
    fun update(appName: String, sn: String, updateAppCodeRepoRequest: UpdateAppCodeRepoRequest): AppCodeRepo
    fun findAll(appName: String): List<AppCodeRepo>
    fun find(appName: String, sn: String): AppCodeRepo

    fun list(appName: String, query: PaginationQuery): PageList<AppCodeRepo>
}