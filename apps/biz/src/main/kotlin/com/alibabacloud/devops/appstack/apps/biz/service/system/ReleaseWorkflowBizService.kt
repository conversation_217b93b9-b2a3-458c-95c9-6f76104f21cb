package com.alibabacloud.devops.appstack.apps.biz.service.system

import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.DiffItem
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflow
import com.alibabacloud.devops.appstack.libs.model.`do`.workflowcontroller.ReleaseWorkflowRevision
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.CreateReleaseWorkflowBizRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.SortReleaseWorkflowRequest
import com.alibabacloud.devops.appstack.libs.model.request.workflowcontroller.UpdateReleaseWorkflowRequest

/**
 * 系统发布流程
 *
 * <AUTHOR>
 * @date 2023-11-08
 */
interface ReleaseWorkflowBizService {
    fun create(systemName: String, request: CreateReleaseWorkflowBizRequest): ReleaseWorkflow
    fun findAll(systemName: String): List<ReleaseWorkflow>
    fun find(systemName: String, sn: String): ReleaseWorkflow
    fun findWithRevision(systemName: String, sn: String): ReleaseWorkflowRevision?
    fun findYamlBySha(systemName: String, sn: String, sha: String): String
    fun listRevisionPaginated(systemName: String, sn: String, current: Long, pageSize: Long): Pagination<Revision>
    fun compare(systemName: String, sn: String, beforeRevisionSha: String, afterRevisionSha: String): Triple<String, String, List<DiffItem<String>>>
    fun update(systemName: String, sn: String, updateReleaseWorkflowRequest: UpdateReleaseWorkflowRequest): ReleaseWorkflow
    fun delete(systemName: String, sn: String)
    fun sort(systemName: String, request: SortReleaseWorkflowRequest): List<ReleaseWorkflow>
}