package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.EnvBizService
import com.alibabacloud.devops.appstack.apps.biz.service.EnvProxyBizService
import com.alibabacloud.devops.appstack.apps.biz.service.ResourceListVO
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.PodExecToken
import com.alibabacloud.devops.appstack.libs.model.`do`.resourcemanager.WorkloadBrief
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.fabric8.kubernetes.api.model.Pod
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * @author: <EMAIL>
 * @date: 2022-04-11 12:05
 * @version: CloudAuthBizApi, v0.1
 **/

@Tag(name = "EnvProxy", description = "环境资源代理查询 API")
@RestController
@RequestMapping("/api/v1")
open class EnvProxyBizApi {

    @Autowired
    lateinit var envProxyBizService: EnvProxyBizService

    @Autowired
    lateinit var envBizService: EnvBizService

    @GetMapping("/apps/{appName}/envs/{envName}/ns/{namespace}/deployments/{deploymentName}/pods")
    fun findAllPodByDeployment(
        @PathVariable appName: String,
        @PathVariable envName: String,
        @PathVariable namespace: String,
        @PathVariable deploymentName: String
    ): Response<List<Pod>> {
        return Response.success(envProxyBizService.findAllPods(appName, envName, namespace, deploymentName))
    }

    @GetMapping("/apps/{appName}/envs/{envName}/resources/{resourceKind}")
    fun findResourceList(
        @PathVariable appName: String,
        @PathVariable envName: String,
        @PathVariable resourceKind: String,
    ): Response<ResourceListVO> {
        return Response.success(envProxyBizService.findResourceList(appName, envName, resourceKind))
    }

    @GetMapping("/apps/{appName}/envs/{envName}/resources:count")
    fun countResources(
        @PathVariable appName: String,
        @PathVariable envName: String,
    ): Response<Map<String, Int>> {
        return Response.success(envProxyBizService.countResource(appName, envName))
    }

    @GetMapping("/apps/{appName}/envs/{envName}/namespaces/{namespace}/pods/{podName}/containers/{containerName}:execToken")
    fun getExecPodToken(
        @PathVariable appName: String,
        @PathVariable envName: String,
        @PathVariable namespace: String,
        @PathVariable podName: String,
        @PathVariable containerName: String
    ): Response<PodExecToken> {
        val env = envBizService.find(appName, envName)
        checkExists(env) { ErrorCode.AS_ENV_NOT_FOUND }
        return Response.success(envProxyBizService.generatePodExecToken(env, namespace, podName, containerName))
    }

    @GetMapping("/apps/{appName}/envs/{envName}/ns/{namespace}/deployments/{name}/revisions/{revision}")
    fun findDeploymentRevisionInfo(
        @PathVariable("appName") appName: String,
        @PathVariable("envName") envName: String,
        @PathVariable("namespace") namespace: String,
        @PathVariable("name") name: String,
        @PathVariable("revision") revision: String,
        @RequestParam("taskSn", required = false) taskSn: String? = null
    ): Response<WorkloadBrief?> {
        return Response.success(envProxyBizService.findDeploymentRevisionInfo(
            appName = appName,
            envName = envName,
            namespace = namespace,
            name = name,
            revision = revision,
            taskSn = taskSn
        ))
    }
}