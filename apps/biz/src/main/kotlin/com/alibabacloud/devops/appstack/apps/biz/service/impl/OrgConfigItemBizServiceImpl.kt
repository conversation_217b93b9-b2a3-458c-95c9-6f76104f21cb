package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.model.ext.toModel
import com.alibabacloud.devops.appstack.apps.biz.model.ext.toPO
import com.alibabacloud.devops.appstack.apps.biz.model.mapper.*
import com.alibabacloud.devops.appstack.apps.biz.service.OrgConfigItemBizService
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.util.checkExists
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.ChangeRequestMustBindWorkItemV1OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.OnlyViewAccessableAppV1OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.OnlyViewAccessableSystemV1OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.`do`.ReleaseVersionRegexV1OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2023-12-23
 */
@Service
open class OrgConfigItemBizServiceImpl : OrgConfigItemBizService {

    @Autowired
    lateinit var orgConfigItemMapper: OrgConfigItemMapper

    override fun findAll(): List<OrgConfigItem> {
        return OrgConfigItem.currentValidConfigs.map { entry ->
            val name = entry.key
            val version = entry.value
            val po = orgConfigItemMapper.find(name, version.name)
            po?.toModel() ?: init(name, version)
        }
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_CUSTOM_MANAGE)
        ]
    )
    override fun update(name: String, req: Map<String, Any>): OrgConfigItem {
        val version = OrgConfigItem.currentValidConfigs[name]
        checkExists(version) { ErrorCode.AS_ORG_CONFIG_NOT_FOUND }
        val orgConfigItemPO = orgConfigItemMapper.find(name, version.name)
        checkExists(orgConfigItemPO) { ErrorCode.AS_ORG_CONFIG_NOT_FOUND }

        val map = req.toMutableMap()
        map[OrgConfigItem::name.name] = name
        map[OrgConfigItem::version.name] = version
        val orgConfigItem =
            jacksonObjectMapper().readValue(jacksonObjectMapper().writeValueAsString(map), OrgConfigItem::class.java)
        orgConfigItemPO.content = jacksonObjectMapper().writeValueAsString(orgConfigItem.parseAttributes())
        orgConfigItemMapper.update(orgConfigItemPO)
        return orgConfigItemPO.toModel()
    }

    override fun find(name: String): OrgConfigItem {
        val version = OrgConfigItem.currentValidConfigs[name]
        checkExists(version) { ErrorCode.AS_ORG_CONFIG_NOT_FOUND }
        val po = orgConfigItemMapper.find(name, version.name)
        return po?.toModel() ?: init(name, version)
    }

    private fun init(name: String, version: OrgConfigItem.Version): OrgConfigItem {
        val orgConfigItem = if (name == OrgConfigItem.ONLY_VIEW_ACCESSABLE_APP && version == OrgConfigItem.Version.V1) {
            OnlyViewAccessableAppV1OrgConfigItem()
        } else if (name == OrgConfigItem.ONLY_VIEW_ACCESSABLE_SYSTEM && version == OrgConfigItem.Version.V1) {
            OnlyViewAccessableSystemV1OrgConfigItem()
        } else if (name == OrgConfigItem.RELEASE_VERSION_REGEX && version == OrgConfigItem.Version.V1) {
            ReleaseVersionRegexV1OrgConfigItem()
        } else if (name == OrgConfigItem.CHANGE_REQUEST_MUST_BIND_WORKITEM && version == OrgConfigItem.Version.V1) {
            ChangeRequestMustBindWorkItemV1OrgConfigItem()
        } else {
            TODO()
        }
        orgConfigItemMapper.insert(orgConfigItem.toPO())
        return orgConfigItem
    }
}