package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.service.ChangeRequestStageRuleConfigBizService
import com.alibabacloud.devops.appstack.apps.biz.service.StageRuleConfigBaseService
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.model.`do`.cr.StageRuleConfig
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.request.temp.StageRuleConfigRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @date 2023-11-08
 */
@Service
open class ChangeRequestStageRuleConfigBizServiceImpl: ChangeRequestStageRuleConfigBizService {

    @Autowired
    lateinit var stageRuleConfigBaseService: StageRuleConfigBaseService

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.ORG_APP_VIEW),
            Access(action = Action.APP_VIEW, resourceArgIndex = 0),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0),
        ]
    )
    override fun find(appName: String, releaseWorkflowSn: String, releaseStageSn: String): StageRuleConfig? {
        return stageRuleConfigBaseService.find(appName, releaseWorkflowSn, releaseStageSn)
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_MANAGE),
            Access(action = Action.APP_WORKFLOW_MANAGE, resourceArgIndex = 0)
        ]
    )
    override fun upsert(
        appName: String,
        releaseWorkflowSn: String,
        releaseStageSn: String,
        stageRuleConfigRequest: StageRuleConfigRequest
    ): StageRuleConfig? {
        return stageRuleConfigBaseService.upsert(appName, releaseWorkflowSn, releaseStageSn, stageRuleConfigRequest)
    }
}