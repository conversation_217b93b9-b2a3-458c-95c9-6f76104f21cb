package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.config.WebSocketConfig
import com.alibabacloud.devops.appstack.apps.biz.service.ChangeOrderWebSocketService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils
import org.springframework.web.socket.CloseStatus
import org.springframework.web.socket.TextMessage
import org.springframework.web.socket.WebSocketSession
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentSkipListSet

/**
 * <AUTHOR>
 * @date 2022-01-28
 */
@Slf4k
class ChangeOrderWebSocketServiceImpl : ChangeOrderWebSocketService() {

    companion object {
        val changeOrderClientMap: ConcurrentHashMap<String, ConcurrentSkipListSet<String>> =
            ConcurrentHashMap()
        val sessionMap: ConcurrentHashMap<String, WebSocketSession> = ConcurrentHashMap()
    }

    override fun notifyStateChanged(changeOrderSn: String, content: String) {
        val sessionIdSet = changeOrderClientMap.get(changeOrderSn)
        if (CollectionUtils.isNotEmpty(sessionIdSet)) {
            sessionIdSet!!.forEach {
                try {
                    sessionMap.get(it)?.sendMessage(TextMessage(content))
                    logger.info("Notify State Changed for ChangeOrderSn[$changeOrderSn] sessionId[$it] content[$content]")
                } catch (exception: Exception) {
                    val session = sessionMap.remove(it)
                    logger.info("Session[${session?.id}] has been closed, Remove it")
                    changeOrderClientMap.get(changeOrderSn)?.remove(it)
                    try {
                        session?.close()
                    } catch (exception: Exception) {

                    }
                }
            }
        }
    }

    override fun afterConnectionEstablished(session: WebSocketSession) {
        val changeOrderSn = getChangeOrderSn(session)
        changeOrderSn?.let {
            sessionMap.put(session.id, session)
            changeOrderClientMap.putIfAbsent(it, ConcurrentSkipListSet(setOf(session.id)))?.add(session.id)
            logger.info("WebSocket Client Connected for ChangeOrderSn[$changeOrderSn] Session[${session.id}]")
        }
    }

    override fun afterConnectionClosed(session: WebSocketSession, status: CloseStatus) {
        val changeOrderSn = getChangeOrderSn(session)
        sessionMap.remove(session.id)
        changeOrderSn?.let {
            changeOrderClientMap.get(it)?.remove(session.id)
            logger.info("WebSocket Client DisConnected for ChangeOrderSn[$changeOrderSn] Session[${session.id}] StatusCode[${status.code}]")
        }
    }

    override fun handleTextMessage(session: WebSocketSession, message: TextMessage) {
    }

    private fun getChangeOrderSn(session: WebSocketSession): String? {
        val changeOrderSn = session.attributes.get(WebSocketConfig.CHANGE_ORDER_SN_KEY)
        changeOrderSn?.let {
            if (it is String) {
                return it
            }
        }
        return null
    }
}