package com.alibabacloud.devops.appstack.apps.biz.service.impl.apptemplate.adaptor

import com.alibabacloud.devops.appstack.apps.biz.model.ext.toModel
import com.alibabacloud.devops.appstack.apps.biz.model.po.AppTemplateConfigPO
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateConfigStorageAdaptor
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.libs.common.util.JacksonUtil
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.app.AppTemplateConfig
import com.alibabacloud.devops.appstack.libs.model.`do`.app.SourceConfiguration
import com.alibabacloud.devops.appstack.libs.model.`do`.apptemplate.ArtifactRepoTemplate
import com.alibabacloud.devops.appstack.libs.model.`do`.apptemplate.CodeRepoTemplate
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.AbstractConfigurationUpsertRequest
import com.alibabacloud.devops.appstack.libs.model.request.apptemplate.SourceConfigurationUpsertRequest
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * <AUTHOR>
 * @create 2024/4/15 10:49 AM
 **/
@Service
open class AppTemplateConfigSourceStorageAdaptor : AppTemplateConfigStorageAdaptor<SourceConfiguration>() {

    @Autowired
    lateinit var auditLogService: AuditLogService

    override fun configType(): AppTemplateConfig.TypeEnum {
        return AppTemplateConfig.TypeEnum.Source
    }

    override fun convertConfiguration(appTemplateName: String, configuration: AbstractConfigurationUpsertRequest): Any {
        val config = configuration as SourceConfigurationUpsertRequest
        return SourceConfigV1DTO(
            codeRepos = config.codeRepos,
            artifactRepos = config.artifactRepos
        )
    }

    override fun convertToModel(appTemplateConfigPO: AppTemplateConfigPO): AppTemplateConfig {
        val dto = JacksonUtil.jacksonObjectMapper().readValue<SourceConfigDTO>(appTemplateConfigPO.configuration!!)
        val configuration = when (dto) {
            is SourceConfigV1DTO -> SourceConfiguration(codeRepos = dto.codeRepos, artifactRepos = dto.artifactRepos)
            else -> TODO()
        }
        return appTemplateConfigPO.toModel(configuration)
    }

    override fun logAudit(appTemplate: AppTemplate) {
        auditLogService.commonLog(OrgEventType.APP_TEMPLATE_MODIFY_SOURCES_AUDIT, appTemplate.name, appTemplate.displayName)
    }

    @JsonTypeInfo(
        use = JsonTypeInfo.Id.NAME,
        include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "version",
        visible = true
    )
    @JsonSubTypes(
        JsonSubTypes.Type(value = SourceConfigV1DTO::class, name = "v1"),
    )
    interface SourceConfigDTO {
        val version: String
    }

    data class SourceConfigV1DTO(
        override val version: String = "v1",
        val codeRepos: List<CodeRepoTemplate>,
        val artifactRepos: List<ArtifactRepoTemplate>
    ) : SourceConfigDTO {
    }
}