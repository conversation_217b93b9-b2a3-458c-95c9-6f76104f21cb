package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateVariableGroupConfigBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.Pagination
import com.alibabacloud.devops.appstack.libs.model.`do`.app.VariableGroupConfiguration
import com.alibabacloud.devops.appstack.libs.model.`do`.vcs.Revision
import com.alibabacloud.devops.appstack.libs.model.request.VariableTemplateBatchUpdateRequest
import com.alibabacloud.devops.appstack.libs.model.response.Response
import com.alibabacloud.devops.appstack.libs.model.vo.AppTemplateVariableGroupDiffDTO
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 * @date 2023-10-25
 */
@Tag(name = "AppTemplateVariableGroupConfig", description = "应用模板变量组配置相关 API")
@RestController
@RequestMapping("/api/v1/appTemplates/{name}/configs/VariableGroup")
open class AppTemplateVariableGroupConfigBizApi {

    @Autowired
    lateinit var appTemplateVariableGroupConfigBizService: AppTemplateVariableGroupConfigBizService


    @GetMapping("/revisions/{revisionSha}")
    fun findAppTemplateVariableGroupRevisionBySha(
        @PathVariable("name") name: String,
        @PathVariable("revisionSha") revisionSha: String
    ): Response<VariableGroupConfiguration> {
        return Response.success(appTemplateVariableGroupConfigBizService.findByRevison(name, revisionSha))
    }

    @GetMapping("/revisions")
    fun findAppTemplateVariableGroupRevisionPaginated(
        @PathVariable("name") name: String,
        @RequestParam("current", required = false, defaultValue = "1") current: Long,
        @RequestParam("pageSize", required = false, defaultValue = "10") pageSize: Long
    ): Response<Pagination<Revision>> {
        return Response.success(appTemplateVariableGroupConfigBizService.listRevisionPaginated(name, current, pageSize))
    }

    @GetMapping("/revisions:diff")
    fun compareAppTemplateVariableGroupByRevison(
        @PathVariable("name") name: String,
        @RequestParam("beforeRevisionSha") beforeRevisionSha: String,
        @RequestParam("afterRevisionSha") afterRevisionSha: String
    ): Response<AppTemplateVariableGroupDiffDTO> {
        return Response.success(appTemplateVariableGroupConfigBizService.compareRevision(name, beforeRevisionSha, afterRevisionSha))
    }

    @Operation(summary = "发起批量更新")
    @PostMapping("/batchUpdate")
    fun batchUpdate(
        @PathVariable("name") name: String,
        @RequestBody request: VariableTemplateBatchUpdateRequest
    ): Response<Boolean>{
        appTemplateVariableGroupConfigBizService.batchUpdate(name, request)
        return Response.success(true)
    }
}