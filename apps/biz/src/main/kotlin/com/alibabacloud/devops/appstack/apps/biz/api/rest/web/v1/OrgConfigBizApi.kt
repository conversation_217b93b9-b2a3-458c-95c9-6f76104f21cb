package com.alibabacloud.devops.appstack.apps.biz.api.rest.web.v1

import com.alibabacloud.devops.appstack.apps.biz.service.OrgConfigItemBizService
import com.alibabacloud.devops.appstack.libs.model.`do`.OrgConfigItem
import com.alibabacloud.devops.appstack.libs.model.response.Response
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

/**
 * <AUTHOR>
 * @date 2023-12-23
 */
@Tag(name = "OrgConfigBizApi", description = "企业级配置相关 API")
@RequestMapping("/api/v1/orgConfigs")
@RestController
open class OrgConfigBizApi {

    @Autowired
    lateinit var orgConfigItemBizService: OrgConfigItemBizService

    @Operation(summary = "初始化/查询全部企业配置")
    @GetMapping
    fun findAll(): Response<List<OrgConfigItem>> {
        return Response.success(orgConfigItemBizService.findAll())
    }

    @Operation(summary = "按条件查询企业配置")
    @GetMapping("/{name}")
    fun find(
        @PathVariable name: String
    ): Response<OrgConfigItem> {
        return Response.success(orgConfigItemBizService.find(name))
    }

    @Operation(summary = "更新企业配置")
    @PutMapping("/{name}")
    fun update(
        @PathVariable name: String,
        @RequestBody req: Map<String, Any>
    ): Response<OrgConfigItem> {
        return Response.success(orgConfigItemBizService.update(name, req))
    }
}
