package com.alibabacloud.devops.appstack.apps.biz.service.impl

import com.alibabacloud.devops.appstack.apps.biz.model.FlowRole
import com.alibabacloud.devops.appstack.apps.biz.model.FlowRolePermission
import com.alibabacloud.devops.appstack.apps.biz.model.vo.AppActionVO
import com.alibabacloud.devops.appstack.apps.biz.model.vo.SystemActionVO
import com.alibabacloud.devops.appstack.apps.biz.service.AppTemplateBizService
import com.alibabacloud.devops.appstack.apps.biz.service.AuditLogService
import com.alibabacloud.devops.appstack.apps.biz.service.IamService
import com.alibabacloud.devops.appstack.apps.biz.service.PermissionService
import com.alibabacloud.devops.appstack.apps.biz.service.ServiceConnectionBizService
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k
import com.alibabacloud.devops.appstack.libs.common.annotation.Slf4k.Companion.logger
import com.alibabacloud.devops.appstack.libs.common.constant.ErrorCode
import com.alibabacloud.devops.appstack.libs.common.constant.ShowType
import com.alibabacloud.devops.appstack.libs.common.exception.BizException
import com.alibabacloud.devops.appstack.libs.common.util.AuthUtil
import com.alibabacloud.devops.appstack.libs.core.spring.boot.starter.service.CoreFacades
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.IamClient
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Access
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.annotation.Can
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.config.protocol.EnvProtocol
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.config.protocol.SystemLabelProtocol
import com.alibabacloud.devops.appstack.libs.iam.spring.boot.starter.constant.Constant
import com.alibabacloud.devops.appstack.libs.model.iam.Action
import com.alibabacloud.devops.appstack.libs.model.iam.AppRole
import com.alibabacloud.devops.appstack.libs.model.iam.AppTemplateRole
import com.alibabacloud.devops.appstack.libs.model.iam.OrgRole
import com.alibabacloud.devops.appstack.libs.model.iam.ResRole
import com.alibabacloud.devops.appstack.libs.model.iam.SystemRole
import com.alibabacloud.devops.appstack.libs.model.iam.VarRole
import com.alibabacloud.devops.appstack.libs.model.org.OrgEventType
import com.alibabacloud.devops.appstack.libs.org.spring.boot.starter.service.OrgFacades
import com.alibabacloud.devops.iam.constant.ProtocolType
import com.alibabacloud.devops.iam.constant.SubjectType
import com.alibabacloud.devops.iam.exception.IamNotFoundException
import com.alibabacloud.devops.iam.model.Permission
import com.alibabacloud.devops.iam.model.PolicyOption
import com.alibabacloud.devops.iam.model.Role
import com.alibabacloud.devops.iam.model.request.CreateRoleRequest
import com.alibabacloud.devops.iam.model.request.UpdateRoleRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import java.util.stream.Collectors

/**
 * @author: <EMAIL>
 * @date: 2021-11-02 15:37
 * @version: PermissionServiceImpl, v0.1
 **/
@Slf4k
@Service
@ConditionalOnProperty(name = ["devops.iam.environment"], havingValue = "aliyun", matchIfMissing = true)
open class PermissionServiceImpl : PermissionService {

    @Autowired
    lateinit var iamClient: IamClient

    @Autowired
    lateinit var iamService: IamService

    @Autowired
    lateinit var coreFacades: CoreFacades

    @Autowired
    lateinit var serviceConnectionBizService: ServiceConnectionBizService

    @Autowired
    lateinit var orgFacades: OrgFacades

    @Autowired
    lateinit var auditLogService: AuditLogService

    @Autowired
    lateinit var appTemplateBizService: AppTemplateBizService

    companion object {

        enum class FlowPermissionCode(
            val code: String,
        ) {
            SERVICE_CONNECTION_CREATE("tb.role.serviceConnection.create"),
            SERVICE_CONNECTION_MANAGE("tb.role.serviceConnection.admin"),
        }

        val CODE_MAP = mapOf(
            Action.ORG_SERVICE_CONNECTION_CREATE.code to FlowPermissionCode.SERVICE_CONNECTION_CREATE.code,
            Action.ORG_SERVICE_CONNECTION_MANAGE.code to FlowPermissionCode.SERVICE_CONNECTION_MANAGE.code,
        )

        val ORG_ROLE_LEVEL = mapOf(
            OrgRole.owner to 2,
            OrgRole.admin to 1,
            OrgRole.user to 0,
            OrgRole.guest to -1,
        )

        val ORG_MANAGE_MAP = Action.ORG_MANAGE_MAP.map {
            it.key.code to it.value.map { it.code }.toSet()
        }.toMap()
    }

    override fun findOrgRole(name: String): Role? {
        try {
            return iamClient.roleApi.find(
                ProtocolType.AppStack,
                name
            )
        } catch (e: IamNotFoundException) {
            logger.warn("org role [{}] not found", name)
            return null
        }
    }

    override fun findAllOrgRoles(): List<Pair<Role, Boolean>> {
        try {
            val roleNameList = iamClient.resourceApi.findRole(ProtocolType.AppStack, "any")
            return iamClient.roleApi.findAll(
                ProtocolType.AppStack
            ).map { Pair(it, roleNameList.contains(it.name)) }
        } catch (e: IamNotFoundException) {
            logger.warn("org role list not found")
            return emptyList()
        }
    }

    @Can(accessList = [Access(action = Action.ORG_ROLE_MANAGE)])
    override fun createOrgRole(createRoleRequest: CreateRoleRequest): Role {
        return iamClient.roleApi.create(
            ProtocolType.AppStack,
            createRoleRequest
        )
    }

    @Can(accessList = [Access(action = Action.ORG_ROLE_MANAGE)])
    override fun updateOrgRole(role: Role, updateRoleRequest: UpdateRoleRequest): Role {
        try {
            return iamClient.roleApi.update(
                ProtocolType.AppStack,
                role.name,
                updateRoleRequest
            )
        } catch (e: IamNotFoundException) {
            throw BizException(ErrorCode.AS_ROLE_NOT_FOUND)
        }
    }

    @Can(accessList = [Access(action = Action.ORG_ROLE_MANAGE)])
    override fun deleteOrgRole(role: Role): Boolean {
        try {
            iamClient.roleApi.delete(
                ProtocolType.AppStack,
                role.name
            )
        } catch (e: IamNotFoundException) {
            logger.warn("delete org role [{}] not found", role.name)
        }
        return true
    }

    @Can(accessList = [Access(action = Action.ORG_ROLE_MANAGE)])
    override fun saveOrgPermission(role: Role, policyOptionList: List<PolicyOption>): Permission {
        updateServiceConnectionPermission(role, policyOptionList)
        val permissions = iamClient.permissionApi.save(
            ProtocolType.AppStack,
            role.name,
            policyOptionList,
        )
        return permissions
    }

    override fun findAppRole(name: String): Role? {
        try {
            return iamClient.roleApi.find(
                ProtocolType.AppStackApp,
                name
            )
        } catch (e: IamNotFoundException) {
            logger.warn("app role [{}] not found", name)
            return null
        }
    }

    @Can(accessList = [Access(action = Action.ORG_APP_ROLE_MANAGE)])
    override fun saveAppPermission(role: Role, policyOptionList: List<PolicyOption>): Permission {
        val map = policyOptionList.stream().collect(Collectors.groupingBy(PolicyOption::getProtocol))
        map.forEach { (protocol, list) ->
            iamClient.permissionApi.save(ProtocolType.nameOf(protocol), role.name, list)
        }
        val protocolPermissionMap = map.entries.associate {
            ProtocolType.nameOf(it.key) to iamClient.permissionApi.save(
                ProtocolType.nameOf(it.key),
                role.name,
                it.value
            )
        }
        val permission = merge(*protocolPermissionMap.values.toTypedArray())
        auditLogService.commonLog(OrgEventType.APP_ROLE_PERMISSION_MODIFY, id = AuthUtil.getTenant(), name = "")
        return permission
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_ROLE_VIEW),
            Access(action = Action.ORG_ROLE_MANAGE)
        ]
    )
    override fun findOrgPermission(role: Role): Permission {
        val permission = iamClient.permissionApi.find(
            ProtocolType.AppStack,
            role.name
        )
        val serviceConnectionPermission = findServiceConnectionPermission(role)
        permission.policyOptionList.forEach { policyOption ->
            if (CODE_MAP.keys.contains(policyOption.name)) {
                val flowPermissionCode = CODE_MAP[policyOption.name]!!
                policyOption.enable = serviceConnectionPermission[flowPermissionCode]
            }
        }
        return permission
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_APP_ROLE_VIEW),
            Access(action = Action.ORG_APP_ROLE_MANAGE)
        ]
    )
    override fun findAppPermission(role: Role): Permission {
        val appPermission = iamClient.permissionApi.find(
            ProtocolType.AppStackApp,
            role.name
        )
        val envPermission = iamClient.permissionApi.find(
            ProtocolType.AppStackEnv,
            role.name
        )
        return merge(appPermission, envPermission)
    }

    private fun merge(vararg permissions: Permission): Permission {
        val result = Permission.builder()
            .policyOptionList(mutableListOf()).defaultPolicyOptionList(mutableListOf()).build()
        permissions.forEach { permission ->
            result.policyOptionList.addAll(permission.policyOptionList)
            result.defaultPolicyOptionList.addAll(permission.defaultPolicyOptionList)
        }
        return result.sort()
    }

    override fun findAllAppRoles(): List<Role> {
        try {
            return iamClient.roleApi.findAll(
                ProtocolType.AppStackApp
            ).sortedBy {
                AppRole.valueOf(it.name).ordinal
            }
        } catch (e: IamNotFoundException) {
            logger.warn("app role list not found")
            return emptyList()
        }
    }

    @Can(accessList = [Access(action = Action.ORG_APP_ROLE_MANAGE)])
    override fun createAppRole(createRoleRequest: CreateRoleRequest): Role {
        return iamClient.roleApi.create(
            ProtocolType.AppStackApp,
            createRoleRequest
        )
    }

    @Can(accessList = [Access(action = Action.ORG_APP_ROLE_MANAGE)])
    override fun updateAppRole(role: Role, updateRoleRequest: UpdateRoleRequest): Role {
        try {
            return iamClient.roleApi.update(
                ProtocolType.AppStackApp,
                role.name,
                updateRoleRequest
            )
        } catch (e: IamNotFoundException) {
            throw BizException(ErrorCode.AS_ROLE_NOT_FOUND)
        }
    }

    @Can(accessList = [Access(action = Action.ORG_APP_ROLE_MANAGE)])
    override fun deleteAppRole(role: Role): Boolean {
        try {
            iamClient.roleApi.delete(
                ProtocolType.AppStackApp,
                role.name
            )
        } catch (e: IamNotFoundException) {
            logger.warn("delete app role [{}] not found", role.name)
        }
        return true
    }


    override fun findSystemRole(name: String): Role? {
        return try {
            iamClient.roleApi.find(
                ProtocolType.AppStackSystem,
                name
            )
        } catch (e: IamNotFoundException) {
            logger.warn("system role [{}] not found", name)
            null
        }
    }

    override fun findAllSystemRoles(): List<Role> {
        return try {
            iamClient.roleApi.findAll(
                ProtocolType.AppStackSystem
            ).sortedBy {
                SystemRole.valueOf(it.name).ordinal
            }
        } catch (e: IamNotFoundException) {
            logger.warn("system role list not found")
            emptyList()
        }
    }

    @Can(accessList = [Access(action = Action.ORG_SYSTEM_ROLE_MANAGE)])
    override fun createSystemRole(createRoleRequest: CreateRoleRequest): Role {
        return iamClient.roleApi.create(
            ProtocolType.AppStackSystem,
            createRoleRequest
        )
    }

    @Can(accessList = [Access(action = Action.ORG_SYSTEM_ROLE_MANAGE)])
    override fun updateSystemRole(role: Role, updateRoleRequest: UpdateRoleRequest): Role {
        try {
            return iamClient.roleApi.update(
                ProtocolType.AppStackSystem,
                role.name,
                updateRoleRequest
            )
        } catch (e: IamNotFoundException) {
            throw BizException(ErrorCode.AS_ROLE_NOT_FOUND)
        }
    }

    @Can(accessList = [Access(action = Action.ORG_SYSTEM_ROLE_MANAGE)])
    override fun deleteSystemRole(role: Role): Boolean {
        try {
            iamClient.roleApi.delete(
                ProtocolType.AppStackSystem,
                role.name
            )
        } catch (e: IamNotFoundException) {
            logger.warn("delete system role [{}] not found", role.name)
        }
        return true
    }

    @Can(
        accessList = [
            Access(action = Action.ORG_SYSTEM_ROLE_VIEW),
            Access(action = Action.ORG_SYSTEM_ROLE_MANAGE)
        ]
    )
    override fun findSystemPermission(role: Role): Permission {
        val systemPermission = iamClient.permissionApi.find(
            ProtocolType.AppStackSystem,
            role.name
        )
        val systemLabelPermission = iamClient.permissionApi.find(
            ProtocolType.AppStackSystemLabel,
            role.name
        )
        return merge(systemPermission, systemLabelPermission)
    }

    @Can(accessList = [Access(action = Action.ORG_SYSTEM_ROLE_MANAGE)])
    override fun saveSystemPermission(role: Role, policyOptionList: List<PolicyOption>): Permission {
        // TODO jiuya.wb 这个方法的实现有提取可能么？ cc 元琦
        val map = policyOptionList.stream().collect(Collectors.groupingBy(PolicyOption::getProtocol))
        map.forEach { (protocol, list) ->
            iamClient.permissionApi.save(ProtocolType.nameOf(protocol), role.name, list)
        }
        val protocolPermissionMap = map.entries.associate {
            ProtocolType.nameOf(it.key) to iamClient.permissionApi.save(
                ProtocolType.nameOf(it.key),
                role.name,
                it.value
            )
        }
        val permission = merge(*protocolPermissionMap.values.toTypedArray())
        auditLogService.commonLog(OrgEventType.SYSTEM_ROLE_PERMISSION_MODIFY, id = AuthUtil.getTenant(), name = "")
        return permission
    }

    override fun permissionVisible(): Boolean {
        val tenant = AuthUtil.getTenant()
        return iamClient.enable(tenant)
    }

    override fun findOrgActionAllowed(): List<String> {
        checkPermissionEnable()
        val list = try {
            val list = iamClient.policyApi.findAllByResource(ProtocolType.AppStack, "any")
            if (!list.isNullOrEmpty() && list[0].version != Constant.VERSION) {
                iamClient.rebuildConfig()
                logger.info("org [{}] has updated iam config version", AuthUtil.getTenant())
                iamClient.policyApi.findAllByResourceWithoutCache(ProtocolType.AppStack, "any")
                    .map { policy -> policy.name }
            } else {
                list.map { policy -> policy.name }
            }
        } catch (e: IamNotFoundException) {
            logger.warn("org [{}] does not init permission data", AuthUtil.getTenant())
            // 开启权限
            iamClient.initConfig()
            coreFacades.labelFacade.preset()
            logger.info("org [{}] has init permission data", AuthUtil.getTenant())
            iamClient.policyApi.findAllByResourceWithoutCache(ProtocolType.AppStack, "any")
                .map { policy -> policy.name }
        }
        val member =
            orgFacades.organizationFacade.getMember(orgId = AuthUtil.getTenant(), userId = AuthUtil.getUserId()) ?: return emptyList()
        var permissions: List<FlowRolePermission> = listOf()
        try {
            permissions = findFlowRoleById(member.roleId).permissions
        } catch (e: Throwable) {
            logger.error("failed to findFlowRole: roleId:${member.roleId}", e)
        }
        val actionEnabled = CODE_MAP.mapValues { (_, flowPermissionCode) ->
            permissions.find { it.code == flowPermissionCode }?.enabled ?: false
        }
        val result =
            (list.toSet() + actionEnabled.filter { it.value }.keys - actionEnabled.filter { !it.value }.keys).toMutableSet()
        ORG_MANAGE_MAP.forEach { code, set ->
            // 有管理权限，追加其他相关权限，set集合去重
            if (result.contains(code)) {
                result.addAll(set)
            }
        }
        return result.toList()
    }

    override fun findAppActionAllowed(appName: String): AppActionVO {
        val orgAction = findOrgActionAllowed()
        val appPolicyNameSet = findAppPolicyNameSet(appName)
        val envPolicyNameSet: MutableSet<String>
        if (orgAction.contains(Action.ORG_APP_MANAGE.code)) {
            // 应用的全部权限
            // 暂且不可绕过数据库查询，因为需要初始化注册应用，后续可以替换（owner权限点不会改变，获取默认即可，避免数据库查询）
            val appOwnerDefaultActionList =
                iamClient.protocolApi.find(ProtocolType.AppStackApp).getDefaultPolicyList(AppRole.owner.name)
                    .map { policy -> policy.name }
            appPolicyNameSet.addAll(appOwnerDefaultActionList)
            // owner权限点不会改变，获取默认即可，避免数据库查询
            val envOwnerDefaultActionList =
                iamClient.protocolApi.find(ProtocolType.AppStackEnv).getDefaultPolicyList(AppRole.owner.name)
                    .map { policy -> policy.name }
            envPolicyNameSet = envOwnerDefaultActionList.toMutableSet()
        } else {
            if (orgAction.contains(Action.ORG_APP_VIEW.code)) {
                // 用户有企业级查看应用权限，即拥有应用查看权限
                appPolicyNameSet.add(Action.APP_VIEW.code)
            }
            envPolicyNameSet = findEnvPolicyNameSet(appName)
        }
        return AppActionVO(
            app = appPolicyNameSet.toList().sorted(),
            env = EnvProtocol.Utils.convertToActionLabelMap(envPolicyNameSet),
        )
    }

    override fun findSystemActionAllowed(systemName: String): SystemActionVO {
        val orgAction = findOrgActionAllowed()
        val systemPolicyNameSet = findSystemPolicyNameSet(systemName)
        val systemLabelPolicyNameSet: Set<String>
        if (orgAction.contains(Action.ORG_SYSTEM_MANAGE.code)) {
            val systemOwnerDefaultActionList =
                iamClient.protocolApi.find(ProtocolType.AppStackSystem).getDefaultPolicyList(SystemRole.owner.name)
                    .map { policy -> policy.name }
            systemPolicyNameSet.addAll(systemOwnerDefaultActionList)
            val systemLabelDefaultActionList =
                iamClient.protocolApi.find(ProtocolType.AppStackSystemLabel).getDefaultPolicyList(SystemRole.owner.name)
                    .map { it.name }
            systemLabelPolicyNameSet = systemLabelDefaultActionList.toSet()
        } else {
            if (orgAction.contains(Action.ORG_SYSTEM_VIEW.code)) {
                systemPolicyNameSet.add(Action.SYSTEM_VIEW.code)
            }
            systemLabelPolicyNameSet = findSystemLabelPolicyNameSet(systemName)
        }
        return SystemActionVO(
            system = systemPolicyNameSet.toList().sorted(),
            systemLabel = SystemLabelProtocol.Utils.convertToActionLabelMap(systemLabelPolicyNameSet)
        )
    }

    override fun findResActionAllowed(poolName: String, instanceName: String): List<String> {
        val orgAction = findOrgActionAllowed()
        val resPolicyNameSet = findResPolicyNameSet(poolName, instanceName)
        if (orgAction.contains(Action.ORG_RESOURCE_MANAGE.code)) {
            val resOwnerDefaultActionList: List<String> =
                iamClient.protocolApi.find(ProtocolType.AppStackRes).getDefaultPolicyList(ResRole.owner.name)
                    .map { policy -> policy.name }
            resPolicyNameSet.addAll(resOwnerDefaultActionList)
        } else if (orgAction.contains(Action.ORG_RESOURCE_VIEW.code)) {
            resPolicyNameSet.add(Action.RES_VIEW.code)
        }
        return resPolicyNameSet.toList()
    }

    override fun checkServiceConnectionPermission(vararg actions: Action) {
        actions.forEach {
            if (!CODE_MAP.keys.contains(it.code))
                throw BizException(ErrorCode.AS_PERMISSION_DENIED)
        }
        val member =
            orgFacades.organizationFacade.getMember(orgId = AuthUtil.getTenant(), userId = AuthUtil.getUserId())
        if (member == null || member.isDisabled) {
            throw BizException(ErrorCode.AS_PERMISSION_DENIED)
        }
        val permissions = findFlowRoleById(member.roleId).permissions
        actions.forEach { action ->
            val flowPermissionCode = CODE_MAP[action.code]!!
            val find = permissions.find { it.code == flowPermissionCode }
            if (find?.enabled == true) {
                return
            }
        }
        throw BizException(ErrorCode.AS_PERMISSION_DENIED)
    }

    override fun findVarRole(name: String): Role? {
        try {
            return iamClient.roleApi.find(
                ProtocolType.AppStackVar,
                name
            )
        } catch (e: IamNotFoundException) {
            logger.warn("var role [{}] not found", name)
            return null
        }
    }

    override fun findVarActionAllowed(name: String): List<String> {
        val orgAction = findOrgActionAllowed()
        val varPolicyNameSet = findVarPolicyNameSet(name)
        if (orgAction.contains(Action.ORG_VAR_MANAGE.code)) {
            val resOwnerDefaultActionList: List<String> =
                iamClient.protocolApi.find(ProtocolType.AppStackVar).getDefaultPolicyList(VarRole.owner.name)
                    .map { policy -> policy.name }
            varPolicyNameSet.addAll(resOwnerDefaultActionList)
        } else if (orgAction.contains(Action.ORG_VAR_VIEW.code)) {
            varPolicyNameSet.add(Action.VAR_VIEW.code)
        }
        return varPolicyNameSet.toList()
    }

    override fun findAppTemplateRole(name: String): Role? {
        return try {
            iamClient.roleApi.find(
                ProtocolType.AppStackAppTemplate,
                name
            )
        } catch (e: IamNotFoundException) {
            logger.warn("app template role [{}] not found", name)
            null
        }
    }

    override fun findAppTemplateActionAllowed(name: String): List<String> {
        val orgAction = findOrgActionAllowed()
        val varPolicyNameSet = findAppTemplatePolicyNameSet(name)
        if (orgAction.contains(Action.ORG_APP_TEMPLATE_MANAGE.code)) {
            val resOwnerDefaultActionList: List<String> =
                iamClient.protocolApi.find(ProtocolType.AppStackAppTemplate)
                    .getDefaultPolicyList(AppTemplateRole.owner.name)
                    .map { policy -> policy.name }
            varPolicyNameSet.addAll(resOwnerDefaultActionList)
        } else if (orgAction.contains(Action.ORG_APP_TEMPLATE_VIEW.code)) {
            varPolicyNameSet.add(Action.APP_TEMPLATE_VIEW.code)
        }
        return varPolicyNameSet.toList()
    }

    override fun findAllAppTemplateRoles(): List<Role> {
        return try {
            iamClient.roleApi.findAll(
                ProtocolType.AppStackAppTemplate
            ).sortedBy {
                AppTemplateRole.valueOf(it.name).ordinal
            }
        } catch (e: IamNotFoundException) {
            logger.warn("app template role list not found")
            emptyList()
        }
    }

    protected fun checkPermissionEnable() {
        if (!permissionVisible()) {
            logger.info("企业尚未开启权限功能，忽略下面【未初始化权限数据】报错")
            val bizException = BizException(ErrorCode.AS_PERMISSION_NOT_INIT)
            bizException.errorEntry.showType = ShowType.SILENT
            throw bizException
        }
    }

    private fun findAppPolicyNameSet(appName: String): MutableSet<String> {
        checkPermissionEnable()
        val policyNameSet = HashSet<String>()
        val extraSubjectMap = getExtraSubjectMap(ProtocolType.AppStackApp, appName)
        try {
            val applicationPolicyList =
                iamClient.policyApi.findAllByResource(ProtocolType.AppStackApp, appName, extraSubjectMap)
            applicationPolicyList.forEach { policy ->
                policyNameSet.add(policy.name)
            }
        } catch (e: IamNotFoundException) {
            logger.warn("not find app [{}] permission", appName)
            val appCreator = coreFacades.appFacade.findCreatorByName(appName)
            iamService.registerResource(ProtocolType.AppStackApp, appName, AuthUtil.getTenant(), appCreator)
            val applicationPolicyList =
                iamClient.policyApi.findAllByResourceWithoutCache(ProtocolType.AppStackApp, appName)
            applicationPolicyList.forEach { policy ->
                policyNameSet.add(policy.name)
            }
        }
        // FIXME 更好的逻辑，应该是页面上勾选权限点的时候做联动勾选，比如勾选"管理"就联动勾选"创建"
        if (policyNameSet.contains(Action.APP_VARIABLE_MANAGE.code)) {
            policyNameSet.add(Action.APP_VARIABLE_CREATE.code)
        }
        if (policyNameSet.contains(Action.APP_CR_MANAGE.code)) {
            policyNameSet.add(Action.APP_CR_CREATE.code)
        }
        return policyNameSet
    }

    private fun getExtraSubjectMap(protocolType: ProtocolType, resourceName: String): Map<SubjectType, List<String>> {
        val extraSubjectMap = mutableMapOf<SubjectType, List<String>>()
        try {
            // 找到所有应用配的group和team
            val resourcePlayers =
                iamClient.resourceApi.find(protocolType, resourceName).rolePlayerList.map { it.playerList }
                    .reduce { acc, list -> acc + list }
            val groupPlayerIds = resourcePlayers.filter { it.type == SubjectType.Group }.map { it.id }.toSet()
            val teamPlayerIds = resourcePlayers.filter { it.type == SubjectType.Team }.map { it.id }.toSet()

            // 和用户取交集groups
            val userJoinedGroupIds = if (groupPlayerIds.isNotEmpty()) {
                iamService.getGroupPlayer(AuthUtil.getTenant(), AuthUtil.getUserId()).map { it.id }.intersect(groupPlayerIds).toList()
            } else emptyList()
            if (userJoinedGroupIds.isNotEmpty()) {
                extraSubjectMap[SubjectType.Group] = userJoinedGroupIds
            }

            // 和用户取交集teams
            val userJoinedTeamIds = if (teamPlayerIds.isNotEmpty()) {
                iamService.getTeamPlayer(AuthUtil.getTenant(), AuthUtil.getUserId()).map { it.id }.intersect(teamPlayerIds).toList()
            } else emptyList()
            if (userJoinedTeamIds.isNotEmpty()) {
                extraSubjectMap[SubjectType.Team] = userJoinedTeamIds
            }
        } catch (e: Exception) {
            logger.error("find user related team and group failed:", e)
        }
        return extraSubjectMap.toMap()
    }

    private fun findSystemPolicyNameSet(systemName: String): MutableSet<String> {
        checkPermissionEnable()
        val policyNameSet = mutableSetOf<String>()
        val extraSubjectMap = getExtraSubjectMap(ProtocolType.AppStackSystem, systemName)
        try {
            val systemPolicyList =
                iamClient.policyApi.findAllByResource(ProtocolType.AppStackSystem, systemName, extraSubjectMap)
            systemPolicyList.forEach { policy ->
                policyNameSet.add(policy.name)
            }
        } catch (e: IamNotFoundException) {
            logger.warn("system [{}] permission not found", systemName)
            val systemCreator = coreFacades.appFacade.findCreatorByName(systemName)
            iamService.registerResource(ProtocolType.AppStackSystem, systemName, AuthUtil.getTenant(), systemCreator)
            val systemPolicyList =
                iamClient.policyApi.findAllByResourceWithoutCache(ProtocolType.AppStackSystem, systemName)
            systemPolicyList.forEach { policy ->
                policyNameSet.add(policy.name)
            }
        }
        if (policyNameSet.contains(Action.SYSTEM_RELEASE_MANAGE.code)) {
            policyNameSet.add(Action.SYSTEM_RELEASE_CREATE.code)
        }
        return policyNameSet
    }

    private fun findEnvPolicyNameSet(appName: String): MutableSet<String> {
        checkPermissionEnable()
        val policyNameSet = HashSet<String>()
        val extraSubjectMap = getExtraSubjectMap(ProtocolType.AppStackApp, appName)
        try {
            val envPolicyList =
                iamClient.policyApi.findAllByRoleOnResource(ProtocolType.AppStackEnv, ProtocolType.AppStackApp, appName, extraSubjectMap)
            envPolicyList.forEach { policy ->
                policyNameSet.add(policy.name)
            }
        } catch (e: IamNotFoundException) {
            logger.warn("not find app [{}] env permission", appName)
        }
        return policyNameSet
    }

    private fun findSystemLabelPolicyNameSet(systemName: String): Set<String> {
        checkPermissionEnable()
        return try {
            iamClient.policyApi.findAllByRoleOnResource(
                ProtocolType.AppStackSystemLabel,
                ProtocolType.AppStackSystem,
                systemName
            ).map { it.name }.toSet()
        } catch (e: IamNotFoundException) {
            logger.warn("not find system [$systemName] label permission")
            emptySet()
        }
    }

    private fun findResPolicyNameSet(poolName: String, instanceName: String): MutableSet<String> {
        val resourceInstanceVO = coreFacades.resourceFacade.findInstance(poolName, instanceName)
        val resourceName = resourceInstanceVO.primaryKey()
        val extraSubjectMap = getExtraSubjectMap(ProtocolType.AppStackRes, resourceName)
        val policyNameSet = HashSet<String>()
        try {
            val policies =
                iamClient.policyApi.findAllByResource(ProtocolType.AppStackRes, resourceName, extraSubjectMap)
            policies.forEach { policy ->
                policyNameSet.add(policy.name)
            }
        } catch (e: IamNotFoundException) {
            logger.warn("not find resource [{}] permission", resourceInstanceVO.primaryKey())
            iamService.registerResource(
                protocolType = ProtocolType.AppStackRes,
                resourceName = resourceName,
                orgId = AuthUtil.getTenant(),
                ownerId = resourceInstanceVO.creatorId ?: AuthUtil.getUserId()
            )
            val policies =
                iamClient.policyApi.findAllByResourceWithoutCache(ProtocolType.AppStackRes, resourceName)
            policies.forEach { policy ->
                policyNameSet.add(policy.name)
            }
        }
        return policyNameSet
    }

    private fun findVarPolicyNameSet(varName: String): MutableSet<String> {
        val policyNameSet = HashSet<String>()
        try {
            val varPolicyList = iamClient.policyApi.findAllByResource(ProtocolType.AppStackVar, varName)
            varPolicyList.forEach { policy ->
                policyNameSet.add(policy.name)
            }
        } catch (e: IamNotFoundException) {
            logger.warn("not find var [{}] permission", varName)
            val globalVar = coreFacades.globalVarFacade.find(varName)
            iamService.registerResource(
                ProtocolType.AppStackVar,
                varName,
                AuthUtil.getTenant(),
                globalVar?.creator ?: AuthUtil.getUserId()
            )
            val applicationPolicyList =
                iamClient.policyApi.findAllByResourceWithoutCache(ProtocolType.AppStackVar, varName)
            applicationPolicyList.forEach { policy ->
                policyNameSet.add(policy.name)
            }
        }
        return policyNameSet
    }

    private fun findAppTemplatePolicyNameSet(appTemplateName: String): MutableSet<String> {
        val policyNameSet = HashSet<String>()
        val extraSubjectMap = getExtraSubjectMap(ProtocolType.AppStackAppTemplate, appTemplateName)
        try {
            val varPolicyList = iamClient.policyApi.findAllByResource(
                ProtocolType.AppStackAppTemplate,
                appTemplateName,
                extraSubjectMap
            )
            varPolicyList.forEach { policy ->
                policyNameSet.add(policy.name)
            }
        } catch (e: IamNotFoundException) {
            logger.warn("not find appTemplate [$appTemplateName] permission")
            val appTemplate = appTemplateBizService.find(appTemplateName)
            iamService.registerResource(
                ProtocolType.AppStackAppTemplate,
                appTemplateName,
                AuthUtil.getTenant(),
                appTemplate.creatorId ?: AuthUtil.getUserId()
            )
            val applicationPolicyList =
                iamClient.policyApi.findAllByResourceWithoutCache(ProtocolType.AppStackAppTemplate, appTemplateName)
            applicationPolicyList.forEach { policy ->
                policyNameSet.add(policy.name)
            }
        }
        return policyNameSet
    }

    private fun findServiceConnectionPermission(role: Role): Map<String, Boolean> {
        if (role.protocol != ProtocolType.AppStack.getName()) {
            return emptyMap()
        }
        val permissions = findFlowRole(role.name).permissions
        return FlowPermissionCode.values().associate { flowPermission ->
            flowPermission.code to (permissions.find { it.code == flowPermission.code }?.enabled ?: false)
        }
    }

    private fun updateServiceConnectionPermission(role: Role, policyOptionList: List<PolicyOption>) {
        if (role.protocol != ProtocolType.AppStack.getName()) {
            return
        }
        val actionEnabled = policyOptionList.filter { CODE_MAP.keys.contains(it.name) }.associate {
            Action.codeOf(it.name) to it.enable
        }

        val flowRole = findFlowRole(role.name)
        val flowRolePermissionList = flowRole.permissions
        var hasChanged = false
        actionEnabled.forEach { (action, enable) ->
            val flowPermissionCode = CODE_MAP[action.code]!!
            val flowRolePermission = flowRolePermissionList.find { it.code == flowPermissionCode }!!
            if (flowRolePermission.enabled != enable) {
                hasChanged = true
                flowRolePermission.enabled = enable
            }
        }
        if (hasChanged) {
            val permissions = flowRolePermissionList.filter { it.enabled }.joinToString(separator = ",") { it.code }
            serviceConnectionBizService.updateRolePermission(flowRole.id, flowRole.name, permissions)
        }
    }

    private fun findFlowRole(roleName: String): FlowRole {
        val orgRole = OrgRole.valueOf(roleName)
        val organizationRoleId = getRoleId(orgRole = orgRole)
        return findFlowRoleById(roleId = organizationRoleId)
    }

    private fun findFlowRoleById(roleId: String): FlowRole {
        return serviceConnectionBizService.queryRolePermission(roleId)
    }

    private fun getRoleId(orgRole: OrgRole): String {
        val roles = orgFacades.organizationFacade.getRoles(orgId = AuthUtil.getTenant())
        val organizationRole = roles.first { it.isDefault && it.level == ORG_ROLE_LEVEL[orgRole] }
        return organizationRole.id
    }
}