#!/usr/bin/env sh
set -x

# 模板替换
APP_NAME=${APP_NAME:appstack-core}
IMAGE=${IMAGE:-zeus-acr-cn-beijing-1-registry.cn-beijing.cr.aliyuncs.com/yunxiao-teambition/appstack-biz:20210929211431}
mkdir ".manifests"


if [ -d "./APP-META/kube-config/${APP_NAME}" ];then
  cp -r ./APP-META/kube-config/${APP_NAME}/*.yaml .manifests
else
  cp ./APP-META/kube-config/*.yaml .manifests
fi

echo "start to deploy ${APP_NAME} with ${IMAGE}"
sed -i 's|${IMAGE}|'"${IMAGE}"'|g' .manifests/*
sed -i 's|${APP_NAME}|'"${APP_NAME}"'|g' .manifests/*

# 部署到集群
$KUBECTL_COMMAND apply -f .manifests

# 清理资源
rm -rf .manifests