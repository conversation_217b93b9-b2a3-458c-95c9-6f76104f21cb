FROM reg.docker.alibaba-inc.com/yunxiao-saas/alinux3:union-220901.1-1677808886-1b36651

RUN rpm -ivh --nodeps "http://yum.corp.taobao.com/taobao/7/x86_64/current/ali-jdk/ali-jdk-8.4.7-1519273.alios7.x86_64.rpm" && \
yum clean all

ARG APP_NAME=appstack-web
ENV APP_NAME=${APP_NAME}

USER admin
WORKDIR /home/<USER>

RUN mkdir -p /home/<USER>/${APP_NAME}/target && \
mkdir -p /home/<USER>/${APP_NAME}/config
COPY start.sh /home/<USER>/start.sh
# 下载前端资源脚本到后端镜像
COPY get_assets.sh /home/<USER>/${APP_NAME}/bin/get_assets.sh
# oam 下载脚本
COPY get_oam_resource.sh /home/<USER>/${APP_NAME}/bin/get_oam_resource.sh
COPY ${APP_NAME}.tgz /home/<USER>/${APP_NAME}/target/${APP_NAME}.tgz
# 因为下载前端资源脚本不会变，但下载的前端内容会变，所以必须放在变化的主包后面，防止走docker build缓存
RUN /home/<USER>/${APP_NAME}/bin/get_assets.sh download && \
/home/<USER>/${APP_NAME}/bin/get_assets.sh download_pic && \
/home/<USER>/${APP_NAME}/bin/get_oam_resource.sh download
COPY install-ade2-rollout.sh /home/<USER>/public/oam
COPY install-ade2-kruise-rollout.sh /home/<USER>/public/oam
RUN cd /home/<USER>/${APP_NAME}/target && \
tar xvzf ${APP_NAME}.tgz && \
mv *.jar ${APP_NAME}.jar && \
chmod 755 ${APP_NAME}.jar