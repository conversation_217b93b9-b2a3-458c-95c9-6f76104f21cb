#!/usr/bin/env bash

KRUISE_ROLLOUT_CONTROLLER_CHARTS_FILENAME=${KRUISE_ROLLOUT_CONTROLLER_CHARTS_FILENAME:="./kruise-rollout-stable.tgz"}
KRUISE_ROLLOUT_CONTROLLER_REPLICAS=${KRUISE_ROLLOUT_CONTROLLER_REPLICAS:=1}
KRUISE_ROLLOUT_HELM_NAME=${KRUISE_ROLLOUT_CONTROLLER_NAME:="kruise-rollout-controller-appstack"}
KRUISE_ROLLOUT_CONTROLLER_NAMESPACE=${KRUISE_ROLLOUT_CONTROLLER_NAMESPACE:="kruise-rollout"}
KRUISE_ROLLOUT_MANAGER_NAME="kruise-rollout-controller-manager"

function init_clients() {
  case `uname -m` in \
    x86_64) ARCH=amd64; ;; \
    aarch64) ARCH=arm64; ;; \
    *) echo "un-supported arch, exit ..."; exit 1; ;; \
  esac
  echo "[INIT_CLIENTS] arch = ${ARCH}"

  echo "[INIT_CLIENTS] downloading helm..."
  curl -k  -L $APPSTACK_DOMAIN_WITH_SCHEMA/appstack/public/oam/helm-linux-${ARCH}.tar.gz -o helm-linux-${ARCH}.tar.gz && \
    tar -xzvf helm-linux-${ARCH}.tar.gz && \
    mv ./linux-${ARCH}/helm /usr/local/bin/helm3
  if [[ $? != 0 ]]; then
    echo "[ERROR] failed to download helm client"
    exit 1;
  fi
  chmod +x /usr/local/bin/helm3
  echo "[INIT_CLIENTS] helm client successfully downloaded"

  echo "[INIT_CLIENTS] downloading kubectl..."
  curl -k  -L $APPSTACK_DOMAIN_WITH_SCHEMA/appstack/public/oam/kubectl-linux-${ARCH}.tar.gz -o kubectl-linux-${ARCH}.tar.gz && \
    tar -xzvf kubectl-linux-${ARCH}.tar.gz --strip-components=4 --no-same-owner --wildcards '*/bin' && \
    mv ./kubectl /usr/local/bin/kubectl
  if [[ $? != 0 ]]; then
    echo "[ERROR] failed to download kubectl client"
    exit 1;
  fi
  chmod +x /usr/local/bin/kubectl
  echo "[INIT_CLIENTS] kubectl client successfully downloaded"

  curl -k  -L $APPSTACK_DOMAIN_WITH_SCHEMA/appstack/public/oam/kruise-rollout-stable.tgz -o ./kruise-rollout-stable.tgz
    if [[ $? != 0 ]]; then
    echo "[ERROR] failed to download kruise runtime charts"
    exit 1;
  fi
  echo "[INIT_CLIENTS] kruise runtime charts successfully downloaded"
}

function init_kube_config() {
  echo "[INIT KUBECONFIG] KubeConfig file initializing"
  mkdir -p ~/.kube
  echo $KUBE_CONFIG | base64 -d > ~/.kube/config
  echo "[INIT KUBECONFIG] KubeConfig file initialized"
}

function print_info()
{
  echo "[INFO] environment info"
  kubectl version
  helm3 version
}

function kruise_rollout_agent_install()
{
  kubectl create namespace $KRUISE_ROLLOUT_CONTROLLER_NAMESPACE 2> /dev/null || echo "[KRUISE ROLLOUT NAMESPACE INIT] SUCCESSFUL"
  kubectl get namespace $KRUISE_ROLLOUT_CONTROLLER_NAMESPACE
  if [[ $? != 0 ]]; then
    echo "[ERROR] failed to check namespaces"
    exit 1;
  fi
  echo "[INSTALL] helm upgrade or install kruise-rollout"

  if [[ "$OAM_IMAGE_PULL_SECRET_NAME" != "" ]]; then
    helm3 upgrade --install --set image.repository=$KRUISE_IMAGE_REPOSITORY --set image.tag=$KRUISE_IMAGE_TAG --set "imagePullSecrets[0].name=$OAM_IMAGE_PULL_SECRET_NAME" \
          -n $KRUISE_ROLLOUT_CONTROLLER_NAMESPACE $KRUISE_ROLLOUT_HELM_NAME $KRUISE_ROLLOUT_CONTROLLER_CHARTS_FILENAME \
          --set replicaCount=$KRUISE_ROLLOUT_CONTROLLER_REPLICAS
  else
    helm3 upgrade --install --set image.repository=$KRUISE_IMAGE_REPOSITORY --set image.tag=$KRUISE_IMAGE_TAG \
          -n $KRUISE_ROLLOUT_CONTROLLER_NAMESPACE $KRUISE_ROLLOUT_HELM_NAME $KRUISE_ROLLOUT_CONTROLLER_CHARTS_FILENAME \
          --set replicaCount=$KRUISE_ROLLOUT_CONTROLLER_REPLICAS
  fi
  if [[ $? != 0 ]]; then
    echo "[ERROR] failed to install kruise-rollout release"
    exit 1;
  fi

  successful="false"

  for i in {1..12} ; do
    result=$(kubectl get deployment $KRUISE_ROLLOUT_MANAGER_NAME -n $KRUISE_ROLLOUT_CONTROLLER_NAMESPACE  2>&1)
  	if [[ $? == 0 ]]; then
  		echo "[INSTALL] Deployment $KRUISE_ROLLOUT_MANAGER_NAME is starting"
  		break
  	fi
  	sleep 5
  done

  for i in {1..40} ; do
    result=$(kubectl get deployment $KRUISE_ROLLOUT_MANAGER_NAME -n $KRUISE_ROLLOUT_CONTROLLER_NAMESPACE -o yaml 2>&1 | grep "  readyReplicas: $KRUISE_ROLLOUT_CONTROLLER_REPLICAS")
    if [[ $? == 0 ]]; then
      echo "[INSTALL] agent install successful"
      successful="true"
      break
    fi
    sleep 5
  done

  if [[ $successful == "false" ]]; then
  	echo "[WARNING] agent install maybe failed: Deployment $KRUISE_ROLLOUT_CONTROLLER_NAME in namespace $KRUSIE_ROLOUT_CONTROLLER_NAMESPACE not ready"
  fi

}

init_clients
init_kube_config
print_info
kruise_rollout_agent_install