#!/bin/sh
#
# Download insight vpc front assets to container
# modify time: 2022.04.12
# description: Download insight vpc front assets to container path /home/<USER>/public/****

ADMIN_DIR=/home/<USER>
RESOURCE_PATH=$ADMIN_DIR/public/oam
#HELM_URL=https://rdc-public-software.oss-cn-hangzhou.aliyuncs.com/helm/helm-v3.4.1-linux-amd64.tar.gz
#KUBECTL_URL=https://rdc-beijing-public.oss-cn-beijing.aliyuncs.com/kubernetes-release/release/v1.16.4/bin/linux/amd64/kubectl
VELA_ROLLOUT_URL=https://rdc-beijing-public.oss-cn-beijing.aliyuncs.com/kubernetes-release/oam/vela-rollout-stable-v5.tgz
HELM_AMD64_URL=https://rdc-public-software.oss-cn-hangzhou.aliyuncs.com/helm/helm-v3.8.2-linux-amd64.tar.gz
HELM_ARM64_URL=https://rdc-public-software.oss-cn-hangzhou.aliyuncs.com/helm/helm-v3.8.2-linux-arm64.tar.gz
KUBECTL_AMD64_URL=https://rdc-public-software.oss-cn-hangzhou.aliyuncs.com/kubectl/kubectl-1.24.15-1-linux-amd64-debian-11.tar.gz
KUBECTL_ARM64_URL=https://rdc-public-software.oss-cn-hangzhou.aliyuncs.com/kubectl/kubectl-1.24.15-1-linux-arm64-debian-11.tar.gz
KRUISE_ROLLOUT_URL=https://rdc-beijing-public.oss-cn-beijing.aliyuncs.com/kubernetes-release/oam/kruise-rollout-0.4.1-for-v5.tgz


# 下载前端js资源
download() {
    if [ -d $RESOURCE_PATH ];then
       rm -rf  $RESOURCE_PATH
    fi
    mkdir -p $RESOURCE_PATH
    curl -L $HELM_AMD64_URL -o $RESOURCE_PATH/helm-linux-amd64.tar.gz
    curl -L $HELM_ARM64_URL -o $RESOURCE_PATH/helm-linux-arm64.tar.gz
    curl -L $KUBECTL_AMD64_URL -o $RESOURCE_PATH/kubectl-linux-amd64.tar.gz
    curl -L $KUBECTL_ARM64_URL -o $RESOURCE_PATH/kubectl-linux-arm64.tar.gz
    curl -L $VELA_ROLLOUT_URL -o $RESOURCE_PATH/vela-rollout-stable.tgz
    curl -L $KRUISE_ROLLOUT_URL -o $RESOURCE_PATH/kruise-rollout-stable.tgz
}


case "$1" in
    download)
        download && exit 0
        ;;
    *)
        echo $"Usage: sh $0 download"
        exit 2
esac

exit $?