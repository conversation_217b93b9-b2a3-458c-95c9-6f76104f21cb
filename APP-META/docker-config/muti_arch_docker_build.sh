docker login -u images@1228139241420561 -p aegis-paas repo-registry.cn-shanghai.cr.aliyuncs.com
docker buildx create --name mybuilder --use
ls -alht
tree ./APP-META

tag=v${DATETIME}-${CI_COMMIT_ID}-union

image_biz=repo-registry.cn-shanghai.cr.aliyuncs.com/yunxiao-paas/appstack-biz:${tag}
echo "docker buildx build --platform linux/amd64,linux/arm64 --tag ${image_biz} -f APP-META/docker-config/Dockerfile --build-arg APP_NAME=appstack-biz --progress=plain --push APP-META/docker-config"
docker buildx build --platform linux/amd64,linux/arm64 --tag ${image_biz} -f ./APP-META/docker-config/Dockerfile --build-arg APP_NAME=appstack-biz --progress=plain --push APP-META/docker-config
echo "USER_image_biz_id=${image_biz}" >> .env

image_change_controller=repo-registry.cn-shanghai.cr.aliyuncs.com/yunxiao-paas/appstack-change-controller:${tag}
echo "docker buildx build --platform linux/amd64,linux/arm64 --tag ${image_change_controller} -f APP-META/docker-config/Dockerfile --build-arg APP_NAME=appstack-change-controller --progress=plain --push APP-META/docker-config"
docker buildx build --platform linux/amd64,linux/arm64 --tag ${image_change_controller} -f ./APP-META/docker-config/Dockerfile --build-arg APP_NAME=appstack-change-controller --progress=plain --push APP-META/docker-config
echo "USER_image_change_controller_id=${image_change_controller}" >> .env

image_core=repo-registry.cn-shanghai.cr.aliyuncs.com/yunxiao-paas/appstack-core:${tag}
echo "docker buildx build --platform linux/amd64,linux/arm64 --tag ${image_core} -f APP-META/docker-config/Dockerfile --build-arg APP_NAME=appstack-core --progress=plain --push APP-META/docker-config"
docker buildx build --platform linux/amd64,linux/arm64 --tag ${image_core} -f ./APP-META/docker-config/Dockerfile --build-arg APP_NAME=appstack-core --progress=plain --push APP-META/docker-config
echo "USER_image_core_id=${image_core}" >> .env

image_iam=repo-registry.cn-shanghai.cr.aliyuncs.com/yunxiao-paas/appstack-iam:${tag}
echo "docker buildx build --platform linux/amd64,linux/arm64 --tag ${image_iam} -f APP-META/docker-config/Dockerfile --build-arg APP_NAME=appstack-iam --progress=plain --push APP-META/docker-config"
docker buildx build --platform linux/amd64,linux/arm64 --tag ${image_iam} -f ./APP-META/docker-config/Dockerfile --build-arg APP_NAME=appstack-iam --progress=plain --push APP-META/docker-config
echo "USER_image_iam_id=${image_iam}" >> .env

image_resource_manager=repo-registry.cn-shanghai.cr.aliyuncs.com/yunxiao-paas/appstack-resource-manager:${tag}
echo "docker buildx build --platform linux/amd64,linux/arm64 --tag ${image_resource_manager} -f APP-META/docker-config/Dockerfile --build-arg APP_NAME=appstack-resource-manager --progress=plain --push APP-META/docker-config"
docker buildx build --platform linux/amd64,linux/arm64 --tag ${image_resource_manager} -f ./APP-META/docker-config/Dockerfile --build-arg APP_NAME=appstack-resource-manager --progress=plain --push APP-META/docker-config
echo "USER_image_resource_manager_id=${image_resource_manager}" >> .env

image_web=repo-registry.cn-shanghai.cr.aliyuncs.com/yunxiao-paas/appstack-web:${tag}
echo "docker buildx build --platform linux/amd64,linux/arm64 --tag ${image_web} -f APP-META/docker-config/Dockerfile_multi_arch_web --build-arg APP_NAME=appstack-web --progress=plain --push APP-META/docker-config"
docker buildx build --platform linux/amd64,linux/arm64 --tag ${image_web} -f ./APP-META/docker-config/Dockerfile_multi_arch_web --build-arg APP_NAME=appstack-web --progress=plain --push APP-META/docker-config
echo "USER_image_web_id=${image_web}" >> .env

image_workflow_controller=repo-registry.cn-shanghai.cr.aliyuncs.com/yunxiao-paas/appstack-workflow-controller:${tag}
echo "docker buildx build --platform linux/amd64,linux/arm64 --tag ${image_workflow_controller} -f APP-META/docker-config/Dockerfile --build-arg APP_NAME=appstack-workflow-controller --progress=plain --push APP-META/docker-config"
docker buildx build --platform linux/amd64,linux/arm64 --tag ${image_workflow_controller} -f ./APP-META/docker-config/Dockerfile --build-arg APP_NAME=appstack-workflow-controller --progress=plain --push APP-META/docker-config
echo "USER_image_workflow_controller_id=${image_workflow_controller}" >> .env
