FROM reg.docker.alibaba-inc.com/yunxiao-saas/alinux3:union-220901.1-1677808886-1b36651

RUN rpm -ivh --nodeps "http://yum.corp.taobao.com/taobao/7/x86_64/current/ali-jdk/ali-jdk-8.4.7-1519273.alios7.x86_64.rpm" && \
yum clean all

ARG APP_NAME=appstack-change-controller
ENV APP_NAME=${APP_NAME}

USER admin
WORKDIR /home/<USER>

RUN mkdir -p /home/<USER>/${APP_NAME}/target && \
mkdir -p /home/<USER>/${APP_NAME}/config
COPY start.sh /home/<USER>/start.sh
COPY ${APP_NAME}.tgz /home/<USER>/${APP_NAME}/target/${APP_NAME}.tgz
RUN cd /home/<USER>/${APP_NAME}/target && \
tar xvzf ${APP_NAME}.tgz && \
mv *.jar ${APP_NAME}.jar && \
chmod 755 ${APP_NAME}.jar