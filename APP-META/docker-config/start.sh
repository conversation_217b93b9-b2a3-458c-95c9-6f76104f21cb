export JAVA_HOME=/opt/taobao/java
export CPU_COUNT="$(grep -c 'cpu[0-9][0-9]*' /proc/stat)"
if [ ! -d "/home/<USER>/logs" ]; then
	mkdir /home/<USER>/logs;
fi

if [[ $MEM_LIMIT ]]; then
  memTotal=$MEM_LIMIT
else
  memTotal=`cat /proc/meminfo | grep MemTotal | awk '{printf "%d", $2/1024*0.75 }'`
fi

if [ $memTotal -ge 60000 ]; then
    JAVA_OPTS="${JAVA_OPTS} -Xms4g -Xmx4g -Xmn2g"
elif [ $memTotal -ge 4096 ]; then
    JAVA_OPTS="${JAVA_OPTS} -Xms2g -Xmx2g -Xmn1g"
elif [ $memTotal -ge 3072 ]; then
    JAVA_OPTS="${JAVA_OPTS} -Xms1536m -Xmx1536m -Xmn768m"
elif [ $memTotal -ge 2048 ]; then
    JAVA_OPTS="${JAVA_OPTS} -Xms1g -Xmx1g -Xmn512m"
elif [ $memTotal -ge 1024 ]; then
    JAVA_OPTS="${JAVA_OPTS} -Xms512m -Xmx512m -Xmn256m"
elif [ $memTotal -ge 512 ]; then
    JAVA_OPTS="${JAVA_OPTS} -Xms256m -Xmx256m -Xmn128m"
else
    JAVA_OPTS="${JAVA_OPTS} -Xms128m -Xmx128m -Xmn64m"
fi

JAVA_OPTS="${JAVA_OPTS}"
JAVA_OPTS="${JAVA_OPTS} -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=256m"
JAVA_OPTS="${JAVA_OPTS} -XX:MaxDirectMemorySize=1g"
JAVA_OPTS="${JAVA_OPTS} -XX:SurvivorRatio=10"
JAVA_OPTS="${JAVA_OPTS} -XX:+UseConcMarkSweepGC -XX:CMSMaxAbortablePrecleanTime=5000"
JAVA_OPTS="${JAVA_OPTS} -XX:+CMSClassUnloadingEnabled -XX:CMSInitiatingOccupancyFraction=80 -XX:+UseCMSInitiatingOccupancyOnly"
JAVA_OPTS="${JAVA_OPTS} -XX:+ExplicitGCInvokesConcurrent -Dsun.rmi.dgc.server.gcInterval=2592000000 -Dsun.rmi.dgc.client.gcInterval=2592000000"
JAVA_OPTS="${JAVA_OPTS} -XX:ParallelGCThreads=${CPU_COUNT}"
JAVA_OPTS="${JAVA_OPTS} -XX:+PrintGCDetails -XX:+PrintGCDateStamps"
JAVA_OPTS="${JAVA_OPTS} -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/home/<USER>/logs/java.hprof"
JAVA_OPTS="${JAVA_OPTS} -Djava.awt.headless=true"
JAVA_OPTS="${JAVA_OPTS} -Dsun.net.client.defaultConnectTimeout=10000"
JAVA_OPTS="${JAVA_OPTS} -Dsun.net.client.defaultReadTimeout=30000"
JAVA_OPTS="${JAVA_OPTS} -Dfile.encoding=UTF-8"
JAVA_OPTS="${JAVA_OPTS} -Dlog4j2.formatMsgNoLookups=true"
JAVA_OPTS="${JAVA_OPTS} -Dproject.name=${APP_NAME}"
echo "JAVA_OPTS:"
echo $JAVA_OPTS

$JAVA_HOME/bin/java -jar -server $JAVA_OPTS /home/<USER>/${APP_NAME}/target/${APP_NAME}.jar