#!/bin/sh
#
# Download insight vpc front assets to container
# modify time: 2022.04.12
# description: Download insight vpc front assets to container path /home/<USER>/public/****

ADMIN_DIR=/home/<USER>
ASSERTS_PROJECT_NAME=appstack-assets
ASSERTS_VERSION=5.11.6
STATIC_ASSETS_PATH=$ADMIN_DIR/public/ais-fed/$ASSERTS_PROJECT_NAME
PUBLIC_TEMP_TGZS=$ADMIN_DIR/tgzs
# 注意第一张图片为logo url, 固定下载
CARD_PIC_URLS=("https://img.alicdn.com/imgextra/i4/O1CN018x1B401eEJD68K86B_!!6000000003839-55-tps-102-102.svg")

# 下载前端js资源
download() {
    if [ -d $STATIC_ASSETS_PATH/$ASSERTS_VERSION ];then
       rm -rf  $STATIC_ASSETS_PATH/$ASSERTS_VERSION
    fi
    mkdir -p $STATIC_ASSETS_PATH/$ASSERTS_VERSION
    mkdir -p $PUBLIC_TEMP_TGZS
    QUERY_ASSETS_PATH_URL="https://devops.aliyun.com/projex/inner/api/v1/common/asset/build/list?projectList=$ASSERTS_PROJECT_NAME&branch=release/$ASSERTS_VERSION"
    QUERY_ASSETS_PATH_RESULT=$(curl -k $QUERY_ASSETS_PATH_URL -H 'X-Tenant-Id:alibaba' -H 'X-Operator-Id:AK-ADMIN')
    ASSET_RESULTS="`echo $QUERY_ASSETS_PATH_RESULT| sed 's/^.*\[//' | sed 's/\].*$//'`"
    DOWN_LOAD_URL=(${ASSET_RESULTS#*dist\":})
    # 去掉双引号和右括号得到最终的下载url
    DOWN_LOAD_URL="`echo $DOWN_LOAD_URL| sed 's/\"//'| sed 's/\"//'| sed 's/}//'`"
    echo "Download targz url is $DOWN_LOAD_URL"
}

extract(){
    # 开始下载并解压前端tar包到指定目录
    wget -N -q -O $PUBLIC_TEMP_TGZS/assert.tar.gz $DOWN_LOAD_URL 2>&1
    tar zxf $PUBLIC_TEMP_TGZS/assert.tar.gz -C $STATIC_ASSETS_PATH/$ASSERTS_VERSION
    ls -alh $STATIC_ASSETS_PATH/$ASSERTS_VERSION
    echo "Download and extract assets success!"
}

# 下载指标卡图片cdn资源
download_pic(){
    for pic_url in ${CARD_PIC_URLS[@]}
    do
        pic_url_suffix="`echo $pic_url| sed 's/^.*imgextra\///'  `"
        pic_url_prefix="`echo $pic_url_suffix| sed 's/\/.*//'`"
        mkdir -p $STATIC_ASSETS_PATH/$ASSERTS_VERSION/$pic_url_prefix
        echo $pic_url_prefix $pic_url_suffix
        wget -N -q -O $STATIC_ASSETS_PATH/$ASSERTS_VERSION/$pic_url_suffix  $pic_url 2>&1
    done
    ls -alh $STATIC_ASSETS_PATH/$ASSERTS_VERSION/
}

case "$1" in
    download)
        download && extract && exit 0
        ;;
    download_pic)
        download_pic && exit 0
        ;;
    *)
        echo $"Usage: sh $0 download"
        exit 2
esac

exit $?