#!/usr/bin/env bash

function init_clients() {
  case `uname -m` in \
    x86_64) ARCH=amd64; ;; \
    aarch64) ARCH=arm64; ;; \
    *) echo "un-supported arch, exit ..."; exit 1; ;; \
  esac
  echo "[INIT_CLIENTS] arch = ${ARCH}"

  echo "[INIT_CLIENTS] downloading helm..."
  curl -k  -L $APPSTACK_DOMAIN_WITH_SCHEMA/appstack/public/oam/helm-linux-${ARCH}.tar.gz -o helm-linux-${ARCH}.tar.gz && \
    tar -xzvf helm-linux-${ARCH}.tar.gz && \
    mv ./linux-${ARCH}/helm /usr/local/bin/helm3
  if [[ $? != 0 ]]; then
    echo "[ERROR] failed to download helm client"
    exit 1;
  fi
  chmod +x /usr/local/bin/helm3
  echo "[INIT_CLIENTS] helm client successfully downloaded"

  echo "[INIT_CLIENTS] downloading kubectl..."
  curl -k  -L $APPSTACK_DOMAIN_WITH_SCHEMA/appstack/public/oam/kubectl-linux-${ARCH}.tar.gz -o kubectl-linux-${ARCH}.tar.gz && \
    tar -xzvf kubectl-linux-${ARCH}.tar.gz --strip-components=4 --no-same-owner --wildcards '*/bin' && \
    mv ./kubectl /usr/local/bin/kubectl
  if [[ $? != 0 ]]; then
    echo "[ERROR] failed to download kubectl client"
    exit 1;
  fi
  chmod +x /usr/local/bin/kubectl
  echo "[INIT_CLIENTS] kubectl client successfully downloaded"

  curl -k  -L $APPSTACK_DOMAIN_WITH_SCHEMA/appstack/public/oam/vela-rollout-stable.tgz -o ./vela-rollout-stable.tgz
    if [[ $? != 0 ]]; then
    echo "[ERROR] failed to download OAM runtime charts"
    exit 1;
  fi
  echo "[INIT_CLIENTS] OAM runtime charts successfully downloaded"
}

function init_kube_config() {
  echo "[INIT KUBECONFIG] KubeConfig file initializing"
  mkdir -p ~/.kube
  echo $KUBE_CONFIG | base64 -d > ~/.kube/config
  echo "[INIT KUBECONFIG] KubeConfig file initialized"
}

function print_info()
{
  echo "[INFO] environment info"
  kubectl version
  helm3 version
}

function agent_install()
{
  kubectl get namespace edas-oam-system
  if [[ $? == 0 ]]; then
    echo "[ERROR] namespace edas-oam-system detected - aborted"
    exit 1;
  fi

  kubectl create namespace vela-system 2> /dev/null || echo "[NAMESPACE INIT] SUCCESSFUL"
  kubectl get namespaces
  if [[ $? != 0 ]]; then
    echo "[ERROR] failed to check namespaces"
    exit 1;
  fi

  echo "[INSTALL] helm upgrade kubevela"
  if [[ "$OAM_IMAGE_PULL_SECRET_NAME" != "" ]]; then
    helm3 upgrade --install --set image.repository=$OAM_IMAGE_REPOSITORY --set image.tag=$OAM_IMAGE_TAG --set "imagePullSecrets[0].name=$OAM_IMAGE_PULL_SECRET_NAME" -n vela-system vela-rollout-appstack ./vela-rollout-stable.tgz
  else
    helm3 upgrade --install --set image.repository=$OAM_IMAGE_REPOSITORY --set image.tag=$OAM_IMAGE_TAG -n vela-system vela-rollout-appstack ./vela-rollout-stable.tgz
  fi
  if [[ $? != 0 ]]; then
    echo "[ERROR] failed to install kubevela/vela-rollout release"
    exit 1;
  fi

  successful="false"

  for i in {1..12} ; do
  	result=`kubectl get deployment vela-rollout-appstack -n vela-system  2>&1`
  	if [[ $? == 0 ]]; then
  		echo "[INSTALL] Deployment vela-rollout-appstack is starting"
  		break
  	fi
  	sleep 5
  done


  for i in {1..40} ; do
    result=`kubectl get deployment vela-rollout-appstack -n vela-system -o yaml 2>&1 | grep '  readyReplicas: 1'`
    if [[ $? == 0 ]]; then
      echo "[INSTALL] agent install successful"
      successful="true"
      break
    fi
    sleep 5
  done

  if [[ $successful == "false" ]]; then
  	echo "[WARNING] agent install maybe failed: Deployment vela-rollout-appstack not ready"
  fi
}

init_clients
init_kube_config
print_info
agent_install