apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ${APP_NAME}
  name: ${APP_NAME}
spec:
  selector:
    matchLabels:
      app: ${APP_NAME}
  template:
    metadata:
      labels:
        app: ${APP_NAME}
    spec:
      initContainers:
        - name: agent-container
          image: apache/skywalking-java-agent:8.5.0-alpine
          volumeMounts:
            - name: skywalking-agent
              mountPath: /agent
          command: [ "/bin/sh" ]
          args: [ "-c", "cp -R /skywalking/agent /agent/" ]
      containers:
        - image: ${IMAGE}
          imagePullPolicy: Always
          name: main
          command: [ "/bin/sh" ]
          args: [
              "/home/<USER>/start.sh"
          ]
          resources:
            requests:
              cpu: 2000m
              memory: 4096Mi
          envFrom:
            - configMapRef:
                name: ${APP_NAME}-config
          env:
            - name: ALIYUN_LOGTAIL_USER_DEFINED_ID
              value: sls-appstack-log
            - name: ALIYUN_FILE_LOGTAIL_USER_DEFINED_ID
              value: ${APP_NAME}
            - name: SW_AGENT_OPEN_DEBUG
              value: "false"
            - name: JAVA_TOOL_OPTIONS
              value: "-Dskywalking_config=/etc/skywalking/agent.config -javaagent:/home/<USER>/skywalking/agent/skywalking-agent.jar -Dskywalking.agent.service_name=${APP_NAME} -DSW_AGENT_COLLECTOR_BACKEND_SERVICES=collector.backend_service=tracing-analysis-dc-hz.aliyuncs.com:8000 -DSW_AGENT_NAME=${APP_NAME}"
            - name: OSS_ENDPOINT
              value: "http://oss-cn-hangzhou.aliyuncs.com"
            - name: OSS_AK_ID
              value: "Ln7l876SDetpzj4V"
            - name: OSS_AK_SECRET
              value: "******************************"
            - name: OSS_BUCKET_NAME
              value: "appstack-env"
            - name: MQ_ACCESS_KEY
              value: "LTAI4G7LJXRZdkQzz871M2f7"
            - name: MQ_SECRET_KEY
              value: "******************************"
            - name: MQ_NAME_SRV_ADDR
              value: "http://MQ_INST_1005298046719350_BXYhhwJH.mq-internet-access.mq-internet.aliyuncs.com:80"
            - name: BROADCAST_MQ_TOPIC
              value: "APPSTACK_BROADCAST_DAILY"
          volumeMounts:
            - mountPath: /home/<USER>/${APP_NAME}/logs/
              name: volumn-sls
            - name: skywalking-agent
              mountPath: /home/<USER>/skywalking
            - name: skywalking-agent-config
              mountPath: /etc/skywalking/
      imagePullSecrets:
        - name: yunxiao-teambition-zeus-user
      restartPolicy: Always
      volumes:
        - emptyDir: { }
          name: volumn-sls
        - name: skywalking-agent
          emptyDir: { }
        - name: skywalking-agent-config
          configMap:
            name: skywalking-agent-config
            items:
              - key: agent.config
                path: agent.config